import '../l10n/app_localizations.dart';

class WeekdayHelper {
  /// Get the full weekday name (e.g., "Monday", "<PERSON><PERSON>")
  static String getWeekdayName(AppLocalizations localizations, int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return localizations.translate('calendar.weekdays.monday');
      case DateTime.tuesday:
        return localizations.translate('calendar.weekdays.tuesday');
      case DateTime.wednesday:
        return localizations.translate('calendar.weekdays.wednesday');
      case DateTime.thursday:
        return localizations.translate('calendar.weekdays.thursday');
      case DateTime.friday:
        return localizations.translate('calendar.weekdays.friday');
      case DateTime.saturday:
        return localizations.translate('calendar.weekdays.saturday');
      case DateTime.sunday:
        return localizations.translate('calendar.weekdays.sunday');
      default:
        return '';
    }
  }

  /// Get the short weekday name (e.g., "Mon", "Lun")
  static String getWeekdayShort(AppLocalizations localizations, int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return localizations.translate('calendar.weekdays.monday_short');
      case DateTime.tuesday:
        return localizations.translate('calendar.weekdays.tuesday_short');
      case DateTime.wednesday:
        return localizations.translate('calendar.weekdays.wednesday_short');
      case DateTime.thursday:
        return localizations.translate('calendar.weekdays.thursday_short');
      case DateTime.friday:
        return localizations.translate('calendar.weekdays.friday_short');
      case DateTime.saturday:
        return localizations.translate('calendar.weekdays.saturday_short');
      case DateTime.sunday:
        return localizations.translate('calendar.weekdays.sunday_short');
      default:
        return '';
    }
  }

  /// Get the abbreviated weekday name (e.g., "M", "L")
  static String getWeekdayAbbr(AppLocalizations localizations, int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return localizations.translate('calendar.weekdays.monday_abbr');
      case DateTime.tuesday:
        return localizations.translate('calendar.weekdays.tuesday_abbr');
      case DateTime.wednesday:
        return localizations.translate('calendar.weekdays.wednesday_abbr');
      case DateTime.thursday:
        return localizations.translate('calendar.weekdays.thursday_abbr');
      case DateTime.friday:
        return localizations.translate('calendar.weekdays.friday_abbr');
      case DateTime.saturday:
        return localizations.translate('calendar.weekdays.saturday_abbr');
      case DateTime.sunday:
        return localizations.translate('calendar.weekdays.sunday_abbr');
      default:
        return '';
    }
  }

  /// Get formatted weekday with day number (e.g., "Mon 15", "Lun 15")
  static String getWeekdayWithDay(AppLocalizations localizations, DateTime date) {
    final weekdayShort = getWeekdayShort(localizations, date.weekday);
    return '$weekdayShort ${date.day}';
  }

  /// Get full formatted date with weekday (e.g., "Monday, January 15, 2024", "Luni, 15 ianuarie 2024")
  static String getFullDateWithWeekday(AppLocalizations localizations, DateTime date) {
    final weekdayName = getWeekdayName(localizations, date.weekday);
    return '$weekdayName, ${date.day}/${date.month}/${date.year}';
  }
}
