/// Utility class for Romanian phone number formatting and validation
class PhoneNumberUtils {
  /// Romanian country code
  static const String romanianCountryCode = '+40';
  
  /// Romanian mobile prefixes (without country code)
  static const List<String> romanianMobilePrefixes = [
    '72', '73', '74', '75', '76', '77', '78', '79', // Orange
    '70', '71', // Vodafone  
    '74', '75', '76', '77', // <PERSON>gi
    '78', '79', // Telekom
  ];

  /// Format a phone number to Romanian standard (+40XXXXXXXXX)
  /// 
  /// Accepts various formats:
  /// - "0728626399" -> "+40728626399"
  /// - "728626399" -> "+40728626399" 
  /// - "+40728626399" -> "+40728626399"
  /// - "40728626399" -> "+40728626399"
  /// - "+40 728 626 399" -> "+40728626399"
  /// - "0728 626 399" -> "+40728626399"
  static String formatToRomanianStandard(String phoneNumber) {
    if (phoneNumber.trim().isEmpty) {
      return phoneNumber;
    }

    // Remove all spaces, dashes, and other non-digit characters except +
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Handle different input formats
    if (cleaned.startsWith('+40')) {
      // Already has +40 prefix
      return cleaned;
    } else if (cleaned.startsWith('0040') && cleaned.length == 13) {
      // Double country code format (e.g., 0040728626399)
      return '+${cleaned.substring(2)}'; // Remove leading 00, add +
    } else if (cleaned.startsWith('40') && cleaned.length == 11) {
      // Has 40 prefix without +
      return '+$cleaned';
    } else if ((cleaned.length == 9 || cleaned.length == 10)) {
      // Handle both 9 and 10 character inputs - trim leading 0 if present
      String phoneDigits = cleaned.startsWith('0') ? cleaned.substring(1) : cleaned;

      // Ensure we have 9 digits and valid Romanian mobile prefix
      if (phoneDigits.length == 9 && _isValidRomanianMobilePrefix(phoneDigits.substring(0, 2))) {
        return '$romanianCountryCode$phoneDigits';
      }
    } else if (cleaned.startsWith('+') && cleaned.length > 3) {
      // Has + but not Romanian country code - return as is (international number)
      return cleaned;
    }
    
    // If none of the above patterns match, return the original cleaned number
    return cleaned;
  }

  /// Validate if a phone number is a valid Romanian mobile number
  static bool isValidRomanianMobile(String phoneNumber) {
    final formatted = formatToRomanianStandard(phoneNumber);
    
    // Must start with +40 and have exactly 12 characters total
    if (!formatted.startsWith(romanianCountryCode) || formatted.length != 12) {
      return false;
    }
    
    // Extract the mobile number part (without +40)
    final mobileNumber = formatted.substring(3);
    
    // Must be exactly 9 digits
    if (mobileNumber.length != 9) {
      return false;
    }
    
    // Must start with valid Romanian mobile prefix
    final prefix = mobileNumber.substring(0, 2);
    return _isValidRomanianMobilePrefix(prefix);
  }

  /// Check if a prefix is a valid Romanian mobile prefix
  static bool _isValidRomanianMobilePrefix(String prefix) {
    return romanianMobilePrefixes.contains(prefix);
  }

  /// Format phone number for display with spaces for better readability
  /// "+40728626399" -> "+40 728 626 399"
  static String formatForDisplay(String phoneNumber) {
    final formatted = formatToRomanianStandard(phoneNumber);
    
    if (formatted.startsWith(romanianCountryCode) && formatted.length == 12) {
      // Romanian number: +40 XXX XXX XXX
      return '${formatted.substring(0, 3)} ${formatted.substring(3, 6)} ${formatted.substring(6, 9)} ${formatted.substring(9)}';
    }
    
    // For non-Romanian or invalid numbers, return as is
    return formatted;
  }

  /// Get validation error message for phone numbers (international support)
  static String? getValidationError(String phoneNumber) {
    if (phoneNumber.trim().isEmpty) {
      return 'Numărul de telefon este obligatoriu';
    }
    
    // Basic validation for international phone numbers
    final cleanPhone = phoneNumber.trim();

    // Phone number should start with + for international format
    if (!cleanPhone.startsWith('+')) {
      // Try to format as Romanian if no country code
      final formatted = formatToRomanianStandard(phoneNumber);
      if (formatted.startsWith('+') && formatted.length >= 8) {
        return null; // Successfully formatted as Romanian
      }
      return 'Numărul de telefon trebuie să înceapă cu + (ex: +40, +1, +44)';
    }

    // International number should have at least 8 characters (+X XXXXXX)
    if (cleanPhone.length < 8) {
      return 'Numărul de telefon este prea scurt';
    }
    
    return null; // Valid international number
  }

  /// Extract just the mobile number part (without country code)
  /// "+40728626399" -> "728626399"
  static String extractMobileNumber(String phoneNumber) {
    final formatted = formatToRomanianStandard(phoneNumber);
    
    if (formatted.startsWith(romanianCountryCode) && formatted.length == 12) {
      return formatted.substring(3);
    }
    
    return phoneNumber; // Return original if not Romanian format
  }

  /// Convert display format back to standard format
  /// "+40 728 626 399" -> "+40728626399"
  static String parseFromDisplay(String displayNumber) {
    return formatToRomanianStandard(displayNumber);
  }

  /// Check if phone number needs formatting
  static bool needsFormatting(String phoneNumber) {
    final formatted = formatToRomanianStandard(phoneNumber);
    return phoneNumber != formatted;
  }

  /// Get a list of example phone number formats for help text
  static List<String> getExampleFormats() {
    return [
      '+40 728 626 399',
      '0728 626 399', 
      '728626399',
      '+40728626399',
    ];
  }

  /// Format phone number for input field (with spaces for readability)
  static String formatForInput(String phoneNumber) {
    // Check if the phone number is a placeholder (all zeros after +40)
    if (phoneNumber.startsWith(romanianCountryCode)) {
      final digitsAfterCode = phoneNumber.substring(romanianCountryCode.length);
      if (digitsAfterCode.replaceAll('0', '').isEmpty) {
        return ''; // Return empty string for placeholder numbers
      }
    }
    
    // Remove all non-digit characters except +
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Apply standard formatting first
    String standardized = formatToRomanianStandard(cleaned);
    
    // Then format for display
    return formatForDisplay(standardized);
  }

  /// Normalize phone number for backend API calls
  /// Ensures consistent format for all API communications
  /// Supports both Romanian and international phone numbers
  static String normalizeForApi(String phoneNumber) {
    if (phoneNumber.trim().isEmpty) {
      return phoneNumber;
    }

    // Remove all spaces, dashes, and other non-digit characters except +
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // If it already starts with +, it's an international number - return as is
    if (cleaned.startsWith('+')) {
      return cleaned;
    }

    // Otherwise try to format as Romanian number
    return formatToRomanianStandard(phoneNumber);
  }

  /// Batch format multiple phone numbers
  static List<String> formatMultiple(List<String> phoneNumbers) {
    return phoneNumbers.map((phone) => formatToRomanianStandard(phone)).toList();
  }

  /// Check if two phone numbers are the same (after normalization)
  static bool areEqual(String phone1, String phone2) {
    final normalized1 = formatToRomanianStandard(phone1);
    final normalized2 = formatToRomanianStandard(phone2);
    return normalized1 == normalized2;
  }

  /// Get country code from phone number
  static String? getCountryCode(String phoneNumber) {
    final cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    if (cleaned.startsWith('+40')) {
      return '+40';
    } else if (cleaned.startsWith('+')) {
      // Extract country code (+ followed by 1-3 digits)
      final match = RegExp(r'^\+(\d{1,3})').firstMatch(cleaned);
      return match?.group(0);
    }
    
    return null;
  }

  /// Check if phone number is Romanian
  static bool isRomanianNumber(String phoneNumber) {
    final countryCode = getCountryCode(phoneNumber);
    return countryCode == romanianCountryCode;
  }

  /// Format phone number for storage (normalized format)
  static String formatForStorage(String phoneNumber) {
    return formatToRomanianStandard(phoneNumber);
  }

  static normalizePhoneNumber(String phoneNumber) {
    // Normalize phone number to standard format
    return formatToRomanianStandard(phoneNumber);
  }

  static formatPhoneNumber(String phoneNumber) {
    // Format phone number for display
    return formatForDisplay(phoneNumber);
  }
}
