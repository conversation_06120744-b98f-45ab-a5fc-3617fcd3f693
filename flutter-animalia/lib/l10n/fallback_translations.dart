/// Fallback translations to prevent app crashes when translation files are missing
class FallbackTranslations {
  static Map<String, String> getTranslations(String languageCode) {
    switch (languageCode) {
      case 'en':
        return _englishTranslations;
      case 'ro':
      default:
        return _romanianTranslations;
    }
  }

  static const Map<String, String> _romanianTranslations = {
    // SMS Template Types
    'sms_templates.appointment_confirmation': 'Confirmare Programare',
    'sms_templates.appointment_cancellation': 'Anulare Programare',
    'sms_templates.appointment_reschedule': 'Reprogramare',
    'sms_templates.reminder': 'Reminder',
    'sms_templates.appointment_completion': 'Finalizare Programare',
    'sms_templates.follow_up': 'Follow-up',
    
    // Common
    'common.loading': 'Se încarcă...',
    'common.error': 'Eroare',
    'common.success': 'Succes',
    'common.cancel': 'Anulează',
    'common.save': 'Salvează',
    'common.delete': 'Șterge',
    'common.edit': '<PERSON><PERSON><PERSON><PERSON>',
    'common.add': 'Adaugă',
    'common.ok': 'OK',
    'common.yes': 'Da',
    'common.no': 'Nu',
  };

  static const Map<String, String> _englishTranslations = {
    // SMS Template Types
    'sms_templates.appointment_confirmation': 'Appointment Confirmation',
    'sms_templates.appointment_cancellation': 'Appointment Cancellation',
    'sms_templates.appointment_reschedule': 'Appointment Reschedule',
    'sms_templates.reminder': 'Reminder',
    'sms_templates.appointment_completion': 'Appointment Completion',
    'sms_templates.follow_up': 'Follow-up',
    
    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.add': 'Add',
    'common.ok': 'OK',
    'common.yes': 'Yes',
    'common.no': 'No',
  };
}