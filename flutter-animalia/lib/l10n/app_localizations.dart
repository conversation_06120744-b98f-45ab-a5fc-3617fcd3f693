import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'fallback_translations.dart';

/// Main localization class that handles translation loading and access
class AppLocalizations {
  final Locale locale;
  Map<String, String> _localizedStrings = {};
  bool _isLoaded = false;

  AppLocalizations(this.locale);
  
  /// Static method to preload translations for better mobile compatibility
  static Future<void> preloadTranslations() async {
    try {
      final roLocalizations = AppLocalizations(const Locale('ro'));
      await roLocalizations.load();
      
      final enLocalizations = AppLocalizations(const Locale('en'));
      await enLocalizations.load();
    } catch (e) {
      // Fallbacks will be used
    }
  }

  /// Helper method to get the current localization instance from context
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  /// Load the JSON file for the specified locale
  Future<bool> load() async {
    if (_isLoaded && _localizedStrings.isNotEmpty) {
      return true;
    }
    
    try {
      _localizedStrings.clear();
      
      String? jsonString = await _loadTranslationAsset();
      
      if (jsonString == null) {
        createFallbackTranslations();
        return false;
      }

      Map<String, dynamic> jsonMap = json.decode(jsonString);
      
      if (jsonMap.isEmpty) {
        createFallbackTranslations();
        return false;
      }

      _localizedStrings = _flattenTranslations(jsonMap);
      
      if (_localizedStrings.isEmpty) {
        createFallbackTranslations();
        return false;
      }
      
      _isLoaded = true;
      return true;
    } catch (e, stackTrace) {
      createFallbackTranslations();
      return false;
    }
  }
  
  /// Try to load translation asset with multiple fallback paths
  Future<String?> _loadTranslationAsset() async {
    final paths = [
      'translations/${locale.languageCode}.json',
      'assets/translations/${locale.languageCode}.json',
      'translations/ro.json',
      'assets/translations/ro.json',
    ];
    
    for (final path in paths) {
      try {
        final jsonString = await rootBundle.loadString(path);
        if (jsonString.isNotEmpty) {
          return jsonString;
        }
      } catch (e) {
        continue;
      }
    }
    
    return null;
  }
  
  /// Create comprehensive fallback translations to prevent app crashes
  void createFallbackTranslations() {
    _localizedStrings = Map<String, String>.from(FallbackTranslations.getTranslations(locale.languageCode));
    _isLoaded = true;
  }

  /// Flatten nested JSON structure into dot-notation keys
  /// Example: {"login": {"welcome": "Hello"}} -> {"login.welcome": "Hello"}
  Map<String, String> _flattenTranslations(Map<String, dynamic> json, [String prefix = '']) {
    final Map<String, String> result = {};

    json.forEach((key, value) {
      final String newKey = prefix.isEmpty ? key : '$prefix.$key';

      if (value is Map<String, dynamic>) {
        result.addAll(_flattenTranslations(value, newKey));
      } else {
        result[newKey] = value.toString();
      }
    });

    return result;
  }

  /// Get translated string by key
  String translate(String key, {Map<String, String>? params}) {
    String? translation = _localizedStrings[key];
    
    // If translation not found, try fallback translations
    if (translation == null) {
      final fallbackTranslations = FallbackTranslations.getTranslations(locale.languageCode);
      translation = fallbackTranslations[key] ?? '[Translation missing: $key]';
    }

    // Replace parameters if provided
    if (params != null) {
      params.forEach((paramKey, paramValue) {
        translation = translation!.replaceAll('{$paramKey}', paramValue);
      });
    }

    return translation!;
  }

  /// Shorthand method for translation
  String t(String key, {Map<String, String>? params}) {
    return translate(key, params: params);
  }

  /// Check if a translation key exists
  bool hasTranslation(String key) {
    return _localizedStrings.containsKey(key);
  }
  
  /// Get the count of loaded translations
  int get translationCount => _localizedStrings.length;
}

/// Localization delegate for the app
class AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['ro', 'en'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    Locale targetLocale = locale;
    if (!isSupported(locale)) {
      targetLocale = const Locale('ro');
    }
    
    AppLocalizations localizations = AppLocalizations(targetLocale);
    
    try {
      await localizations.load();
      
      if (localizations.translationCount == 0) {
        localizations.createFallbackTranslations();
      }
      
    } catch (e) {
      localizations.createFallbackTranslations();
    }
    
    return localizations;
  }

  @override
  bool shouldReload(AppLocalizationsDelegate old) {
    return kDebugMode;
  }
}

/// Extension on BuildContext for easier access to translations
extension LocalizationExtension on BuildContext {
  AppLocalizations? get l10n {
    try {
      return AppLocalizations.of(this);
    } catch (e) {
      return null;
    }
  }

  String tr(String key, {Map<String, String>? params}) {
    try {
      final localizations = AppLocalizations.of(this);
      return localizations.translate(key, params: params);
    } catch (e) {
      return key;
    }
  }
}
