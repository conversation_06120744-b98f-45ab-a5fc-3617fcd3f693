import 'dart:io';

import 'package:excel/excel.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../models/report_data.dart';

/// Service for exporting reports to Excel format
class ExcelExportService {
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  static final DateFormat _dateTimeFormat = DateFormat('dd/MM/yyyy HH:mm');

  /// Export pet report data to Excel
  static Future<String> exportPetReport(PetReportData data) async {
    final excel = Excel.createExcel();
    final sheet = excel['Raport Animale'];

    // Header information
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Raport Animale pe Mărime și Rasă');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Perioada: ${_dateFormat.format(data.startDate)} - ${_dateFormat.format(data.endDate)}');
    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Generat la: ${_dateTimeFormat.format(data.generatedAt)}');
    sheet.cell(CellIndex.indexByString('A4')).value = TextCellValue('Total animale: ${data.totalPets}');

    // Size breakdown section
    sheet.cell(CellIndex.indexByString('A6')).value = TextCellValue('Distribuție pe Mărime');
    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('Mărime');
    sheet.cell(CellIndex.indexByString('B7')).value = TextCellValue('Număr');
    sheet.cell(CellIndex.indexByString('C7')).value = TextCellValue('Procent');

    int row = 8;
    final totalSize = data.sizeBreakdown.fold(0.0, (sum, item) => sum + item.value);
    for (final item in data.sizeBreakdown) {
      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(item.label);
      sheet.cell(CellIndex.indexByString('B$row')).value = IntCellValue(item.value.toInt());
      sheet.cell(CellIndex.indexByString('C$row')).value = TextCellValue('${((item.value / totalSize) * 100).toStringAsFixed(1)}%');
      row++;
    }

    // Breed breakdown section
    row += 2;
    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('Distribuție pe Rasă');
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('Rasă');
    sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue('Număr');
    row++;

    for (final item in data.breedBreakdown) {
      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(item.label);
      sheet.cell(CellIndex.indexByString('B$row')).value = IntCellValue(item.value.toInt());
      row++;
    }

    return await _saveAndShareExcel(excel, 'raport_animale_${_getDateSuffix(data.startDate, data.endDate)}.xlsx');
  }

  /// Export service report data to Excel
  static Future<String> exportServiceReport(ServiceReportData data) async {
    final excel = Excel.createExcel();
    final sheet = excel['Raport Servicii'];

    // Header information
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Raport Servicii Solicitate');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Perioada: ${_dateFormat.format(data.startDate)} - ${_dateFormat.format(data.endDate)}');
    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Generat la: ${_dateTimeFormat.format(data.generatedAt)}');
    sheet.cell(CellIndex.indexByString('A4')).value = TextCellValue('Total servicii: ${data.totalServices}');
    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('Venituri totale: ${data.totalRevenue.toStringAsFixed(2)} RON');

    // Service requests section
    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('Serviciu');
    sheet.cell(CellIndex.indexByString('B7')).value = TextCellValue('Număr Solicitări');
    sheet.cell(CellIndex.indexByString('C7')).value = TextCellValue('Venituri (RON)');
    sheet.cell(CellIndex.indexByString('D7')).value = TextCellValue('Procent din Total');

    int row = 8;
    for (int i = 0; i < data.serviceRequests.length; i++) {
      final request = data.serviceRequests[i];
      final revenue = data.serviceRevenue[i];
      final percentage = (revenue.value / data.totalRevenue) * 100;

      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(request.label);
      sheet.cell(CellIndex.indexByString('B$row')).value = IntCellValue(request.value.toInt());
      sheet.cell(CellIndex.indexByString('C$row')).value = DoubleCellValue(revenue.value);
      sheet.cell(CellIndex.indexByString('D$row')).value = TextCellValue('${percentage.toStringAsFixed(1)}%');
      row++;
    }

    return await _saveAndShareExcel(excel, 'raport_servicii_${_getDateSuffix(data.startDate, data.endDate)}.xlsx');
  }

  /// Export client report data to Excel
  static Future<String> exportClientReport(ClientReportData data) async {
    final excel = Excel.createExcel();
    final sheet = excel['Raport Clienți'];

    // Header information
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Raport Top Clienți');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Perioada: ${_dateFormat.format(data.startDate)} - ${_dateFormat.format(data.endDate)}');
    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Generat la: ${_dateTimeFormat.format(data.generatedAt)}');
    sheet.cell(CellIndex.indexByString('A4')).value = TextCellValue('Total clienți: ${data.totalClients}');
    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('Cheltuială medie: ${data.averageSpending.toStringAsFixed(2)} RON');

    // Top clients section
    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('Poziție');
    sheet.cell(CellIndex.indexByString('B7')).value = TextCellValue('Client');
    sheet.cell(CellIndex.indexByString('C7')).value = TextCellValue('Cheltuieli (RON)');

    int row = 8;
    for (int i = 0; i < data.topClients.length; i++) {
      final client = data.topClients[i];
      sheet.cell(CellIndex.indexByString('A$row')).value = IntCellValue(i + 1);
      sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue(client.label);
      sheet.cell(CellIndex.indexByString('C$row')).value = DoubleCellValue(client.value);
      row++;
    }

    // Summary section
    row += 2;
    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('Rezumat');
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('Clienți noi');
    sheet.cell(CellIndex.indexByString('B$row')).value = IntCellValue(data.newClients);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('Clienți recurenți');
    sheet.cell(CellIndex.indexByString('B$row')).value = IntCellValue(data.returningClients);

    return await _saveAndShareExcel(excel, 'raport_clienti_${_getDateSuffix(data.startDate, data.endDate)}.xlsx');
  }

  /// Export staff performance report data to Excel
  static Future<String> exportStaffPerformanceReport(StaffPerformanceReportData data) async {
    final excel = Excel.createExcel();
    final sheet = excel['Performanță Staff'];

    // Header information
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Raport Performanță Staff');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Staff: ${data.staffName}');
    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Perioada: ${_dateFormat.format(data.startDate)} - ${_dateFormat.format(data.endDate)}');
    sheet.cell(CellIndex.indexByString('A4')).value = TextCellValue('Generat la: ${_dateTimeFormat.format(data.generatedAt)}');

    // Performance metrics
    sheet.cell(CellIndex.indexByString('A6')).value = TextCellValue('Metrici Performanță');
    sheet.cell(CellIndex.indexByString('A7')).value = TextCellValue('Total programări');
    sheet.cell(CellIndex.indexByString('B7')).value = IntCellValue(data.totalAppointments);
    sheet.cell(CellIndex.indexByString('A8')).value = TextCellValue('Programări finalizate');
    sheet.cell(CellIndex.indexByString('B8')).value = IntCellValue(data.completedAppointments);
    sheet.cell(CellIndex.indexByString('A9')).value = TextCellValue('Programări anulate');
    sheet.cell(CellIndex.indexByString('B9')).value = IntCellValue(data.cancelledAppointments);
    sheet.cell(CellIndex.indexByString('A10')).value = TextCellValue('Venituri totale (RON)');
    sheet.cell(CellIndex.indexByString('B10')).value = DoubleCellValue(data.totalRevenue);
    sheet.cell(CellIndex.indexByString('A11')).value = TextCellValue('Rating mediu');
    sheet.cell(CellIndex.indexByString('B11')).value = DoubleCellValue(data.averageRating);
    sheet.cell(CellIndex.indexByString('A12')).value = TextCellValue('Rata utilizare');
    sheet.cell(CellIndex.indexByString('B12')).value = TextCellValue('${(data.utilizationRate * 100).toStringAsFixed(1)}%');

    // Service breakdown
    int row = 14;
    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('Distribuție Servicii');
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('Serviciu');
    sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue('Număr');
    row++;

    for (final service in data.serviceBreakdown) {
      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(service.label);
      sheet.cell(CellIndex.indexByString('B$row')).value = IntCellValue(service.value.toInt());
      row++;
    }

    return await _saveAndShareExcel(excel, 'raport_staff_${data.staffName.replaceAll(' ', '_')}_${_getDateSuffix(data.startDate, data.endDate)}.xlsx');
  }

  /// Export revenue report data to Excel
  static Future<String> exportRevenueReport(RevenueReportData data) async {
    final excel = Excel.createExcel();
    final sheet = excel['Raport Venituri'];

    // Header information
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('Raport Venituri');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue('Perioada: ${_dateFormat.format(data.startDate)} - ${_dateFormat.format(data.endDate)}');
    sheet.cell(CellIndex.indexByString('A3')).value = TextCellValue('Generat la: ${_dateTimeFormat.format(data.generatedAt)}');
    sheet.cell(CellIndex.indexByString('A4')).value = TextCellValue('Venituri totale: ${data.totalRevenue.toStringAsFixed(2)} RON');
    sheet.cell(CellIndex.indexByString('A5')).value = TextCellValue('Venituri medii zilnice: ${data.averageDailyRevenue.toStringAsFixed(2)} RON');
    sheet.cell(CellIndex.indexByString('A6')).value = TextCellValue('Rata creștere: ${(data.growthRate * 100).toStringAsFixed(1)}%');

    // Daily revenue data
    sheet.cell(CellIndex.indexByString('A8')).value = TextCellValue('Venituri Zilnice');
    sheet.cell(CellIndex.indexByString('A9')).value = TextCellValue('Data');
    sheet.cell(CellIndex.indexByString('B9')).value = TextCellValue('Venituri (RON)');

    int row = 10;
    for (final point in data.dailyRevenue) {
      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(_dateFormat.format(point.date));
      sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(point.value);
      row++;
    }

    // Revenue by service
    row += 2;
    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('Venituri pe Serviciu');
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('Serviciu');
    sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue('Venituri (RON)');
    sheet.cell(CellIndex.indexByString('C$row')).value = TextCellValue('Procent');
    row++;

    for (final service in data.revenueByService) {
      final percentage = (service.value / data.totalRevenue) * 100;
      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(service.label);
      sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(service.value);
      sheet.cell(CellIndex.indexByString('C$row')).value = TextCellValue('${percentage.toStringAsFixed(1)}%');
      row++;
    }

    return await _saveAndShareExcel(excel, 'raport_venituri_${_getDateSuffix(data.startDate, data.endDate)}.xlsx');
  }

  /// Save Excel file and share it
  static Future<String> _saveAndShareExcel(Excel excel, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    
    final bytes = excel.save();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
      
      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Raport generat din aplicația Animalia',
      );
      
      return file.path;
    }
    
    throw Exception('Nu s-a putut genera fișierul Excel');
  }

  /// Generate date suffix for file names
  static String _getDateSuffix(DateTime startDate, DateTime endDate) {
    final start = DateFormat('ddMMyyyy').format(startDate);
    final end = DateFormat('ddMMyyyy').format(endDate);
    return '${start}_$end';
  }
}
