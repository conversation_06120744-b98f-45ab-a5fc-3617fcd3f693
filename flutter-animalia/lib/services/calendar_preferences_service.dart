import 'package:shared_preferences/shared_preferences.dart';

/// Calendar view mode options
enum CalendarHourViewMode {
  businessHours, // Show only business hours
  fullDay, // Show all 24 hours
}

/// Preferred time format for calendar hours
enum CalendarTimeFormat {
  twelveHour,
  twentyFourHour,
}

/// Predefined color options for unavailable slots
enum UnavailableSlotColor {
  gray('<PERSON>', '<PERSON><PERSON>'),
  red('<PERSON>', '<PERSON>o<PERSON><PERSON>'),
  blue('<PERSON>', '<PERSON>st<PERSON>'),
  purple('<PERSON>', '<PERSON>'),
  orange('Orange', 'Portocaliu'),
  brown('<PERSON>', 'Maro');

  const UnavailableSlotColor(this.englishName, this.romanianName);

  final String englishName;
  final String romanianName;
}

/// Service for managing calendar-related user preferences
class CalendarPreferencesService {
  static const String _viewModeKey = 'calendar_view_mode';
  static const String _showFullDayKey = 'calendar_show_full_day';
  static const String _timeSlotHeightKey = 'calendar_time_slot_height';
  static const String _unavailableSlotOpacityKey = 'calendar_unavailable_slot_opacity';
  static const String _unavailableSlotColorKey = 'calendar_unavailable_slot_color';
  static const String _timeFormatKey = 'calendar_time_format';
  static const String _showRevenueSummaryKey = 'calendar_show_revenue_summary';
  static const String _googleCalendarSyncEnabledKey = 'calendar_google_sync_enabled';
  static const String _showGoogleCalendarEventsKey = 'calendar_show_google_events';

  /// Get the saved calendar hour view mode
  static Future<CalendarHourViewMode> getHourViewMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedMode = prefs.getString(_viewModeKey);
      
      if (savedMode != null) {
        return CalendarHourViewMode.values.firstWhere(
          (mode) => mode.toString() == savedMode,
          orElse: () => CalendarHourViewMode.businessHours,
        );
      }
      
      return CalendarHourViewMode.businessHours;
    } catch (e) {
      return CalendarHourViewMode.businessHours;
    }
  }

  /// Save the calendar hour view mode
  static Future<void> setHourViewMode(CalendarHourViewMode mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_viewModeKey, mode.toString());
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Get the saved time format preference
  static Future<CalendarTimeFormat> getTimeFormat() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedFormat = prefs.getString(_timeFormatKey);

      if (savedFormat != null) {
        return CalendarTimeFormat.values.firstWhere(
          (format) => format.toString() == savedFormat,
          orElse: () => CalendarTimeFormat.twelveHour,
        );
      }

      return CalendarTimeFormat.twelveHour;
    } catch (e) {
      return CalendarTimeFormat.twelveHour;
    }
  }

  /// Save the time format preference
  static Future<void> setTimeFormat(CalendarTimeFormat format) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_timeFormatKey, format.toString());
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Get the legacy show full day preference (for migration)
  static Future<bool> getShowFullDay() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_showFullDayKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Get the saved time slot height
  static Future<double> getTimeSlotHeight() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getDouble(_timeSlotHeightKey) ?? 80.0; // Updated default to 80px for better visibility
    } catch (e) {
      return 80.0; // Updated default to 80px for better visibility
    }
  }

  /// Save the time slot height
  static Future<void> setTimeSlotHeight(double height) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_timeSlotHeightKey, height);
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Get the saved unavailable slot opacity
  static Future<double> getUnavailableSlotOpacity() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getDouble(_unavailableSlotOpacityKey) ?? 0.3; // Increased default opacity to 0.3
    } catch (e) {
      return 0.3; // Increased default opacity to 0.3
    }
  }

  /// Save the unavailable slot opacity
  static Future<void> setUnavailableSlotOpacity(double opacity) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_unavailableSlotOpacityKey, opacity.clamp(0.15, 0.9)); // Expanded range
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Get the saved unavailable slot color
  static Future<UnavailableSlotColor> getUnavailableSlotColor() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final colorName = prefs.getString(_unavailableSlotColorKey);
      if (colorName != null) {
        return UnavailableSlotColor.values.firstWhere(
          (color) => color.englishName == colorName,
          orElse: () => UnavailableSlotColor.gray,
        );
      }
      return UnavailableSlotColor.gray;
    } catch (e) {
      return UnavailableSlotColor.gray;
    }
  }

  /// Save the unavailable slot color
  static Future<void> setUnavailableSlotColor(UnavailableSlotColor color) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_unavailableSlotColorKey, color.englishName);
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Get the saved preference for showing the revenue summary
  static Future<bool> getShowRevenueSummary() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_showRevenueSummaryKey) ?? true;
    } catch (e) {
      return true;
    }
  }

  /// Save the preference for showing the revenue summary
  static Future<void> setShowRevenueSummary(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_showRevenueSummaryKey, value);
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Get the saved preference for Google Calendar sync
  static Future<bool> getGoogleCalendarSyncEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_googleCalendarSyncEnabledKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Save the preference for Google Calendar sync
  static Future<void> setGoogleCalendarSyncEnabled(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_googleCalendarSyncEnabledKey, value);
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Get the saved preference for showing Google Calendar events
  static Future<bool> getShowGoogleCalendarEvents() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_showGoogleCalendarEventsKey) ?? true; // Show by default
    } catch (e) {
      return true;
    }
  }

  /// Save the preference for showing Google Calendar events
  static Future<void> setShowGoogleCalendarEvents(bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_showGoogleCalendarEventsKey, value);
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Migrate legacy preference to new system
  static Future<void> migrateLegacyPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final showFullDay = prefs.getBool(_showFullDayKey);

      if (showFullDay != null) {
        // Migrate to new system
        final newMode = showFullDay
            ? CalendarHourViewMode.fullDay
            : CalendarHourViewMode.businessHours;
        await setHourViewMode(newMode);

        // Remove legacy preference
        await prefs.remove(_showFullDayKey);
      }
    } catch (e) {
      // Silently fail - not critical
    }
  }
}
