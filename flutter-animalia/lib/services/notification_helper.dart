import '../models/notification_settings.dart';
import '../services/notification_settings_service.dart';
import '../utils/debug_logger.dart';

/// Helper service for triggering notifications throughout the app
/// This service respects user notification settings and provides easy integration
class NotificationHelper {
  /// Send notification for new appointment
  /// [tr] - Translation function that accepts (key, params)
  static Future<bool> sendNewAppointmentNotification({
    required String appointmentId,
    required String clientName,
    required String petName,
    required DateTime appointmentDate,
    required String groomerName,
    required String Function(String key, {Map<String, String>? params}) tr,
  }) async {
    try {
      // Check if notifications should be sent
      final shouldSend =
          await NotificationSettingsService.shouldSendNotification(
        notificationType: 'new_appointment',
        priority: NotificationPriority.normal,
      );

      if (!shouldSend) {
        DebugLogger.logVerbose(
            '🔔 New appointment notification blocked by user settings');

        return false;
      }

      // Get notification settings for customization
      final settingsResponse =
          await NotificationSettingsService.getNotificationSettings();
      final settings = settingsResponse.data;

      // Prepare notification data
      final notificationData = {
        'type': 'new_appointment',
        'appointmentId': appointmentId,
        'title': tr('notification_messages.new_appointment_title'),
        'body': tr('notification_messages.new_appointment_body', params: {
          'petName': petName,
          'clientName': clientName,
          'date': _formatDate(appointmentDate),
        }),
        'data': {
          'appointmentId': appointmentId,
          'clientName': clientName,
          'petName': petName,
          'appointmentDate': appointmentDate.toIso8601String(),
          'groomerName': groomerName,
        },
        'sound': settings?.soundPreference ?? 'default',
        'vibration': settings?.vibrationEnabled ?? true,
        'priority':
            settings?.notificationRules.defaultPriority.value ?? 'NORMAL',
      };

      // Send notification (integrate with your push notification service)
      final success = await _sendPushNotification(notificationData);

      DebugLogger.logVerbose('🔔 New appointment notification sent: $success');

      return success;
    } catch (e) {
      DebugLogger.logVerbose(
          '❌ Error sending new appointment notification: $e');

      return false;
    }
  }

  /// Send notification for appointment cancellation
  /// [tr] - Translation function that accepts (key, params)
  static Future<bool> sendAppointmentCancellationNotification({
    required String appointmentId,
    required String clientName,
    required String petName,
    required DateTime appointmentDate,
    required String reason,
    required String Function(String key, {Map<String, String>? params}) tr,
  }) async {
    try {
      final shouldSend =
          await NotificationSettingsService.shouldSendNotification(
        notificationType: 'appointment_cancellation',
        priority: NotificationPriority.normal,
      );

      if (!shouldSend) return false;

      final settingsResponse =
          await NotificationSettingsService.getNotificationSettings();
      final settings = settingsResponse.data;

      final notificationData = {
        'type': 'appointment_cancellation',
        'appointmentId': appointmentId,
        'title': tr('notification_messages.appointment_cancelled_title'),
        'body': tr('notification_messages.appointment_cancelled_body', params: {
          'petName': petName,
          'clientName': clientName,
          'date': _formatDate(appointmentDate),
        }),
        'data': {
          'appointmentId': appointmentId,
          'clientName': clientName,
          'petName': petName,
          'appointmentDate': appointmentDate.toIso8601String(),
          'reason': reason,
        },
        'sound': settings?.soundPreference ?? 'default',
        'vibration': settings?.vibrationEnabled ?? true,
        'priority':
            settings?.notificationRules.defaultPriority.value ?? 'NORMAL',
      };

      return await _sendPushNotification(notificationData);
    } catch (e) {
      DebugLogger.logVerbose('❌ Error sending cancellation notification: $e');

      return false;
    }
  }

  /// Send notification for payment confirmation
  /// [tr] - Translation function that accepts (key, params)
  static Future<bool> sendPaymentConfirmationNotification({
    required String paymentId,
    required String clientName,
    required double amount,
    required String paymentMethod,
    required String Function(String key, {Map<String, String>? params}) tr,
  }) async {
    try {
      final shouldSend =
          await NotificationSettingsService.shouldSendNotification(
        notificationType: 'payment_confirmation',
        priority: NotificationPriority.normal,
      );

      if (!shouldSend) return false;

      final settingsResponse =
          await NotificationSettingsService.getNotificationSettings();
      final settings = settingsResponse.data;

      final notificationData = {
        'type': 'payment_confirmation',
        'paymentId': paymentId,
        'title': tr('notification_messages.payment_confirmed_title'),
        'body': tr('notification_messages.payment_confirmed_body', params: {
          'amount': amount.toStringAsFixed(2),
          'clientName': clientName,
        }),
        'data': {
          'paymentId': paymentId,
          'clientName': clientName,
          'amount': amount,
          'paymentMethod': paymentMethod,
        },
        'sound': settings?.soundPreference ?? 'default',
        'vibration': settings?.vibrationEnabled ?? true,
        'priority':
            settings?.notificationRules.defaultPriority.value ?? 'NORMAL',
      };

      return await _sendPushNotification(notificationData);
    } catch (e) {
      DebugLogger.logVerbose(
          '❌ Error sending payment confirmation notification: $e');

      return false;
    }
  }

  /// Send notification for team member updates
  /// [tr] - Translation function that accepts (key, params)
  static Future<bool> sendTeamMemberUpdateNotification({
    required String updateType, // 'joined', 'left', 'role_changed'
    required String memberName,
    required String? newRole,
    required String Function(String key, {Map<String, String>? params}) tr,
  }) async {
    try {
      final shouldSend =
          await NotificationSettingsService.shouldSendNotification(
        notificationType: 'team_member_update',
        priority: NotificationPriority.normal,
      );

      if (!shouldSend) return false;

      final settingsResponse =
          await NotificationSettingsService.getNotificationSettings();
      final settings = settingsResponse.data;

      String title = tr('notification_messages.team_update_title');
      String body = '';

      switch (updateType) {
        case 'joined':
          body = tr('notification_messages.team_member_joined', params: {
            'memberName': memberName,
          });
          break;
        case 'left':
          body = tr('notification_messages.team_member_left', params: {
            'memberName': memberName,
          });
          break;
        case 'role_changed':
          body = tr('notification_messages.team_member_role_changed', params: {
            'memberName': memberName,
            'newRole': newRole ?? '',
          });
          break;
        default:
          body = tr('notification_messages.team_member_update_default', params: {
            'memberName': memberName,
          });
      }

      final notificationData = {
        'type': 'team_member_update',
        'title': title,
        'body': body,
        'data': {
          'updateType': updateType,
          'memberName': memberName,
          'newRole': newRole,
        },
        'sound': settings?.soundPreference ?? 'default',
        'vibration': settings?.vibrationEnabled ?? true,
        'priority':
            settings?.notificationRules.defaultPriority.value ?? 'NORMAL',
      };

      return await _sendPushNotification(notificationData);
    } catch (e) {
      DebugLogger.logVerbose('❌ Error sending team update notification: $e');

      return false;
    }
  }

  /// Send critical system maintenance notification
  /// [tr] - Translation function that accepts (key, params)
  static Future<bool> sendSystemMaintenanceNotification({
    required String maintenanceType,
    required DateTime scheduledTime,
    required Duration estimatedDuration,
    required String description,
    required String Function(String key, {Map<String, String>? params}) tr,
  }) async {
    try {
      final shouldSend =
          await NotificationSettingsService.shouldSendNotification(
        notificationType: 'system_maintenance',
        priority: NotificationPriority.critical,
      );

      if (!shouldSend) return false;

      final settingsResponse =
          await NotificationSettingsService.getNotificationSettings();
      final settings = settingsResponse.data;

      final notificationData = {
        'type': 'system_maintenance',
        'title': tr('notification_messages.system_maintenance_title'),
        'body': tr('notification_messages.system_maintenance_body', params: {
          'maintenanceType': maintenanceType,
          'date': _formatDate(scheduledTime),
        }),
        'data': {
          'maintenanceType': maintenanceType,
          'scheduledTime': scheduledTime.toIso8601String(),
          'estimatedDuration': estimatedDuration.inMinutes,
          'description': description,
        },
        'sound': settings?.soundPreference ?? 'default',
        'vibration': settings?.vibrationEnabled ?? true,
        'priority': 'CRITICAL', // Always critical for system maintenance
      };

      return await _sendPushNotification(notificationData);
    } catch (e) {
      DebugLogger.logVerbose('❌ Error sending maintenance notification: $e');

      return false;
    }
  }

  /// Format date for display in notifications
  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}.${date.year} la ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Send push notification (integrate with your push notification service)
  /// This is a placeholder - replace with your actual push notification implementation
  static Future<bool> _sendPushNotification(
      Map<String, dynamic> notificationData) async {
    try {
      // Integrate with push notification service (Firebase, OneSignal, etc.)
      // Example implementation:

      DebugLogger.logVerbose('🔔 Sending push notification:');
      DebugLogger.logVerbose('   Type: ${notificationData['type']}');
      DebugLogger.logVerbose('   Title: ${notificationData['title']}');
      DebugLogger.logVerbose('   Body: ${notificationData['body']}');
      DebugLogger.logVerbose('   Sound: ${notificationData['sound']}');
      DebugLogger.logVerbose('   Vibration: ${notificationData['vibration']}');
      DebugLogger.logVerbose('   Priority: ${notificationData['priority']}');

      // Simulate successful notification sending
      await Future.delayed(const Duration(milliseconds: 100));
      return true;

      // Real implementation would look like:
      // final response = await FirebaseMessaging.instance.sendMessage(
      //   RemoteMessage(
      //     notification: RemoteNotification(
      //       title: notificationData['title'],
      //       body: notificationData['body'],
      //     ),
      //     data: Map<String, String>.from(notificationData['data']),
      //     android: AndroidNotification(
      //       sound: notificationData['sound'],
      //       priority: notificationData['priority'] == 'CRITICAL'
      //         ? AndroidNotificationPriority.high
      //         : AndroidNotificationPriority.normal,
      //     ),
      //   ),
      // );
      // return response.messageId != null;
    } catch (e) {
      DebugLogger.logVerbose('❌ Error in _sendPushNotification: $e');

      return false;
    }
  }

  /// Check if notifications are enabled for the current salon
  static Future<bool> areNotificationsEnabled() async {
    try {
      final response =
          await NotificationSettingsService.getNotificationSettings();
      return response.success &&
          (response.data?.pushNotificationsEnabled ?? false);
    } catch (e) {
      return false;
    }
  }

  /// Get current notification settings for the salon
  static Future<NotificationSettings?> getCurrentSettings() async {
    try {
      final response =
          await NotificationSettingsService.getNotificationSettings();
      return response.success ? response.data : null;
    } catch (e) {
      return null;
    }
  }
}
