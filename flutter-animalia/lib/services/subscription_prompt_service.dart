import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/api_config.dart';
import '../models/salon_subscription.dart';
import '../screens/subscription/subscription_purchase_screen.dart';
import '../services/analytics/subscription_conversion_analytics_service.dart';
import '../utils/debug_logger.dart';
import '../l10n/app_localizations.dart';

/// Service to handle strategic subscription prompts at optimal moments
class SubscriptionPromptService {
  static const Duration _promptDelay = Duration(seconds: 10);
  static const String _lastPromptKey = 'last_subscription_prompt';
  static const Duration _minimumTimeBetweenPrompts = Duration(hours: 24);

  /// Show subscription prompt after first appointment completion
  static Future<void> showAfterFirstAppointment({
    required BuildContext context,
    required String salonId,
  }) async {
    if (!await _shouldShowPrompt('first_appointment')) return;

    // Track prompt shown
    await SubscriptionConversionAnalyticsService.trackPromptShown(
      promptType: 'first_appointment',
      promptTrigger: 'appointment_completion',
      currentTier: SubscriptionTier.free,
      context: {
        'appointments_completed': 1,
        'trigger_delay_seconds': _promptDelay.inSeconds,
      },
    );

    // Delay before showing prompt
    await Future.delayed(_promptDelay);

    if (!context.mounted) return;

    final promptStartTime = DateTime.now();

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.celebration, color: Colors.orange, size: 28),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                context.tr('subscription.first_appointment.title'),
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('subscription.first_appointment.subtitle'),
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Text(
              context.tr('subscription.first_appointment.upgrade_title'),
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text(context.tr('subscription.first_appointment.features.sms')),
            Text(context.tr('subscription.first_appointment.features.team')),
            Text(context.tr('subscription.first_appointment.features.reports')),
            Text(context.tr('subscription.first_appointment.features.support')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Track dismissal
              SubscriptionConversionAnalyticsService.trackPromptDismissed(
                promptType: 'first_appointment',
                dismissReason: 'maybe_later',
                timeViewedMs: DateTime.now().difference(promptStartTime).inMilliseconds,
              );
              Navigator.of(context).pop();
            },
            child: Text(context.tr('common.maybe_later')),
          ),
          ElevatedButton(
            onPressed: () {
              // Track click
              SubscriptionConversionAnalyticsService.trackPromptClicked(
                promptType: 'first_appointment',
                promptTrigger: 'appointment_completion',
                currentTier: SubscriptionTier.free,
                timeToClickMs: DateTime.now().difference(promptStartTime).inMilliseconds,
              );
              Navigator.of(context).pop();
              _navigateToSubscriptionScreen(context, salonId, 'first_appointment_prompt');
            },
            child: Text(context.tr('subscription.view_plans')),
          ),
        ],
      ),
    );

    await _recordPromptShown('first_appointment');
  }

  /// Show subscription prompt when staff limit is reached
  static Future<void> showWhenStaffLimitReached({
    required BuildContext context,
    required String salonId,
  }) async {
    if (!await _shouldShowPrompt('staff_limit')) return;

    // Track prompt shown
    await SubscriptionConversionAnalyticsService.trackPromptShown(
      promptType: 'staff_limit',
      promptTrigger: 'team_member_limit_reached',
      currentTier: SubscriptionTier.free,
    );

    if (!context.mounted) return;

    final promptStartTime = DateTime.now();

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.group_add, color: Colors.blue, size: 28),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                context.tr('subscription.staff_limit.title'),
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('subscription.staff_limit.subtitle'),
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Text(
              context.tr('subscription.staff_limit.upgrade_title'),
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text(context.tr('subscription.staff_limit.features.limit')),
            Text(context.tr('subscription.staff_limit.features.team_management')),
            Text(context.tr('subscription.staff_limit.features.reports_per_groomer')),
            Text(context.tr('subscription.staff_limit.features.parallel_appointments')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Track dismissal
              SubscriptionConversionAnalyticsService.trackPromptDismissed(
                promptType: 'staff_limit',
                dismissReason: 'maybe_later',
                timeViewedMs: DateTime.now().difference(promptStartTime).inMilliseconds,
              );
              Navigator.of(context).pop();
            },
            child: Text(context.tr('common.maybe_later')),
          ),
          ElevatedButton(
            onPressed: () {
              // Track click
              SubscriptionConversionAnalyticsService.trackPromptClicked(
                promptType: 'staff_limit',
                promptTrigger: 'team_member_limit_reached',
                currentTier: SubscriptionTier.free,
                timeToClickMs: DateTime.now().difference(promptStartTime).inMilliseconds,
              );
              Navigator.of(context).pop();
              _navigateToSubscriptionScreen(context, salonId, 'staff_limit_prompt');
            },
            child: Text(context.tr('subscription.view_plans')),
          ),
        ],
      ),
    );

    await _recordPromptShown('staff_limit');
  }

  /// Show subscription prompt when SMS quota is reached
  static Future<void> showWhenSmsQuotaReached({
    required BuildContext context,
    required String salonId,
  }) async {
    if (!await _shouldShowPrompt('sms_quota')) return;

    // Track prompt shown
    await SubscriptionConversionAnalyticsService.trackPromptShown(
      promptType: 'sms_quota',
      promptTrigger: 'monthly_sms_limit_reached',
      currentTier: SubscriptionTier.free,
    );

    if (!context.mounted) return;

    final promptStartTime = DateTime.now();

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.sms_failed, color: Colors.red, size: 28),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                context.tr('subscription.sms_quota.title'),
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('subscription.sms_quota.subtitle'),
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            Text(
              context.tr('subscription.sms_quota.upgrade_title'),
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text(context.tr('subscription.sms_quota.features.sms_200')),
            Text(context.tr('subscription.sms_quota.features.sms_500')),
            Text(context.tr('subscription.sms_quota.features.auto_confirm')),
            Text(context.tr('subscription.sms_quota.features.reminders')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Track dismissal
              SubscriptionConversionAnalyticsService.trackPromptDismissed(
                promptType: 'sms_quota',
                dismissReason: 'maybe_later',
                timeViewedMs: DateTime.now().difference(promptStartTime).inMilliseconds,
              );
              Navigator.of(context).pop();
            },
            child: Text(context.tr('common.maybe_later')),
          ),
          ElevatedButton(
            onPressed: () {
              // Track click
              SubscriptionConversionAnalyticsService.trackPromptClicked(
                promptType: 'sms_quota',
                promptTrigger: 'monthly_sms_limit_reached',
                currentTier: SubscriptionTier.free,
                timeToClickMs: DateTime.now().difference(promptStartTime).inMilliseconds,
              );
              Navigator.of(context).pop();
              _navigateToSubscriptionScreen(context, salonId, 'sms_quota_prompt');
            },
            child: Text(context.tr('subscription.view_plans')),
          ),
        ],
      ),
    );

    await _recordPromptShown('sms_quota');
  }

  /// Show subscription prompt at appointment milestones
  static Future<void> showAtAppointmentMilestone({
    required BuildContext context,
    required String salonId,
    required int appointmentCount,
  }) async {
    final milestones = [5, 15, 30, 50, 100];
    if (!milestones.contains(appointmentCount)) return;

    if (!await _shouldShowPrompt('milestone_$appointmentCount')) return;

    // Track prompt shown
    await SubscriptionConversionAnalyticsService.trackPromptShown(
      promptType: 'appointment_milestone',
      promptTrigger: 'milestone_$appointmentCount',
      currentTier: SubscriptionTier.free,
      context: {
        'appointment_count': appointmentCount,
        'milestone_reached': appointmentCount,
      },
    );

    // Delay before showing prompt
    await Future.delayed(_promptDelay);

    if (!context.mounted) return;

    final promptStartTime = DateTime.now();

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.rocket_launch, color: Colors.green, size: 28),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                context.tr('subscription.milestone.title'),
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('subscription.milestone.completed_appointments', params: {
                'count': appointmentCount.toString(),
              }),
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Text(
              context.tr('subscription.milestone.upgrade_title'),
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text(context.tr('subscription.milestone.features.auto_confirm')),
            Text(context.tr('subscription.milestone.features.reminders')),
            Text(context.tr('subscription.milestone.features.advanced_reports')),
            Text(context.tr('subscription.milestone.features.priority_support')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Track dismissal
              SubscriptionConversionAnalyticsService.trackPromptDismissed(
                promptType: 'appointment_milestone',
                dismissReason: 'maybe_later',
                timeViewedMs: DateTime.now().difference(promptStartTime).inMilliseconds,
              );
              Navigator.of(context).pop();
            },
            child: Text(context.tr('common.maybe_later')),
          ),
          ElevatedButton(
            onPressed: () {
              // Track click
              SubscriptionConversionAnalyticsService.trackPromptClicked(
                promptType: 'appointment_milestone',
                promptTrigger: 'milestone_$appointmentCount',
                currentTier: SubscriptionTier.free,
                timeToClickMs: DateTime.now().difference(promptStartTime).inMilliseconds,
              );
              Navigator.of(context).pop();
              _navigateToSubscriptionScreen(context, salonId, 'milestone_prompt');
            },
            child: Text(context.tr('subscription.view_plans')),
          ),
        ],
      ),
    );

    await _recordPromptShown('milestone_$appointmentCount');
  }

  // Private helper methods

  static Future<bool> _shouldShowPrompt(String promptType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastPromptTime = prefs.getString('${_lastPromptKey}_$promptType');
      
      if (lastPromptTime != null) {
        final lastPrompt = DateTime.parse(lastPromptTime);
        final timeSinceLastPrompt = DateTime.now().difference(lastPrompt);
        
        if (timeSinceLastPrompt < _minimumTimeBetweenPrompts) {
          if (ApiConfig.enableLogging) {
            DebugLogger.logInit('⏰ Subscription prompt "$promptType" skipped - too soon since last prompt');
          }
          return false;
        }
      }
      
      return true;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logInit('❌ Error checking prompt timing: $e');
      }
      return true; // Default to showing prompt if check fails
    }
  }

  static Future<void> _recordPromptShown(String promptType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('${_lastPromptKey}_$promptType', DateTime.now().toIso8601String());
    } catch (e) {
      if (ApiConfig.enableLogging) {
        DebugLogger.logInit('❌ Error recording prompt shown: $e');
      }
    }
  }

  static void _navigateToSubscriptionScreen(BuildContext context, String salonId, String entrySource) {
    // Track subscription screen view
    SubscriptionConversionAnalyticsService.trackSubscriptionScreenView(
      entrySource: entrySource,
      currentTier: SubscriptionTier.free,
    );

    // Determine the appropriate tier to show based on entry source
    SubscriptionTier? initialTier;

    if (entrySource == 'staff_limit_prompt') {
      // For staff limit, suggest Team plan (supports team management)
      initialTier = SubscriptionTier.team;
    } else if (entrySource == 'sms_quota_prompt') {
      // For SMS quota, suggest Freelancer plan (includes SMS)
      initialTier = SubscriptionTier.freelancer;
    } else if (entrySource == 'first_appointment_prompt' || entrySource == 'milestone_prompt') {
      // For general prompts, suggest Freelancer plan as entry point
      initialTier = SubscriptionTier.freelancer;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SubscriptionPurchaseScreen(
          salonId: salonId,
          showTrialOption: false,
          initialTier: initialTier, // Pass the appropriate tier based on context
        ),
      ),
    );
  }
}
