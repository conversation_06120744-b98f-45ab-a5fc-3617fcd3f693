import 'dart:io' show Platform;

import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

import '../config/environment.dart';
import '../firebase_options.dart';

class FirebaseConfig {
  static bool isInitialized = false;

  static Future<bool> initializeFirebase() async {
    if (isInitialized) {
      return true;
    }

    try {
      FirebaseOptions? options;

      // Get environment-specific Firebase configuration
      final firebaseConfig = EnvironmentConfig.firebaseConfig;
      final bundleId = EnvironmentConfig.bundleId;

      if (kIsWeb) {
        // Web configuration
        options = FirebaseOptions(
          apiKey: firebaseConfig['apiKey']!,
          appId: firebaseConfig['webAppId'] ?? firebaseConfig['appId']!,
          messagingSenderId: firebaseConfig['messagingSenderId']!,
          projectId: firebaseConfig['projectId']!,
          authDomain: firebaseConfig['authDomain']!,
        );
      } else if (Platform.isIOS) {
        // iOS configuration - Using environment-specific settings
        options = FirebaseOptions(
          apiKey: firebaseConfig['apiKey']!,
          appId: firebaseConfig['iosAppId'] ?? firebaseConfig['appId']!,
          messagingSenderId: firebaseConfig['messagingSenderId']!,
          projectId: firebaseConfig['projectId']!,
          authDomain: firebaseConfig['authDomain']!,
          iosBundleId: bundleId,
        );
      } else if (Platform.isAndroid) {
        // Android configuration - Using environment-specific settings
        options = FirebaseOptions(
          apiKey: firebaseConfig['apiKey']!,
          appId: firebaseConfig['androidAppId'] ?? firebaseConfig['appId']!,
          messagingSenderId: firebaseConfig['messagingSenderId']!,
          projectId: firebaseConfig['projectId']!,
          authDomain: firebaseConfig['authDomain']!,
        );
        debugPrint('App id used ${options?.appId}');
        debugPrint('Android Firebase configuration: $options');
      }

      // Always use DefaultFirebaseOptions for consistency
      DebugLogger.logInit('🔧 Using DefaultFirebaseOptions for Firebase initialization...');
      DebugLogger.logInit('🌍 Environment: ${EnvironmentConfig.currentEnvironment.name}');
      DebugLogger.logInit('📦 Bundle ID: ${EnvironmentConfig.bundleId}');
      try {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
      } catch (e) {
        if (e.toString().contains('duplicate-app')) {
          DebugLogger.logInit('🔧 Firebase already initialized, using existing instance');
        } else {
          rethrow;
        }
      }
      DebugLogger.logInit('✅ Firebase initialized successfully');
      DebugLogger.logInit('🔍 FIREBASE CONFIG VERIFICATION:');
      DebugLogger.logInit('- Environment: ${EnvironmentConfig.currentEnvironment.name}');
      DebugLogger.logInit('- Project ID: ${Firebase.app().options.projectId}');
      DebugLogger.logInit('- App ID: ${Firebase.app().options.appId}');
      DebugLogger.logInit('- API Key: ${Firebase.app().options.apiKey}');
      DebugLogger.logInit('- Auth Domain: ${Firebase.app().options.authDomain}');
      if (!kIsWeb && Platform.isIOS) {
        DebugLogger.logInit('- iOS Bundle ID: ${Firebase.app().options.iosBundleId}');
      }
      isInitialized = true;
      return true;
    } catch (e) {
      DebugLogger.logInit('Error initializing Firebase: $e');
      // Instead of silently falling back to mock mode, we'll show an error
      // This ensures the app doesn't proceed with mock authentication
      DebugLogger.logInit('Firebase initialization failed - real authentication is required');
      isInitialized = false;
      return false;
    }
  }
}
