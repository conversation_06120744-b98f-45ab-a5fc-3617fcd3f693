import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';

import '../../config/theme/app_theme.dart';
import '../../models/salon_invitation.dart';
import '../../models/salon_subscription.dart';
import '../../models/user_role.dart';
import '../../models/user_salon_association.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/client_provider.dart';
import '../../providers/role_provider.dart';
import '../../providers/subscription_provider.dart';
import '../../screens/profile/salon_creation_screen.dart';
import '../../screens/profile/user_invitations_screen.dart';
import '../../services/subscription/revenue_cat_paywall_service.dart';
import '../../services/invitation_service.dart';
import '../../services/salon_service.dart';
import '../../utils/debug_logger.dart';

class RevolutStyleSalonSwitcher extends StatefulWidget {
  final List<UserSalonAssociation> salons;
  final bool showManagementActions;

  const RevolutStyleSalonSwitcher({
    super.key,
    required this.salons,
    this.showManagementActions = true,
  });

  @override
  State<RevolutStyleSalonSwitcher> createState() => _RevolutStyleSalonSwitcherState();
}

class _RevolutStyleSalonSwitcherState extends State<RevolutStyleSalonSwitcher>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  int _currentIndex = 0;
  bool _isSwitching = false; // Idempotency flag to prevent double-clicking
  List<SalonInvitation> _pendingInvitations = [];
  bool _isLoadingInvitations = false;

  @override
  void initState() {
    super.initState();

    // Find the current active salon index
    _currentIndex = widget.salons.indexWhere((salon) => salon.isCurrentSalon);
    if (_currentIndex == -1) _currentIndex = 0;

    // Load pending invitations
    if (widget.showManagementActions) {
      _loadPendingInvitations();
    }
    
    _pageController = PageController(
      initialPage: _currentIndex,
      viewportFraction: 0.85,
    );
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.5),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      context.tr('salon_switcher.title'),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 48), // Balance the close button
                ],
              ),
            ),
            
            // Cards
            Expanded(
              child: AnimatedBuilder(
                animation: _scaleAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: PageView.builder(
                      controller: _pageController,
                      onPageChanged: (index) {
                        setState(() {
                          _currentIndex = index;
                        });
                      },
                      itemCount: widget.salons.length,
                      itemBuilder: (context, index) {
                        return _buildSalonCard(widget.salons[index], index);
                      },
                    ),
                  );
                },
              ),
            ),
            
            // Page indicator
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  widget.salons.length,
                  (index) => Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: index == _currentIndex ? 24 : 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: index == _currentIndex 
                          ? Theme.of(context).colorScheme.primary
                        : Colors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
            ),
            
            // Action buttons
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Switch salon button
                  if (!widget.salons[_currentIndex].isCurrentSalon)
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSwitching ? null : () => _switchToSalon(widget.salons[_currentIndex]),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isSwitching ? Colors.grey : Theme.of(context).colorScheme.onSurface,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: _isSwitching
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  context.tr('salon_switcher.switching_progress'),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            )
                          : Text(
                              context.tr('salon_switcher.switch_to_salon'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                      ),
                    ),

                  if (!widget.salons[_currentIndex].isCurrentSalon)
                    const SizedBox(height: 12),

                  // Management actions (only show if enabled)
                  if (widget.showManagementActions) ...[

                    // Pending invitations button
                    if (_pendingInvitations.isNotEmpty)
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: () => _viewPendingInvitations(),
                          icon: Stack(
                            children: [
                              const Icon(Icons.mail_outline),
                              Positioned(
                                right: 0,
                                top: 0,
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  constraints: const BoxConstraints(
                                    minWidth: 12,
                                    minHeight: 12,
                                  ),
                                  child: Text(
                                    '${_pendingInvitations.length}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 8,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          label: Text(context.tr('salon_switcher.invitations_count', params: {'count': _pendingInvitations.length.toString()})),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.orange.shade300,
                            side: BorderSide(color: Colors.orange.shade300),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),

                    if (_pendingInvitations.isNotEmpty)
                      const SizedBox(height: 12),
                  ],

                  // Create new salon button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => _navigateToCreateSalon(context),
                      icon: const Icon(Icons.add),
                      label: Text(context.tr('create_salon.title')),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.white,
                        side: const BorderSide(color: Colors.white),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalonCard(UserSalonAssociation salon, int index) {
    final isActive = salon.isCurrentSalon;
    final isCurrent = index == _currentIndex;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: EdgeInsets.symmetric(
        horizontal: 8,
        vertical: isCurrent ? 20 : 40,
      ),
      child: Transform(
        alignment: Alignment.center,
        transform: Matrix4.identity()
          ..setEntry(3, 2, 0.001)
          ..rotateY(isCurrent ? 0 : 0.1),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isActive
                  ? [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.primary.withValues(alpha: 0.8)]
                  : [Theme.of(context).colorScheme.primary.withValues(alpha: 0.2), Theme.of(context).colorScheme.primary.withValues(alpha: 0.8)]
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.3),
                spreadRadius: 2,
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.business,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const Spacer(),
                    if (isActive)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          context.tr('salon_switcher.active_badge'),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Salon name
                Text(
                  salon.salon.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // Address
                Text(
                  salon.salon.address ?? context.tr('salon_switcher.unknown_address'),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 16,
                  ),
                ),

                const SizedBox(height: 12),

                // Phone number and description
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Phone number
                    if (salon.salon.phone != null && salon.salon.phone!.isNotEmpty)
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            color: Colors.white.withValues(alpha: 0.7),
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            salon.salon.formattedPhone ?? salon.salon.phone!,
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.8),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),

                    // Description
                    if (salon.salon.description != null && salon.salon.description!.isNotEmpty) ...[
                      if (salon.salon.phone != null && salon.salon.phone!.isNotEmpty)
                        const SizedBox(height: 6),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.white.withValues(alpha: 0.7),
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              salon.salon.description!,
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.8),
                                fontSize: 14,
                                height: 1.3,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),

                const Spacer(),
                
                // Role and permissions
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        salon.roleDisplayName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        salon.clientDataPermission.displayName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),

                // Quick action buttons (only for current card and if management actions enabled)
                if (widget.showManagementActions && isCurrent) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      // Edit button (only for chief groomers)
                      if (_canManageSalon(salon))
                        Expanded(
                          child: _buildQuickActionButton(
                            icon: Icons.edit,
                            label: context.tr('salon_switcher.edit_action'),
                            onPressed: () => _editSalon(salon),
                          ),
                        ),

                      if (_canManageSalon(salon))
                        const SizedBox(width: 8),

                      // Delete button (only for chief groomers)
                      if (_canManageSalon(salon))
                        Expanded(
                          child: _buildQuickActionButton(
                            icon: Icons.delete,
                            label: context.tr('salon_switcher.delete_action'),
                            onPressed: () => _deleteSalon(salon),
                            isDestructive: true,
                          ),
                        ),

                      // If no management actions available, show info button
                      if (!_canManageSalon(salon))
                        Expanded(
                          child: _buildQuickActionButton(
                            icon: Icons.info_outline,
                            label: context.tr('salon_switcher.details_action'),
                            onPressed: () => _showSalonInfo(salon),
                          ),
                        ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _switchToSalon(UserSalonAssociation salon) async {
    // Prevent double-clicking with idempotency check
    if (_isSwitching) {
      DebugLogger.logVerbose('⚠️ RevolutSalonSwitcher: Salon switch already in progress, ignoring duplicate request');
      return;
    }

    setState(() {
      _isSwitching = true;
    });

    try {
      DebugLogger.logVerbose('🔄 RevolutSalonSwitcher: Starting salon switch to: ${salon.salon.name} (${salon.salonId})');

      final response = await SalonService.switchActiveSalon(salon.salonId);

      if (mounted) {
        if (response.success) {
          DebugLogger.logVerbose('✅ RevolutSalonSwitcher: Salon switch API call successful');
          DebugLogger.logVerbose('🔄 RevolutSalonSwitcher: Clearing cached data from all providers...');

          // Clear data from all providers to ensure complete isolation
          final roleProvider = context.read<RoleProvider>();
          final clientProvider = context.read<ClientProvider>();
          final calendarProvider = context.read<CalendarProvider>();

          // Clear all salon-specific caches in calendar provider (appointments, clients, staff, services)
          await calendarProvider.clearAllCachesForSalonSwitch(salon.salonId);

          // Clear and reload client data for new salon
          await clientProvider.clearForSalonSwitch(salon.salonId);

          // Reload calendar data for new salon
          await calendarProvider.reloadDataForNewSalon(salon.salonId);

          // Refresh role provider to get new salon permissions
          await roleProvider.refresh();

          Navigator.of(context).pop();

          DebugLogger.logVerbose('✅ RevolutSalonSwitcher: Successfully switched to salon: ${salon.salon.name}');
          DebugLogger.logVerbose('📊 RevolutSalonSwitcher: All provider data cleared and refreshed for new salon');
        } else {
          DebugLogger.logVerbose('❌ RevolutSalonSwitcher: Failed to switch salon: ${response.error}');
        }
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ RevolutSalonSwitcher: Error switching salon: $e');
    } finally {
      // Reset the switching flag
      if (mounted) {
        setState(() {
          _isSwitching = false;
        });
      }
    }
  }

  /// Navigate to salon creation screen
  Future<void> _navigateToCreateSalon(BuildContext context) async {
    // Check if user can create salons based on their subscription
    final subscriptionProvider = context.read<SubscriptionProvider>();
    final canCreate = await subscriptionProvider.canUserCreateSalons();

    if (!canCreate) {
      // Show upgrade prompt instead of allowing salon creation
      _showUpgradePrompt(context);
      return;
    }

    // Close the current switcher first
    Navigator.of(context).pop();

    // Navigate to salon creation screen
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const SalonCreationScreen(),
      ),
    );

    if (result == true && mounted) {
      // Salon was created successfully, refresh all providers
      final roleProvider = context.read<RoleProvider>();
      final calendarProvider = context.read<CalendarProvider>();

      // Refresh role provider first
      DebugLogger.logVerbose('🔄 Refreshing role provider after salon creation...');
      await roleProvider.refresh();

      // Add a longer delay to ensure backend has processed the staff creation
      DebugLogger.logVerbose('🔄 Waiting for backend to process staff creation...');
      await Future.delayed(const Duration(seconds: 2));

      // Retry staff loading with multiple attempts
      if (mounted) {
        await _retryStaffLoading(calendarProvider);
        await calendarProvider.loadServices();
      }

      // Force a rebuild of the UI to show new navigation options
      if (mounted) {
        DebugLogger.logVerbose('✅ Salon created successfully - calendar should now be available');
      }
    }
  }

  /// Retry staff loading with multiple attempts
  Future<void> _retryStaffLoading(CalendarProvider calendarProvider) async {
    const maxAttempts = 3;
    const delayBetweenAttempts = Duration(seconds: 1);

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      DebugLogger.logVerbose('🔄 Staff loading attempt $attempt/$maxAttempts');

      await calendarProvider.loadStaff();

      // Check if staff was loaded successfully
      if (calendarProvider.availableStaff.isNotEmpty) {
        DebugLogger.logVerbose('✅ Staff loaded successfully on attempt $attempt');
        return;
      }

      if (attempt < maxAttempts) {
        DebugLogger.logVerbose('⏳ No staff found, waiting before retry...');
        await Future.delayed(delayBetweenAttempts);
      }
    }

    DebugLogger.logVerbose('❌ Failed to load staff after $maxAttempts attempts');
  }

  /// Load pending invitations
  Future<void> _loadPendingInvitations() async {
    if (_isLoadingInvitations) return;

    setState(() {
      _isLoadingInvitations = true;
    });

    try {
      final response = await InvitationService.getPendingInvitations();
      if (response.success && response.data != null) {
        setState(() {
          _pendingInvitations = response.data!;
        });
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Failed to load pending invitations: $e');
    } finally {
      setState(() {
        _isLoadingInvitations = false;
      });
    }
  }

  /// Check if user can manage the salon (chief groomer only)
  bool _canManageSalon(UserSalonAssociation salon) {
    return salon.groomerRole == GroomerRole.chiefGroomer;
  }

  /// Build quick action button for salon card
  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isDestructive
                ? Colors.red.withValues(alpha: 0.3)
                : Colors.white.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isDestructive ? Colors.red.shade300 : Colors.white,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: isDestructive ? Colors.red.shade300 : Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Edit salon settings
  Future<void> _editSalon(UserSalonAssociation salon) async {
    Navigator.of(context).pop(); // Close switcher

    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => SalonCreationScreen(salon: salon.salon),
      ),
    );

    if (result == true) {
      // Refresh role provider to update salon list
      final roleProvider = context.read<RoleProvider>();
      final calendarProvider = context.read<CalendarProvider>();

      await roleProvider.refresh();
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        await calendarProvider.loadServices();
      }
    }
  }

  /// Delete salon
  Future<void> _deleteSalon(UserSalonAssociation salon) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('salon_switcher.delete_salon_title')),
        content: Text(context.tr('salon_switcher.delete_salon_message', params: {'name': salon.salon.name})),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.tr('common.cancel')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(context.tr('salon_switcher.delete_salon_button')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final response = await SalonService.deleteSalon(salon.salonId);
        if (response.success) {
          showTopSnackBar(context, 
            SnackBar(
              content: Text(context.tr('salon_switcher.salon_deleted_success', params: {'name': salon.salon.name})),
              backgroundColor: Theme.of(context).colorScheme.onSurface,
            ),
          );

          // Add a delay to ensure backend has processed the deletion
          DebugLogger.logVerbose('🔄 RevolutSalonSwitcher: Waiting before refreshing role provider...');
          await Future.delayed(const Duration(seconds: 1));

          // Refresh the role provider to update salon list
          final roleProvider = context.read<RoleProvider>();
          DebugLogger.logVerbose('🔄 RevolutSalonSwitcher: Refreshing role provider...');
          await roleProvider.refresh();
          DebugLogger.logVerbose('✅ RevolutSalonSwitcher: Role provider refresh completed');

          // Close the switcher - the MainLayout will handle showing onboarding if needed
          Navigator.of(context).pop();
        } else {
          showTopSnackBar(context, 
            SnackBar(
              content: Text(context.tr('salon_switcher.salon_delete_error', params: {'error': response.error ?? ''})),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text(context.tr('salon_switcher.salon_delete_error', params: {'error': e.toString()})),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// View pending invitations
  Future<void> _viewPendingInvitations() async {
    Navigator.of(context).pop(); // Close switcher

    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const UserInvitationsScreen(),
      ),
    );

    // Always refresh invitations when returning from invitations screen
    // This ensures the badge count is updated regardless of what happened
    await _loadPendingInvitations();

    if (result == true) {
      // Refresh role provider to update salon list if invitations were accepted
      final roleProvider = context.read<RoleProvider>();
      await roleProvider.refresh();
    }
  }

  /// Show salon information
  void _showSalonInfo(UserSalonAssociation salon) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(salon.salon.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(context.tr('salon_switcher.salon_info_address', params: {'address': salon.salon.address ?? context.tr('salon_switcher.unknown_address')})),
            const SizedBox(height: 8),
            Text(context.tr('salon_switcher.salon_info_role', params: {'role': salon.roleDisplayName})),
            const SizedBox(height: 8),
            Text(context.tr('salon_switcher.salon_info_permissions', params: {'permissions': salon.clientDataPermission.displayName})),
            if (salon.salon.phone != null) ...[
              const SizedBox(height: 8),
              Text(context.tr('salon_switcher.salon_info_phone', params: {'phone': salon.salon.formattedPhone ?? salon.salon.phone!})),
            ],
            if (salon.salon.email != null) ...[
              const SizedBox(height: 8),
              Text(context.tr('salon_switcher.salon_info_email', params: {'email': salon.salon.email!})),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('salon_switcher.salon_info_close')),
          ),
        ],
      ),
    );
  }

  /// Show upgrade prompt for salon creation
  void _showUpgradePrompt(BuildContext context) {
    // Get current salon ID for the paywall
    final currentSalonId = widget.salons.firstWhere((s) => s.isCurrentSalon).salonId;

    // Show new RevenueCat paywall with tabs, defaulting to Enterprise tier for salon creation
    RevenueCatPaywallService.showPaywall(
      context: context,
      defaultTier: SubscriptionTier.enterprise,
      salonId: currentSalonId,
    );
  }
}
