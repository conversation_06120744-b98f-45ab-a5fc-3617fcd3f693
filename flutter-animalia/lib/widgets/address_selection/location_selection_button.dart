import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../l10n/app_localizations.dart';
import 'full_screen_map_picker.dart';

/// A button widget for selecting location that opens the full-screen map picker
class LocationSelectionButton extends StatelessWidget {
  final String? selectedAddress;
  final LatLng? selectedLocation;
  final Function(LatLng location, String? address)? onLocationSelected;
  final String? label;
  final String? hint;
  final bool isRequired;
  final String? Function(String?)? validator;
  final bool showReminderDisclaimer;

  const LocationSelectionButton({
    super.key,
    this.selectedAddress,
    this.selectedLocation,
    this.onLocationSelected,
    this.label,
    this.hint,
    this.isRequired = false,
    this.validator,
    this.showReminderDisclaimer = false,
  });

  @override
  Widget build(BuildContext context) {
    final hasSelection = selectedAddress != null && selectedAddress!.isNotEmpty;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (label != null) ...[
          Text(
            isRequired ? '$label *' : label!,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
        ],
        
        // Selection button
        InkWell(
          onTap: () => _openLocationPicker(context),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: hasSelection
                    ? Theme.of(context).colorScheme.primary.withOpacity(0.3)
                    : Theme.of(context).colorScheme.outline,
                width: hasSelection ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: hasSelection
                        ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                        : Theme.of(context).colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    hasSelection ? Icons.location_on : Icons.add_location,
                    color: hasSelection
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                
                // Text content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hasSelection ? context.tr('create_salon.location_selected') : context.tr('create_salon.location_select'),
                        style: TextStyle(
                          fontSize: 12,
                          color: hasSelection
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        hasSelection
                            ? selectedAddress!
                            : (hint ?? 'Apăsați pentru a selecta locația'),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: hasSelection ? FontWeight.w600 : FontWeight.w400,
                          color: hasSelection
                              ? Theme.of(context).colorScheme.onSurface
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                
                // Arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
        
        // Validation error
        if (validator != null) ...[
          const SizedBox(height: 4),
          Builder(
            builder: (context) {
              final error = validator!(selectedAddress);
              if (error != null) {
                return Text(
                  error,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.error,
                    fontSize: 12,
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],

        // Reminder disclaimer
        if (showReminderDisclaimer) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondaryContainer,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.secondary.withOpacity(0.5),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).colorScheme.secondary,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    context.tr('create_salon.location_disclaimer'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSecondaryContainer,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _openLocationPicker(BuildContext context) async {
    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => FullScreenMapPicker(
          initialLocation: selectedLocation,
          initialAddress: selectedAddress,
          onLocationSelected: onLocationSelected,
        ),
      ),
    );

    if (result != null && onLocationSelected != null) {
      final location = result['location'] as LatLng?;
      final address = result['address'] as String?;
      
      if (location != null) {
        onLocationSelected!(location, address);
      }
    }
  }
}
