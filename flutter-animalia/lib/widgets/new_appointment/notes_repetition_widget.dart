import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';
import '../../models/salon_subscription.dart';
import '../../providers/subscription_provider.dart';
import '../../services/auth/auth_service.dart';
import '../../services/subscription/revenue_cat_paywall_service.dart';
import '../../services/ui_notification_service.dart';
import 'appointment_form_data.dart';
import 'recurrence_selection_widget.dart';

class NotesRepetitionWidget extends StatelessWidget {
  final AppointmentFormData formData;
  final Function(bool) onNotesVisibilityChanged;
  final Function(bool) onRepetitionVisibilityChanged;
  final Function(String) onRepetitionFrequencyChanged;
  final Function(String) onNotesChanged;
  final Function(int) onRecurrenceFrequencyChanged;
  final Function(String) onRecurrencePeriodChanged;
  final Function(int) onTotalRepetitionsChanged;
  final Function(String) onPaymentModelChanged;
  final Function(double) onDiscountPercentageChanged;

  const NotesRepetitionWidget({
    super.key,
    required this.formData,
    required this.onNotesVisibilityChanged,
    required this.onRepetitionVisibilityChanged,
    required this.onRepetitionFrequencyChanged,
    required this.onNotesChanged,
    required this.onRecurrenceFrequencyChanged,
    required this.onRecurrencePeriodChanged,
    required this.onTotalRepetitionsChanged,
    required this.onPaymentModelChanged,
    required this.onDiscountPercentageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Small + buttons row
        Row(
          children: [
            _buildSmallAddButton(
              context.tr('new_appointment.notes_label'),
              Icons.note_alt,
              formData.showNotes,
              () => onNotesVisibilityChanged(!formData.showNotes),
              context,
            ),
            const SizedBox(width: 12),
            _buildSmallAddButton(
              context.tr('new_appointment.recurring_appointments'),
              Icons.repeat,
              formData.showRepetition,
              () => _handleRecurringAppointmentAccess(context),
              context,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Show notes field if visible
        if (formData.showNotes) ...[
          _buildNotesField(context),
          const SizedBox(height: 16),
        ],

        // Show recurrence options if visible
        if (formData.showRepetition) ...[
          RecurrenceSelectionWidget(
            formData: formData,
            onFrequencyChanged: onRecurrenceFrequencyChanged,
            onPeriodChanged: onRecurrencePeriodChanged,
            onRepetitionsChanged: onTotalRepetitionsChanged,
            onPaymentModelChanged: onPaymentModelChanged,
            onDiscountPercentageChanged: onDiscountPercentageChanged,
          ),
          const SizedBox(height: 16),
        ],
      ],
    );
  }

  Widget _buildSmallAddButton(String label, IconData icon, bool isSelected, VoidCallback onTap, BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surfaceVariant,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isSelected ? Icons.check : Icons.add,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey[600],
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesField(BuildContext context) {
    return TextFormField(
      decoration: InputDecoration(
        labelText: context.tr('new_appointment.notes_label'),
        hintText: context.tr('new_appointment.notes_placeholder'),
        border: const OutlineInputBorder(),
        prefixIcon: Icon(Icons.note_alt, color: Theme.of(context).primaryColor),
      ),
      maxLines: 3,
      initialValue: formData.notes,
      onChanged: onNotesChanged,
      textCapitalization: TextCapitalization.sentences,
    );
  }

  /// Handle recurring appointment access with Team plan guard
  Future<void> _handleRecurringAppointmentAccess(BuildContext context) async {
    final subscriptionProvider = context.read<SubscriptionProvider>();
    final currentTier = subscriptionProvider.currentTier;

    // Check if user has Team plan or higher
    if (currentTier != null &&
        (currentTier == SubscriptionTier.team || currentTier == SubscriptionTier.enterprise)) {
      // User has access, toggle recurring appointments
      onRepetitionVisibilityChanged(!formData.showRepetition);
    } else {
      // User needs Team plan, show paywall
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId != null) {
        RevenueCatPaywallService.showPaywall(
          context: context,
          defaultTier: SubscriptionTier.team,
          salonId: salonId,
        );
      } else {
        UINotificationService.showError(
          context: context,
          title: 'Nu s-a putut determina salonul curent',
          message: 'Te rugăm să te asiguri că ești conectat la un salon valid.',
        );
      }
    }
  }
}
