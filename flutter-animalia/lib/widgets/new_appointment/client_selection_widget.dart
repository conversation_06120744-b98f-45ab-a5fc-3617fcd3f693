import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../utils/formatters/phone_number_utils.dart';
import '../../l10n/app_localizations.dart';
import '../common/standard_form_field.dart';
import '../common/address_action_widget.dart';
import '../../models/client.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_form_data.dart';
import '../../models/pet.dart';
import '../../providers/calendar_provider.dart';
import 'package:provider/provider.dart';

class ClientSelectionWidget extends StatefulWidget {
  final AppointmentFormData formData;
  final bool isLoadingClients;
  final Function(bool) onClientTypeChanged;
  final Function(Client) onClientSelected;
  final Function(String) onClientNameChanged;
  final Function(String) onClientPhoneChanged;
  // New pet-related callbacks for integrated form
  final Function(String) onPetNameChanged;
  final Function(String) onPetBreedChanged;
  final Function(String) onPetSpeciesChanged;

  const ClientSelectionWidget({
    super.key,
    required this.formData,
    this.isLoadingClients = false,
    required this.onClientTypeChanged,
    required this.onClientSelected,
    required this.onClientNameChanged,
    required this.onClientPhoneChanged,
    required this.onPetNameChanged,
    required this.onPetBreedChanged,
    required this.onPetSpeciesChanged,
  });

  @override
  State<ClientSelectionWidget> createState() => _ClientSelectionWidgetState();
}

class _ClientSelectionWidgetState extends State<ClientSelectionWidget> {
  List<String> _currentBreeds = [];
  bool _isLoadingBreeds = false;

  // Phone number state management
  String _completePhoneNumber = '';
  String _initialCountryCode = 'RO';
  static const String _lastCountryCodeKey = 'last_client_country_code';

  @override
  void initState() {
    super.initState();

    // Extract country code from phone number if provided
    if (widget.formData.clientPhone.isNotEmpty) {
      _completePhoneNumber = widget.formData.clientPhone;
      _initialCountryCode = _extractCountryCode(widget.formData.clientPhone);
    } else {
      // Load last used country code if no phone provided
      _loadLastCountryCode();
    }

    // Load breeds for the current pet species if available
    if (widget.formData.petSpecies.isNotEmpty) {
      _refreshBreedsForSpecies(widget.formData.petSpecies);
    } else {
      // Default to dog breeds if no species is set
      _refreshBreedsForSpecies('dog');
    }
  }

  // Extract country code from phone number (e.g., +40 -> RO)
  String _extractCountryCode(String phoneNumber) {
    if (phoneNumber.startsWith('+40')) return 'RO';
    if (phoneNumber.startsWith('+1')) return 'US';
    if (phoneNumber.startsWith('+44')) return 'GB';
    if (phoneNumber.startsWith('+49')) return 'DE';
    if (phoneNumber.startsWith('+33')) return 'FR';
    if (phoneNumber.startsWith('+34')) return 'ES';
    if (phoneNumber.startsWith('+39')) return 'IT';
    // Add more country codes as needed
    return 'RO'; // Default to Romania
  }

  // Load last used country code from SharedPreferences
  Future<void> _loadLastCountryCode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedCode = prefs.getString(_lastCountryCodeKey);
      if (savedCode != null && savedCode.isNotEmpty) {
        setState(() {
          _initialCountryCode = savedCode;
        });
      }
    } catch (e) {
      // Ignore errors, use default
    }
  }

  // Save last used country code to SharedPreferences
  Future<void> _saveLastCountryCode(String countryCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastCountryCodeKey, countryCode);
    } catch (e) {
      // Ignore errors
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 16),
        if (widget.formData.isExistingClient)
          _buildExistingClientDropdown(context)
        else
          _buildNewClientFields(),
      ],
    );
  }


  Widget _buildExistingClientDropdown(BuildContext context) {
    if (widget.isLoadingClients) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).colorScheme.outline),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(width: 12),
            Text(context.tr('new_appointment.loading_clients')),
          ],
        ),
      );
    }

    return _buildClientSearchButton(context);
  }

  Widget _buildClientSearchButton(BuildContext context) {
    final hasClientSelected = widget.formData.clientId.isNotEmpty &&
                              !widget.formData.clientId.startsWith('new-');

    return InkWell(
      onTap: () => _navigateToClientSearch(context),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).colorScheme.outline),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Client',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  SizedBox(height: 4),
                  if (hasClientSelected) ...[
                    // Client name
                    Text(
                      widget.formData.clientName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 4),
                    // Phone number
                    Text(
                      widget.formData.clientPhone,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    // Address (if available)
                    if (widget.formData.clientAddress.isNotEmpty) ...[
                      SizedBox(height: 4),
                      AddressActionWidget(
                        address: widget.formData.clientAddress,
                        textStyle: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        iconColor: Theme.of(context).colorScheme.primary,
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ] else
                    Text(
                      context.tr('new_appointment.select_client'),
                      style: TextStyle(
                        fontSize: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                ],
              ),
            ),
             Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
          ],
        ),
      ),
    );
  }

  void _navigateToClientSearch(BuildContext context) async {
    final result = await Navigator.of(context).pushNamed(
      '/client-search',
      arguments: widget.formData.availableClients,
    );

    if (result != null) {
      if (result is Client) {
        widget.onClientSelected(result);
      } else if (result == 'new_client') {
        // Switch to new client mode
        widget.onClientTypeChanged(false);
      }
    }
  }

  Widget _buildNewClientFields() {
    return Column(
      children: [
        // 1. Phone number first (required)
        _buildPhoneNumberAutocomplete(),
        const SizedBox(height: 16),

        // 2. Pet name second (required)
        TextFormField(
          decoration: InputDecoration(
            labelText: context.tr('new_appointment.pet_name_required'),
            border: OutlineInputBorder(),
            hintText: context.tr('new_appointment.pet_name_placeholder'),
          ),
          initialValue: widget.formData.petName,
          onChanged: widget.onPetNameChanged,
          textCapitalization: TextCapitalization.words,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return context.tr('new_appointment.pet_name_validation');
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // 3. Pet breed third (required)
        _buildPetBreedField(),
        const SizedBox(height: 16),

        // 4. Client name last (optional)
        TextFormField(
          decoration: InputDecoration(
            labelText: context.tr('new_appointment.client_name_optional'),
            border: OutlineInputBorder(),
            helperText: context.tr('new_appointment.client_name_helper'),
          ),
          initialValue: widget.formData.clientName,
          onChanged: widget.onClientNameChanged,
          textCapitalization: TextCapitalization.words,
        ),
      ],
    );
  }

  // Feature 5: Phone number field with country code dropdown
  Widget _buildPhoneNumberAutocomplete() {
    return IntlPhoneField(
      decoration: InputDecoration(
        labelText: 'Telefon client *',
        hintText: '731 234 567',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        prefixIcon: const Icon(Icons.phone_outlined),
        contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      ),
      initialCountryCode: _initialCountryCode,
      dropdownIconPosition: IconPosition.trailing,
      flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8.0),
      dropdownTextStyle: TextStyle(
        color: Theme.of(context).colorScheme.onSurface,
      ),
      showDropdownIcon: true,
      disableLengthCheck: false,
      keyboardType: TextInputType.phone,
      onChanged: (phone) {
        setState(() {
          _completePhoneNumber = phone.completeNumber;
        });
        widget.onClientPhoneChanged(phone.completeNumber);
        // Save the selected country code for next time
        _saveLastCountryCode(phone.countryISOCode);

        // Check if this phone number matches an existing client
        _checkForExistingClient(phone.completeNumber);
      },
      validator: (value) {
        if (value == null || value.number.isEmpty) {
          return 'Numărul de telefon este obligatoriu';
        }
        // Basic validation - accept international numbers with at least 6 digits
        if (value.number.length < 6) {
          return 'Numărul de telefon nu este valid';
        }
        // IntlPhoneField already validates the number format for the selected country
        return null;
      },
      initialValue: widget.formData.clientPhone.isNotEmpty
          ? widget.formData.clientPhone.replaceAll(RegExp(r'[^\d]'), '')
          : null,
    );
  }

  // Check if phone number matches an existing client and show suggestion
  void _checkForExistingClient(String phoneNumber) {
    if (phoneNumber.isEmpty) return;

    final normalizedPhone = phoneNumber.replaceAll(RegExp(r'[\s\-\+\(\)]'), '');

    final matchingClient = widget.formData.availableClients.firstWhere(
      (client) {
        final clientPhone = client.phone.replaceAll(RegExp(r'[\s\-\+\(\)]'), '');
        return clientPhone == normalizedPhone || client.phone == phoneNumber;
      },
      orElse: () => Client(
        id: '',
        name: '',
        phone: '',
        email: '',
        address: '',
        notes: '',
        petIds: [],
        registrationDate: DateTime.now(),
      ),
    );

    // If found a matching client, show a snackbar suggestion
    if (matchingClient.id.isNotEmpty && mounted) {
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Client existent găsit: ${matchingClient.name}'),
          action: SnackBarAction(
            label: 'Selectează',
            onPressed: () {
              widget.onClientSelected(matchingClient);
              widget.onClientTypeChanged(true);
            },
          ),
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  Widget _buildPetBreedField() {
    return Autocomplete<String>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        // Use breeds from backend with fallback to static data
        final suggestions = _currentBreeds.isNotEmpty
            ? _currentBreeds
            : ['Metis', 'Necunoscut']; // Simple fallback
        if (textEditingValue.text.isEmpty) {
          return suggestions;
        }
        return suggestions.where((option) =>
            option.toLowerCase().contains(textEditingValue.text.toLowerCase()));
      },
      onSelected: (String selection) {
        widget.onPetBreedChanged(selection);
      },
      fieldViewBuilder: (context, controller, focusNode, onEditingComplete) {
        // Sync the autocomplete controller with our breed value
        if (controller.text != widget.formData.petBreed) {
          controller.text = widget.formData.petBreed;
        }

        return TextFormField(
          controller: controller,
          focusNode: focusNode,
          onEditingComplete: onEditingComplete,
          onChanged: (value) {
            widget.onPetBreedChanged(value);
          },
          decoration: InputDecoration(
            labelText: context.tr('new_appointment.breed_required'),
            hintText: _isLoadingBreeds
                ? context.tr('new_appointment.breed_loading')
                : context.tr('new_appointment.breed_placeholder'),
            prefixIcon: Icon(Icons.search),
            suffixIcon: _isLoadingBreeds
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : IconButton(
                    icon: Icon(Icons.refresh),
                    onPressed: () => _refreshBreedsForSpecies(widget.formData.petSpecies),
                    tooltip: context.tr('new_appointment.breed_refresh_tooltip'),
                  ),
            border: OutlineInputBorder(),
          ),
          textCapitalization: TextCapitalization.words,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return context.tr('new_appointment.breed_validation');
            }
            return null;
          },
        );
      },
      optionsViewBuilder: (context, onSelected, options) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 200),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: options.length,
                itemBuilder: (context, index) {
                  final option = options.elementAt(index);
                  return InkWell(
                    onTap: () => onSelected(option),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      child: Text(option),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _refreshBreedsForSpecies(String species) async {
    if (species.isEmpty) return;

    setState(() => _isLoadingBreeds = true);

    try {
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      final breeds = await calendarProvider.getBreedsForSpecies(species);

      if (mounted) {
        setState(() {
          _currentBreeds = breeds;
          _isLoadingBreeds = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _currentBreeds = ['Metis', 'Necunoscut']; // Fallback
          _isLoadingBreeds = false;
        });
      }
    }
  }
}
