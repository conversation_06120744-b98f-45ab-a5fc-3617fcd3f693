import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import 'appointment_form_data.dart';
import 'payment_model_selection_widget.dart';

class RecurrenceSelectionWidget extends StatelessWidget {
  final AppointmentFormData formData;
  final Function(int) onFrequencyChanged;
  final Function(String) onPeriodChanged;
  final Function(int) onRepetitionsChanged;
  final Function(String) onPaymentModelChanged;
  final Function(double) onDiscountPercentageChanged;

  const RecurrenceSelectionWidget({
    super.key,
    required this.formData,
    required this.onFrequencyChanged,
    required this.onPeriodChanged,
    required this.onRepetitionsChanged,
    required this.onPaymentModelChanged,
    required this.onDiscountPercentageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.repeat,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('new_appointment.recurrence_title'),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Frequency and Period Row
            Row(
              children: [
                // Frequency dropdown (1-99)
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('new_appointment.every_label'),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<int>(
                        value: formData.recurrenceFrequency,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: List.generate(99, (index) => index + 1)
                            .map((frequency) => DropdownMenuItem(
                                  value: frequency,
                                  child: Text(frequency.toString()),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            onFrequencyChanged(value);
                          }
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                
                // Period dropdown
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('new_appointment.period_label'),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        value: formData.recurrencePeriod,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: [
                          DropdownMenuItem(value: 'days', child: Text(context.tr('new_appointment.period_days'))),
                          DropdownMenuItem(value: 'weeks', child: Text(context.tr('new_appointment.period_weeks'))),
                          DropdownMenuItem(value: 'months', child: Text(context.tr('new_appointment.period_months'))),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            onPeriodChanged(value);
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Total repetitions
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('new_appointment.total_appointments_label'),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<int>(
                  value: formData.totalRepetitions,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: List.generate(24, (index) => index + 1)
                      .map((repetitions) => DropdownMenuItem(
                            value: repetitions,
                            child: Text('$repetitions ${repetitions == 1 ? context.tr('new_appointment.appointment_singular') : context.tr('new_appointment.appointment_plural')}'),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      onRepetitionsChanged(value);
                    }
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Payment model selection
            PaymentModelSelectionWidget(
              formData: formData,
              onPaymentModelChanged: onPaymentModelChanged,
              onDiscountPercentageChanged: onDiscountPercentageChanged,
            ),

            const SizedBox(height: 16),

            // Preview section
            _buildPreviewSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.preview,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 8),
              Text(
                context.tr('new_appointment.preview_label'),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _generatePreviewText(context),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  String _generatePreviewText(BuildContext context) {
    final frequency = formData.recurrenceFrequency;
    final period = formData.recurrencePeriod;
    final total = formData.totalRepetitions;
    
    String periodText;
    switch (period) {
      case 'days':
        periodText = frequency == 1 ? context.tr('new_appointment.period_day_singular') : context.tr('new_appointment.period_days');
        break;
      case 'weeks':
        periodText = frequency == 1 ? context.tr('new_appointment.period_week_singular') : context.tr('new_appointment.period_weeks');
        break;
      case 'months':
        periodText = frequency == 1 ? context.tr('new_appointment.period_month_singular') : context.tr('new_appointment.period_months');
        break;
      default:
        periodText = period;
    }
    
    final frequencyText = frequency == 1 ? '' : '$frequency ';
    final appointmentsText = total == 1 ? context.tr('new_appointment.appointment_singular') : context.tr('new_appointment.appointment_plural');
    
    return context.tr('new_appointment.preview_text', params: {
      'total': total.toString(),
      'appointments': appointmentsText,
      'frequency': frequencyText,
      'period': periodText,
    });
  }
}
