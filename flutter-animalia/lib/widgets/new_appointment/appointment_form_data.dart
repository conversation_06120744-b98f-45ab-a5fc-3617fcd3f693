import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../l10n/app_localizations.dart';
import '../../models/client.dart';
import '../../models/pet.dart';
import '../../models/service.dart';
import '../../services/staff_service.dart';
import 'appointment_form_constants.dart';

/// Custom service model for ad-hoc service editing in appointments
class CustomService {
  final String originalServiceName;
  final String customName;
  final double customPrice;
  final String customDescription;

  CustomService({
    required this.originalServiceName,
    required this.customName,
    required this.customPrice,
    this.customDescription = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'originalServiceName': originalServiceName,
      'customName': customName,
      'customPrice': customPrice,
      'customDescription': customDescription,
    };
  }

  factory CustomService.fromJson(Map<String, dynamic> json) {
    return CustomService(
      originalServiceName: json['originalServiceName'] ?? '',
      customName: json['customName'] ?? '',
      customPrice: json['customPrice']?.toDouble() ?? 0.0,
      customDescription: json['customDescription'] ?? '',
    );
  }
}

/// Form data model for appointment creation
class AppointmentFormData {
  // Client data
  String clientId;
  String clientName;
  String clientPhone;
  String clientAddress;
  bool isExistingClient;

  // Pet data
  String petId;
  String petName;
  String petSpecies;
  String petBreed;
  String petSize;

  // Appointment data
  DateTime appointmentDate;
  List<String> services;
  Map<String, CustomService> customServices; // Map service name to custom service details
  DateTime startTime;
  DateTime endTime;
  bool isPaid;
  String notes;
  String assignedCoworker; // Staff name for backward compatibility
  String assignedStaffId; // Staff ID for proper identification
  bool showNotes; // Whether notes field is visible
  bool showRepetition; // Whether repetition field is visible
  String repetitionFrequency; // 'daily', 'weekly', 'biweekly', 'monthly'

  // Enhanced recurrence options
  int recurrenceFrequency; // 1-99
  String recurrencePeriod; // 'days', 'weeks', 'months'
  int totalRepetitions; // 1-24

  // Payment model options for recurring appointments
  String paymentModel; // 'per_appointment', 'upfront_with_discount'
  double discountPercentage; // 0-100 for upfront payments

  // UI state
  List<Client> availableClients;
  List<Pet> clientPets;
  List<String> availableServices;
  List<StaffResponse> availableStaff; // Changed from List<String> to List<Groomer>

  // Remember previously selected existing client
  String? savedClientId;
  String? savedClientName;
  String? savedClientPhone;
  List<Pet> savedClientPets;

  AppointmentFormData({
    this.clientId = '',
    this.clientName = '',
    this.clientPhone = '',
    this.clientAddress = '',
    this.isExistingClient = true,
    this.petId = '',
    this.petName = '',
    this.petSpecies = 'dog',
    this.petBreed = '',
    this.petSize = 'M',
    required this.appointmentDate,
    this.services = const [],
    this.customServices = const {},
    required this.startTime,
    required this.endTime,
    this.isPaid = false,
    this.notes = '',
    this.assignedCoworker = '',
    this.assignedStaffId = '',
    this.showNotes = false,
    this.showRepetition = false,
    this.repetitionFrequency = 'none',
    this.recurrenceFrequency = 1,
    this.recurrencePeriod = 'weeks',
    this.totalRepetitions = 6,
    this.paymentModel = 'per_appointment',
    this.discountPercentage = 0.0,
    this.availableClients = const [],
    this.clientPets = const [],
    this.availableServices = const [],
    this.availableStaff = const [],
    this.savedClientId,
    this.savedClientName,
    this.savedClientPhone,
    List<Pet>? savedClientPets,
  }) : savedClientPets = savedClientPets ?? const [];

  /// Validates the form data
  String? validate(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    if (clientPhone.isEmpty) {
      return l10n.translate('new_appointment.validation_client_phone_required');
    }
    if (petName.isEmpty) {
      return l10n.translate('new_appointment.validation_pet_name_required');
    }
    if (petBreed.isEmpty) {
      return l10n.translate('new_appointment.validation_pet_breed_required');
    }
    return null;
  }

  /// Validates the form data for backend ScheduleAppointmentRequest DTO
  String? validateForBackend(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    // Validate required fields for ScheduleAppointmentRequest
    if (!isNewClient && clientId.isEmpty) {
      return l10n.translate('new_appointment.validation_client_required');
    }
    // if (!isNewPet && petId.isEmpty) {
    //   return 'ID-ul animalului este obligatoriu';
    // }
    if (assignedStaffId.isEmpty) {
      return l10n.translate('new_appointment.validation_staff_required');
    }
    if (services.isEmpty) {
      return l10n.translate('new_appointment.validation_service_required');
    }
    if (startTime.isAfter(endTime)) {
      return l10n.translate('new_appointment.validation_start_before_end');
    }
    if (startTime.isBefore(DateTime.now().add(const Duration(minutes: 15)))) {
      return l10n.translate('new_appointment.validation_time_in_past');
    }

    // Validate client and pet data for new entries
    if (isNewClient && clientPhone.isEmpty) {
      return l10n.translate('new_appointment.validation_client_phone_required');
    }
    if (isNewPet && petName.isEmpty) {
      return l10n.translate('new_appointment.validation_pet_name_required');
    }
    if (isNewPet && petBreed.isEmpty) {
      return l10n.translate('new_appointment.validation_pet_breed_required');
    }

    return null;
  }

  /// Creates appointment data map for API (legacy format)
  Map<String, dynamic> toAppointmentData() {
    final totalPrice = getTotalPrice();
    // Calculate actual duration from appointment time slot (start to end time)
    final actualDuration = endTime.difference(startTime).inMinutes;

    return {
      'id': 'appt-${DateTime.now().millisecondsSinceEpoch}',
      'clientId': clientId,
      'clientName': clientName,
      'clientPhone': clientPhone,
      'petId': petId,
      'petName': petName,
      'petSpecies': petSpecies,
      'petBreed': petBreed,
      'petSize': petSize,
      'service': services.isNotEmpty ? services.first : 'Serviciu general',
      'services': services,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'isPaid': isPaid,
      'notes': notes,
      'assignedGroomer': assignedCoworker,
      'assignedGroomerId': assignedStaffId,
      'assignedCoworker': assignedCoworker, // For backward compatibility
      'totalPrice': totalPrice,
      'totalDuration': actualDuration, // Use actual appointment duration
      'repetitionFrequency': repetitionFrequency,
      'status': 'confirmed',
    };
  }

  /// Creates appointment data map for backend ScheduleAppointmentRequest DTO
  Map<String, dynamic> toScheduleAppointmentRequest(Map<String, String> serviceNameToIdMap) {
    // Convert service names to service IDs using the provided mapping
    final serviceIds = services.map((serviceName) =>
      serviceNameToIdMap[serviceName] ?? serviceName
    ).toList();

    // Debug logging for service mapping

    // Check for duplicate service IDs
    final uniqueServiceIds = serviceIds.toSet();
    if (uniqueServiceIds.length != serviceIds.length) {
    }

    // Format repetition frequency for backend (convert 'none' to null)
    final backendRepetitionFrequency = repetitionFrequency == 'none' ? null : repetitionFrequency;

    final request = {
      'clientId': clientId,
      'clientName': clientName,
      'clientPhone': clientPhone,
      'petId': petId,
      'petName': petName,
      'petSpecies': petSpecies,
      'petBreed': petBreed,
      'petSize': petSize,
      'staffId': assignedStaffId,
      'appointmentDate': '${appointmentDate.year}-${appointmentDate.month.toString().padLeft(2, '0')}-${appointmentDate.day.toString().padLeft(2, '0')}',
      'startTime': '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}:00',
      'endTime': '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}:00',
      'serviceIds': serviceIds,
      // Include custom services data if any exist
      if (customServices.isNotEmpty) 'customServices': customServices.map((serviceName, customService) =>
        MapEntry(serviceNameToIdMap[serviceName] ?? serviceName, customService.toJson())),
      if (notes.isNotEmpty) 'notes': notes,
      if (backendRepetitionFrequency != null) 'repetitionFrequency': backendRepetitionFrequency,
      // Enhanced recurrence options
      if (showRepetition && totalRepetitions > 1) ...{
        'recurrenceFrequency': recurrenceFrequency,
        'recurrencePeriod': recurrencePeriod.toUpperCase(),
        'totalRepetitions': totalRepetitions,
        'paymentModel': paymentModel.toUpperCase(),
        if (paymentModel == 'upfront_with_discount') 'discountPercentage': discountPercentage,
      },
    };

    // Debug logging to verify correct date/time alignment

    return request;
  }

  /// Updates client data when selecting existing client
  void updateClientData(Client client) {
    clientId = client.id;
    clientName = client.name;
    clientPhone = client.phone;
    clientAddress = client.address;

    // Remember selection for later restoration
    savedClientId = client.id;
    savedClientName = client.name;
    savedClientPhone = client.phone;

    // Clear previous client's pets to avoid showing stale data
    clientPets = [];

    // Reset pet selection to avoid invalid state
    petId = '';
    petName = '';
    petSpecies = 'dog';
    petBreed = '';
    petSize = 'M';
  }

  /// Updates pet data when selecting existing pet
  Future<void> updatePetData(Pet pet) async {
    petId = pet.id;
    petName = pet.name;
    petSpecies = pet.species;
    petBreed = pet.breed;
    
    try {
      petSize = await AppointmentFormConstants.getBreedSize(pet.breed);
    } catch (e) {
      petSize = 'M'; // Default to medium on error
    }
  }

  /// Updates staff data when selecting staff member
  void updateStaffData(StaffResponse staff) {
    assignedStaffId = staff.id;
    assignedCoworker = staff.name;
  }

  /// Resets to new client mode
  void resetToNewClient() {
    clientId = 'new-${DateTime.now().millisecondsSinceEpoch}';
    clientName = '';
    clientPhone = '';
    clientAddress = '';
    petId = 'new-pet-${DateTime.now().millisecondsSinceEpoch}';
    petName = '';
    petSpecies = 'dog';
    petBreed = '';
    petSize = 'M';
  }

  /// Resets to new pet mode
  void resetToNewPet() {
    petId = 'new-pet-${DateTime.now().millisecondsSinceEpoch}';
    petName = '';
    petSpecies = 'dog';
    petBreed = '';
    petSize = 'M';
  }

  /// Checks if current pet is new
  bool get isNewPet => petId.startsWith('new-pet-');

  /// Checks if current client is new
  bool get isNewClient => clientId.startsWith('new-');

  /// Adds a service to the list
  void addService(String service) {
    if (!services.contains(service)) {
      services = [...services, service];
    }
  }

  /// Removes a service from the list
  void removeService(String service) {
    services = services.where((s) => s != service).toList();
    // Also remove any custom service data for this service
    customServices = Map.from(customServices)..remove(service);
  }

  /// Adds or updates a custom service
  void setCustomService(String serviceName, CustomService customService) {
    customServices = Map.from(customServices)..[serviceName] = customService;
  }

  /// Gets custom service data for a service name
  CustomService? getCustomService(String serviceName) {
    return customServices[serviceName];
  }

  /// Checks if a service has custom modifications
  bool hasCustomService(String serviceName) {
    return customServices.containsKey(serviceName);
  }

  /// Calculates total duration in minutes
  int getTotalDuration(Map<String, int> serviceDurations) {
    return services.fold(0, (total, service) {
      return total + (serviceDurations[service] ?? 60);
    });
  }

  /// Calculates total duration using service details
  int getTotalDurationFromDetails(Map<String, Map<String, dynamic>> serviceDetails) {
    return services.fold(0, (total, service) {
      final details = serviceDetails[service];
      return total + ((details?['duration'] as int?) ?? 60);
    });
  }

  /// Calculates total price using service details
  double getTotalPriceFromDetails(Map<String, Map<String, dynamic>> serviceDetails) {
    return services.fold(0.0, (total, service) {
      final details = serviceDetails[service];
      if (details == null) return total + 50.0;
      if (details['sizePrices'] != null && details['sizePrices'] is Map) {
        final prices = Map<String, double>.from(details['sizePrices']);
        return total + (prices[petSize] ?? details['price'] ?? 50.0);
      }
      return total + (details['price'] ?? 50.0);
    });
  }

  /// Enhanced price calculation with size-based ranges
  double getTotalPriceFromDetailsWithSize(
    Map<String, Map<String, dynamic>> serviceDetails,
    String petSize,
  ) {
    return services.fold(0.0, (total, serviceName) {
      final details = serviceDetails[serviceName];
      if (details == null) return total + 50.0;

      try {
        // Create Service object from details to use new pricing methods
        final service = Service.fromJson(details);
        return total + service.getPriceForSize(petSize);
      } catch (e) {
        // Fallback to old logic if Service creation fails
        if (details['sizePrices'] != null && details['sizePrices'] is Map) {
          final prices = Map<String, double>.from(details['sizePrices']);
          return total + (prices[petSize] ?? details['price'] ?? 50.0);
        }
        return total + (details['price'] ?? 50.0);
      }
    });
  }

  /// Get price range text for current pet size
  String getTotalPriceRangeText(
    Map<String, Map<String, dynamic>> serviceDetails,
  ) {
    if (services.isEmpty) return '0.00 RON';

    double minTotal = 0.0;
    double maxTotal = 0.0;
    bool hasRanges = false;

    for (final serviceName in services) {
      final details = serviceDetails[serviceName];
      if (details == null) {
        minTotal += 50.0;
        maxTotal += 50.0;
        continue;
      }

      try {
        final service = Service.fromJson(details);

        if (service.sizePrices != null && service.sizePrices!.containsKey(petSize)) {
          final basePrice = service.sizePrices![petSize]!;
          final minPrice = service.sizeMinPrices?[petSize];
          final maxPrice = service.sizeMaxPrices?[petSize];

          if (minPrice != null && maxPrice != null) {
            minTotal += minPrice;
            maxTotal += maxPrice;
            hasRanges = true;
          } else {
            minTotal += basePrice;
            maxTotal += basePrice;
          }
        } else {
          // Fixed pricing
          if (service.minPrice != null && service.maxPrice != null) {
            minTotal += service.minPrice!;
            maxTotal += service.maxPrice!;
            hasRanges = true;
          } else {
            minTotal += service.price;
            maxTotal += service.price;
          }
        }
      } catch (e) {
        // Fallback to old logic
        final price = details['price'] ?? 50.0;
        minTotal += price;
        maxTotal += price;
      }
    }

    if (hasRanges && minTotal != maxTotal) {
      return '${minTotal.toStringAsFixed(2)} - ${maxTotal.toStringAsFixed(2)} RON';
    }
    return '${minTotal.toStringAsFixed(2)} RON';
  }

  /// Calculates total price (fallback method with default prices)
  double getTotalPrice() {
    // Fallback prices if service details are not available
    const servicePrices = {
      'Baie completă': 50.0,
      'Tuns și aranjat': 80.0,
      'Toaletare completă': 120.0,
      'Tăiat unghii': 25.0,
      'Curățat urechi': 20.0,
      'Spălat dinți': 30.0,
      'Tratament anti-purici': 45.0,
      'Tuns de vară': 90.0,
      'Aranjat blană lungă': 100.0,
      'Pachet complet premium': 150.0,
      'Îngrijire gheare': 25.0,
      'Masaj relaxant': 60.0,
    };

    return services.fold(0.0, (total, service) {
      return total + (servicePrices[service] ?? 50.0);
    });
  }

  /// Gets the actual appointment duration in minutes (from start to end time)
  int getActualDuration() {
    return endTime.difference(startTime).inMinutes;
  }

  /// Check if any selected services have size-based pricing
  bool hasServicesWithSizeBasedPricing(Map<String, Map<String, dynamic>> serviceDetails) {
    for (final serviceName in services) {
      final details = serviceDetails[serviceName];
      if (details != null) {
        try {
          final service = Service.fromJson(details);
          if (service.sizePrices != null) {
            return true;
          }
        } catch (e) {
          // Continue checking other services
        }
      }
    }
    return false;
  }

  /// Get detailed pricing breakdown for debugging
  Map<String, dynamic> getPricingBreakdown(Map<String, Map<String, dynamic>> serviceDetails) {
    final breakdown = <String, dynamic>{
      'petSize': petSize,
      'services': <Map<String, dynamic>>[],
      'totalPrice': 0.0,
      'hasSizeBasedPricing': false,
    };

    double total = 0.0;
    bool hasSizeBased = false;

    for (final serviceName in services) {
      final details = serviceDetails[serviceName];
      if (details != null) {
        try {
          final service = Service.fromJson(details);
          final price = service.getPriceForSize(petSize);
          final isSizeBased = service.sizePrices != null;

          breakdown['services'].add({
            'name': serviceName,
            'price': price,
            'isSizeBasedPricing': isSizeBased,
            'priceText': service.getFormattedPriceForSize(petSize),
          });

          total += price;
          if (isSizeBased) hasSizeBased = true;
        } catch (e) {
          // Fallback pricing
          final price = details['price'] ?? 50.0;
          breakdown['services'].add({
            'name': serviceName,
            'price': price,
            'isSizeBasedPricing': false,
            'priceText': '${price.toStringAsFixed(2)} RON',
            'error': e.toString(),
          });
          total += price;
        }
      }
    }

    breakdown['totalPrice'] = total;
    breakdown['hasSizeBasedPricing'] = hasSizeBased;
    return breakdown;
  }

  /// Gets formatted duration string for services
  String getFormattedServiceDuration(Map<String, int> serviceDurations) {
    final totalMinutes = getTotalDuration(serviceDurations);
    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;

    if (hours > 0 && minutes > 0) {
      return '${hours}h ${minutes}min';
    } else if (hours > 0) {
      return '${hours}h';
    } else {
      return '${minutes}min';
    }
  }

  /// Gets formatted duration string for actual appointment time
  String getFormattedActualDuration() {
    final totalMinutes = getActualDuration();
    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;

    if (hours > 0 && minutes > 0) {
      return '${hours}h ${minutes}min';
    } else if (hours > 0) {
      return '${hours}h';
    } else {
      return '${minutes}min';
    }
  }


}
