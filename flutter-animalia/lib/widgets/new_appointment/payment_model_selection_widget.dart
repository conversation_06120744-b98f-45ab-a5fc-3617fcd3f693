import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';
import 'appointment_form_data.dart';
import '../../providers/calendar_provider.dart';

class PaymentModelSelectionWidget extends StatelessWidget {
  final AppointmentFormData formData;
  final Function(String) onPaymentModelChanged;
  final Function(double) onDiscountPercentageChanged;

  const PaymentModelSelectionWidget({
    super.key,
    required this.formData,
    required this.onPaymentModelChanged,
    required this.onDiscountPercentageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payment,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('new_appointment.payment_model_title'),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Payment model radio buttons
            Column(
              children: [
                RadioListTile<String>(
                  title: Text(context.tr('new_appointment.payment_per_appointment')),
                  subtitle: Text(context.tr('new_appointment.payment_per_appointment_subtitle')),
                  value: 'per_appointment',
                  groupValue: formData.paymentModel,
                  onChanged: (value) {
                    if (value != null) {
                      onPaymentModelChanged(value);
                    }
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                
                RadioListTile<String>(
                  title: Text(context.tr('new_appointment.payment_upfront_discount')),
                  subtitle: Text(_getUpfrontSubtitle(context)),
                  value: 'upfront_with_discount',
                  groupValue: formData.paymentModel,
                  onChanged: (value) {
                    if (value != null) {
                      onPaymentModelChanged(value);
                    }
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
            
            // Discount percentage input (only show for upfront payment)
            if (formData.paymentModel == 'upfront_with_discount') ...[
              const SizedBox(height: 16),
              _buildDiscountInput(context),
            ],
            
            // Payment summary (only show for upfront payment)
            if (formData.paymentModel == 'upfront_with_discount') ...[
              const SizedBox(height: 16),
              _buildPaymentSummary(context),
            ],
          ],
        ),
      ),
    );
  }

  String _getUpfrontSubtitle(BuildContext context) {
    if (formData.paymentModel == 'upfront_with_discount' && formData.discountPercentage > 0) {
      return context.tr('new_appointment.payment_upfront_subtitle_with_discount', params: {
        'discount': formData.discountPercentage.toStringAsFixed(0),
      });
    }
    return context.tr('new_appointment.payment_upfront_subtitle');
  }

  Widget _buildDiscountInput(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('new_appointment.discount_percentage_label'),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          initialValue: formData.discountPercentage.toStringAsFixed(1),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            suffixText: '%',
            hintText: '0.0',
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
          ],
          onChanged: (value) {
            final percentage = double.tryParse(value) ?? 0.0;
            if (percentage >= 0 && percentage <= 100) {
              onDiscountPercentageChanged(percentage);
            }
          },
          validator: (value) {
            final percentage = double.tryParse(value ?? '') ?? 0.0;
            if (percentage < 0 || percentage > 100) {
              return context.tr('new_appointment.discount_validation_error');
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPaymentSummary(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, calendarProvider, child) {
        // Get actual service prices
        final serviceDetails = calendarProvider.getServiceDetails();
        final appointmentPrice = formData.getTotalPriceFromDetailsWithSize(serviceDetails, formData.petSize);
        final totalAppointments = formData.totalRepetitions;
        final originalTotal = appointmentPrice * totalAppointments;
        final discountAmount = originalTotal * (formData.discountPercentage / 100);
        final finalTotal = originalTotal - discountAmount;

        return _buildPaymentSummaryContent(context, appointmentPrice, totalAppointments, originalTotal, discountAmount, finalTotal);
      },
    );
  }

  Widget _buildPaymentSummaryContent(BuildContext context, double appointmentPrice, int totalAppointments, double originalTotal, double discountAmount, double finalTotal) {

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr('new_appointment.payment_summary_title'),
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          
          _buildSummaryRow(
            context,
            context.tr('new_appointment.price_per_appointment'),
            '${appointmentPrice.toStringAsFixed(0)} RON',
          ),
          _buildSummaryRow(
            context,
            context.tr('new_appointment.number_of_appointments'),
            totalAppointments.toString(),
          ),
          _buildSummaryRow(
            context,
            context.tr('new_appointment.original_total'),
            '${originalTotal.toStringAsFixed(0)} RON',
          ),
          _buildSummaryRow(
            context,
            context.tr('new_appointment.discount_amount', params: {
              'discount': formData.discountPercentage.toStringAsFixed(0),
            }),
            '-${discountAmount.toStringAsFixed(0)} RON',
            isDiscount: true,
          ),
          const Divider(),
          _buildSummaryRow(
            context,
            context.tr('new_appointment.total_to_pay'),
            '${finalTotal.toStringAsFixed(0)} RON',
            isFinal: true,
          ),
          const SizedBox(height: 4),
          Text(
            context.tr('new_appointment.savings_message', params: {
              'amount': discountAmount.toStringAsFixed(0),
            }),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.green,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String value, {
    bool isDiscount = false,
    bool isFinal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isFinal ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isFinal ? FontWeight.w600 : FontWeight.normal,
              color: isDiscount 
                  ? Colors.green 
                  : isFinal 
                      ? Theme.of(context).colorScheme.primary
                      : null,
            ),
          ),
        ],
      ),
    );
  }
}
