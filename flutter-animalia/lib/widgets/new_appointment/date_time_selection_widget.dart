import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

import '../../l10n/app_localizations.dart';
import 'appointment_form_constants.dart';
import 'appointment_form_data.dart';

class DateTimeSelectionWidget extends StatefulWidget {
  final AppointmentFormData formData;
  final Function(DateTime) onDateChanged;
  final Function(DateTime) onStartTimeChanged;
  final Function(DateTime) onEndTimeChanged;
  final Function(Duration)? onDurationChanged;

  const DateTimeSelectionWidget({
    Key? key,
    required this.formData,
    required this.onDateChanged,
    required this.onStartTimeChanged,
    required this.onEndTimeChanged,
    this.onDurationChanged,
  }) : super(key: key);

  @override
  State<DateTimeSelectionWidget> createState() => _DateTimeSelectionWidgetState();
}

// Simple implementation for v1 - only date picker and hour picker
// V2 will include advanced features like time slots and calendar view
class _DateTimeSelectionWidgetState extends State<DateTimeSelectionWidget> {
  // V2 Features (commented out for now):
  // - TabController for switching between "Ore disponibile" and "Calendar" tabs
  // - Time slot grid with availability checking
  // - Animation controllers for smooth transitions
  // - Quick duration selector chips

  /*
  // V2: Advanced time slot management
  late TabController _tabController;
  final List<Duration> _quickDurations = [
    const Duration(minutes: 30),
    const Duration(minutes: 45),
    const Duration(hours: 1),
    const Duration(hours: 1, minutes: 30),
    const Duration(hours: 2),
  ];

  // V2: Animation for time slot selection
  late AnimationController _animationController;
  int _selectedTimeSlotIndex = -1;
  */

  @override
  void initState() {
    super.initState();
    // V1: Simple initialization - no complex controllers needed

    /*
    // V2: Advanced initialization with tab and animation controllers
    _tabController = TabController(length: 2, vsync: this);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    // Find initial selected time slot
    _initializeSelectedTimeSlot();
    */
  }

  /*
  // V2: Time slot initialization for advanced grid view
  void _initializeSelectedTimeSlot() {
    final timeSlots = _generateTimeSlots();
    for (int i = 0; i < timeSlots.length; i++) {
      if (_isSameTime(timeSlots[i], widget.formData.startTime)) {
        _selectedTimeSlotIndex = i;
        break;
      }
    }
  }

  bool _isSameTime(DateTime time1, DateTime time2) {
    return time1.hour == time2.hour && time1.minute == time2.minute;
  }
  */

  @override
  void dispose() {
    // V1: Simple disposal - no controllers to dispose
    super.dispose();

    /*
    // V2: Dispose advanced controllers
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
    */
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Streamlined date selection
        _buildDateHeader(),
        const SizedBox(height: 12),
        // Start and end time on the same line for efficiency
        _buildTimeSelectionRow(),
        // Removed redundant time display for cleaner UX

        /*
        // V2: Advanced tab-based interface with "Ore disponibile" and "Calendar"
        _buildTabBar(),
        const SizedBox(height: 8),
        SizedBox(
          height: 280, // Fixed height for the tab content
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildTimeSlotGrid(),
              _buildCalendarView(),
            ],
          ),
        ),
        const SizedBox(height: 16),
        _buildQuickDurationSelector(),
        const SizedBox(height: 8),
        */
      ],
    );
  }

  Widget _buildDateHeader() {
    return InkWell(
      onTap: _showDateTimePicker,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('new_appointment.date_time_title'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    '${DateFormat('dd/MM/yyyy', 'ro').format(widget.formData.appointmentDate)} la ${AppointmentFormConstants.formatTime(widget.formData.startTime)}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.edit,
              color: Theme.of(context).colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }

  // Combined time selection row for start and end times
  Widget _buildTimeSelectionRow() {
    return Row(
      children: [
        // Start time selector
        Expanded(
          child: InkWell(
            onTap: _showDirectStartTimePicker,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('new_appointment.start_time_label'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    AppointmentFormConstants.formatTime(widget.formData.startTime),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        // End time selector
        Expanded(
          child: InkWell(
            onTap: _showDirectEndTimePicker,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('new_appointment.end_time_label'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        AppointmentFormConstants.formatTime(widget.formData.endTime),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '(${_formatDuration(_calculateCurrentDuration())})',
                        style: TextStyle(
                          fontSize: 10,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /*
  // V2: Advanced tab bar for switching between "Ore disponibile" and "Calendar"
  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: 'Ore disponibile'),
          Tab(text: 'Calendar'),
        ],
        labelColor: Theme.of(context).colorScheme.primary,
        unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
        indicator: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        dividerColor: Colors.transparent,
      ),
    );
  }
  */

  /*
  // V2: Advanced time slot grid with availability checking and animations
  Widget _buildTimeSlotGrid() {
    final timeSlots = _generateTimeSlots();

    return GridView.builder(
      padding: const EdgeInsets.symmetric(vertical: 16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        childAspectRatio: 2.2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: timeSlots.length,
      itemBuilder: (context, index) {
        final timeSlot = timeSlots[index];
        final isSelected = _selectedTimeSlotIndex == index;
        final slotInfo = _getTimeSlotInfo(timeSlot); // Should return {isAvailable, isInteractive, disabledReason}
        final isAvailable = slotInfo.isAvailable;
        final isInteractive = slotInfo.isInteractive;
        final disabledReason = slotInfo.disabledReason;

        // Determine slot color
        Color slotColor;
        if (isSelected) {
          slotColor = Theme.of(context).colorScheme.primary;
        } else if (!isInteractive && disabledReason == 'Se încarcă...') {
          // Loading state: use neutral or shimmer color
          slotColor = Theme.of(context).colorScheme.surface.withOpacity(0.5);
        } else if (isAvailable) {
          slotColor = Theme.of(context).colorScheme.surface;
        } else {
          slotColor = Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3);
        }

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            color: slotColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : isAvailable
                      ? Theme.of(context).colorScheme.outline.withOpacity(0.5)
                      : Colors.transparent,
            ),
            boxShadow: isSelected ? [
              BoxShadow(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: isAvailable && isInteractive ? () => _selectTimeSlot(index, timeSlot) : null,
              borderRadius: BorderRadius.circular(8),
              child: Center(
                child: Text(
                  AppointmentFormConstants.formatTime(timeSlot),
                  style: TextStyle(
                    color: isSelected
                        ? Theme.of(context).colorScheme.onPrimary
                        : isAvailable
                            ? Theme.of(context).colorScheme.onSurface
                            : (!isInteractive && disabledReason == 'Se încarcă...')
                                ? Theme.of(context).colorScheme.onSurface.withOpacity(0.5)
                                : Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        },
      },
    );
  }
  */

  /*
  // V2: Calendar view for advanced date selection
  Widget _buildCalendarView() {
    // A simplified calendar view that shows a month calendar
    // with the selected date highlighted
    return CalendarDatePicker(
      initialDate: widget.formData.appointmentDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      onDateChanged: (date) {
        widget.onDateChanged(date);
        // Switch to time slot tab after selecting a date
        _tabController.animateTo(0);
      },
    );
  }

  // V2: Quick duration selector with preset time options
  Widget _buildQuickDurationSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Durată:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: _quickDurations.map((duration) {
              final isSelected = _isDurationSelected(duration);
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: ChoiceChip(
                  label: Text(_formatDuration(duration)),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      _applyDuration(duration);
                    }
                  },
                  backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                  selectedColor: Theme.of(context).colorScheme.primaryContainer,
                  labelStyle: TextStyle(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
  */

  Widget _buildSelectedTimeDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Programare selectată:',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  '${DateFormat('dd/MM/yyyy', 'ro').format(widget.formData.appointmentDate)} la ${AppointmentFormConstants.formatTime(widget.formData.startTime)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                // V2: Duration display will be added back with quick duration selector
                /*
                Text(
                  'Durată: ${_formatDuration(_calculateCurrentDuration())}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                */
              ],
            ),
          ),
          // V1: Simple edit button for time picker
          IconButton(
            icon: Icon(
              Icons.edit,
              color: Theme.of(context).colorScheme.primary,
              size: 20,
            ),
            onPressed: _showDirectStartTimePicker,
          ),
        ],
      ),
    );
  }

  // Combined date and time picker for streamlined selection
  void _showDateTimePicker() async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // First, show date picker
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: widget.formData.appointmentDate.isBefore(today)
          ? today
          : widget.formData.appointmentDate,
      firstDate: today, // Prevent selecting past dates
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      widget.onDateChanged(selectedDate);

      // Immediately show time picker after date selection
      _showDirectStartTimePicker();
    }
  }

  // Apple-style start time picker using CupertinoPicker
  void _showDirectStartTimePicker() async {
    final currentTime = widget.formData.startTime;
    final initialHour = currentTime.hour;
    final initialMinute = (currentTime.minute ~/ 15) * 15; // Round to nearest 15 minutes

    final result = await showModalBottomSheet<DateTime>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return _AppleStyleTimePickerBottomSheet(
          title: context.tr('new_appointment.select_start_time'),
          initialHour: initialHour,
          initialMinute: initialMinute,
          onTimeSelected: (hour, minute) {
            final selectedDateTime = DateTime(
              currentTime.year,
              currentTime.month,
              currentTime.day,
              hour,
              minute,
            );
            return selectedDateTime;
          },
        );
      },
    );

    if (result != null) {
      final now = DateTime.now();

      // Validate that the selected time is not in the past
      if (result.isBefore(now.add(const Duration(minutes: 15)))) {
        _showTimeInPastError();
        return;
      }

      widget.onStartTimeChanged(result);

      // Maintain current duration when changing start time
      final currentDuration = _calculateCurrentDuration();
      final newEndTime = result.add(currentDuration);
      widget.onEndTimeChanged(newEndTime);
    }
  }

  // Apple-style end time picker using CupertinoPicker
  void _showDirectEndTimePicker() async {
    final currentTime = widget.formData.endTime;
    final initialHour = currentTime.hour;
    final initialMinute = (currentTime.minute ~/ 15) * 15; // Round to nearest 15 minutes

    final result = await showModalBottomSheet<DateTime>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return _AppleStyleTimePickerBottomSheet(
          title: context.tr('new_appointment.select_end_time'),
          initialHour: initialHour,
          initialMinute: initialMinute,
          minTime: widget.formData.startTime.add(const Duration(minutes: 15)),
          onTimeSelected: (hour, minute) {
            final selectedDateTime = DateTime(
              currentTime.year,
              currentTime.month,
              currentTime.day,
              hour,
              minute,
            );
            return selectedDateTime;
          },
        );
      },
    );

    if (result != null) {
      final minEndTime = widget.formData.startTime.add(const Duration(minutes: 15));

      // Validate that end time is after start time
      if (result.isBefore(minEndTime)) {
        _showEndTimeError();
        return;
      }

      widget.onEndTimeChanged(result);
    }
  }

  // Generate time options in 15-minute intervals
  List<DateTime> _generateTimeOptions() {
    final date = widget.formData.appointmentDate;
    final now = DateTime.now();
    final isToday = date.year == now.year && date.month == now.month && date.day == now.day;

    final options = <DateTime>[];
    final startHour = isToday ? now.hour : 8; // Start from 8 AM or current hour if today
    final startMinute = isToday ? ((now.minute ~/ 15) + 1) * 15 : 0; // Round up to next 15-minute interval if today

    for (int hour = startHour; hour < 22; hour++) { // Until 10 PM
      final minuteStart = (hour == startHour && isToday) ? startMinute : 0;
      for (int minute = minuteStart; minute < 60; minute += 15) {
        if (minute >= 60) break;
        final time = DateTime(date.year, date.month, date.day, hour, minute);
        if (!isToday || time.isAfter(now.add(const Duration(minutes: 14)))) {
          options.add(time);
        }
      }
    }

    return options;
  }



  // Show error when user tries to select time in the past
  void _showTimeInPastError() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('new_appointment.time_in_past_error')),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }



  // Show error when end time is before start time
  void _showEndTimeError() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('new_appointment.end_time_before_start_error')),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  // Calculate current duration
  Duration _calculateCurrentDuration() {
    return widget.formData.endTime.difference(widget.formData.startTime);
  }

  // Format duration for display
  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      final minutes = duration.inMinutes % 60;
      if (minutes == 0) {
        return '${duration.inHours}h';
      } else {
        return '${duration.inHours}h ${minutes}min';
      }
    } else {
      return '${duration.inMinutes}min';
    }
  }

  /*
  // V2: Custom time dialog with advanced time slot integration
  void _showCustomTimeDialog() async {
    final TimeOfDay initialStartTime = TimeOfDay(
      hour: widget.formData.startTime.hour,
      minute: widget.formData.startTime.minute,
    );

    final startTime = await showTimePicker(
      context: context,
      initialTime: initialStartTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              backgroundColor: Theme.of(context).colorScheme.surface,
              hourMinuteShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              dayPeriodShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (startTime != null) {
      final newStartTime = DateTime(
        widget.formData.appointmentDate.year,
        widget.formData.appointmentDate.month,
        widget.formData.appointmentDate.day,
        startTime.hour,
        startTime.minute,
      );

      widget.onStartTimeChanged(newStartTime);

      // Update selected time slot index
      _initializeSelectedTimeSlot();
    }
  }
  */

  /*
  // V2: Advanced helper methods for time slot management and duration handling

  List<DateTime> _generateTimeSlots() {
    return AppointmentFormConstants.generateTimeOptions(widget.formData.appointmentDate);
  }

  bool _isTimeSlotAvailable(DateTime timeSlot) {
    // In a real app, this would check against booked appointments
    // For now, just check if it's within business hours
    final hour = timeSlot.hour;
    return hour >= AppointmentFormConstants.businessStartHour &&
           hour < AppointmentFormConstants.businessEndHour;
  }

  void _selectTimeSlot(int index, DateTime timeSlot) {
    setState(() {
      _selectedTimeSlotIndex = index;
      _animationController.forward(from: 0.0);
    });

    widget.onStartTimeChanged(timeSlot);
  }

  bool _isDurationSelected(Duration duration) {
    final currentDuration = _calculateCurrentDuration();
    return currentDuration.inMinutes == duration.inMinutes;
  }

  Duration _calculateCurrentDuration() {
    return widget.formData.endTime.difference(widget.formData.startTime);
  }

  void _applyDuration(Duration duration) {
    final endTime = widget.formData.startTime.add(duration);
    widget.onEndTimeChanged(endTime);
    if (widget.onDurationChanged != null) {
      widget.onDurationChanged!(duration);
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      final minutes = duration.inMinutes % 60;
      if (minutes == 0) {
        return '${duration.inHours}h';
      } else {
        return '${duration.inHours}h ${minutes}min';
      }
    } else {
      return '${duration.inMinutes}min';
    }
  }
  */
}

/// Apple-style time picker bottom sheet for appointment creation
class _AppleStyleTimePickerBottomSheet extends StatefulWidget {
  final String title;
  final int initialHour;
  final int initialMinute;
  final DateTime? minTime;
  final DateTime Function(int hour, int minute) onTimeSelected;

  const _AppleStyleTimePickerBottomSheet({
    required this.title,
    required this.initialHour,
    required this.initialMinute,
    required this.onTimeSelected,
    this.minTime,
  });

  @override
  State<_AppleStyleTimePickerBottomSheet> createState() => _AppleStyleTimePickerBottomSheetState();
}

class _AppleStyleTimePickerBottomSheetState extends State<_AppleStyleTimePickerBottomSheet> {
  late int _selectedHour;
  late int _selectedMinute;

  @override
  void initState() {
    super.initState();
    _selectedHour = widget.initialHour;
    _selectedMinute = widget.initialMinute;
  }

  void _triggerSelectionHaptic() {
    HapticFeedback.selectionClick();
  }

  void _triggerConfirmationHaptic() {
    HapticFeedback.mediumImpact();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 380, // Apple-style height
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header - Apple style with centered time display
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Column(
              children: [
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 17, // Apple's standard title size
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                // Large time display like Apple's alarm
                Text(
                  '${_selectedHour.toString().padLeft(2, '0')}:${_selectedMinute.toString().padLeft(2, '0')}',
                  style: TextStyle(
                    fontSize: 48, // Large Apple-style time display
                    fontWeight: FontWeight.w300, // Light weight like Apple
                    color: Theme.of(context).colorScheme.onSurface,
                    fontFeatures: const [FontFeature.tabularFigures()], // Monospace numbers
                  ),
                ),
              ],
            ),
          ),

          // Apple-style time picker wheels
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  // Hour picker - Apple style
                  Expanded(
                    flex: 2,
                    child: CupertinoPicker(
                      itemExtent: 44, // Apple's standard item height
                      diameterRatio: 1.07, // Apple's exact curvature
                      squeeze: 1.25, // Apple's compression ratio
                      useMagnifier: true, // Apple uses magnification
                      magnification: 1.22, // Apple's magnification factor
                      scrollController: FixedExtentScrollController(
                        initialItem: _selectedHour,
                      ),
                      onSelectedItemChanged: (index) {
                        _triggerSelectionHaptic();
                        setState(() {
                          _selectedHour = index;
                        });
                      },
                      children: List.generate(24, (index) {
                        return Center(
                          child: Text(
                            index.toString().padLeft(2, '0'),
                            style: TextStyle(
                              fontSize: 24, // Apple's wheel text size
                              fontWeight: FontWeight.w400, // Apple's weight
                              color: Theme.of(context).colorScheme.onSurface,
                              fontFeatures: const [FontFeature.tabularFigures()],
                            ),
                          ),
                        );
                      }),
                    ),
                  ),

                  // Colon separator - Apple style
                  Container(
                    width: 20,
                    alignment: Alignment.center,
                    child: Text(
                      ':',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w300,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ),

                  // Minute picker - Apple style
                  Expanded(
                    flex: 2,
                    child: CupertinoPicker(
                      itemExtent: 44, // Apple's standard item height
                      diameterRatio: 1.07, // Apple's exact curvature
                      squeeze: 1.25, // Apple's compression ratio
                      useMagnifier: true, // Apple uses magnification
                      magnification: 1.22, // Apple's magnification factor
                      scrollController: FixedExtentScrollController(
                        initialItem: _selectedMinute ~/ 15,
                      ),
                      onSelectedItemChanged: (index) {
                        _triggerSelectionHaptic();
                        setState(() {
                          _selectedMinute = index * 15;
                        });
                      },
                      children: [0, 15, 30, 45].map((minute) {
                        return Center(
                          child: Text(
                            minute.toString().padLeft(2, '0'),
                            style: TextStyle(
                              fontSize: 24, // Apple's wheel text size
                              fontWeight: FontWeight.w400, // Apple's weight
                              color: Theme.of(context).colorScheme.onSurface,
                              fontFeatures: const [FontFeature.tabularFigures()],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Apple-style action buttons
          Container(
            padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      Navigator.of(context).pop();
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      context.tr('new_appointment.cancel'),
                      style: TextStyle(
                        fontSize: 17, // Apple's button text size
                        fontWeight: FontWeight.w400,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _triggerConfirmationHaptic();
                      final selectedDateTime = widget.onTimeSelected(_selectedHour, _selectedMinute);
                      Navigator.of(context).pop(selectedDateTime);
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      context.tr('new_appointment.confirm'),
                      style: const TextStyle(
                        fontSize: 17, // Apple's button text size
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}