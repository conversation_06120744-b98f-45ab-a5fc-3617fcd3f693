import 'package:flutter/material.dart';

import '../../models/client.dart';
import '../../models/pet.dart';
import '../../models/user_role.dart';
import '../../providers/calendar_provider.dart';
import '../../services/appointment/calendar_service.dart';
import '../../services/staff_service.dart';
import '../../utils/debug_logger.dart';
import '../../utils/formatters/phone_number_utils.dart';
import 'appointment_form_constants.dart';
import 'appointment_form_data.dart';

/// Business logic helper for appointment form
class AppointmentFormLogic {
  final CalendarProvider calendarProvider;

  AppointmentFormLogic(this.calendarProvider);

  /// Loads initial data for the form
  Future<void> loadInitialData(AppointmentFormData formData, {String? preselectedStaffId}) async {
    await Future.wait([
      _loadServices(formData, preselectedStaffId: preselectedStaffId),
      _loadClients(formData),
      _loadStaff(formData, preselectedStaffId: preselectedStaffId),
    ]);
  }

  /// Loads available services
  Future<void> _loadServices(AppointmentFormData formData, {String? preselectedStaffId}) async {
    // Load services from backend using CalendarProvider
    await calendarProvider.loadServices();
    formData.availableServices = calendarProvider.getAvailableServiceNames();

    // Feature 3: Auto-select default standard service if available and no service is selected
    if (formData.services.isEmpty && formData.availableServices.isNotEmpty) {
      const defaultServiceName = 'Serviciu Standard';
      if (formData.availableServices.contains(defaultServiceName)) {
        formData.services = [defaultServiceName];
        DebugLogger.logVerbose('✅ Auto-selected default service: $defaultServiceName');
      } else {
        // Fallback: select the first available service
        formData.services = [formData.availableServices.first];
        DebugLogger.logVerbose('✅ Auto-selected first available service: ${formData.availableServices.first}');
      }
    }
  }

  /// Loads available staff members
  Future<void> _loadStaff(AppointmentFormData formData, {String? preselectedStaffId}) async {
    try {
      // Get active staff from the current salon
      final response = await StaffService.getCurrentSalonStaff(activeOnly: true);

      if (response.success && response.data != null) {
        // Filter for staff with groomer roles only
        final groomerStaff = response.data!.activeStaff.where((staff) =>
          staff.groomerRole == GroomerRole.chiefGroomer ||
          staff.groomerRole == GroomerRole.groomer ||
          staff.groomerRole == GroomerRole.assistant
        ).toList();

        formData.availableStaff = groomerStaff;

        // Handle staff preselection or default selection
        if (preselectedStaffId != null) {
          // Find and preselect the specified staff member
          final preselectedStaff = formData.availableStaff
              .where((staff) => staff.id == preselectedStaffId)
              .isNotEmpty
              ? formData.availableStaff
                  .where((staff) => staff.id == preselectedStaffId)
                  .first
              : null;

          if (preselectedStaff != null) {
            formData.updateStaffData(preselectedStaff);
          } else if (formData.availableStaff.isNotEmpty) {
            // Fallback to first staff if preselected not found
            formData.updateStaffData(formData.availableStaff.first);
          }
        } else if (formData.assignedCoworker.isEmpty && formData.availableStaff.isNotEmpty) {
          // Set default staff if none selected and no preselection
          formData.updateStaffData(formData.availableStaff.first);
        }
      } else {
        formData.availableStaff = [];
      }
    } catch (e) {
      formData.availableStaff = [];
    }
  }

  /// Loads available clients
  Future<void> _loadClients(AppointmentFormData formData) async {
    try {
      // Get all clients from HTTP service via CalendarProvider
      final clients = await calendarProvider.calendarService.getAllClients();
      formData.availableClients = clients;
    } catch (e) {
      formData.availableClients = [];
    }

    // Don't auto-select the first client - let user choose
    // Reset client data to empty state
    formData.clientId = '';
    formData.clientName = '';
    formData.clientPhone = '';
    formData.clientPets = [];
  }

  /// Loads pets for a specific client
  Future<void> loadPetsForClient(AppointmentFormData formData, String clientId) async {
    try {
      DebugLogger.logVerbose('🔄 AppointmentFormLogic: Loading pets for client: $clientId');

      // Clear pets first to show loading state
      formData.clientPets = [];

      // Use force refresh to bypass any caching issues
      final pets = await calendarProvider.refreshPetsForClient(clientId);
      formData.clientPets = pets;
      formData.savedClientPets = [...pets];

      DebugLogger.logVerbose('✅ AppointmentFormLogic: Loaded ${pets.length} pets for client $clientId');

      if (pets.isNotEmpty) {
        // Auto-select first pet if available
        formData.updatePetData(pets.first);
        DebugLogger.logVerbose('🐕 AppointmentFormLogic: Auto-selected pet: ${pets.first.name}');
      } else {
        DebugLogger.logVerbose('⚠️ AppointmentFormLogic: No pets found for client $clientId');
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ AppointmentFormLogic: Error loading pets for client $clientId: $e');
      formData.clientPets = [];
    }
  }

  /// Updates end time based on selected services
  Future<void> updateEndTimeBasedOnServices(AppointmentFormData formData) async {
    final serviceDurations = await calendarProvider.getServiceDurations();
    final totalDuration = formData.getTotalDuration(serviceDurations);

    // Calculate exact end time based on services - no rounding
    final newEndTime = formData.startTime.add(Duration(minutes: totalDuration));

    // Ensure the end time doesn't go beyond 23:59
    if (newEndTime.hour >= 24) {
      // If it goes to the next day, cap it at 23:45
      formData.endTime = DateTime(
        formData.startTime.year,
        formData.startTime.month,
        formData.startTime.day,
        23,
        45,
      );
    } else {
      formData.endTime = newEndTime;
    }
  }



  /// Creates appointment from form data using new backend DTO format
  Future<AppointmentCreationResult> createAppointment(AppointmentFormData formData) async {
    try {
      // Get service name to ID mapping from CalendarProvider
      final serviceNameToIdMap = calendarProvider.getServiceNameToIdMap();

      // Use the new ScheduleAppointmentRequest DTO format with real service IDs
      final scheduleRequest = formData.toScheduleAppointmentRequest(serviceNameToIdMap);
      return await calendarProvider.addAppointmentFromFormData(scheduleRequest);
    } catch (e) {
      return AppointmentCreationResult.failure('Error creating appointment: $e');
    }
  }

  /// Validates form data using enhanced backend validation
  String? validateForm(AppointmentFormData formData, BuildContext context) {
    // Use the enhanced backend validation
    final backendValidation = formData.validateForBackend(context);
    if (backendValidation != null) {
      return backendValidation;
    }

    // Phone validation - accept international phone numbers
    if (formData.clientPhone.trim().isEmpty) {
      return 'Vă rugăm să introduceți un număr de telefon';
    }

    // Basic validation: phone must start with + and have at least 8 characters (+X XXXXXX)
    final cleanPhone = formData.clientPhone.trim();
    if (!cleanPhone.startsWith('+') || cleanPhone.length < 8) {
      return 'Vă rugăm să introduceți un număr de telefon valid (format internațional: +XX...)';
    }

    // Working hours validation
    final settings = calendarProvider.workingHoursSettings;
    if (settings != null) {
      final withinHours = settings.isTimeRangeWithinWorkingHours(
        formData.startTime,
        formData.endTime,
      );
      if (!withinHours) {
        return 'Programarea trebuie să fie în intervalul orar de lucru';
      }
    }

    return null;
  }



  /// Handles client type change
  void handleClientTypeChange(AppointmentFormData formData, bool isExisting) {
    if (isExisting) {
      formData.isExistingClient = true;

      // Restore previous selection if available
      if (formData.savedClientId != null && formData.savedClientId!.isNotEmpty) {
        formData.clientId = formData.savedClientId!;
        formData.clientName = formData.savedClientName ?? '';
        formData.clientPhone = formData.savedClientPhone ?? '';
        formData.clientPets = [...formData.savedClientPets];
      } else {
        // Reset to empty selection - let user choose
        formData.clientId = '';
        formData.clientName = '';
        formData.clientPhone = '';
        formData.clientPets = [];
      }
      formData.petId = '';
      formData.petName = '';
      formData.petSpecies = 'dog';
    } else {
      // Save current selection before switching to new client mode
      if (!formData.isNewClient && formData.clientId.isNotEmpty) {
        formData.savedClientId = formData.clientId;
        formData.savedClientName = formData.clientName;
        formData.savedClientPhone = formData.clientPhone;
        formData.savedClientPets = [...formData.clientPets];
      }

      formData.isExistingClient = false;
      formData.resetToNewClient();
    }
  }

  /// Handles client selection
  Future<void> handleClientSelection(AppointmentFormData formData, Client client) async {
    DebugLogger.logVerbose('🔄 AppointmentFormLogic: Handling client selection: ${client.name} (${client.id})');

    // Update client data (this also clears pets)
    formData.updateClientData(client);

    // Load pets for the selected client
    await loadPetsForClient(formData, client.id);

    // Auto-populate services from most recent appointment
    await autoPopulateServicesFromRecentAppointment(formData, client.id);

    DebugLogger.logVerbose('✅ AppointmentFormLogic: Client selection completed');
  }

  /// Auto-populates services based on client's most recent appointment
  Future<void> autoPopulateServicesFromRecentAppointment(AppointmentFormData formData, String clientId) async {
    try {
      DebugLogger.logVerbose('🔄 AppointmentFormLogic: Auto-populating services for client: $clientId');

      // Defensive: ensure available services are loaded so we can validate names and compute durations
      if (formData.availableServices.isEmpty) {
        await calendarProvider.loadServices();
        formData.availableServices = calendarProvider.getAvailableServiceNames();
        DebugLogger.logVerbose('🔁 AppointmentFormLogic: Loaded available services before auto-populating (count: ${formData.availableServices.length})');
      }

      // Fetch the most recent appointment for this client
      final recentAppointment = await calendarProvider.calendarService.getMostRecentClientAppointment(clientId);

      if (recentAppointment != null && recentAppointment.services.isNotEmpty) {
        DebugLogger.logVerbose('🔍 AppointmentFormLogic: Found recent appointment ${recentAppointment.id} from ${recentAppointment.startTime}');
        DebugLogger.logVerbose('🔍 AppointmentFormLogic: Services: ${recentAppointment.services}');
        DebugLogger.logVerbose('🔍 AppointmentFormLogic: Custom services: ${recentAppointment.customServices}');

        // Clear existing services and custom services first
        formData.services = [];
        formData.customServices = {};

        // Get available services to validate against
        final availableServices = formData.availableServices;

        // Add services from the recent appointment that are still available
        for (final serviceName in recentAppointment.services) {
          if (availableServices.contains(serviceName)) {
            formData.addService(serviceName);
            DebugLogger.logVerbose('✅ AppointmentFormLogic: Auto-populated service: $serviceName');

            // Check if this service had custom pricing in the previous appointment
            // Note: customServices might be keyed by service ID instead of service name
            String? customServiceKey;
            if (recentAppointment.customServices != null) {
              // First try to find by service name
              if (recentAppointment.customServices!.containsKey(serviceName)) {
                customServiceKey = serviceName;
              } else {
                // If not found by name, try to find by service ID
                // Look through the services array to find the ID for this service name
                for (final serviceEntry in recentAppointment.customServices!.entries) {
                  final customServiceData = serviceEntry.value;
                  if (customServiceData['originalServiceName'] == serviceName) {
                    customServiceKey = serviceEntry.key;
                    break;
                  }
                }
              }
            }

            if (customServiceKey != null && recentAppointment.customServices != null) {
              final customServiceData = recentAppointment.customServices![customServiceKey]!;

              // Create CustomService object from the previous appointment data
              final customService = CustomService(
                originalServiceName: serviceName,
                customName: customServiceData['customName'] ?? serviceName,
                customPrice: (customServiceData['customPrice'] as num?)?.toDouble() ?? 0.0,
                customDescription: customServiceData['customDescription'] ?? '',
              );

              formData.setCustomService(serviceName, customService);
              DebugLogger.logVerbose('💰 AppointmentFormLogic: Auto-populated custom pricing for $serviceName: ${customService.customPrice} RON (key: $customServiceKey)');
            } else {
              DebugLogger.logVerbose('ℹ️ AppointmentFormLogic: No custom pricing found for service: $serviceName');
            }
          } else {
            DebugLogger.logVerbose('⚠️ AppointmentFormLogic: Service not available: $serviceName');
          }
        }

        // If no services from recent appointment are available, try the primary service
        if (formData.services.isEmpty && recentAppointment.service.isNotEmpty) {
          if (availableServices.contains(recentAppointment.service)) {
            formData.addService(recentAppointment.service);
            DebugLogger.logVerbose('✅ AppointmentFormLogic: Auto-populated primary service: ${recentAppointment.service}');

            // Check for custom pricing on primary service too
            String? primaryCustomServiceKey;
            if (recentAppointment.customServices != null) {
              // First try to find by service name
              if (recentAppointment.customServices!.containsKey(recentAppointment.service)) {
                primaryCustomServiceKey = recentAppointment.service;
              } else {
                // If not found by name, try to find by service ID
                for (final serviceEntry in recentAppointment.customServices!.entries) {
                  final customServiceData = serviceEntry.value;
                  if (customServiceData['originalServiceName'] == recentAppointment.service) {
                    primaryCustomServiceKey = serviceEntry.key;
                    break;
                  }
                }
              }
            }

            if (primaryCustomServiceKey != null && recentAppointment.customServices != null) {
              final customServiceData = recentAppointment.customServices![primaryCustomServiceKey]!;

              final customService = CustomService(
                originalServiceName: recentAppointment.service,
                customName: customServiceData['customName'] ?? recentAppointment.service,
                customPrice: (customServiceData['customPrice'] as num?)?.toDouble() ?? 0.0,
                customDescription: customServiceData['customDescription'] ?? '',
              );

              formData.setCustomService(recentAppointment.service, customService);
              DebugLogger.logVerbose('💰 AppointmentFormLogic: Auto-populated custom pricing for primary service ${recentAppointment.service}: ${customService.customPrice} RON (key: $primaryCustomServiceKey)');
            }
          }
        }

        if (formData.services.isNotEmpty) {
          final customServicesCount = formData.customServices.length;
          DebugLogger.logVerbose('✅ AppointmentFormLogic: Successfully auto-populated ${formData.services.length} service(s)${customServicesCount > 0 ? ' with $customServicesCount custom pricing(s)' : ''}');
        } else {
          DebugLogger.logVerbose('ℹ️ AppointmentFormLogic: No services from recent appointment are currently available');
        }

        // Recalculate end time after services were changed
        if (formData.services.isNotEmpty) {
          await updateEndTimeBasedOnServices(formData);
        }
      } else {
        DebugLogger.logVerbose('ℹ️ AppointmentFormLogic: No recent appointment found for client $clientId or no services in recent appointment');
      }
    } catch (e) {
      DebugLogger.logError('❌ AppointmentFormLogic: Error auto-populating services: $e');
      // Don't throw error - auto-population is a convenience feature, not critical
    }
  }

  /// Handles adding new pet
  void handleAddNewPet(AppointmentFormData formData) {
    formData.resetToNewPet();
  }

  /// Toggles between new pet mode and existing pet selection
  void handleToggleNewPet(AppointmentFormData formData) {
    if (formData.isNewPet) {
      if (formData.clientPets.isNotEmpty) {
        formData.updatePetData(formData.clientPets.first);
      } else {
        formData.petId = '';
        formData.petName = '';
        formData.petSpecies = 'dog';
        formData.petBreed = '';
        formData.petSize = 'M';
      }
    } else {
      formData.resetToNewPet();
    }
  }

  /// Handles pet selection
  void handlePetSelection(AppointmentFormData formData, Pet pet) {
    formData.updatePetData(pet);
  }

  /// Handles breed change for new pet
  Future<void> handlePetBreedChange(AppointmentFormData formData, String breed) async {
    final oldSize = formData.petSize;
    formData.petBreed = breed;
    
    try {
      // Check if breed has size mapping and update accordingly
      final hasMapping = await AppointmentFormConstants.hasBreedSizeMapping(breed);
      if (hasMapping) {
        formData.petSpecies = await AppointmentFormConstants.getBreedSpecies(breed);
        formData.petSize = await AppointmentFormConstants.getBreedSize(breed);
      }
    } catch (e) {
      DebugLogger.logError('Error updating breed data for $breed: $e');
      // Keep existing values on error
    }

    // Debug logging for breed-to-size mapping
    DebugLogger.logVerbose('🐕 Breed changed: $breed');
    DebugLogger.logVerbose('📏 Size mapping: $oldSize → ${formData.petSize}');

    if (oldSize != formData.petSize) {
      DebugLogger.logVerbose('💰 Size change detected - prices will be recalculated');
    }
  }

  /// Handles adding a service
  Future<void> handleAddService(AppointmentFormData formData, String service) async {
    formData.addService(service);
    await updateEndTimeBasedOnServices(formData);
  }

  /// Handles removing a service
  Future<void> handleRemoveService(AppointmentFormData formData, String service) async {
    formData.removeService(service);
    await updateEndTimeBasedOnServices(formData);
  }

  /// Handles start time change
  Future<void> handleStartTimeChange(AppointmentFormData formData, DateTime startTime) async {
    formData.startTime = startTime;
    await updateEndTimeBasedOnServices(formData);
  }

  /// Handles appointment date change
  Future<void> handleAppointmentDateChange(AppointmentFormData formData, DateTime newDate) async {
    formData.appointmentDate = newDate;

    // Update start and end times to the new date
    formData.startTime = DateTime(
      newDate.year,
      newDate.month,
      newDate.day,
      formData.startTime.hour,
      formData.startTime.minute,
    );

    await updateEndTimeBasedOnServices(formData);
  }

  /// Handles staff assignment change
  void handleStaffChange(AppointmentFormData formData, String staffId) {
    final staff = formData.availableStaff
        .where((s) => s.id == staffId)
        .isNotEmpty
        ? formData.availableStaff
            .where((s) => s.id == staffId)
            .first
        : null;

    if (staff != null) {
      formData.updateStaffData(staff);
    }
  }

  /// Handles notes visibility change
  void handleNotesVisibilityChange(AppointmentFormData formData, bool showNotes) {
    formData.showNotes = showNotes;
    if (!showNotes) {
      formData.notes = '';
    }
  }

  /// Handles repetition visibility change
  void handleRepetitionVisibilityChange(AppointmentFormData formData, bool showRepetition) {
    formData.showRepetition = showRepetition;
    if (!showRepetition) {
      formData.repetitionFrequency = 'none';
    }
  }

  /// Handles repetition frequency change
  void handleRepetitionFrequencyChange(AppointmentFormData formData, String frequency) {
    formData.repetitionFrequency = frequency;
  }
}
