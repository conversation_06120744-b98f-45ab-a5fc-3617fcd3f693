import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/service.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/role_provider.dart';
import '../../utils/debug_logger.dart';
import 'appointment_form_data.dart';
import '../../screens/services/service_form_screen.dart';
import '../permission_guard.dart';
import '../../services/ui_notification_service.dart';

class MultipleServicesWidget extends StatefulWidget {
  final AppointmentFormData formData;
  final Future<void> Function(String) onAddService;
  final Future<void> Function(String) onRemoveService;

  const MultipleServicesWidget({
    super.key,
    required this.formData,
    required this.onAddService,
    required this.onRemoveService,
  });

  @override
  State<MultipleServicesWidget> createState() => _MultipleServicesWidgetState();
}

class _MultipleServicesWidgetState extends State<MultipleServicesWidget> {

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        // Show selected services in bordered containers
        if (widget.formData.services.isNotEmpty) ...[
          ...widget.formData.services.map((service) => _buildServiceChip(service)),
          const SizedBox(height: 8),
        ],
        // Always show the "Add Service" button
        _buildAddServiceButton(),
      ],
    );
  }

  Widget _buildAddServiceButton() {
    // Use normal styling regardless of whether services are selected
    // This fixes the red styling issue when no services are selected
    final hasServices = widget.formData.services.isNotEmpty;

    return InkWell(
      onTap: () => _showAddServiceDialog(context),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).colorScheme.outline),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('service_selection.service_label'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    hasServices
                        ? context.tr('service_selection.add_another_service')
                        : context.tr('service_selection.select_service_required'),
                    style:  TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
             Icon(Icons.add, color: Theme.of(context).primaryColor),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceChip(String service) {
    return Consumer<CalendarProvider>(
      builder: (context, calendarProvider, child) {
        final serviceDetails = calendarProvider.getServiceDetails();
        final details = serviceDetails[service];

        // Check if this service has custom modifications
        final customService = widget.formData.getCustomService(service);
        final isCustomized = customService != null;

        // Use custom values if available, otherwise use original service details
        final duration = details?['duration'] ?? 60; // Always use original duration
        final displayName = isCustomized ? customService.customName : service;

        // Create Service object to use new pricing methods
        String priceText;
        bool hasSizeBasedPricing = false;
        try {
          if (isCustomized) {
            // Use custom price
            priceText = '${customService.customPrice.toStringAsFixed(2)} RON';
          } else if (details != null) {
            final serviceObj = Service.fromJson(details);
            priceText = serviceObj.getFormattedPriceForSize(widget.formData.petSize);
            hasSizeBasedPricing = serviceObj.sizePrices != null;

            // Debug logging for price calculation
            DebugLogger.logVerbose('💰 Service "$service" price for size ${widget.formData.petSize}: $priceText');
          } else {
            priceText = '50.00 RON';
          }
        } catch (e) {
          // Fallback to old logic
          final price = details?['price'] ?? 50.0;
          priceText = '${price.toStringAsFixed(0)} RON';
          DebugLogger.logVerbose('⚠️ Fallback pricing for service "$service": $priceText');
        }

        // Get service color if available (default to grey)
        final serviceColor = details?['color'] as String? ?? '#9E9E9E';

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(
              color: _parseColor(serviceColor).withOpacity(0.4),
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(8),
            color: _parseColor(serviceColor).withOpacity(0.08),
          ),
          child: Row(
            children: [
              // Color indicator
              Container(
                width: 4,
                height: 40,
                decoration: BoxDecoration(
                  color: _parseColor(serviceColor),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Flexible(
                          child: Text(
                            displayName,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        if (isCustomized) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Text(
                              'CUSTOM',
                              style: TextStyle(
                                fontSize: 9,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ] else if (hasSizeBasedPricing) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              widget.formData.petSize,
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${duration}min',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.attach_money,
                          size: 14,
                          color: Theme.of(context).primaryColor,
                        ),
                        Text(
                          priceText,
                          style:  TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              // Edit button
              InkWell(
                onTap: () => _showEditServiceDialog(context, service),
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    Icons.edit,
                    size: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
              const SizedBox(width: 4),
              // Remove button
              InkWell(
                onTap: () async => await widget.onRemoveService(service),
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.close,
                    size: 16,
                    color: Colors.red,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showAddServiceDialog(BuildContext context) {
    final availableServices = widget.formData.availableServices
        .where((service) => !widget.formData.services.contains(service))
        .toList();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Consumer2<CalendarProvider, RoleProvider>(
          builder: (context, calendarProvider, roleProvider, child) {
            final serviceDetails = calendarProvider.getServiceDetails();

            // Group available services by color
            final groupedServices = _groupServicesByColor(availableServices, serviceDetails);

            // Only add 1 to itemCount if user has permission to create services
            final canCreateServices = roleProvider.hasManagementAccess || roleProvider.canManageServices();
            final itemCount = groupedServices.length + (canCreateServices ? 1 : 0);

            return AlertDialog(
              title: Text(context.tr('service_selection.select_service_title')),
              content: SizedBox(
                width: double.maxFinite,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: itemCount,
                  itemBuilder: (context, index) {
                    if (index == groupedServices.length && canCreateServices) {
                      return ListTile(
                        leading:  Icon(Icons.add, color: Theme.of(context).primaryColor),
                        title: Text(context.tr('service_selection.create_new_service')),
                        onTap: () async {
                          Navigator.of(context).pop();
                          await _showCreateServiceScreen(context);
                        },
                      );
                    }
                    final service = groupedServices[index];
                    final details = serviceDetails[service];
                    final duration = details?['duration'] ?? 60;

                    // Create Service object to use new pricing methods
                    String priceText;
                    bool hasSizeBasedPricing = false;
                    try {
                      if (details != null) {
                        final serviceObj = Service.fromJson(details);
                        priceText = serviceObj.getFormattedPriceForSize(widget.formData.petSize);
                        hasSizeBasedPricing = serviceObj.sizePrices != null;

                        // Debug logging for service selection
                        DebugLogger.logVerbose('🛍️ Service selection "$service" price for size ${widget.formData.petSize}: $priceText');
                      } else {
                        priceText = '50.00 RON';
                      }
                    } catch (e) {
                      // Fallback to old logic
                      final price = details?['price'] ?? 50.0;
                      priceText = '${price.toStringAsFixed(0)} RON';
                      DebugLogger.logVerbose('⚠️ Fallback pricing for service selection "$service": $priceText');
                    }

                    // Get service color if available (default to grey)
                    final serviceColor = details?['color'] as String? ?? '#9E9E9E';

                    // Check if this is the first service or if the color changed from previous
                    String? previousServiceColor;
                    if (index > 0) {
                      final previousDetails = serviceDetails[groupedServices[index - 1]];
                      previousServiceColor = previousDetails?['color'] as String? ?? '#9E9E9E';
                    }
                    final bool colorChanged = previousServiceColor != null && previousServiceColor != serviceColor;

                    return Column(
                      children: [
                        // Add divider when color changes
                        if (colorChanged)
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Divider(thickness: 1, color: Colors.grey[300]),
                          ),

                        ListTile(
                          leading: Container(
                            width: 4,
                            height: 40,
                            decoration: BoxDecoration(
                              color: _parseColor(serviceColor),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                      title: Row(
                        children: [
                          Expanded(child: Text(service)),
                          if (hasSizeBasedPricing) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Theme.of(context).primaryColor, width: 0.5),
                              ),
                              child: Text(
                                context.tr('service_selection.size_label', params: {'size': widget.formData.petSize}),
                                style:  TextStyle(
                                  fontSize: 10,
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${duration}min',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            priceText,
                            style:  TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                          trailing:  Icon(Icons.add, color: Theme.of(context).primaryColor),
                          onTap: () async {
                            Navigator.of(context).pop();
                            await widget.onAddService(service);
                          },
                        ),
                      ],
                    );
                  },
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(context.tr('service_selection.cancel')),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _showCreateServiceScreen(BuildContext context) async {
    // Store the context and provider reference before the async operation
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);

    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => ServiceFormScreen(
          showCreateAnotherOption: false, // Disable "Create Another" workflow
          onServiceSaved: (service) async {
            // Handle the service creation without using the disposed context
            await _handleNewServiceCreated(service, calendarProvider);
          },
        ),
      ),
    );
  }

  Future<void> _handleNewServiceCreated(Service service, CalendarProvider calendarProvider) async {
    try {
      // Reload services from the backend
      await calendarProvider.loadServices();

      // Update the available services list in the form data
      if (mounted) {
        setState(() {
          widget.formData.availableServices = calendarProvider.getAvailableServiceNames();
        });
      }

      // Add the new service to the appointment
      await widget.onAddService(service.name);

      // Success feedback handled by ServiceFormDialog
    } catch (e) {
      // Handle errors
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('service_selection.error_title'),
          message: context.tr('service_selection.add_service_error', params: {'error': e.toString()}),
        );
      }
    }
  }

  void _showEditServiceDialog(BuildContext context, String serviceName) {
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
    final serviceDetails = calendarProvider.getServiceDetails();
    final originalDetails = serviceDetails[serviceName];

    // Get current custom service or create from original
    final existingCustom = widget.formData.getCustomService(serviceName);

    // Initialize with existing custom values or original service values
    String customName = existingCustom?.customName ?? serviceName;
    double customPrice = existingCustom?.customPrice ?? (originalDetails?['price']?.toDouble() ?? 50.0);
    String customDescription = existingCustom?.customDescription ?? '';

    // Controllers for the form
    final nameController = TextEditingController(text: customName);
    final priceController = TextEditingController(text: customPrice.toStringAsFixed(2));
    final descriptionController = TextEditingController(text: customDescription);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.tr('service_selection.edit_service_title', params: {'name': serviceName})),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: InputDecoration(
                    labelText: context.tr('service_selection.custom_service_name'),
                    hintText: context.tr('service_selection.custom_service_name_placeholder'),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: priceController,
                  decoration: InputDecoration(
                    labelText: context.tr('service_selection.custom_price_label'),
                    hintText: context.tr('service_selection.custom_price_placeholder'),
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: InputDecoration(
                    labelText: context.tr('service_selection.custom_description_label'),
                    hintText: context.tr('service_selection.custom_description_placeholder'),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.info_outline, color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          context.tr('service_selection.custom_service_info'),
                          style: const TextStyle(fontSize: 12, color: Colors.orange),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            if (existingCustom != null)
              TextButton(
                onPressed: () {
                  // Reset to original service
                  setState(() {
                    widget.formData.customServices = Map.from(widget.formData.customServices)..remove(serviceName);
                  });
                  Navigator.of(context).pop();
                },
                child: Text(context.tr('service_selection.reset')),
              ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.tr('service_selection.cancel')),
            ),
            ElevatedButton(
              onPressed: () {
                // Validate inputs
                final name = nameController.text.trim();
                final priceText = priceController.text.trim();

                if (name.isEmpty) {
                  UINotificationService.showError(
                    context: context,
                    title: context.tr('service_selection.error_title'),
                    message: context.tr('service_selection.name_required_error'),
                  );
                  return;
                }

                final price = double.tryParse(priceText);
                if (price == null || price < 0) {
                  UINotificationService.showError(
                    context: context,
                    title: context.tr('service_selection.error_title'),
                    message: context.tr('service_selection.price_invalid_error'),
                  );
                  return;
                }

                // Create custom service
                final customService = CustomService(
                  originalServiceName: serviceName,
                  customName: name,
                  customPrice: price,
                  customDescription: descriptionController.text.trim(),
                );

                // Save custom service
                setState(() {
                  widget.formData.setCustomService(serviceName, customService);
                });

                Navigator.of(context).pop();

                UINotificationService.showSuccess(
                  context: context,
                  title: context.tr('service_selection.success_title'),
                  message: context.tr('service_selection.service_customized_success'),
                );
              },
              child: Text(context.tr('service_selection.save')),
            ),
          ],
        );
      },
    );
  }

  /// Parse hex color string to Color object
  Color _parseColor(String hexColor) {
    try {
      // Remove # if present
      final hex = hexColor.replaceAll('#', '');
      // Add FF for full opacity if not present
      final colorValue = int.parse('FF$hex', radix: 16);
      return Color(colorValue);
    } catch (e) {
      // Return default color if parsing fails
      return Theme.of(context).primaryColor;
    }
  }

  /// Group services by color for better visual organization
  List<String> _groupServicesByColor(List<String> servicesList, Map<String, Map<String, dynamic>> serviceDetails) {
    // Create a map to group services by color
    final Map<String, List<String>> colorGroups = {};

    for (final serviceName in servicesList) {
      final details = serviceDetails[serviceName];
      final color = details?['color'] as String? ?? '#9E9E9E'; // Default to grey if no color

      if (!colorGroups.containsKey(color)) {
        colorGroups[color] = [];
      }
      colorGroups[color]!.add(serviceName);
    }

    // Sort each color group alphabetically by service name
    for (final group in colorGroups.values) {
      group.sort((a, b) => a.compareTo(b));
    }

    // Flatten the groups back into a single list
    // Sort groups by color to maintain consistent order
    final sortedColors = colorGroups.keys.toList()..sort();
    final List<String> groupedServices = [];

    for (final color in sortedColors) {
      groupedServices.addAll(colorGroups[color]!);
    }

    return groupedServices;
  }
}
