import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../models/report_data.dart';
import '../../l10n/app_localizations.dart';

/// Widget for selecting date ranges in reports
class DateRangeSelector extends StatefulWidget {
  final DateRangePreset selectedPreset;
  final DateRange? customRange;
  final Function(DateRangePreset preset, DateRange range) onRangeChanged;

  const DateRangeSelector({
    super.key,
    required this.selectedPreset,
    this.customRange,
    required this.onRangeChanged,
  });

  @override
  State<DateRangeSelector> createState() => _DateRangeSelectorState();
}

class _DateRangeSelectorState extends State<DateRangeSelector> {
  late DateRangePreset _selectedPreset;
  late DateRange _currentRange;

  @override
  void initState() {
    super.initState();
    _selectedPreset = widget.selectedPreset;
    _currentRange = widget.customRange ?? _selectedPreset.getDateRange();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                 Icon(
                  Icons.date_range,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                 Text(
                  context.tr('date_range_selector.time_interval'),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const Spacer(),
                if (_selectedPreset == DateRangePreset.custom)
                  TextButton.icon(
                    onPressed: _showCustomDatePicker,
                    icon: const Icon(Icons.edit_calendar, size: 16),
                    label: Text(context.tr('common.edit')),
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).primaryColor,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Preset buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: DateRangePreset.values.map((preset) {
                final isSelected = _selectedPreset == preset;
                return FilterChip(
                  label: Text(context.tr(preset.translationKey)),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      _selectPreset(preset);
                    }
                  },
                  selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).primaryColor,
                  labelStyle: TextStyle(
                    color: isSelected ? Theme.of(context).primaryColor : Colors.grey[700],
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 12),
            
            // Current range display
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.grey[800] 
                    : Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).brightness == Brightness.dark 
                      ? Colors.grey[600]! 
                      : Colors.grey[300]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: Theme.of(context).brightness == Brightness.dark 
                        ? Colors.grey[400] 
                        : Colors.grey[600],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _formatDateRange(_currentRange),
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).brightness == Brightness.dark 
                            ? Colors.grey[300] 
                            : Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Text(
                    context.tr('date_range_selector.days_count').replaceAll('{count}', '${_currentRange.daysDifference + 1}'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).brightness == Brightness.dark 
                          ? Colors.grey[400] 
                          : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _selectPreset(DateRangePreset preset) {
    setState(() {
      _selectedPreset = preset;
      if (preset != DateRangePreset.custom) {
        _currentRange = preset.getDateRange();
        widget.onRangeChanged(preset, _currentRange);
      } else {
        _showCustomDatePicker();
      }
    });
  }

  void _showCustomDatePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 2)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: _currentRange.start,
        end: _currentRange.end,
      ),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final newRange = DateRange(
        start: picked.start,
        end: picked.end,
      );
      
      setState(() {
        _selectedPreset = DateRangePreset.custom;
        _currentRange = newRange;
      });
      
      widget.onRangeChanged(DateRangePreset.custom, newRange);
    }
  }

  String _formatDateRange(DateRange range) {
    final locale = Localizations.localeOf(context).languageCode;
    final formatter = DateFormat('dd MMM yyyy', locale);
    return '${formatter.format(range.start)} - ${formatter.format(range.end)}';
  }
}

/// Quick date range selector for compact layouts
class QuickDateRangeSelector extends StatelessWidget {
  final DateRangePreset selectedPreset;
  final Function(DateRangePreset preset) onPresetChanged;

  const QuickDateRangeSelector({
    super.key,
    required this.selectedPreset,
    required this.onPresetChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      width: double.infinity,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: DateRangePreset.values.length - 1, // Exclude custom
        itemBuilder: (context, index) {
          final preset = DateRangePreset.values[index];
          final isSelected = selectedPreset == preset;
          
          return Padding(
            padding: EdgeInsets.only(
              left: index == 0 ? 16 : 4,
              right: index == DateRangePreset.values.length - 2 ? 16 : 4,
            ),
            child: FilterChip(
              label: Text(context.tr(preset.translationKey)),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  onPresetChanged(preset);
                }
              },
              selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
              checkmarkColor: Theme.of(context).primaryColor,
              labelStyle: TextStyle(
                color: isSelected ? Theme.of(context).primaryColor : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 12,
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Date range info widget for displaying current selection
class DateRangeInfo extends StatelessWidget {
  final DateRange range;
  final String? additionalInfo;

  const DateRangeInfo({
    super.key,
    required this.range,
    this.additionalInfo,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.date_range,
            size: 16,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 6),
          Text(
            _formatDateRange(range),
            style:  TextStyle(
              fontSize: 12,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (additionalInfo != null) ...[
            const SizedBox(width: 6),
            Text(
              '• $additionalInfo',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).primaryColor.withOpacity(0.7),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatDateRange(DateRange range) {
    final formatter = DateFormat('dd MMM');
    return '${formatter.format(range.start)} - ${formatter.format(range.end)}';
  }
}
