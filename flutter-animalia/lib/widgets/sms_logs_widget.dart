import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/sms_log.dart';
import '../services/sms_logs_service.dart';
import '../utils/debug_logger.dart';
import '../l10n/app_localizations.dart';

class SmsLogsWidget extends StatefulWidget {
  const SmsLogsWidget({super.key});

  @override
  State<SmsLogsWidget> createState() => _SmsLogsWidgetState();
}

class _SmsLogsWidgetState extends State<SmsLogsWidget> {
  final ScrollController _scrollController = ScrollController();
  final List<SmsLog> _smsLogs = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 0;
  final int _pageSize = 10;
  String? _error;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadSmsLogs();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMore) {
        _loadMoreSmsLogs();
      }
    }
  }

  Future<void> _loadSmsLogs() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _currentPage = 0;
      _smsLogs.clear();
      _hasMore = true;
    });

    try {
      final response = await SmsLogsService.getSmsLogs(
        page: _currentPage,
        size: _pageSize,
      );

      if (response.success && response.data != null) {
        DebugLogger.logVerbose('SMS Logs loaded: ${response.data!.content.length} items, total: ${response.data!.totalElements}');
        setState(() {
          _smsLogs.addAll(response.data!.content);
          _hasMore = !response.data!.last;
          _currentPage++;
        });
      } else {
        DebugLogger.logVerbose('SMS Logs error: ${response.error}');
        setState(() {
          _error = response.error ?? 'Eroare la încărcarea SMS-urilor';
        });
      }
    } catch (e) {
      DebugLogger.logVerbose('SMS Logs exception: $e');
      setState(() {
        _error = 'Eroare la încărcarea SMS-urilor: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreSmsLogs() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await SmsLogsService.getSmsLogs(
        page: _currentPage,
        size: _pageSize,
      );

      if (response.success && response.data != null) {
        setState(() {
          _smsLogs.addAll(response.data!.content);
          _hasMore = !response.data!.last;
          _currentPage++;
        });
      }
    } catch (e) {
      // Silently handle pagination errors
      DebugLogger.logVerbose('Error loading more SMS logs: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    context.tr('sms_reminders_sms_history_title'),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadSmsLogs,
                  tooltip: 'Reîmprospătează',
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (_error != null)
              _buildErrorWidget()
            else if (_smsLogs.isEmpty && !_isLoading)
              _buildEmptyWidget()
            else
              _buildSmsLogsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error, color: Colors.red),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _error!,
              style: TextStyle(color: Colors.red[700]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.sms_outlined,
            size: 48,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            context.tr('sms_reminders_sms_history_empty_title'),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.tr('sms_reminders_sms_history_empty_subtitle'),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmsLogsList() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 400),
      child: ListView.builder(
        controller: _scrollController,
        shrinkWrap: true,
        itemCount: _smsLogs.length + (_hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _smsLogs.length) {
            return _buildLoadingIndicator();
          }
          
          final smsLog = _smsLogs[index];
          return _buildSmsLogItem(smsLog);
        },
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(),
    );
  }

  Widget _buildSmsLogItem(SmsLog smsLog) {
    final dateFormat = DateFormat('dd.MM.yyyy HH:mm');

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: smsLog.getTypeColor().withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: smsLog.getTypeColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      smsLog.getTypeIcon(),
                      size: 14,
                      color: smsLog.getTypeColor(),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      smsLog.messageType.displayName,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: smsLog.getTypeColor(),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              // WhatsApp badge if it's a WhatsApp message
              if (smsLog.isWhatsApp)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF25D366).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.chat_bubble,
                        size: 14,
                        color: const Color(0xFF25D366),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'WhatsApp',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF25D366),
                        ),
                      ),
                    ],
                  ),
                ),
              if (smsLog.isWhatsApp) const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                    width: 0.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.sms,
                      size: 12,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      smsLog.smsUnitsDisplay,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Text(
                dateFormat.format(smsLog.sentAt),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Row(
            children: [
              Icon(
                Icons.person,
                size: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  smsLog.displayName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Icon(
                Icons.phone,
                size: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              const SizedBox(width: 4),
              Text(
                smsLog.formattedPhoneNumber,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  smsLog.displayMessageContent,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      smsLog.characterCountDisplay,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontSize: 10,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                    if (smsLog.smsUnits > 1)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: Colors.orange.withOpacity(0.3),
                            width: 0.5,
                          ),
                        ),
                        child: Text(
                          'Mesaj lung',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontSize: 9,
                            fontWeight: FontWeight.w500,
                            color: Colors.orange[700],
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
