import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/user_role.dart';
import '../../providers/role_provider.dart';
import '../../services/staff_service.dart';
import '../../utils/debug_logger.dart';

class EditStaffDialog extends StatefulWidget {
  final StaffResponse staff;
  final Function(StaffResponse)? onStaffUpdated;

  const EditStaffDialog({
    super.key,
    required this.staff,
    this.onStaffUpdated,
  });

  @override
  State<EditStaffDialog> createState() => _EditStaffDialogState();
}

class _EditStaffDialogState extends State<EditStaffDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nicknameController = TextEditingController();
  final _notesController = TextEditingController();

  GroomerRole _selectedRole = GroomerRole.groomer;
  ClientDataPermission _selectedPermission = ClientDataPermission.fullAccess;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    _nicknameController.text = widget.staff.nickname ?? '';
    _notesController.text = widget.staff.notes ?? '';
    _selectedRole = widget.staff.groomerRole;
    _selectedPermission = widget.staff.clientDataPermission;
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStaffInfo(),
                      const SizedBox(height: 24),
                      if (widget.staff.groomerRole == GroomerRole.chiefGroomer) ...[
                        _buildChiefGroomerRestriction(),
                        const SizedBox(height: 24),
                      ],
                      _buildNicknameSection(),
                      const SizedBox(height: 24),
                      _buildRoleSection(),
                      const SizedBox(height: 24),
                      if (widget.staff.phone != null) ...[
                        _buildPermissionsSection(),
                        const SizedBox(height: 24),
                      ],
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration:  BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.edit,
            color: Colors.white,
            size: 28,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('edit_staff_dialog.title'),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.staff.name,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildStaffInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
           Text(
            context.tr('edit_staff_dialog.member_info_title'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
               Icon(Icons.person, color: Theme.of(context).primaryColor, size: 20),
              const SizedBox(width: 8),
              Text(
                '${context.tr('edit_staff_dialog.name_label')}: ${widget.staff.name}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
          if (widget.staff.phone != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                 Icon(Icons.phone, color: Theme.of(context).primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  '${context.tr('edit_staff_dialog.phone_label')}: ${widget.staff.phone}',
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ],
          if (widget.staff.email != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                 Icon(Icons.email, color: Theme.of(context).primaryColor, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${context.tr('edit_staff_dialog.email_label')}: ${widget.staff.email}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChiefGroomerRestriction() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.amber.shade700,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('edit_staff_dialog.chief_groomer_restriction_title'),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.amber.shade800,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  context.tr('edit_staff_dialog.chief_groomer_restriction_message'),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.amber.shade700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNicknameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
         Text(
          context.tr('edit_staff_dialog.display_name_title'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _nicknameController,
          decoration:  InputDecoration(
            hintText: context.tr('edit_staff_dialog.display_name_placeholder'),
            prefixIcon: Icon(Icons.badge, color: Theme.of(context).primaryColor),
            border: OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Theme.of(context).primaryColor),
            ),
            helperText: context.tr('edit_staff_dialog.display_name_helper'),
          ),
          validator: (value) {
            // Nickname is optional, but if provided, should be reasonable length
            if (value != null && value.trim().isNotEmpty) {
              if (value.trim().length < 2) {
                return context.tr('edit_staff_dialog.display_name_min_error');
              }
              if (value.trim().length > 50) {
                return context.tr('edit_staff_dialog.display_name_max_error');
              }
            }
            return null;
          },
          textCapitalization: TextCapitalization.words,
        ),
        const SizedBox(height: 8),
        Text(
          context.tr('edit_staff_dialog.display_name_fallback_info', params: {'name': widget.staff.name}),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildRoleSection() {
    final isChiefGroomer = widget.staff.groomerRole == GroomerRole.chiefGroomer;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
         Text(
          context.tr('edit_staff_dialog.team_role_title'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 12),
        ...GroomerRole.values
            // ignore: deprecated_member_use_from_same_package
            .where((role) => role != GroomerRole.regularGroomer) // Exclude deprecated role
            .where((role) => _canAssignRole(role)) // Exclude roles that current user cannot assign
            .map((role) => RadioListTile<GroomerRole>(
                  title: Text(
                    role.localizedDisplayName(context),
                    style: TextStyle(
                      color: isChiefGroomer ? Colors.grey : null,
                    ),
                  ),
                  subtitle: Text(
                    role.localizedDescription(context),
                    style: TextStyle(
                      color: isChiefGroomer ? Colors.grey : null,
                    ),
                  ),
                  value: role,
                  groupValue: _selectedRole,
                  onChanged: isChiefGroomer ? null : (GroomerRole? value) {
                    if (value != null) {
                      setState(() {
                        _selectedRole = value;
                      });
                    }
                  },
                  activeColor: Theme.of(context).primaryColor,
                )),
      ],
    );
  }

  Widget _buildPermissionsSection() {
    final isChiefGroomer = widget.staff.groomerRole == GroomerRole.chiefGroomer;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
         Text(
          context.tr('edit_staff_dialog.client_permissions_title'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 12),
        ...ClientDataPermission.values.map((permission) => RadioListTile<ClientDataPermission>(
              title: Text(
                permission.localizedDisplayName(context),
                style: TextStyle(
                  color: isChiefGroomer ? Colors.grey : null,
                ),
              ),
              subtitle: Text(
                permission.localizedDescription(context),
                style: TextStyle(
                  color: isChiefGroomer ? Colors.grey : null,
                ),
              ),
              value: permission,
              groupValue: _selectedPermission,
              onChanged: isChiefGroomer ? null : (ClientDataPermission? value) {
                if (value != null) {
                  setState(() {
                    _selectedPermission = value;
                  });
                }
              },
              activeColor: Theme.of(context).primaryColor,
            )),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('edit_staff_dialog.notes_title'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: context.tr('edit_staff_dialog.notes_placeholder'),
            border: OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
            ),
          ),
          textCapitalization: TextCapitalization.sentences,
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: Text(context.tr('edit_staff_dialog.cancel_button')),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: _isLoading ? null : _handleSave,
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(context.tr('edit_staff_dialog.save_button')),
          ),
        ],
      ),
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      DebugLogger.logVerbose('🔄 EditStaffDialog: Updating staff member: ${widget.staff.id}');

      final isChiefGroomer = widget.staff.groomerRole == GroomerRole.chiefGroomer;

      final request = UpdateStaffRequest(
        nickname: _nicknameController.text.trim().isNotEmpty
            ? _nicknameController.text.trim()
            : null,
        // Don't change role/permissions for chief groomer
        groomerRole: isChiefGroomer ? widget.staff.groomerRole : _selectedRole,
        clientDataPermission: isChiefGroomer ? widget.staff.clientDataPermission : _selectedPermission,
        notes: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
      );

      final response = await StaffService.updateStaffInCurrentSalon(
        widget.staff.id,
        request,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.success && response.data != null) {
          DebugLogger.logVerbose('✅ EditStaffDialog: Staff member updated successfully');

          // Call the callback if provided
          widget.onStaffUpdated?.call(response.data!);

          // Show success message
          showTopSnackBar(context, 
            SnackBar(
              content: Text(context.tr('edit_staff_dialog.update_success', params: {'name': response.data!.displayName})),
            ),
          );

          // Close dialog
          Navigator.of(context).pop(response.data);
        } else {
          DebugLogger.logVerbose('❌ EditStaffDialog: Failed to update staff member: ${response.error}');

          // Show error message
          showTopSnackBar(context, 
            SnackBar(
              content: Text(context.tr('edit_staff_dialog.update_error', params: {'error': response.error ?? ''})),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ EditStaffDialog: Exception during staff update: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        showTopSnackBar(context, 
          SnackBar(
            content: Text(context.tr('edit_staff_dialog.update_error_generic', params: {'error': e.toString()})),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Check if the current user can assign the given role
  bool _canAssignRole(GroomerRole role) {
    // If the role is not chief groomer, it can always be assigned
    if (role != GroomerRole.chiefGroomer) {
      return true;
    }

    // Only admins can assign chief groomer roles
    final roleProvider = Provider.of<RoleProvider>(context, listen: false);
    return roleProvider.isAdmin;
  }
}
