import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';

import '../../models/appointment.dart';
import '../../models/salon.dart';
import '../../models/sms_template.dart';
import '../../providers/calendar_provider.dart';
import '../../services/appointment/appointment_service.dart';
import '../../services/auth/auth_service.dart';
import '../../services/error_handling_service.dart';
import '../../services/salon_service.dart';
import '../../services/sms_template_service.dart';
import '../../services/sms_settings_service.dart';
import '../../services/subscription_prompt_service.dart';
import '../../services/appointment_analytics_service.dart';
import '../../utils/debug_logger.dart';
import '../animations/sms_sent_animation.dart';

class AppointmentCompletionDialog extends StatefulWidget {
  final Appointment appointment;

  const AppointmentCompletionDialog({
    super.key,
    required this.appointment,
  });

  @override
  State<AppointmentCompletionDialog> createState() => _AppointmentCompletionDialogState();
}

class _AppointmentCompletionDialogState extends State<AppointmentCompletionDialog> {
  late DateTime _completionTime;
  final TextEditingController _notesController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Default completion time to current time
    _completionTime = DateTime.now();
    
    // If appointment is overdue, suggest current time
    // If appointment is current, suggest current time
    // If appointment is in future, suggest scheduled end time
    if (widget.appointment.isCurrent || widget.appointment.isOverdue) {
      _completionTime = DateTime.now();
    } else {
      _completionTime = widget.appointment.endTime;
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            color: Colors.green.shade600,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              context.tr('new_appointment.complete_appointment_dialog_widget_title'),
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Appointment info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${widget.appointment.clientName} - ${widget.appointment.petName}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${DateFormat('dd MMMM yyyy', Localizations.localeOf(context).languageCode).format(widget.appointment.startTime)} • ${widget.appointment.timeRange}',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Completion time picker
            Text(
              context.tr('new_appointment.completion_time_label'),
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            InkWell(
              onTap: _selectCompletionTime,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).colorScheme.outline),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        DateFormat('HH:mm').format(_completionTime),
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                    Icon(
                      Icons.arrow_drop_down,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ],
                ),
              ),
            ),
            
            // Show warning if completion time is before scheduled end time
            if (_completionTime.isBefore(widget.appointment.endTime))
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.orange.shade600,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Programarea se finalizează mai devreme decât planificat',
                        style: TextStyle(
                          color: Colors.orange.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Show info if completion time is after scheduled end time
            if (_completionTime.isAfter(widget.appointment.endTime))
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      color: Colors.blue.shade600,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Programarea s-a prelungit cu ${_getDelayMinutes()} minute',
                        style: TextStyle(
                          color: Colors.blue.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 20),

            // Notes field
            Text(
              context.tr('new_appointment.completion_notes_label'),
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _notesController,
              decoration: InputDecoration(
                hintText: context.tr('new_appointment.completion_notes_hint'),
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              maxLength: 500,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: Text(context.tr('new_appointment.completion_cancel_button')),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _completeAppointment,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green.shade600,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(context.tr('new_appointment.completion_complete_button')),
        ),
      ],
    );
  }

  Future<void> _selectCompletionTime() async {
    final initialTime = TimeOfDay.fromDateTime(_completionTime);

    final result = await showModalBottomSheet<TimeOfDay>(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _BasicTimePicker(
        initialTime: initialTime,
      ),
    );

    if (result != null && mounted) {
      setState(() {
        // Keep the same date, only change the time
        final today = DateTime.now();
        _completionTime = DateTime(
          today.year,
          today.month,
          today.day,
          result.hour,
          result.minute,
        );
      });
    }
  }

  int _getDelayMinutes() {
    return _completionTime.difference(widget.appointment.endTime).inMinutes;
  }

  Future<void> _completeAppointment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await AppointmentService.completeAppointment(
        widget.appointment.id,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        completedAt: _completionTime,
      );

      if (mounted) {
        if (response.success) {
          // Refresh calendar
          final calendarProvider = context.read<CalendarProvider>();
          await calendarProvider.fetchAppointmentsForDate(
            widget.appointment.startTime,
            forceRefresh: true,
          );

          // Check if SMS completion is enabled and get message before closing dialogs
          final smsSettingsResponse = await SmsSettingsService.getSmsSettings();
          final shouldShowAnimation = smsSettingsResponse.success && smsSettingsResponse.data?.completionMessages == true;

          String? completionMessage;

          if (shouldShowAnimation) {
            // Get appointment completion template and populate it
            completionMessage = await _getAppointmentCompletionMessage();
          }

          Navigator.of(context).pop(); // Close completion dialog
          Navigator.of(context).pop(); // Close appointment details dialog

          // Trigger subscription prompt for aha moments
          await _triggerSubscriptionPrompt();

          // Record appointment completion and trigger subscription prompt
          await _recordCompletionAndTriggerPrompt();

          // Show animation using a delayed approach to ensure we have a valid context
          if (shouldShowAnimation && completionMessage != null) {
            // Use a short delay to ensure the navigation stack is settled
            await Future.delayed(const Duration(milliseconds: 100));

            // Get the current context from the navigator
            final currentContext = Navigator.of(context, rootNavigator: true).context;

            showSmsSentAnimation(
              currentContext,
              clientName: widget.appointment.clientName,
              clientPhone: widget.appointment.clientPhone,
              message: completionMessage,
              onComplete: () {
                ScaffoldMessenger.of(currentContext).showSnackBar(
                  SnackBar(
                    content: const Text('Programarea a fost finalizată cu succes'),
                    backgroundColor: Colors.green.shade600,
                  ),
                );
              },
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Programarea a fost finalizată cu succes'),
                backgroundColor: Colors.green.shade600,
              ),
            );
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Eroare la finalizarea programării: ${response.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorHandlingService.handleAppError(
          operation: 'complete_appointment',
          error: e,
          screenName: 'AppointmentCompletionDialog',
        );
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la finalizarea programării: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Trigger subscription prompt at strategic moments
  Future<void> _triggerSubscriptionPrompt() async {
    try {
      // Get the current salon ID
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        DebugLogger.logInit('❌ No salon ID available for subscription prompt');
        return;
      }

      // Get the current context from the navigator to ensure we have a valid context
      final currentContext = Navigator.of(context, rootNavigator: true).context;

      // For now, show the first appointment prompt
      // In a real implementation, you would check appointment count and show appropriate prompts
      await SubscriptionPromptService.showAfterFirstAppointment(
        context: currentContext,
        salonId: salonId,
      );
    } catch (e) {
      DebugLogger.logInit('❌ Failed to trigger subscription prompt: $e');
    }
  }

  /// Record appointment completion and trigger subscription prompt at strategic moments
  Future<void> _recordCompletionAndTriggerPrompt() async {
    try {
      // Record the appointment completion for analytics
      await AppointmentAnalyticsService.recordAppointmentCompleted();

      // Get analytics to determine which prompt to show
      final analytics = await AppointmentAnalyticsService.getAnalyticsSummary();
      final completedCount = analytics['completedAppointments'] as int? ?? 0;
      final shouldShow = analytics['shouldShowPrompt'] as bool? ?? false;

      if (!shouldShow) {
        DebugLogger.logInit('📊 No subscription prompt needed for $completedCount appointments');
        return;
      }

      // Get the current context from the navigator to ensure we have a valid context
      final currentContext = Navigator.of(context, rootNavigator: true).context;

      // Get the current salon ID from auth service
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        DebugLogger.logInit('❌ No salon ID available for subscription prompt');
        return;
      }
      DebugLogger.logInit('🎯 Triggering subscription prompt for appointment #$completedCount');

      // Show appropriate prompt based on milestone
      if (completedCount == 1) {
        SubscriptionPromptService.showAfterFirstAppointment(
          context: currentContext,
          salonId: salonId,
        );
      }
    } catch (e) {

      DebugLogger.logInit('❌ Failed to record completion and trigger prompt: $e');
    }
  }

  Future<String> _getAppointmentCompletionMessage() async {
    try {
      // Get the appointment completion SMS template
      final response = await SmsTemplateService.getSmsTemplate(SmsTemplateType.appointmentCompletion);

      String templateContent;
      if (response.success && response.data != null && response.data!.isActive) {
        // Use custom template
        templateContent = response.data!.templateContent;
      } else {
        // Use default template
        templateContent = SmsTemplateType.appointmentCompletion.getDefaultContent();
      }

      // Populate template variables with appointment data
      return await _populateTemplateVariables(templateContent);
    } catch (e) {
      // Fallback to a simple message if template service fails
      return 'Programarea pentru ${widget.appointment.petName} a fost finalizată cu succes. Vă mulțumim!';
    }
  }

  Future<String> _populateTemplateVariables(String template) async {
    // Get actual salon information from the salon service
    Salon? salon;
    try {
      final response = await SalonService.getCurrentUserSalon();
      if (response.success && response.data != null) {
        salon = response.data;
      }
    } catch (e) {
      // If salon service fails, we'll use fallback values
    }

    // Use actual salon data or fallback values
    final salonName = salon?.name ?? 'Salonul nostru';
    final salonPhone = salon?.phone ?? '0721 000 000';
    final salonAddress = salon?.address ?? 'Adresa salonului';

    return template
        .replaceAll('{SALON_NAME}', salonName)
        .replaceAll('{OWNER_NAME}', widget.appointment.clientName)
        .replaceAll('{PET_NAME}', widget.appointment.petName)
        .replaceAll('{APPOINTMENT_DATE}', DateFormat('d MMMM yyyy', 'ro').format(widget.appointment.startTime))
        .replaceAll('{APPOINTMENT_TIME}', DateFormat('HH:mm').format(widget.appointment.startTime))
        .replaceAll('{SALON_ADDRESS}', salonAddress)
        .replaceAll('{SALON_PHONE}', salonPhone);
  }
}

class _BasicTimePicker extends StatefulWidget {
  final TimeOfDay initialTime;

  const _BasicTimePicker({
    required this.initialTime,
  });

  @override
  __BasicTimePickerState createState() => __BasicTimePickerState();
}

class __BasicTimePickerState extends State<_BasicTimePicker> {
  late TimeOfDay _selectedTime;

  @override
  void initState() {
    super.initState();
    _selectedTime = widget.initialTime;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Selectează ora',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'Anulează',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Time picker
          SizedBox(
            height: 180,
            child: Row(
              children: [
                // Hour picker
                Expanded(
                  child: ListWheelScrollView.useDelegate(
                    itemExtent: 40,
                    physics: const FixedExtentScrollPhysics(),
                    onSelectedItemChanged: (index) {
                      setState(() {
                        _selectedTime = TimeOfDay(
                          hour: index,
                          minute: _selectedTime.minute,
                        );
                      });
                    },
                    childDelegate: ListWheelChildBuilderDelegate(
                      builder: (context, index) {
                        return Center(
                          child: Text(
                            '${index.toString().padLeft(2, '0')}',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w500,
                              color: index == _selectedTime.hour
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        );
                      },
                      childCount: 24,
                    ),
                  ),
                ),

                // Divider
                Container(
                  width: 1,
                  height: 180,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
                ),

                // Minute picker
                Expanded(
                  child: ListWheelScrollView.useDelegate(
                    itemExtent: 40,
                    physics: const FixedExtentScrollPhysics(),
                    onSelectedItemChanged: (index) {
                      setState(() {
                        _selectedTime = TimeOfDay(
                          hour: _selectedTime.hour,
                          minute: index * 5,
                        );
                      });
                    },
                    childDelegate: ListWheelChildBuilderDelegate(
                      builder: (context, index) {
                        return Center(
                          child: Text(
                            '${index * 5}'.padLeft(2, '0'),
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w500,
                              color: index * 5 == _selectedTime.minute
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        );
                      },
                      childCount: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Confirm button
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(_selectedTime);
            },
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 50),
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Confirmă ora',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

