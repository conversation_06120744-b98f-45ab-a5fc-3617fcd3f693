import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';
import '../../models/appointment.dart';
import '../../models/client.dart';
import '../../models/image_upload_response.dart';
import '../../models/service.dart';
import '../../providers/appointment_settings_provider.dart';
import '../../providers/calendar_provider.dart';
import '../../screens/clients/client_details_screen.dart';
import '../../services/api_service.dart';
import '../../services/appointment/appointment_service.dart';
import '../../services/appointment_subscription_service.dart';
import '../../services/auth/auth_service.dart';
import '../../services/error_handling_service.dart';
import '../../services/staff_service.dart';
import '../../services/url_launcher_service.dart';
import '../../utils/debug_logger.dart';
import '../../utils/snack_bar_utils.dart';
import '../common/address_action_widget.dart';
import '../common/cross_platform_image.dart';
import '../common/custom_bottom_sheet.dart';
import 'appointment_completion_dialog.dart';
import 'sms_template_selector_dialog.dart';

class AppointmentDetailsDialog extends StatefulWidget {
  final Appointment appointment;

  const AppointmentDetailsDialog({
    super.key,
    required this.appointment,
  });

  @override
  State<AppointmentDetailsDialog> createState() => _AppointmentDetailsDialogState();
}

class _AppointmentDetailsDialogState extends State<AppointmentDetailsDialog> {
  final ImagePicker _imagePicker = ImagePicker();
  final List<String> _appointmentPhotos = [];
  bool _isUploadingPhoto = false;
  bool _isDisposing = false;
  @override
  void initState() {
    super.initState();
    // Initialize photos from appointment
    _appointmentPhotos.clear();
    _appointmentPhotos.addAll(widget.appointment.photos);

    // Debug log to check appointment photos
    DebugLogger.logVerbose('🔍 DEBUG: AppointmentDetailsDialog initialized');
    DebugLogger.logVerbose('  - Appointment ID: ${widget.appointment.id}');
    DebugLogger.logVerbose('  - Photos count: ${widget.appointment.photos.length}');
    DebugLogger.logVerbose('  - Photos: ${widget.appointment.photos}');
    // Fetch pet and client details after the widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      calendarProvider.getPetById(widget.appointment.petId);
      calendarProvider.getClientById(widget.appointment.clientId);
      calendarProvider.getPetsForClient(widget.appointment.clientId);
    });
  }

  @override
  void dispose() {
    _isDisposing = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Responsive breakpoint: consider narrow screens (phones in portrait)
    final isCompact = MediaQuery.of(context).size.width < 480;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Enhanced Header with better visual hierarchy (responsive)
            Container(
              padding: EdgeInsets.all(isCompact ? 12.0 : 20.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Top row with close button and status
                  Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.arrow_back),
                        iconSize: 24,
                      ),
                      const Spacer(),
                      Text(
                        _getStatusText(),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: isCompact ? 16 : 18,
                          fontWeight: FontWeight.w600,
                          color: _getStatusColor(),
                        ),
                      ),
                      const Spacer(),
                      const SizedBox(width: 48), // Balance the back button
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Service and client info - allow wrapping on narrow screens
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.all(isCompact ? 10 : 12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Icon(
                          _getServiceIcon(widget.appointment.service),
                          color: Theme.of(context).primaryColor,
                          size: isCompact ? 28 : 32,
                        ),
                      ),
                      SizedBox(width: isCompact ? 10 : 16),
                      // Use Flexible instead of Expanded so this column can shrink and wrap on small widths
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.appointment.service,
                              style: TextStyle(
                                fontSize: isCompact ? 18 : 22,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                            ),
                            const SizedBox(height: 4),
                            GestureDetector(
                              onTap: _navigateToClientDetails,
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: isCompact ? 10 : 12, vertical: isCompact ? 6 : 6),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.person,
                                      size: isCompact ? 14 : 16,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                    const SizedBox(width: 6),
                                    Flexible(
                                      child: Text(
                                        '${widget.appointment.clientName} - ${widget.appointment.petName}',
                                        style: TextStyle(
                                          fontSize: isCompact ? 13 : 14,
                                          fontWeight: FontWeight.w600,
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      size: isCompact ? 10 : 12,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Subscription indicator
                      if (widget.appointment.hasSubscription) ...[
                        const SizedBox(width: 8),
                        FutureBuilder<String?>(
                          future: AuthService.getCurrentSalonId(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData && snapshot.data != null) {
                              return GestureDetector(
                                onTap: () => _navigateToClientSubscriptions(context),
                                child: Container(
                                  padding: EdgeInsets.symmetric(horizontal: isCompact ? 10 : 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.repeat,
                                        size: isCompact ? 16 : 18,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        widget.appointment.subscriptionDisplayInfo,
                                        style: TextStyle(
                                          fontSize: isCompact ? 12 : 13,
                                          fontWeight: FontWeight.w600,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),

            // Small time card right under the header (responsive)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: isCompact ? 12.0 : 20.0, vertical: isCompact ? 8.0 : 12.0),
              child: _buildTimeCard(),
            ),

            // Content with improved spacing and visual hierarchy
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),

                    // Client Information Card (with tap to navigate)
                    _buildClientInformationCard(),
                    const SizedBox(height: 24),

                    // Pet Information Card
                    _buildPetInformationCard(),
                    const SizedBox(height: 24),

                    // Service Details Card
                    _buildServiceDetailsCard(),
                    const SizedBox(height: 24),

                    // Groomer Information Card
                    _buildGroomerInformationCard(),
                    const SizedBox(height: 24),

                    // Notes Card
                    if (widget.appointment.notes.isNotEmpty) ...[
                      _buildNotesCard(),
                      const SizedBox(height: 24),
                    ],

                    // Photos Section (moved to bottom)
                    _buildPhotosSection(),

                    // Bottom padding for better scrolling
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ),

          ],
        ),
      ),
      // Floating Action Buttons for appointment actions
      floatingActionButton: !_isAppointmentCanceled() || _isAppointmentCompleted()
          ? _buildFloatingActionButtons()
          : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Auto-finalize status or finalization button
        Consumer<AppointmentSettingsProvider>(
          builder: (context, settingsProvider, child) {
            if (!_isAppointmentCompleted()) {
              if (!settingsProvider.autoFinalizeEnabled) {
                return FloatingActionButton.extended(
                  heroTag: "complete_appointment",
                  onPressed: () => _showCompletionDialog(context),
                  backgroundColor: Colors.green.shade600,
                  foregroundColor: Colors.white,
                  icon: const Icon(Icons.check_circle_outline),
                  label: Text(context.tr('new_appointment.complete_appointment_button')),
                );
              }
            }
            return const SizedBox.shrink();
          },
        ),
        if (!_isAppointmentCompleted() && !_isAppointmentCanceled()) ...[
          const SizedBox(height: 12),
          FloatingActionButton.extended(
            heroTag: "reschedule_appointment",
            onPressed: widget.appointment.status == 'completed' ? null : () {
              _showRescheduleDialog(context);
            },
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.schedule),
            label: Text(context.tr('new_appointment.modify_appointment_button')),
          ),
          const SizedBox(height: 12),
          FloatingActionButton.extended(
            heroTag: "cancel_appointment",
            onPressed: () => _showCancelConfirmationDialog(context),
            backgroundColor: Colors.red.shade600,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.cancel_outlined),
            label: Text(context.tr('new_appointment.cancel_appointment_button')),
          ),
        ],
      ],
    );
  }

  // Add method to navigate to client details
  void _navigateToClientDetails() async {
    try {
      // Get client details from calendar provider
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      final client = calendarProvider.clientsCache[widget.appointment.clientId];

      if (client != null) {
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ClientDetailsScreen(client: client),
          ),
        );
      } else {
        // If client not in cache, create a basic client object for navigation
        final basicClient = Client(
          id: widget.appointment.clientId,
          name: widget.appointment.clientName,
          phone: widget.appointment.clientPhone,
          email: '',
          petCount: 1,
          registrationDate: DateTime.now(),
        );

        await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ClientDetailsScreen(client: basicClient),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBarSafe(context,
          SnackBar(
            content: Text('Eroare la deschiderea detaliilor clientului: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Add photos section
  Widget _buildPhotosSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.photo_library,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  context.tr('new_appointment.photos_title'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _isUploadingPhoto ? null : _showPhotoOptions,
                  icon: Icon(
                    Icons.add_a_photo,
                    color: Theme.of(context).primaryColor,
                  ),
                  tooltip: context.tr('new_appointment.add_photo_tooltip'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isUploadingPhoto)
              const Center(
                child: CircularProgressIndicator(),
              )
            else if (_appointmentPhotos.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    style: BorderStyle.solid,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.photo_camera,
                      size: 48,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      context.tr('new_appointment.no_photos_message'),
                      style: TextStyle(
                        fontSize: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      context.tr('new_appointment.add_first_photo_hint'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            else
              _buildPhotosGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotosGrid() {
    // Compute number of columns based on screen width (responsive)
    final width = MediaQuery.of(context).size.width;
    int crossAxisCount = 3;
    if (width < 600) {
      crossAxisCount = 2;
    } else if (width < 400) {
      crossAxisCount = 1;
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: _appointmentPhotos.length,
      itemBuilder: (context, index) {
        final String photoUrl = _appointmentPhotos[index];
        return GestureDetector(
          onTap: () => _showPhotoViewer(index),
          onLongPress: () => _showPhotoOptions(photoUrl: photoUrl, index: index),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: CrossPlatformImage(
                networkUrl: photoUrl,
                fit: BoxFit.cover,
                placeholder: const Center(child: CircularProgressIndicator()),
                errorWidget: const Icon(Icons.error),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showPhotoOptions({String? photoUrl, int? index}) {
    CustomBottomSheet.show(
      context: context,
      title: photoUrl != null ? context.tr('new_appointment.photo_options_title') : context.tr('new_appointment.add_photo_title'),
      isScrollControlled: true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (photoUrl == null) ...[
            ListTile(
              leading: Icon(Icons.camera_alt, color: Theme.of(context).colorScheme.onSurface),
              title: Text(context.tr('new_appointment.camera_option')),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromSource(ImageSource.camera);
              },
            ),
            ListTile(
              leading: Icon(Icons.photo_library, color: Theme.of(context).colorScheme.onSurface),
              title: Text(context.tr('new_appointment.gallery_option')),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromSource(ImageSource.gallery);
              },
            ),
          ] else ...[
            ListTile(
              leading: Icon(Icons.visibility, color: Theme.of(context).colorScheme.onSurface),
              title: Text(context.tr('new_appointment.view_photo_option')),
              onTap: () {
                Navigator.pop(context);
                _showPhotoViewer(index!);
              },
            ),
            ListTile(
              leading: Icon(Icons.delete, color: Theme.of(context).colorScheme.error),
              title: Text(context.tr('new_appointment.delete_photo_option')),
              onTap: () {
                Navigator.pop(context);
                _deletePhoto(index!);
              },
            ),
          ],
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Future<void> _pickImageFromSource(ImageSource source) async {
    try {
      setState(() {
        _isUploadingPhoto = true;
      });

      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        // Upload to Cloudinary
        final uploadResponse = await ApiService.uploadXFile<ImageUploadResponse>(
          '/api/images/upload',
          image,
          fromJson: (data) => ImageUploadResponse.fromJson(Map<String, dynamic>.from(data)),
        );

        if (uploadResponse.success && uploadResponse.data != null && mounted) {
          setState(() {
            _appointmentPhotos.add(uploadResponse.data!.url);
            _isUploadingPhoto = false;
          });

          // Save photos to appointment
          await _savePhotosToAppointment();

          if (mounted && !_isDisposing) {
            showTopSnackBarSafe(context,
              SnackBar(
                content: Text(context.tr('new_appointment.photo_uploaded_success')),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        } else {
          setState(() {
            _isUploadingPhoto = false;
          });

          if (mounted && !_isDisposing) {
            showTopSnackBarSafe(context,
              SnackBar(
                content: Text(context.tr('new_appointment.photo_upload_error', params: {'error': uploadResponse.error ?? 'Eroare necunoscută'})),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        setState(() {
          _isUploadingPhoto = false;
        });
      }
    } catch (e) {
      setState(() {
        _isUploadingPhoto = false;
      });

      if (mounted && !_isDisposing) {
        showTopSnackBarSafe(context,
          SnackBar(
            content: Text('Eroare la selectarea imaginii: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showPhotoViewer(int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _PhotoViewerScreen(
          photos: _appointmentPhotos,
          initialIndex: initialIndex,
          onDelete: (index) {
            _deletePhoto(index);
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }

  void _deletePhoto(int index) async {
    setState(() {
      _appointmentPhotos.removeAt(index);
    });

    // Save updated photos to appointment
    await _savePhotosToAppointment();

    if (mounted && !_isDisposing) {
      showTopSnackBarSafe(context,
        SnackBar(
          content: Text(context.tr('new_appointment.photo_deleted_success')),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _savePhotosToAppointment() async {
    try {
      final response = await AppointmentService.updateAppointmentPhotos(
        widget.appointment.id,
        _appointmentPhotos,
      );

      if (response.success) {
        // Refresh appointment data to get updated photos from server
        await _refreshAppointmentData();
      } else {
        if (mounted && !_isDisposing) {
          showTopSnackBarSafe(context,
            SnackBar(
              content: Text('Eroare la salvarea fotografiilor: ${response.message}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted && !_isDisposing) {
        showTopSnackBarSafe(context,
          SnackBar(
            content: Text('Eroare la salvarea fotografiilor: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _refreshAppointmentData() async {
    try {
      DebugLogger.logVerbose('🔄 DEBUG: Refreshing appointment data for ID: ${widget.appointment.id}');
      final response = await AppointmentService.getAppointment(widget.appointment.id);

      if (response.success && response.data != null && mounted && !_isDisposing) {
        DebugLogger.logVerbose('✅ DEBUG: Appointment data refreshed successfully');
        DebugLogger.logVerbose('  - Photos count: ${response.data!.photos.length}');
        DebugLogger.logVerbose('  - Photos: ${response.data!.photos}');

        setState(() {
          // Update local photos list with server data
          _appointmentPhotos.clear();
          _appointmentPhotos.addAll(response.data!.photos);
        });

        DebugLogger.logVerbose('  - Local photos updated: ${_appointmentPhotos.length} photos');

        // Also refresh the calendar provider to update the appointment in the calendar
        final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
        await calendarProvider.fetchAppointmentsForDate(widget.appointment.startTime, forceRefresh: true);
      } else {
        DebugLogger.logVerbose('❌ DEBUG: Failed to refresh appointment data');
        DebugLogger.logVerbose('  - Success: ${response.success}');
        DebugLogger.logVerbose('  - Data null: ${response.data == null}');
        DebugLogger.logVerbose('  - Error: ${response.error}');
      }
    } catch (e) {
      // Silent error - don't show error message for refresh failures
      DebugLogger.logVerbose('❌ DEBUG: Exception refreshing appointment data: $e');
    }
  }

  // Helper methods for status in title
  String _getStatusText() {
    switch (widget.appointment.status.toLowerCase()) {
      case 'confirmed':
      case 'confirmat':
        return context.tr('new_appointment.status_confirmed');
      case 'scheduled':
      case 'programat':
        return context.tr('new_appointment.status_scheduled');
      case 'pending':
      case 'in asteptare':
        return context.tr('new_appointment.status_pending');
      case 'canceled':
      case 'anulat':
      case 'cancelled':
        return context.tr('new_appointment.status_cancelled');
      case 'completed':
      case 'finalizat':
        return context.tr('new_appointment.status_completed');
      default:
        return context.tr('new_appointment.status_scheduled');
    }
  }

  Color _getStatusColor() {
    switch (widget.appointment.status.toLowerCase()) {
      case 'confirmed':
      case 'confirmat':
      case 'scheduled':
      case 'programat':
      case 'completed':
      case 'finalizat':
        return Theme.of(context).primaryColor;
      case 'pending':
      case 'in asteptare':
        return Colors.orange;
      case 'canceled':
      case 'anulat':
      case 'cancelled':
        return Colors.red;
      default:
        return Theme.of(context).primaryColor;
    }
  }

  // Simple time/date display instead of large status card
  Widget _buildTimeCard() {
    // Compute isCompact here to avoid rebuilding the whole widget on every frame
    final isCompact = MediaQuery.of(context).size.width < 480;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.appointment.timeRange,
                  style: TextStyle(
                    fontSize: 16,
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  DateFormat('EEEE, dd MMMM yyyy', 'ro').format(widget.appointment.startTime),
                  style: TextStyle(
                    fontSize: isCompact ? 12 : 14,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceDetailsCard() {
    return Consumer<CalendarProvider>(
      builder: (context, calendarProvider, child) {
        final serviceDetails = calendarProvider.getServiceDetails();

        // Get all services for this appointment
        final allServices = widget.appointment.services.isNotEmpty
            ? widget.appointment.services
            : [widget.appointment.service].where((s) => s.isNotEmpty).toList();

        // Calculate total duration from individual services or use appointment duration
        int totalDuration = widget.appointment.durationInMinutes;
        if (allServices.isNotEmpty) {
          totalDuration = allServices.fold(0, (sum, serviceName) {
            // Always use original service duration, ignore custom duration
            final serviceDetail = serviceDetails[serviceName];
            return sum + (serviceDetail?['duration'] as int? ?? 60);
          });
        }

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.description,
                      color: Theme.of(context).primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      context.tr('new_appointment.service_details_title'),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Display all services
                if (allServices.isNotEmpty) ...[
                  if (allServices.length == 1) ...[
                    // Single service
                    _buildSingleServiceRow(allServices.first),
                  ] else ...[
                    // Multiple services
                    _buildDetailRow('Servicii:', ''),
                    const SizedBox(height: 4),
                    ...allServices.asMap().entries.map((entry) {
                      final index = entry.key;
                      final serviceName = entry.value;
                      final customService = widget.appointment.customServices?[serviceName];
                      final serviceDetail = serviceDetails[serviceName];

                      // Use custom service data if available
                      final displayName = customService?['customName'] as String? ?? serviceName;
                      final serviceDuration = customService?['customDuration'] as int? ?? serviceDetail?['duration'] as int? ?? 60;

                      return Padding(
                        padding: const EdgeInsets.only(left: 16, bottom: 4),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${index + 1}. ',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.grey,
                              ),
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          displayName,
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      ),
                                      if (customService != null) ...[
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.orange.withValues(alpha: 0.2),
                                            borderRadius: BorderRadius.circular(4),
                                          ),
                                          child: Text(
                                            'CUSTOM',
                                            style: TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.orange.shade700,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                  if (customService != null && displayName != serviceName) ...[
                                    Text(
                                      'Original: $serviceName',
                                      style: const TextStyle(
                                        fontSize: 11,
                                        color: Colors.grey,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ],
                                  Text(
                                    '$serviceDuration minute',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                    const SizedBox(height: 8),
                  ],
                ] else ...[
                  _buildDetailRow(context.tr('new_appointment.service_single_label') + ':', context.tr('calendar.appointments.general_service')),
                ],

                _buildDetailRow(context.tr('new_appointment.total_duration_short') + ':', '$totalDuration minute'),
                _buildDetailRow(context.tr('new_appointment.start_time_short') + ':', DateFormat('HH:mm').format(widget.appointment.startTime)),
                _buildDetailRow(context.tr('new_appointment.end_time_short') + ':', DateFormat('HH:mm').format(widget.appointment.endTime)),

                // Price information
                const SizedBox(height: 8),
                _buildPriceInformation(serviceDetails),

                // Show completion time information for completed appointments
                if (widget.appointment.isCompleted && widget.appointment.completedAt != null) ...[
                  const SizedBox(height: 8),
                  _buildCompletionTimeInfo(),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGroomerInformationCard() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        // Find the staff member by groomerId first, then by name
        StaffResponse? staff;

        // First try to find by groomerId (most reliable)
        if (widget.appointment.groomerId != null && widget.appointment.groomerId!.isNotEmpty) {
          try {
            staff = provider.availableStaff.firstWhere(
              (s) => s.id == widget.appointment.groomerId,
            );
          } catch (e) {
            // Staff not found by ID, continue to name matching
          }
        }

        // Fallback to name matching if not found by ID
        if (staff == null) {
          String staffName;
          try {
            staffName = widget.appointment.assignedGroomer.isNotEmpty
                ? widget.appointment.assignedGroomer
                : 'Groomer'; // Fallback if no staff assigned or if it's null
          } catch (e) {
            staffName = 'Groomer'; // Fallback on any error
          }

          // Find the staff by name (check both full name and display name)
          try {
            staff = provider.availableStaff.firstWhere(
              (s) => s.name == staffName || s.displayName == staffName,
            );
          } catch (e) {
            // If staff not found, use first available staff
            staff = provider.availableStaff.isNotEmpty
              ? provider.availableStaff.first
              : null;
          }
        }



        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person_pin,
                      color: Theme.of(context).primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      context.tr('new_appointment.staff_info_title'),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const Spacer(),
                    if (staff != null)
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: provider.getStaffColor(staff.id),
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.grey.shade300, width: 2),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildDetailRow(context.tr('new_appointment.staff_name_label'), staff?.displayName ?? widget.appointment.assignedGroomer ?? 'Ana Popescu'),
                if (staff != null) ...[
                  // Show full name if different from display name
                  if (staff.nickname != null && staff.nickname!.isNotEmpty && staff.nickname != staff.name)
                    _buildDetailRow(context.tr('new_appointment.staff_full_name_label'), staff.name),
                  _buildDetailRow(context.tr('new_appointment.staff_phone_label'), staff.phone ?? context.tr('new_appointment.staff_phone_not_available')),
                  if (staff.specialties.isNotEmpty)
                    _buildDetailRow(context.tr('new_appointment.staff_specialties_label'), staff.specialties.join(', ')),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPetInformationCard() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final pet = provider.petsCache[widget.appointment.petId];

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.pets,
                      color: Theme.of(context).primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      context.tr('new_appointment.pet_info_title'),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Pet notes section (priority display if medical conditions)
                if (pet != null && pet.notes.isNotEmpty) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: pet.hasMedicalConditions
                          ? Theme.of(context).colorScheme.errorContainer.withValues(alpha: 0.3)
                          : Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: pet.hasMedicalConditions
                            ? Theme.of(context).colorScheme.error.withValues(alpha: 0.5)
                            : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              pet.hasMedicalConditions ? Icons.warning : Icons.note_alt,
                              color: pet.hasMedicalConditions
                                  ? Theme.of(context).colorScheme.error
                                  : Theme.of(context).colorScheme.primary,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              pet.hasMedicalConditions ? context.tr('new_appointment.pet_observations_standard') : context.tr('new_appointment.groomer_notes_label'),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: pet.hasMedicalConditions
                                    ? Theme.of(context).colorScheme.error
                                    : Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          pet.notes,
                          style: TextStyle(
                            fontSize: 13,
                            height: 1.4,
                            color: pet.hasMedicalConditions
                                ? Theme.of(context).colorScheme.error
                                : Theme.of(context).colorScheme.onSurface,
                            fontWeight: pet.hasMedicalConditions ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                if (pet == null) ...[
                  _buildDetailRow(context.tr('new_appointment.pet_name_label'), widget.appointment.petName),
                  if (provider.isLoadingClientData)
                     Center(child: CircularProgressIndicator(color: Theme.of(context).primaryColor)),
                ] else ...[
                  _buildDetailRow(context.tr('new_appointment.pet_name_label'), pet.name,
                    valueColor: pet.hasMedicalConditions ? Theme.of(context).colorScheme.error : null),
                  _buildDetailRow(context.tr('new_appointment.pet_breed_label'), pet.breed),
                  _buildDetailRow(context.tr('new_appointment.pet_gender_label'), _getGenderText(pet.gender)),
                  _buildDetailRow(context.tr('new_appointment.pet_age_label'), pet.age),
                  _buildDetailRow(context.tr('new_appointment.pet_weight_label'), '${pet.weight} kg'),
                  _buildDetailRow(context.tr('new_appointment.pet_color_label'), pet.color),
                  if (pet.hasMedicalConditions) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.error.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.warning,
                            color: Theme.of(context).colorScheme.error,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            context.tr('new_appointment.special_attention_needed'),
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.error,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildClientInformationCard() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        // Try to get client address from cache
        final client = provider.clientsCache[widget.appointment.clientId];
        final clientAddress = widget.appointment.clientAddress.isNotEmpty
            ? widget.appointment.clientAddress
            : (client?.address ?? '');

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: InkWell(
            onTap: _navigateToClientDetails,
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.person, color: Theme.of(context).primaryColor, size: 24),
                      const SizedBox(width: 12),
                      Text(
                        context.tr('new_appointment.client_info_title'),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      const Spacer(),
                      // Client avatar with enhanced design
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                          border: Border.all(color: Theme.of(context).primaryColor.withValues(alpha: 0.3)),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            widget.appointment.clientName.isNotEmpty
                                ? widget.appointment.clientName[0].toUpperCase()
                                : '?',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Client details with better typography
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        _buildDetailRow(context.tr('new_appointment.client_name_label'), widget.appointment.clientName),
                        const SizedBox(height: 8),
                        _buildDetailRow(context.tr('new_appointment.client_phone_label'), widget.appointment.clientPhone),
                        if (clientAddress.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: 80,
                                child: Text(
                                  context.tr('new_appointment.client_address_label'),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: AddressActionWidget(
                                  address: clientAddress,
                                  textStyle: TextStyle(
                                    fontSize: 14,
                                    color: Theme.of(context).colorScheme.primary,
                                    decoration: TextDecoration.underline,
                                  ),
                                  iconColor: Theme.of(context).colorScheme.primary,
                                  showIcon: true,
                                  padding: EdgeInsets.zero,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Action buttons with improved design
                  Row(
                    children: [
                      Expanded(
                        child: _buildClientActionButton(
                          icon: Icons.phone,
                          label: context.tr('new_appointment.call_button'),
                          color: Colors.green,
                          onPressed: () => _makePhoneCall(),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildClientActionButton(
                          icon: Icons.message,
                          label: context.tr('new_appointment.sms_button'),
                          color: Colors.blue,
                          onPressed: () => _sendSMS(),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildClientActionButton(
                          icon: FontAwesomeIcons.whatsapp,
                          label: context.tr('new_appointment.whatsapp_button'),
                          color: const Color(0xFF25D366),
                          onPressed: () => _openWhatsApp(),
                        ),
                      ),
                    ],
                  ),

                  // Tap hint
                  const SizedBox(height: 12),
                  Center(
                    child: Text(
                      context.tr('new_appointment.tap_for_client_details'),
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNotesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note_alt,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  context.tr('new_appointment.notes_title'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                widget.appointment.notes,
                style: TextStyle(
                  fontSize: 15,
                  height: 1.5,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {
    Color? valueColor,
    bool strikethrough = false,
    bool isBold = false
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 15,
                color: valueColor ?? Theme.of(context).colorScheme.onSurface,
                fontWeight: isBold ? FontWeight.bold : (valueColor != null ? FontWeight.w600 : FontWeight.w500),
                decoration: strikethrough ? TextDecoration.lineThrough : null,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.1),
        foregroundColor: color,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 0,
        minimumSize: const Size(0, 56),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _showCancelConfirmationDialog(BuildContext context) {
    String cancellationReason = '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.tr('new_appointment.cancel_appointment_dialog_title')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(context.tr('new_appointment.cancel_confirmation_message')),
              const SizedBox(height: 16),
              TextField(
                decoration: InputDecoration(
                  labelText: context.tr('new_appointment.cancel_reason_label'),
                  border: const OutlineInputBorder(),
                ),
                maxLines: 2,
                textCapitalization: TextCapitalization.sentences,
                onChanged: (value) => cancellationReason = value,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.tr('new_appointment.cancel_no_button')),
            ),
            ElevatedButton(
              onPressed: () => _handleCancel(context, cancellationReason),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: Text(context.tr('new_appointment.cancel_yes_button')),
            ),
          ],
        );
      },
    );
  }

  void _handleCancel(BuildContext context, String reason) async {
    // Store references before closing dialog
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
    final navigator = Navigator.of(context);

    navigator.pop(); // Close confirmation dialog

    try {
      final response = await AppointmentService.cancelAppointment(
        widget.appointment.id,
        reason: reason.isNotEmpty ? reason : null,
      );

      if (mounted) {
        if (response.success) {
          navigator.pop(); // Close details dialog
          showTopSnackBarSafe(context,
            const SnackBar(
              content: Text('Programarea a fost anulată cu succes'),
              backgroundColor: Colors.red,
            ),
          );
          // Force refresh calendar
          await calendarProvider.fetchAppointmentsForDate(widget.appointment.startTime, forceRefresh: true);
        } else {
          final errorMessage = response.error ?? 'Eroare necunoscută';
          showTopSnackBarSafe(context,
            SnackBar(
              content: Text('Eroare la anularea programării: $errorMessage'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorHandlingService.handleAppError(
          operation: 'cancel_appointment',
          error: e,
          screenName: 'AppointmentDetailsDialog',
        );
        showTopSnackBarSafe(context,
          SnackBar(
            content: Text('Eroare la anularea programării: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  // Helper method to check if appointment is canceled
  bool _isAppointmentCanceled() {
    final status = widget.appointment.status.toLowerCase();
    return status == 'canceled' || status == 'anulat' || status == 'cancelled';
  }

  bool _isAppointmentCompleted() {
    final status = widget.appointment.status.toLowerCase();
    return status == 'completed' || status == 'finalizat' || status == 'finalized' || status == 'done';
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback? onPressed,
    String? label,
  }) {
    if (label != null) {
      // Button with label for appointment actions
      return ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 18),
        label: Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: onPressed != null ? color.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
          foregroundColor: onPressed != null ? color : Colors.grey,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } else {
      // Icon-only button (legacy support)
      return IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: onPressed != null ? color : Colors.grey),
        iconSize: 28,
        padding: const EdgeInsets.all(12),
      );
    }
  }

  IconData _getServiceIcon(String service) {
    switch (service) {
      case 'Baie completă':
        return Icons.bathtub;
      case 'Tuns și aranjat':
      case 'Tuns de vară':
        return Icons.content_cut;
      case 'Toaletare completă':
      case 'Pachet complet premium':
        return Icons.spa;
      case 'Tăiat unghii':
      case 'Îngrijire gheare':
        return Icons.cut;
      case 'Curățat urechi':
        return Icons.hearing;
      case 'Spălat dinți':
        return Icons.clean_hands;
      case 'Tratament anti-purici':
        return Icons.bug_report;
      case 'Aranjat blană lungă':
        return Icons.brush;
      case 'Masaj relaxant':
        return Icons.self_improvement;
      default:
        return Icons.pets;
    }
  }

  String _getPetSpeciesText(String species) {
    switch (species) {
      case 'dog':
        return 'Câine';
      case 'cat':
        return 'Pisică';
      case 'bird':
        return 'Pasăre';
      case 'rabbit':
        return 'Iepure';
      case 'rodent':
        return 'Rozătoare';
      case 'reptile':
        return 'Reptilă';
      case 'fish':
        return 'Pește';
      default:
        return species;
    }
  }

  Widget _getPetIcon(String species) {
    IconData iconData;
    Color iconColor;

    switch (species) {
      case 'dog':
        iconData = Icons.pets;
        iconColor = Colors.brown;
        break;
      case 'cat':
        iconData = Icons.pets;
        iconColor = Colors.grey;
        break;
      case 'bird':
        iconData = Icons.flutter_dash;
        iconColor = Colors.blue;
        break;
      default:
        iconData = Icons.pets;
        iconColor = Theme.of(context).primaryColor;
    }

    return Icon(iconData, color: iconColor, size: 20);
  }

  String _getGenderText(String gender) {
    switch (gender.toLowerCase()) {
      case 'male':
      case 'masculin':
      case 'm':
        return context.tr('new_appointment.pet_gender_male');
      case 'female':
      case 'feminin':
      case 'f':
        return context.tr('new_appointment.pet_gender_female');
      default:
        return context.tr('new_appointment.pet_gender_unknown');
    }
  }

  void _showRescheduleDialog(BuildContext context) {
    DateTime selectedDate = widget.appointment.startTime;
    TimeOfDay selectedStartTime = TimeOfDay.fromDateTime(widget.appointment.startTime);
    TimeOfDay selectedEndTime = TimeOfDay.fromDateTime(widget.appointment.endTime);
    final Duration originalDuration =
        widget.appointment.endTime.difference(widget.appointment.startTime);
    String rescheduleReason = '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(context.tr('new_appointment.reschedule_appointment_dialog_title')),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Date picker
                    ListTile(
                      leading: const Icon(Icons.calendar_today),
                      title: Text(context.tr('new_appointment.reschedule_date_label')),
                      subtitle: Text(DateFormat('dd MMMM yyyy', Localizations.localeOf(context).languageCode).format(selectedDate)),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: selectedDate,
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(const Duration(days: 365)),
                        );
                        if (date != null) {
                          setState(() => selectedDate = date);
                        }
                      },
                    ),
                    // Start time picker
                    ListTile(
                      leading: const Icon(Icons.access_time),
                      title: Text(context.tr('new_appointment.reschedule_start_time_label')),
                      subtitle: Text(selectedStartTime.format(context)),
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: selectedStartTime,
                        );
                        if (time != null) {
                          setState(() {
                            selectedStartTime = time;
                            final newStartDateTime = DateTime(
                              selectedDate.year,
                              selectedDate.month,
                              selectedDate.day,
                              time.hour,
                              time.minute,
                            );
                            final newEndDateTime =
                                newStartDateTime.add(originalDuration);
                            selectedEndTime =
                                TimeOfDay.fromDateTime(newEndDateTime);
                          });
                        }
                      },
                    ),
                    // End time picker
                    ListTile(
                      leading: const Icon(Icons.access_time_filled),
                      title: Text(context.tr('new_appointment.reschedule_end_time_label')),
                      subtitle: Text(selectedEndTime.format(context)),
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: selectedEndTime,
                        );
                        if (time != null) {
                          setState(() => selectedEndTime = time);
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    // Reason field
                    TextField(
                      decoration: InputDecoration(
                        labelText: context.tr('new_appointment.reschedule_reason_label'),
                        border: const OutlineInputBorder(),
                      ),
                      maxLines: 2,
                      textCapitalization: TextCapitalization.sentences,
                      onChanged: (value) => rescheduleReason = value,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(context.tr('new_appointment.reschedule_cancel_button')),
                ),
                ElevatedButton(
                  onPressed: () => _handleReschedule(context, selectedDate, selectedStartTime, selectedEndTime, rescheduleReason),
                  style: ElevatedButton.styleFrom(backgroundColor: Theme.of(context).primaryColor),
                  child: Text(context.tr('new_appointment.reschedule_confirm_button')),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _handleReschedule(BuildContext context, DateTime date, TimeOfDay startTime, TimeOfDay endTime, String reason) async {
    // Store provider reference and context before closing dialog
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
    final navigator = Navigator.of(context);

    navigator.pop(); // Close reschedule dialog

    final newStartTime = DateTime(date.year, date.month, date.day, startTime.hour, startTime.minute);
    final newEndTime = DateTime(date.year, date.month, date.day, endTime.hour, endTime.minute);

    // Validate times
    if (newEndTime.isBefore(newStartTime) || newEndTime.isAtSameMomentAs(newStartTime)) {
      if (mounted) {
        showTopSnackBarSafe(context,
          SnackBar(
            content: Text(context.tr('new_appointment.reschedule_time_validation_error')),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    try {
      final response = await AppointmentService.rescheduleAppointment(
        widget.appointment.id,
        newStartTime: newStartTime,
        newEndTime: newEndTime,
        reason: reason.isNotEmpty ? reason : null,
      );

      if (mounted) {
        if (response.success) {
          navigator.pop(); // Close details dialog
          showTopSnackBarSafe(context,
            const SnackBar(
              content: Text('Programarea a fost reprogramată cu succes'),
              backgroundColor: Colors.green,
            ),
          );
          // Force refresh for both old and new dates
          final oldDate = widget.appointment.startTime;
          await calendarProvider.fetchAppointmentsForDate(date, forceRefresh: true);
          // Also refresh the old date if it's different
          if (oldDate.day != date.day || oldDate.month != date.month || oldDate.year != date.year) {
            await calendarProvider.fetchAppointmentsForDate(oldDate, forceRefresh: true);
          }
        } else {
          final errorMessage = response.error ?? 'Eroare necunoscută';
          showTopSnackBarSafe(context,
            SnackBar(
              content: Text('Eroare la reprogramare: $errorMessage'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorHandlingService.handleAppError(
          operation: 'reschedule_appointment',
          error: e,
          screenName: 'AppointmentDetailsDialog',
        );
        showTopSnackBarSafe(context,
          SnackBar(
            content: Text('Eroare la reprogramare: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }


  // Client action methods
  void _makePhoneCall() async {
    final success = await UrlLauncherService.makePhoneCall(widget.appointment.clientPhone);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, 'aplicația de telefon');
    }
  }

  void _sendSMS() async {
    showDialog(
      context: context,
      builder: (context) => SmsTemplateSelectorDialog(
        appointment: widget.appointment,
        messageType: 'sms',
      ),
    );
  }

  void _openWhatsApp() async {
    showDialog(
      context: context,
      builder: (context) => SmsTemplateSelectorDialog(
        appointment: widget.appointment,
        messageType: 'whatsapp',
      ),
    );
  }

  void _showCompletionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AppointmentCompletionDialog(
        appointment: widget.appointment,
      ),
    );
  }

  /// Build completion time information widget
  Widget _buildCompletionTimeInfo() {
    if (!widget.appointment.isCompleted || widget.appointment.completedAt == null) {
      return const SizedBox.shrink();
    }

    final completedAt = widget.appointment.completedAt!;
    final diffMinutes = widget.appointment.completionTimeDifferenceMinutes;
    final diffText = widget.appointment.completionTimeDifferenceText;

    Color statusColor;
    IconData statusIcon;

    if (diffMinutes == 0) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    } else if (diffMinutes > 0) {
      statusColor = Colors.red;
      statusIcon = Icons.schedule;
    } else {
      statusColor = Colors.blue;
      statusIcon = Icons.fast_forward;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                statusIcon,
                color: statusColor,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Finalizare',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: statusColor,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildDetailRow(
            'Ora finalizării:',
            DateFormat('HH:mm').format(completedAt),
            valueColor: statusColor,
          ),
          if (diffText.isNotEmpty)
            _buildDetailRow(
              'Diferență:',
              diffText,
              valueColor: statusColor,
            ),
          if (widget.appointment.actualDuration != null)
            _buildDetailRow(
              'Durata efectivă:',
              '${widget.appointment.actualDuration} minute',
              valueColor: statusColor,
            ),
        ],
      ),
    );
  }

  void _navigateToClientSubscriptions(BuildContext context) async {
    try {
      // Close the appointment details dialog first
      Navigator.of(context).pop();

      // Get the calendar provider to access client data
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);

      // Get client details
      final client = await calendarProvider.getClientById(widget.appointment.clientId);

      if (client != null && mounted) {
        // Navigate to client details page with subscriptions tab selected
        // You'll need to adjust this navigation based on your app's routing structure
        Navigator.of(context).pushNamed(
          '/client-details',
          arguments: {
            'client': client,
            'initialTab': 'subscriptions', // This should open the subscriptions tab
          },
        );
      } else if (mounted) {
        // SnackBarUtils.showError(context, 'Nu s-au putut încărca detaliile clientului');
      }
    } catch (e) {
      if (mounted) {
        // SnackBarUtils.showError(context, 'Eroare la navigarea către abonamente: $e');
      }
    }
  }

  Widget _buildPriceInformation(Map<String, Map<String, dynamic>> serviceDetails) {
    // Calculate the price based on services and pet size
    double calculatedPrice = 0.0;

    // Get pet information to determine size
    return Consumer<CalendarProvider>(
      builder: (context, calendarProvider, child) {
        final pet = calendarProvider.petsCache[widget.appointment.petId];
        // Determine pet size from breed if pet is available, otherwise default to M
        String petSize = 'M';
        if (pet != null && pet.breed.isNotEmpty) {
          // For now, use a simple heuristic based on breed name
          // In a real app, this would call the BreedService
          petSize = _determinePetSizeFromBreed(pet.breed);
        }

        // Get all services for this appointment
        final allServices = widget.appointment.services.isNotEmpty
            ? widget.appointment.services
            : [widget.appointment.service].where((s) => s.isNotEmpty).toList();

        // Calculate price for each service
        for (final serviceName in allServices) {
          // Check if there's a custom service for this service name
          final customService = widget.appointment.customServices?[serviceName];

          if (customService != null) {
            // Use custom price
            calculatedPrice += (customService['customPrice'] as num?)?.toDouble() ?? 50.0;
          } else {
            // Use original service price
            final serviceDetail = serviceDetails[serviceName];
            if (serviceDetail != null) {
              try {
                final service = Service.fromJson(serviceDetail);
                calculatedPrice += service.getPriceForSize(petSize);
              } catch (e) {
                // Fallback to basic price calculation
                if (serviceDetail['sizePrices'] != null && serviceDetail['sizePrices'] is Map) {
                  final prices = Map<String, double>.from(serviceDetail['sizePrices']);
                  calculatedPrice += (prices[petSize] ?? serviceDetail['price'] ?? 50.0);
                } else {
                  calculatedPrice += (serviceDetail['price'] ?? 50.0);
                }
              }
            } else {
              calculatedPrice += 50.0; // Default price
            }
          }
        }

        // Use appointment's totalPrice if available, otherwise use calculated price
        final displayPrice = widget.appointment.totalPrice > 0
            ? widget.appointment.totalPrice
            : calculatedPrice;

        return _buildPriceCard(displayPrice, petSize);
      },
    );
  }

  Widget _buildPriceCard(double price, String petSize) {
    // Check if this is a subscription appointment with discount
    final isSubscription = widget.appointment.isRecurring && widget.appointment.subscriptionId != null;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payments,
                color: Theme.of(context).colorScheme.primary,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                context.tr('new_appointment.price_info_short'),
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          if (isSubscription) ...[
            // Show subscription pricing with discount info
            _buildSubscriptionPricing(price),
          ] else ...[
            // Show regular pricing
            _buildDetailRow(
              context.tr('new_appointment.total_price_short') + ':',
              '${price.toStringAsFixed(2)} RON',
              valueColor: Theme.of(context).colorScheme.primary,
            ),
            _buildDetailRow(
              context.tr('new_appointment.pet_size_short') + ':',
              _getPetSizeDisplayName(petSize),
              valueColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSubscriptionPricing(double finalPrice) {
    return FutureBuilder<Map<String, dynamic>?>(
      future: _getSubscriptionPricingDetails(),
      builder: (context, snapshot) {
        // DEBUG: Log snapshot state
        if (snapshot.hasError) {
        }

        final subscriptionData = snapshot.data;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Show original price if we have subscription data with discount
            if (subscriptionData != null && subscriptionData['hasDiscount'] == true) ...[
              _buildDetailRow(
                'Preț original:',
                '${subscriptionData['originalPrice'].toStringAsFixed(2)} RON',
                valueColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                strikethrough: true,
              ),
              _buildDetailRow(
                'Reducere:',
                '-${subscriptionData['discountPercentage'].toStringAsFixed(1)}% (${subscriptionData['discountAmount'].toStringAsFixed(2)} RON)',
                valueColor: Colors.green,
              ),
              _buildDetailRow(
                'Preț cu reducere:',
                '${subscriptionData['finalPrice'].toStringAsFixed(2)} RON', // Use discounted price from subscription data
                valueColor: Theme.of(context).colorScheme.primary,
                isBold: true,
              ),
            ] else ...[
              _buildDetailRow(
                context.tr('new_appointment.total_price_short') + ':',
                '${finalPrice.toStringAsFixed(2)} RON',
                valueColor: Theme.of(context).colorScheme.primary,
              ),
            ],

            if (widget.appointment.sequenceNumber != null)
              _buildDetailRow(
                'Programare nr.:',
                '#${widget.appointment.sequenceNumber}',
                valueColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),

            // Show subscription badge
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.repeat,
                    size: 12,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    subscriptionData != null && subscriptionData['hasDiscount'] == true
                        ? 'Abonament cu reducere'
                        : 'Parte din abonament',
                    style: TextStyle(
                      fontSize: 11,
                      color: Theme.of(context).colorScheme.secondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // Show savings summary if there's a discount
            if (subscriptionData != null && subscriptionData['hasDiscount'] == true) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.savings,
                      size: 12,
                      color: Colors.green,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Economisești ${subscriptionData['discountAmount'].toStringAsFixed(2)} RON',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.green,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  String _getPetSizeDisplayName(String size) {
    switch (size.toUpperCase()) {
      case 'S':
        return 'Mic (S)';
      case 'M':
        return 'Mediu (M)';
      case 'L':
        return 'Mare (L)';
      case 'XL':
        return 'Extra Mare (XL)';
      default:
        return 'Mediu (M)';
    }
  }

  String _determinePetSizeFromBreed(String breed) {
    // Simple heuristic based on common breed names
    // In a real implementation, this would call the BreedService API
    final breedLower = breed.toLowerCase();

    // Small breeds
    if (breedLower.contains('chihuahua') ||
        breedLower.contains('yorkshire') ||
        breedLower.contains('maltese') ||
        breedLower.contains('pomeranian') ||
        breedLower.contains('papillon') ||
        breedLower.contains('toy') ||
        breedLower.contains('mini')) {
      return 'S';
    }

    // Large breeds
    if (breedLower.contains('german shepherd') ||
        breedLower.contains('labrador') ||
        breedLower.contains('golden retriever') ||
        breedLower.contains('rottweiler') ||
        breedLower.contains('doberman') ||
        breedLower.contains('great dane') ||
        breedLower.contains('mastiff') ||
        breedLower.contains('saint bernard') ||
        breedLower.contains('ciobănesc') ||
        breedLower.contains('pastor')) {
      return 'L';
    }

    // Default to medium for unknown breeds
    return 'M';
  }

  Future<Map<String, dynamic>?> _getSubscriptionPricingDetails() async {

    if (!widget.appointment.isRecurring || widget.appointment.subscriptionId == null) {
      return null;
    }


    try {
      // Get current salon ID from auth service
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return _getFallbackPricingData();
      }


      // Fetch actual subscription data from backend

      final subscription = await AppointmentSubscriptionService.getSubscriptionByAppointmentId(
        salonId,
        widget.appointment.id,
      );
      
      if (subscription != null) {
        // Check if this subscription has upfront payment with discount
        final hasDiscount = subscription.paymentModel == 'UPFRONT_WITH_DISCOUNT' &&
                           subscription.discountPercentage != null &&
                           subscription.discountPercentage! > 0;
        
        if (hasDiscount) {
          final discountPercentage = subscription.discountPercentage!;

          // subscription.appointmentPrice is the ORIGINAL price per appointment (before discount)
          final originalPrice = subscription.appointmentPrice;

          // Calculate the discounted price per appointment
          final discountedPrice = originalPrice * (1 - discountPercentage / 100);
          final discountAmount = originalPrice - discountedPrice;

          final result = {
            'hasDiscount': true,
            'originalPrice': originalPrice,
            'discountPercentage': discountPercentage,
            'discountAmount': discountAmount,
            'finalPrice': discountedPrice, // This is the actual discounted price
            'paymentModel': subscription.paymentModel,
          };

          return result;
        }
      }

      final result = {
        'hasDiscount': false,
        'finalPrice': widget.appointment.totalPrice,
        'paymentModel': subscription?.paymentModel ?? 'PER_APPOINTMENT',
      };

      return result;
    } catch (e) {
      // Fallback to mock data if backend call fails
      final fallbackData = _getFallbackPricingData();
      return fallbackData;
    }
  }

  Map<String, dynamic> _getFallbackPricingData() {

    // Fallback logic for when backend is not available
    final hasDiscount = widget.appointment.sequenceNumber != null;

    if (hasDiscount) {
      // For fallback, we don't have the original price, so we can't show a proper discount
      // Just show that it's part of a subscription without fake discount calculation
      final result = {
        'hasDiscount': false, // Don't show fake discount
        'finalPrice': widget.appointment.totalPrice,
        'paymentModel': 'UPFRONT_WITH_DISCOUNT',
      };
      return result;
    }

    final result = {
      'hasDiscount': false,
      'finalPrice': widget.appointment.totalPrice,
      'paymentModel': 'PER_APPOINTMENT',
    };
    return result;
  }

  Widget _buildSingleServiceRow(String serviceName) {
    final customService = widget.appointment.customServices?[serviceName];
    final displayName = customService?['customName'] as String? ?? serviceName;
    return _buildDetailRow(context.tr('new_appointment.service_single_label') + ':', displayName);
  }
}

// Photo Viewer Screen for full-screen photo viewing
class _PhotoViewerScreen extends StatefulWidget {
  final List<String> photos;
  final int initialIndex;
  final Function(int) onDelete;

  const _PhotoViewerScreen({
    required this.photos,
    required this.initialIndex,
    required this.onDelete,
  });

  @override
  State<_PhotoViewerScreen> createState() => _PhotoViewerScreenState();
}

class _PhotoViewerScreenState extends State<_PhotoViewerScreen> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          '${_currentIndex + 1} din ${widget.photos.length}',
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          IconButton(
            onPressed: () => _showDeleteConfirmation(),
            icon: const Icon(Icons.delete, color: Colors.white),
          ),
        ],
      ),
      body: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemCount: widget.photos.length,
        itemBuilder: (context, index) {
          return InteractiveViewer(
            minScale: 0.5,
            maxScale: 3.0,
            child: Center(
              child: CrossPlatformImage(
                networkUrl: widget.photos[index],
                fit: BoxFit.contain,
                placeholder: const Center(child: CircularProgressIndicator()),
                errorWidget: const Icon(Icons.error, size: 64),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.tr('new_appointment.delete_photo_title')),
          content: Text(context.tr('new_appointment.delete_photo_confirmation')),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.tr('common.cancel')),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.onDelete(_currentIndex);
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: Text(context.tr('common.delete'), style: const TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }
}
