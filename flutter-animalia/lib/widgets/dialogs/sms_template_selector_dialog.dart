import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../l10n/app_localizations.dart';
import '../../models/sms_template.dart';
import '../../models/appointment.dart';
import '../../services/sms_template_service.dart';
import '../../services/url_launcher_service.dart';
import '../../services/salon_service.dart';

/// Dialog for selecting SMS templates when sending messages
class SmsTemplateSelectorDialog extends StatefulWidget {
  final Appointment appointment;
  final String messageType; // 'sms' or 'whatsapp'

  const SmsTemplateSelectorDialog({
    super.key,
    required this.appointment,
    required this.messageType,
  });

  @override
  State<SmsTemplateSelectorDialog> createState() => _SmsTemplateSelectorDialogState();
}

class _SmsTemplateSelectorDialogState extends State<SmsTemplateSelectorDialog> {
  List<SmsTemplate> _templates = [];
  bool _loading = true;
  String? _error;
  SmsTemplate? _selectedTemplate;
  String _previewMessage = '';

  // Salon and owner information
  String _salonName = '';
  String _ownerName = '';
  String _salonAddress = '';
  String _salonPhone = '';

  @override
  void initState() {
    super.initState();
    _loadSalonData();
    _loadTemplates();
  }

  Future<void> _loadSalonData() async {
    try {
      // Get current salon information
      final salonResponse = await SalonService.getCurrentUserSalon();
      if (salonResponse.success && salonResponse.data != null) {
        final salon = salonResponse.data!;
        setState(() {
          _salonName = salon.name;
          _salonAddress = salon.address.isNotEmpty ? salon.address : context.tr('sms_template.default_salon_address');
          _salonPhone = salon.phone ?? context.tr('sms_template.default_salon_phone');
        });

        // For owner name, we'll use the client name from the appointment
        // since the template variable {OWNER_NAME} should refer to the pet owner (client)
        setState(() {
          _ownerName = widget.appointment.clientName;
        });
      }
    } catch (e) {
      // If we can't load salon data, keep the default values
      print('Failed to load salon data: $e');
    }
  }

  Future<void> _loadTemplates() async {
    setState(() {
      _loading = true;
      _error = null;
    });

    try {
      final response = await SmsTemplateService.getSmsTemplates(activeOnly: true);
      if (response.success && response.data != null) {
        setState(() {
          _templates = response.data!;
          _loading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? context.tr('sms_template.load_templates_error');
          _loading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = context.tr('sms_template.load_error_with_details', params: {'error': e.toString()});
        _loading = false;
      });
    }
  }

  void _selectTemplate(SmsTemplate template) {
    setState(() {
      _selectedTemplate = template;
      _previewMessage = _renderTemplate(template);
    });
  }

  String _renderTemplate(SmsTemplate template) {
    // Create variables map for the appointment
    final variables = <String, String>{
      'SALON_NAME': _salonName.isNotEmpty ? _salonName : context.tr('sms_template.default_salon_name'),
      'OWNER_NAME': _ownerName.isNotEmpty ? _ownerName : context.tr('sms_template.default_owner_name'),
      'PET_NAME': widget.appointment.petName,
      'APPOINTMENT_DATE': _formatDate(widget.appointment.startTime),
      'APPOINTMENT_TIME': _formatTime(widget.appointment.startTime),
      'SALON_ADDRESS': _salonAddress.isNotEmpty ? _salonAddress : context.tr('sms_template.default_salon_address'),
      'SALON_PHONE': _salonPhone.isNotEmpty ? _salonPhone : context.tr('sms_template.default_salon_phone'),
      'SERVICE_NAME': widget.appointment.services.isNotEmpty
          ? widget.appointment.services.first
          : context.tr('sms_template.default_service_name'),
    };

    return template.renderMessage(variables);
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}.${dateTime.month.toString().padLeft(2, '0')}.${dateTime.year}';
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _sendMessage() async {
    if (_selectedTemplate == null) return;

    final message = _previewMessage;
    bool success = false;

    if (widget.messageType == 'sms') {
      success = await UrlLauncherService.sendSMS(
        widget.appointment.clientPhone,
        message: message,
      );
    } else if (widget.messageType == 'whatsapp') {
      success = await UrlLauncherService.openWhatsApp(
        widget.appointment.clientPhone,
        message: message,
      );
    }

    if (mounted) {
      Navigator.of(context).pop();

      if (!success) {
        final appName = widget.messageType == 'sms' 
            ? context.tr('sms_template.sms_app_name') 
            : context.tr('sms_template.whatsapp_app_name');
        UrlLauncherService.showLaunchError(context, appName);
      }
    }
  }

  Future<void> _sendWithoutTemplate() async {
    final defaultMessage = context.tr('sms_template.default_message_greeting', params: {'clientName': widget.appointment.clientName});
    bool success = false;

    if (widget.messageType == 'sms') {
      success = await UrlLauncherService.sendSMS(
        widget.appointment.clientPhone,
        message: defaultMessage,
      );
    } else if (widget.messageType == 'whatsapp') {
      success = await UrlLauncherService.openWhatsApp(
        widget.appointment.clientPhone,
        message: defaultMessage,
      );
    }

    if (mounted) {
      Navigator.of(context).pop();

      if (!success) {
        final appName = widget.messageType == 'sms' 
            ? context.tr('sms_template.sms_app_name') 
            : context.tr('sms_template.whatsapp_app_name');
        UrlLauncherService.showLaunchError(context, appName);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isWhatsApp = widget.messageType == 'whatsapp';
    final title = isWhatsApp ? context.tr('sms_template.whatsapp_title') : context.tr('sms_template.sms_title');
    final icon = isWhatsApp ? FontAwesomeIcons.whatsapp : Icons.message;
    final color = isWhatsApp ? const Color(0xFF25D366) : Colors.blue;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(icon, color: color, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: _loading
                  ? const Center(child: CircularProgressIndicator())
                  : _error != null
                      ? _buildErrorState()
                      : _buildTemplateList(),
            ),

            // Preview section
            if (_selectedTemplate != null) _buildPreviewSection(),

            // Action buttons
            _buildActionButtons(color),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text(
            _error!,
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.red[700]),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadTemplates,
            child: Text(context.tr('sms_template.retry_button')),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateList() {
    if (_templates.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.note_outlined, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              context.tr('sms_template.no_templates_available'),
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _templates.length,
      itemBuilder: (context, index) {
        final template = _templates[index];
        final isSelected = _selectedTemplate?.id == template.id;

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          elevation: isSelected ? 4 : 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: isSelected
                ? BorderSide(color: Theme.of(context).primaryColor, width: 2)
                : BorderSide.none,
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            title: Text(
              context.tr(template.templateType.translationKey),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                Text(
                  template.templateContent,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
            trailing: isSelected
                ? Icon(Icons.check_circle, color: Theme.of(context).primaryColor)
                : const Icon(Icons.radio_button_unchecked),
            onTap: () => _selectTemplate(template),
          ),
        );
      },
    );
  }

  Widget _buildPreviewSection() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey[800]
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode
              ? Colors.grey[600]!
              : Colors.grey[300]!
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr('sms_template.preview_message'),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _previewMessage,
            style: TextStyle(
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(Color color) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _sendWithoutTemplate,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                context.tr('sms_template.send_without_template'),
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _selectedTemplate != null ? _sendMessage : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                context.tr('sms_template.send_button'),
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
