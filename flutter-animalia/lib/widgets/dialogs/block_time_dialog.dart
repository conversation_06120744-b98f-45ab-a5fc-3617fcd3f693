import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/block_time_result.dart';
import '../../models/user_role.dart';
import '../../providers/calendar_provider.dart';
import '../../services/auth/auth_service.dart';
import '../../services/staff_service.dart';
import '../../services/ui_notification_service.dart';
import '../../utils/debug_logger.dart';


/// Production-ready block time dialog for the Animalia grooming salon management app
///
/// Features:
/// - Intuitive UX with Material Design and forest green theme
/// - Minimal clicks (3 or fewer) to complete blocking
/// - Smart defaults (current date, 1-hour duration, current user)
/// - Quick action presets (30min, 1hr, 2hr, half-day, full-day)
/// - Staff selection with current user pre-selected
/// - Native date/time pickers with Romanian localization
/// - Predefined reason chips with custom input option
/// - Visual confirmation before saving
/// - Overlap validation and warnings
/// - Responsive design for phone and tablet
/// - Accessibility support
/// - Calendar provider integration with auto-refresh
class BlockTimeDialog extends StatefulWidget {
  final DateTime selectedDate;
  final String? preselectedStaffId;
  final TimeOfDay? preselectedStartTime;

  const BlockTimeDialog({
    Key? key,
    required this.selectedDate,
    this.preselectedStaffId,
    this.preselectedStartTime,
  }) : super(key: key);

  @override
  State<BlockTimeDialog> createState() => _BlockTimeDialogState();
}

class _BlockTimeDialogState extends State<BlockTimeDialog> with TickerProviderStateMixin {
  // Form data
  late DateTime _selectedDate;
  DateTime? _selectedEndDate; // For multi-day support
  late TimeOfDay _startTime;
  late TimeOfDay _endTime;
  String _selectedReason = 'Pauză';
  String _customReason = '';
  List<String> _selectedStaffIds = [];
  bool _isMultiDay = false;

  // Available data
  List<StaffResponse> _availableStaff = [];
  bool _isLoadingStaff = false;

  // UI state
  bool _isLoading = false;
  late TabController _tabController;
  String? _selectedQuickAction; // Track selected quick action

  // Enhanced quick action presets
  final List<Map<String, dynamic>> _quickActions = [
    {'label': 'calendar.quick_action_30_min', 'duration': 30, 'icon': Icons.timer, 'id': '30min'},
    {'label': 'calendar.quick_action_1_hour', 'duration': 60, 'icon': Icons.schedule, 'id': '1hour'},
    {'label': 'calendar.quick_action_2_hours', 'duration': 120, 'icon': Icons.access_time, 'id': '2hours'},
    {'label': 'calendar.quick_action_first_half', 'duration': 240, 'icon': Icons.wb_sunny_outlined, 'id': 'first_half', 'type': 'first_half'},
    {'label': 'calendar.quick_action_second_half', 'duration': 240, 'icon': Icons.wb_sunny, 'id': 'second_half', 'type': 'second_half'},
    {'label': 'calendar.quick_action_full_day', 'duration': 480, 'icon': Icons.brightness_1, 'id': 'full_day', 'type': 'full_day'},
  ];

  // Reason categories
  final List<String> _reasonCategories = [
    'calendar.reason_break',
    'calendar.reason_meeting',
    'calendar.reason_vacation',
    'calendar.reason_personal',
    'calendar.reason_other'
  ];

  @override
  void initState() {
    super.initState();
    _initializeDefaults();
    _loadStaff();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Initialize smart defaults
  void _initializeDefaults() {
    _selectedDate = widget.selectedDate;

    // Smart time defaults
    final now = TimeOfDay.now();
    if (widget.preselectedStartTime != null) {
      _startTime = widget.preselectedStartTime!;
    } else {
      // Round to next 15-minute interval
      final minutes = (now.minute / 15).ceil() * 15;
      if (minutes >= 60) {
        _startTime = TimeOfDay(hour: (now.hour + 1) % 24, minute: 0);
      } else {
        _startTime = TimeOfDay(hour: now.hour, minute: minutes);
      }
    }

    // Default 1-hour duration
    _endTime = _addDurationToTime(_startTime, 60);

    // Initialize default reason with translated key
    _selectedReason = 'calendar.reason_break';
  }

  /// Load available staff and pre-select current user
  Future<void> _loadStaff() async {
    setState(() {
      _isLoadingStaff = true;
    });

    try {
      final response = await StaffService.getCurrentSalonStaff(activeOnly: true);
      if (response.success && response.data != null) {
        setState(() {
          _availableStaff = response.data!.activeStaff;

          // Enhanced current user pre-selection
          if (widget.preselectedStaffId != null) {
            _selectedStaffIds = [widget.preselectedStaffId!];
          } else {
            // Try to get current user ID from AuthService
            _preSelectCurrentUser();
          }
        });
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error loading staff: $e');
    } finally {
      setState(() {
        _isLoadingStaff = false;
      });
    }
  }

  /// Pre-select current user based on AuthService
  Future<void> _preSelectCurrentUser() async {
    try {
      final currentUserId = await AuthService.getCurrentUserId();
      if (currentUserId != null) {
        // Find staff member with matching user ID
        final currentStaff = _availableStaff.firstWhere(
          (staff) => staff.userId == currentUserId || staff.id == currentUserId,
          orElse: () => _availableStaff.isNotEmpty ? _availableStaff.first :
            StaffResponse(
              id: 'default',
              name: 'Utilizator curent',
              phone: '',
              email: '',
              groomerRole: GroomerRole.groomer,
              clientDataPermission: ClientDataPermission.fullAccess,
              isActive: true,
              joinedAt: DateTime.now(),
            ),
        );
        _selectedStaffIds = [currentStaff.id];
        DebugLogger.logShowcase('🎯 Pre-selected current user: ${currentStaff.displayName} (${currentStaff.id})');
      } else {
        // Fallback to first active staff member
        if (_availableStaff.isNotEmpty) {
          _selectedStaffIds = [_availableStaff.first.id];
          DebugLogger.logShowcase('🎯 Fallback: Pre-selected first staff member: ${_availableStaff.first.displayName}');
        }
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error pre-selecting current user: $e');
      // Fallback to first staff member
      if (_availableStaff.isNotEmpty) {
        _selectedStaffIds = [_availableStaff.first.id];
      }
    }
  }

  /// Add duration in minutes to a TimeOfDay
  TimeOfDay _addDurationToTime(TimeOfDay time, int minutes) {
    final totalMinutes = time.hour * 60 + time.minute + minutes;
    return TimeOfDay(
      hour: (totalMinutes ~/ 60) % 24,
      minute: totalMinutes % 60,
    );
  }

  /// Apply quick action preset with enhanced logic
  void _applyQuickAction(Map<String, dynamic> action) {
    setState(() {
      final actionId = action['id'] as String;
      final actionType = action['type'] as String?;
      final durationMinutes = action['duration'] as int;

      _selectedQuickAction = actionId;

      switch (actionType) {
        case 'first_half':
          // First half of day: 8:00-12:00
          _startTime = const TimeOfDay(hour: 8, minute: 0);
          _endTime = const TimeOfDay(hour: 12, minute: 0);
          break;
        case 'second_half':
          // Second half of day: 12:00-18:00
          _startTime = const TimeOfDay(hour: 12, minute: 0);
          _endTime = const TimeOfDay(hour: 18, minute: 0);
          break;
        case 'full_day':
          // Full day: 8:00-18:00
          _startTime = const TimeOfDay(hour: 8, minute: 0);
          _endTime = const TimeOfDay(hour: 18, minute: 0);
          break;
        default:
          // Duration-based actions (30min, 1hr, 2hr)
          _endTime = _addDurationToTime(_startTime, durationMinutes);
          break;
      }
    });
  }

  /// Get duration in minutes between start and end time
  int _getDurationMinutes() {
    final startMinutes = _startTime.hour * 60 + _startTime.minute;
    final endMinutes = _endTime.hour * 60 + _endTime.minute;
    return endMinutes - startMinutes;
  }

  /// Format duration for display
  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes ${AppLocalizations.of(context).translate('calendar.minutes_short')}';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours ${hours == 1 ? AppLocalizations.of(context).translate('calendar.hour_singular') : AppLocalizations.of(context).translate('calendar.hours_plural')}';
      } else {
        return '$hours:${remainingMinutes.toString().padLeft(2, '0')} ${AppLocalizations.of(context).translate('calendar.hours_plural')}';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 600,
          maxHeight: MediaQuery.of(context).size.height * 0.85,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Expanded(
              child: _buildContent(),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  /// Build dialog header with title and close button
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration:  BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.block,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
           Expanded(
            child: Text(
              AppLocalizations.of(context)!.translate('calendar.block_time_title'),
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
            ),
            tooltip: AppLocalizations.of(context)!.translate('calendar.close'),
          ),
        ],
      ),
    );
  }

  /// Build main content area with tabs
  Widget _buildContent() {
    return Column(
      children: [
        // Tab bar
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade300),
            ),
          ),
          child: TabBar(
            controller: _tabController,
            labelColor: Theme.of(context).colorScheme.primary,
            unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
            indicatorColor: Theme.of(context).colorScheme.primary,
            tabs: [
              Tab(
                icon: Icon(Icons.schedule),
                text: AppLocalizations.of(context)!.translate('calendar.time_tab'),
              ),
              Tab(
                icon: Icon(Icons.people),
                text: AppLocalizations.of(context)!.translate('calendar.team_tab'),
              ),
              Tab(
                icon: Icon(Icons.info),
                text: AppLocalizations.of(context)!.translate('calendar.details_tab'),
              ),
            ],
          ),
        ),
        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildTimeTab(),
              _buildStaffTab(),
              _buildDetailsTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// Build time selection tab
  Widget _buildTimeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick actions
          Text(
            AppLocalizations.of(context)!.translate('calendar.quick_actions'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _quickActions.map((action) {
              final isSelected = _selectedQuickAction == action['id'];
              return ActionChip(
                avatar: Icon(
                  action['icon'],
                  size: 18,
                  color: isSelected
                    ? Colors.white
                    : Theme.of(context).colorScheme.onSurface,
                ),
                label: Text(
                  AppLocalizations.of(context)!.translate(action['label']),
                  style: TextStyle(
                    color: isSelected ? Colors.white : null,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                onPressed: () => _applyQuickAction(action),
                backgroundColor: isSelected
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).colorScheme.surfaceVariant,
                side: BorderSide(
                  color: isSelected
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).colorScheme.onSurface,
                  width: isSelected ? 2 : 1,
                ),
                elevation: isSelected ? 4 : 0,
              );
            }).toList(),
          ),
          const SizedBox(height: 24),

          // Multi-day toggle
          Row(
            children: [
               Text(
                AppLocalizations.of(context)!.translate('calendar.multi_day_blocking'),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const Spacer(),
              Switch(
                value: _isMultiDay,
                onChanged: (value) {
                  setState(() {
                    _isMultiDay = value;
                    if (!value) {
                      _selectedEndDate = null;
                    } else {
                      _selectedEndDate = _selectedDate.add(const Duration(days: 1));
                    }
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Date selection
          Text(
            _isMultiDay ? AppLocalizations.of(context)!.translate('calendar.period') : AppLocalizations.of(context)!.translate('calendar.date'),
            style:  TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
           SizedBox(height: 8),
          if (!_isMultiDay) ...[
            // Single date selection
            InkWell(
              onTap: _selectDate,
              child: Container(
                padding:  EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                     Icon(
                      Icons.calendar_today,
                      color: Theme.of(context).primaryColor,
                    ),
                     SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        DateFormat('EEEE, dd MMMM yyyy', Localizations.localeOf(context).languageCode).format(_selectedDate),
                        style:  TextStyle(fontSize: 16),
                      ),
                    ),
                     Icon(
                      Icons.arrow_drop_down,
                      color: Colors.grey,
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            // Multi-day date range selection
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: _selectStartDate,
                    child: Container(
                      padding:  EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                           Text(
                            'De la',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                           SizedBox(height: 4),
                          Row(
                            children: [
                               Icon(Icons.calendar_today,
                                color: Theme.of(context).primaryColor, size: 18),
                               SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  DateFormat('dd MMM yyyy', Localizations.localeOf(context).languageCode).format(_selectedDate),
                                  style:  TextStyle(fontSize: 14),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                 SizedBox(width: 12),
                Expanded(
                  child: InkWell(
                    onTap: _selectEndDate,
                    child: Container(
                      padding:  EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                           Text(
                            'Până la',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                           SizedBox(height: 4),
                          Row(
                            children: [
                               Icon(Icons.calendar_today,
                                color: Theme.of(context).primaryColor, size: 18),
                               SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _selectedEndDate != null
                                    ? DateFormat('dd MMM yyyy', Localizations.localeOf(context).languageCode).format(_selectedEndDate!)
                                    : 'Selectează',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: _selectedEndDate != null ? null : Colors.grey,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
           SizedBox(height: 24),

          // Time selection
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.translate('calendar.start_time'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                     SizedBox(height: 8),
                    InkWell(
                      onTap: () => _selectStartTime(),
                      child: Container(
                        padding:  EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                             Icon(
                              Icons.access_time,
                              color: Theme.of(context).primaryColor,
                            ),
                             SizedBox(width: 12),
                            Text(
                              _startTime.format(context),
                              style:  TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
               SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.translate('calendar.end_time'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                     SizedBox(height: 8),
                    InkWell(
                      onTap: () => _selectEndTime(),
                      child: Container(
                        padding:  EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                             Icon(
                              Icons.access_time,
                              color: Theme.of(context).primaryColor,
                            ),
                             SizedBox(width: 12),
                            Text(
                              _endTime.format(context),
                              style:  TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
           SizedBox(height: 16),

          // Duration display
          Container(
            padding:  EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Theme.of(context).primaryColor.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                 Icon(
                  Icons.timer,
                  color: Theme.of(context).primaryColor,
                ),
                 SizedBox(width: 12),
                Text(
                  '${AppLocalizations.of(context).translate('calendar.duration')}: ${_formatDuration(_getDurationMinutes())}',
                  style:  TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build staff selection tab
  Widget _buildStaffTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.translate('calendar.select_team_members'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context)!.translate('calendar.time_will_be_blocked'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),

          if (_isLoadingStaff) ...[
            Center(
              child: CircularProgressIndicator(
                color: Theme.of(context).primaryColor,
              ),
            ),
          ] else if (_availableStaff.isEmpty) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.warning,
                    color: Colors.orange,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context)!.translate('calendar.no_team_members_available'),
                      style: const TextStyle(color: Colors.orange),
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            // Staff list
            ...(_availableStaff.map((staff) {
              final isSelected = _selectedStaffIds.contains(staff.id);
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: CheckboxListTile(
                  value: isSelected,
                  onChanged: (value) {
                    setState(() {
                      if (value == true) {
                        if (!_selectedStaffIds.contains(staff.id)) {
                          _selectedStaffIds.add(staff.id);
                        }
                      } else {
                        _selectedStaffIds.remove(staff.id);
                      }
                    });
                  },
                  activeColor: Theme.of(context).primaryColor,
                  title: Text(
                    staff.displayName,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  subtitle: Text(
                    staff.groomerRole.displayName,
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                  secondary: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getStaffColor(staff),
                      shape: BoxShape.circle,
                      border: Border.all(color: Theme.of(context).colorScheme.outline),
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(
                      color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.outline,
                    ),
                  ),
                  tileColor: isSelected
                    ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                    : null,
                ),
              );
            }).toList()),

            const SizedBox(height: 16),

            // Select all/none buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedStaffIds = _availableStaff.map((s) => s.id).toList();
                      });
                    },
                    icon: const Icon(Icons.select_all),
                    label: Text(AppLocalizations.of(context)!.translate('calendar.select_all')),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Theme.of(context).primaryColor,
                      side: BorderSide(color: Theme.of(context).primaryColor),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedStaffIds.clear();
                      });
                    },
                    icon: const Icon(Icons.clear),
                    label: Text(AppLocalizations.of(context)!.translate('calendar.deselect')),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey,
                      side: const BorderSide(color: Colors.grey),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Build details tab with reason selection
  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.translate('calendar.blocking_reason'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context)!.translate('calendar.select_blocking_reason'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),

          // Reason chips
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _reasonCategories.map((reason) {
              final isSelected = _selectedReason == reason;
              return FilterChip(
                label: Text(AppLocalizations.of(context)!.translate(reason)),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _selectedReason = reason;
                    if (reason != 'calendar.reason_other') {
                      _customReason = '';
                    }
                  });
                },
                selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
                checkmarkColor: Theme.of(context).primaryColor,
                side: BorderSide(
                  color: isSelected
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade300,
                ),
              );
            }).toList(),
          ),

          // Custom reason input
          if (_selectedReason == 'calendar.reason_other') ...[
            const SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.translate('calendar.custom_reason'),
                hintText: AppLocalizations.of(context)!.translate('calendar.enter_blocking_reason'),
                border: const OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.edit,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              maxLines: 2,
              textCapitalization: TextCapitalization.sentences,
              onChanged: (value) {
                setState(() {
                  _customReason = value;
                });
              },
            ),
          ],

          const SizedBox(height: 24),

          // Summary card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.summarize,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context)!.translate('calendar.block_summary'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildSummaryRow(
                  _isMultiDay ? '${AppLocalizations.of(context)!.translate('calendar.period')}:' : '${AppLocalizations.of(context)!.translate('calendar.date')}:',
                  _isMultiDay && _selectedEndDate != null
                    ? '${DateFormat('dd MMM yyyy', Localizations.localeOf(context).languageCode).format(_selectedDate)} - ${DateFormat('dd MMM yyyy', Localizations.localeOf(context).languageCode).format(_selectedEndDate!)}'
                    : DateFormat('EEEE, dd MMMM yyyy', Localizations.localeOf(context).languageCode).format(_selectedDate),
                ),
                _buildSummaryRow(
                  '${AppLocalizations.of(context)!.translate('calendar.duration')}:',
                  '${_startTime.format(context)} - ${_endTime.format(context)}',
                ),
                _buildSummaryRow(
                  '${AppLocalizations.of(context)!.translate('calendar.duration')}:',
                  _formatDuration(_getDurationMinutes()),
                ),
                _buildSummaryRow(
                  '${AppLocalizations.of(context)!.translate('calendar.team_tab')}:',
                  _selectedStaffIds.isEmpty
                    ? AppLocalizations.of(context)!.translate('calendar.no_member_selected')
                    : '${_selectedStaffIds.length} ${_selectedStaffIds.length == 1 ? AppLocalizations.of(context)!.translate('calendar.member') : AppLocalizations.of(context)!.translate('calendar.members')}',
                ),
                _buildSummaryRow(
                  '${AppLocalizations.of(context)!.translate('calendar.reason')}:',
                  _selectedReason == 'calendar.reason_other' && _customReason.isNotEmpty
                    ? _customReason
                    : AppLocalizations.of(context)!.translate(_selectedReason),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build summary row for details tab
  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  /// Get staff color for visual identification
  Color _getStaffColor(StaffResponse staff) {
    return AppTheme.getStaffColor(staff.id);
  }

  /// Select date (single day mode)
  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: Localizations.localeOf(context),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme:  ColorScheme.light(
              primary: Theme.of(context).primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
        _selectedQuickAction = null; // Clear quick action selection when date changes
      });
    }
  }

  /// Select start date (multi-day mode)
  Future<void> _selectStartDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: Localizations.localeOf(context),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme:  ColorScheme.light(
              primary: Theme.of(context).primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
        // Ensure end date is after start date
        if (_selectedEndDate != null && _selectedEndDate!.isBefore(picked)) {
          _selectedEndDate = picked.add(const Duration(days: 1));
        }
        _selectedQuickAction = null; // Clear quick action selection
      });
    }
  }

  /// Select end date (multi-day mode)
  Future<void> _selectEndDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedEndDate ?? _selectedDate.add(const Duration(days: 1)),
      firstDate: _selectedDate,
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: Localizations.localeOf(context),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme:  ColorScheme.light(
              primary: Theme.of(context).primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedEndDate = picked;
        _selectedQuickAction = null; // Clear quick action selection
      });
    }
  }

  /// Select start time
  Future<void> _selectStartTime() async {
    final picked = await showTimePicker(
      context: context,
      initialTime: _startTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme:  ColorScheme.light(
              primary: Theme.of(context).primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startTime = picked;
        // Ensure end time is after start time
        if (_getDurationMinutes() <= 0) {
          _endTime = _addDurationToTime(_startTime, 60);
        }
      });
    }
  }

  /// Select end time
  Future<void> _selectEndTime() async {
    final picked = await showTimePicker(
      context: context,
      initialTime: _endTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme:  ColorScheme.light(
              primary: Theme.of(context).primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _endTime = picked;
        // Ensure end time is after start time
        if (_getDurationMinutes() <= 0) {
          _startTime = _addDurationToTime(_endTime, -60);
        }
      });
    }
  }

  /// Build footer with action buttons
  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: [
          // Cancel button
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: const BorderSide(color: Colors.grey),
              ),
              child: const Text(
                'Anulează',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Save button
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _validateAndSave,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Blochează timp',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
            ),
          ),
        ],
      ),
    );
  }

  /// Validate form and save block time
  Future<void> _validateAndSave() async {
    // Validation
    final validationError = _validateForm();
    if (validationError != null) {
      UINotificationService.showBlockTimeError(
        context: context,
        errorMessage: validationError,
        onRetry: () {
          // Form is already open, user can correct the error
        },
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);

      // Prepare block time data
      final startDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _startTime.hour,
        _startTime.minute,
      );

      final endDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _endTime.hour,
        _endTime.minute,
      );

      // Get the actual reason text for saving
      final reason = _selectedReason == 'calendar.reason_other' && _customReason.isNotEmpty
        ? _customReason
        : AppLocalizations.of(context)!.translate(_selectedReason);

      // Check availability first
      final availability = await calendarProvider.checkBlockTimeAvailability(
        startTime: startDateTime,
        endTime: endDateTime,
        staffIds: _selectedStaffIds,
      );

      // Handle conflicts if any
      if (availability != null && availability['available'] == false) {
        final conflicts = availability['conflicts'] as List<dynamic>? ?? [];
        if (conflicts.isNotEmpty && mounted) {
          final shouldProceed = await _showConflictDialog(conflicts);
          if (!shouldProceed) {
            setState(() {
              _isLoading = false;
            });
            return;
          }
        }
      }

      // Handle multi-day vs single-day blocking
      bool allSuccessful = true;
      int successCount = 0;
      BlockTimeResult? firstError;

      if (_isMultiDay && _selectedEndDate != null) {
        // Multi-day blocking
        var currentDate = _selectedDate;
        while (!currentDate.isAfter(_selectedEndDate!)) {
          for (final staffId in _selectedStaffIds) {
            final dayStartDateTime = DateTime(
              currentDate.year,
              currentDate.month,
              currentDate.day,
              _startTime.hour,
              _startTime.minute,
            );

            final dayEndDateTime = DateTime(
              currentDate.year,
              currentDate.month,
              currentDate.day,
              _endTime.hour,
              _endTime.minute,
            );

            final result = await calendarProvider.blockTime(
              dayStartDateTime,
              dayEndDateTime,
              reason,
              staffId: staffId,
            );

            if (result.success) {
              successCount++;
            } else {
              allSuccessful = false;
              if (firstError == null) {
                firstError = result;
              }
            }
          }
          currentDate = currentDate.add(const Duration(days: 1));
        }
      } else {
        // Single-day blocking
        for (final staffId in _selectedStaffIds) {
          final result = await calendarProvider.blockTime(
            startDateTime,
            endDateTime,
            reason,
            staffId: staffId,
          );
          if (result.success) {
            successCount++;
          } else {
            allSuccessful = false;
            if (firstError == null) {
              firstError = result;
            }
          }
        }
      }

      if (mounted) {
        Navigator.of(context).pop();

        // Show success/error message and navigate to calendar
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            if (allSuccessful) {
              final totalDays = _isMultiDay && _selectedEndDate != null
                ? _selectedEndDate!.difference(_selectedDate).inDays + 1
                : 1;

              UINotificationService.showBlockTimeSuccess(
                context: context,
                staffCount: _selectedStaffIds.length,
                onViewCalendar: () => _navigateToCalendar(),
              );

              // Auto-navigate to calendar after a short delay
              Future.delayed(const Duration(milliseconds: 1500), () {
                if (mounted) {
                  _navigateToCalendar();
                }
              });
            } else if (successCount > 0) {
              UINotificationService.showWarning(
                context: context,
                title: 'Blocaj parțial',
                message: 'Timpul a fost blocat pentru $successCount blocuri. Verificați calendarul pentru detalii.',
                actionLabel: 'Vezi calendar',
                onActionPressed: () => _navigateToCalendar(),
              );
            } else {
              // Show detailed error notification
              UINotificationService.showBlockTimeError(
                context: context,
                errorMessage: firstError?.errorMessage ?? 'Nu s-a putut bloca timpul pentru niciun membru',
                detailedError: firstError?.errorDetails?.toString(),
                onRetry: () {
                  // Could reopen the dialog or retry the operation
                },
                onViewConflicts: firstError?.isSchedulingConflict == true ? () {
                  // Could show conflict details
                } : null,
              );
            }
          }
        });
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error blocking time: $e');
      if (mounted) {
        Navigator.of(context).pop();
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            UINotificationService.showBlockTimeError(
              context: context,
              errorMessage: 'Eroare neașteptată la blocarea timpului',
              detailedError: e.toString(),
              onRetry: () {
                // Could reopen the dialog
              },
            );
          }
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Validate form data
  String? _validateForm() {
    if (_selectedStaffIds.isEmpty) {
      return AppLocalizations.of(context)!.translate('calendar.select_at_least_one_member');
    }

    if (_isMultiDay && _selectedEndDate == null) {
      return AppLocalizations.of(context)!.translate('calendar.select_end_date');
    }

    if (_isMultiDay && _selectedEndDate!.isBefore(_selectedDate)) {
      return AppLocalizations.of(context)!.translate('calendar.end_date_after_start');
    }

    if (_getDurationMinutes() <= 0) {
      return AppLocalizations.of(context)!.translate('calendar.end_time_after_start');
    }

    if (_getDurationMinutes() > 720) { // 12 hours
      return AppLocalizations.of(context)!.translate('calendar.duration_max_12_hours');
    }

    if (_selectedReason == 'calendar.reason_other' && _customReason.trim().isEmpty) {
      return AppLocalizations.of(context)!.translate('calendar.enter_custom_reason');
    }

    // Check for reasonable multi-day duration (max 30 days)
    if (_isMultiDay && _selectedEndDate != null) {
      final daysDifference = _selectedEndDate!.difference(_selectedDate).inDays;
      if (daysDifference > 30) {
        return AppLocalizations.of(context)!.translate('calendar.max_30_days');
      }
    }

    return null;
  }

  /// Navigate to calendar view showing the blocked time
  void _navigateToCalendar() {
    // Navigate back to main layout and show calendar tab
    // The calendar will automatically show the selected date
    Navigator.of(context).pop(); // Close the dialog first
    // The MainLayout will handle showing the calendar tab
  }

  /// Show conflict dialog and return whether user wants to proceed
  Future<bool> _showConflictDialog(List<dynamic> conflicts) async {
    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.info, color: Colors.blue),
              SizedBox(width: 8),
              Text('Informații despre conflicte'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.translate('calendar.appointments_conflict_message'),
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 12),
                ...conflicts.map((conflict) {
                  final conflictDetails = conflict['conflictDetails'] as Map<String, dynamic>? ?? {};
                  final staffName = conflict['staffName'] as String? ?? AppLocalizations.of(context)!.translate('calendar.unknown_staff');
                  final clientName = conflictDetails['clientName'] as String? ?? AppLocalizations.of(context)!.translate('calendar.unknown_client');
                  final service = conflictDetails['service'] as String? ?? AppLocalizations.of(context)!.translate('calendar.unknown_service');

                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          staffName,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text('${AppLocalizations.of(context)!.translate('calendar.client_label')}: $clientName'),
                        Text('${AppLocalizations.of(context)!.translate('calendar.service_label')}: $service'),
                      ],
                    ),
                  );
                }).toList(),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Blocarea va continua automat. Veți putea reprograma programările din calendar.',
                          style: TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Anulează'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Continuă'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Show error notification
  void _showErrorNotification(String message) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Eroare',
          message: message,
        );
      }
    });
  }
}

