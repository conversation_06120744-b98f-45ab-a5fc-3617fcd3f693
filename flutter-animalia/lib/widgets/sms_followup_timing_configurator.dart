import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../models/sms_followup_timing.dart';
import '../services/sms_followup_timing_service.dart';
import '../l10n/app_localizations.dart';

/// Widget for configuring SMS follow-up timing intervals
class SmsFollowUpTimingConfigurator extends StatefulWidget {
  final List<SmsFollowUpTiming> timings;
  final Function(List<SmsFollowUpTiming>) onTimingsChanged;
  final String salonId;

  const SmsFollowUpTimingConfigurator({
    Key? key,
    required this.timings,
    required this.onTimingsChanged,
    required this.salonId,
  }) : super(key: key);

  @override
  State<SmsFollowUpTimingConfigurator> createState() => _SmsFollowUpTimingConfiguratorState();
}

class _SmsFollowUpTimingConfiguratorState extends State<SmsFollowUpTimingConfigurator> {
  late List<SmsFollowUpTiming> _timings;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _timings = List.from(widget.timings);
  }

  void _addCustomTiming() {
    showDialog(
      context: context,
      builder: (context) => _AddFollowUpTimingDialog(
        existingTimings: _timings,
        onAdd: (hours) async {
          setState(() => _isLoading = true);
          
          try {
            final request = CreateSmsFollowUpTimingRequest(
              salonId: widget.salonId,
              hoursAfter: hours,
              isEnabled: true,
            );
            
            final response = await SmsFollowUpTimingService.createTiming(request);
            
            if (response.success && response.data != null) {
              setState(() {
                _timings.add(response.data!);
              });
              widget.onTimingsChanged(_timings);
              
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(context.tr('sms_followup_timing.added_success')),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            } else {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(response.message ?? context.tr('sms_followup_timing.add_error')),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(context.tr('common.error_with_details', params: {'error': '$e'})),
                  backgroundColor: Colors.red,
                ),
              );
            }
          } finally {
            setState(() => _isLoading = false);
          }
        },
      ),
    );
  }

  void _toggleTiming(SmsFollowUpTiming timing) async {
    setState(() => _isLoading = true);
    
    try {
      final request = UpdateSmsFollowUpTimingRequest(
        hoursAfter: timing.hoursAfter,
        isEnabled: !timing.isEnabled,
      );
      
      final response = await SmsFollowUpTimingService.updateTiming(timing.id, request);
      
      if (response.success && response.data != null) {
        setState(() {
          final index = _timings.indexWhere((t) => t.id == timing.id);
          if (index != -1) {
            _timings[index] = response.data!;
          }
        });
        widget.onTimingsChanged(_timings);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('sms_followup_timing.update_error', params: {'error': '$e'})),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _deleteTiming(SmsFollowUpTiming timing) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('sms_followup_timing.delete_confirmation_title')),
        content: Text(context.tr('sms_followup_timing.delete_confirmation_message', params: {'timing': timing.getDisplayDescription()})),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.tr('common.cancel')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(context.tr('common.delete')),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);
      
      try {
        final response = await SmsFollowUpTimingService.deleteTiming(timing.id);
        
        if (response.success) {
          setState(() {
            _timings.removeWhere((t) => t.id == timing.id);
          });
          widget.onTimingsChanged(_timings);
          
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(context.tr('sms_followup_timing.deleted_success')),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.tr('sms_followup_timing.delete_error', params: {'error': '$e'})),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  context.tr('sms_followup_timing.title'),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _addCustomTiming,
                  icon: Icon(Icons.add),
                  label: Text('Adaugă'),
                ),
              ],
            ),
            SizedBox(height: 16),
            
            if (_timings.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.schedule_send,
                        size: 48,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Nu există timing-uri de follow-up configurate',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              )
            else
              ...(_timings.map((timing) => _buildTimingCard(timing)).toList()),
            
            SizedBox(height: 16),
            
            // Quick presets
            Text(
              'Presetări rapide:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: FollowUpTimingPreset.getCommonPresets().map((preset) {
                final alreadyExists = _timings.any((t) => t.hoursAfter == preset.hours);
                
                return ActionChip(
                  label: Text(
                    preset.label,
                    style: TextStyle(fontSize: 12),
                  ),
                  onPressed: alreadyExists || _isLoading ? null : () async {
                    setState(() => _isLoading = true);
                    
                    try {
                      final request = CreateSmsFollowUpTimingRequest(
                        salonId: widget.salonId,
                        hoursAfter: preset.hours,
                        isEnabled: true,
                      );
                      
                      final response = await SmsFollowUpTimingService.createTiming(request);
                      
                      if (response.success && response.data != null) {
                        setState(() {
                          _timings.add(response.data!);
                        });
                        widget.onTimingsChanged(_timings);
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Eroare: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    } finally {
                      setState(() => _isLoading = false);
                    }
                  },
                  backgroundColor: alreadyExists 
                      ? Theme.of(context).colorScheme.surfaceContainerHighest
                      : null,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimingCard(SmsFollowUpTiming timing) {
    return Card(
      margin: EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          timing.isEnabled ? Icons.schedule_send : Icons.schedule_send_outlined,
          color: timing.isEnabled ? Colors.green : Colors.grey,
        ),
        title: Text(timing.getDisplayDescription()),
        subtitle: Text(
          timing.isEnabled ? 'Activ' : 'Inactiv',
          style: TextStyle(
            color: timing.isEnabled ? Colors.green : Colors.grey,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Switch(
              value: timing.isEnabled,
              onChanged: _isLoading ? null : (_) => _toggleTiming(timing),
            ),
            IconButton(
              icon: Icon(Icons.delete, color: Colors.red),
              onPressed: _isLoading ? null : () => _deleteTiming(timing),
            ),
          ],
        ),
      ),
    );
  }
}

class _AddFollowUpTimingDialog extends StatefulWidget {
  final List<SmsFollowUpTiming> existingTimings;
  final Function(int) onAdd;

  const _AddFollowUpTimingDialog({
    required this.existingTimings,
    required this.onAdd,
  });

  @override
  State<_AddFollowUpTimingDialog> createState() => _AddFollowUpTimingDialogState();
}

class _AddFollowUpTimingDialogState extends State<_AddFollowUpTimingDialog> {
  final _controller = TextEditingController();
  String? _errorText;

  @override
  void initState() {
    super.initState();
    final recommended = SmsFollowUpTimingService.getRecommendedTiming(widget.existingTimings);
    _controller.text = recommended.toString();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _validate() {
    final text = _controller.text.trim();
    if (text.isEmpty) {
      setState(() => _errorText = 'Introduceți numărul de ore');
      return;
    }

    final hours = int.tryParse(text);
    if (hours == null) {
      setState(() => _errorText = 'Introduceți un număr valid');
      return;
    }

    final error = SmsFollowUpTimingService.validateTiming(
      hoursAfter: hours,
      existingTimings: widget.existingTimings,
    );

    setState(() => _errorText = error);

    if (error == null) {
      widget.onAdd(hours);
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Adaugă timing follow-up'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _controller,
            decoration: InputDecoration(
              labelText: 'Ore după completarea programării',
              errorText: _errorText,
              suffixText: 'ore',
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            onChanged: (_) => setState(() => _errorText = null),
          ),
          SizedBox(height: 16),
          Text(
            'Mesajul de follow-up va fi trimis la ${_controller.text.isNotEmpty ? _controller.text : '?'} ore după completarea programării.',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Anulează'),
        ),
        ElevatedButton(
          onPressed: _validate,
          child: Text('Adaugă'),
        ),
      ],
    );
  }
}
