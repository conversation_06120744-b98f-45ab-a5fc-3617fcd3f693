import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../services/api_service.dart';
import '../../services/auth/auth_service.dart';
import '../../l10n/app_localizations.dart';

/// Widget for claiming referral codes during salon creation
class ReferralCodeClaimSection extends StatefulWidget {
  final bool enabled;
  final bool isCreatingSalon; // Flag to indicate if we're in salon creation mode
  final Function(String?)? onReferralCodeChanged; // Callback when referral code changes

  const ReferralCodeClaimSection({
    super.key,
    this.enabled = true,
    this.isCreatingSalon = false,
    this.onReferralCodeChanged,
  });

  @override
  State<ReferralCodeClaimSection> createState() => _ReferralCodeClaimSectionState();
}

class _ReferralCodeClaimSectionState extends State<ReferralCodeClaimSection> {
  final TextEditingController _referralCodeController = TextEditingController();
  bool _isValidatingCode = false;
  String? _validationMessage;
  bool? _isCodeValid;

  @override
  void dispose() {
    _referralCodeController.dispose();
    super.dispose();
  }

  /// Validate referral code
  Future<void> _validateReferralCode(String code) async {
    if (code.isEmpty) {
      setState(() {
        _isValidatingCode = false;
        _validationMessage = null;
        _isCodeValid = null;
      });
      return;
    }

    // Basic format validation
    if (code.length != 8 || !RegExp(r'^[A-Z0-9]{8}$').hasMatch(code)) {
      setState(() {
        _isValidatingCode = false;
        _validationMessage = context.tr('referral_system.code_format_error');
        _isCodeValid = false;
      });
      return;
    }

    // If we're creating a salon, validate against a temporary salon ID
    if (widget.isCreatingSalon) {
      setState(() {
        _isValidatingCode = true;
        _validationMessage = null;
        _isCodeValid = null;
      });

      try {
        // Use a temporary salon ID for validation during creation
        final response = await ApiService.post<Map<String, dynamic>>(
          '/api/referral/temp-salon/validate',
          body: {'code': code},
        );

        if (mounted) {
          if (response.success && response.data != null) {
            final isValid = response.data!['isValid'] as bool? ?? false;
            final message = response.data!['message'] as String?;

            setState(() {
              _isValidatingCode = false;
              _isCodeValid = isValid;
              _validationMessage = message ?? (isValid ? context.tr('referral_system.code_valid') : context.tr('referral_system.code_invalid'));
            });

            // Notify parent about the code validity and value
            if (isValid) {
              widget.onReferralCodeChanged?.call(code);
            } else {
              widget.onReferralCodeChanged?.call(null);
            }
          } else {
            setState(() {
              _isValidatingCode = false;
              _validationMessage = context.tr('referral_system.validation_error_generic');
              _isCodeValid = false;
            });
            widget.onReferralCodeChanged?.call(null);
          }
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isValidatingCode = false;
            _validationMessage = context.tr('referral_system.validation_error_generic');
            _isCodeValid = false;
          });
          widget.onReferralCodeChanged?.call(null);
        }
      }
      return;
    }

    setState(() {
      _isValidatingCode = true;
      _validationMessage = null;
      _isCodeValid = null;
    });

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        setState(() {
          _isValidatingCode = false;
          _validationMessage = context.tr('referral_system.salon_identification_error');
          _isCodeValid = false;
        });
        return;
      }

      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/referral/$salonId/validate',
        body: {'code': code},
      );

      if (mounted) {
        if (response.success && response.data != null) {
          final isValid = response.data!['isValid'] as bool? ?? false;
          final message = response.data!['message'] as String?;

          setState(() {
            _isValidatingCode = false;
            _isCodeValid = isValid;
            _validationMessage = message ?? (isValid ? context.tr('referral_system.code_valid') : context.tr('referral_system.code_invalid'));
          });
        } else {
          setState(() {
            _isValidatingCode = false;
            _validationMessage = context.tr('referral_system.validation_error_generic');
            _isCodeValid = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isValidatingCode = false;
          _validationMessage = context.tr('referral_system.validation_error_generic');
          _isCodeValid = false;
        });
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.card_giftcard,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('create_salon.referral_code_optional'),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      context.tr('create_salon.referral_code_description'),
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Input field only
            TextFormField(
              controller: _referralCodeController,
              enabled: widget.enabled,
              decoration: InputDecoration(
                labelText: context.tr('create_salon.referral_code_label'),
                hintText: 'Ex: ABC12345',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.confirmation_number),
                suffixIcon: _isValidatingCode
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: Padding(
                          padding: EdgeInsets.all(12.0),
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      )
                    : _isCodeValid != null
                        ? Icon(
                            _isCodeValid! ? Icons.check_circle : Icons.error,
                            color: _isCodeValid! ? Colors.green : Colors.red,
                          )
                        : null,
                helperText: _validationMessage,
                helperStyle: TextStyle(
                  color: _isCodeValid == false ? Colors.red : Colors.green,
                ),
              ),
              textCapitalization: TextCapitalization.characters,
              maxLength: 8,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z0-9]')),
                TextInputFormatter.withFunction((oldValue, newValue) {
                  return newValue.copyWith(text: newValue.text.toUpperCase());
                }),
              ],
              onChanged: (value) {
                _validateReferralCode(value.trim().toUpperCase());
              },
            ),
          ],
        ),
      ),
    );
  }
}
