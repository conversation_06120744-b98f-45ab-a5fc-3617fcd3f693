import 'package:flutter/material.dart';
import '../../models/api_response.dart';
import '../../services/api_service.dart';
import '../../services/auth/auth_service.dart';
import '../../l10n/app_localizations.dart';

/// Model for progressive reward information
class ProgressiveReward {
  final int referralNumber;
  final int rewardAmount;
  final bool isCompleted;

  ProgressiveReward({
    required this.referralNumber,
    required this.rewardAmount,
    required this.isCompleted,
  });

  factory ProgressiveReward.fromJson(Map<String, dynamic> json) {
    return ProgressiveReward(
      referralNumber: json['referralNumber'] as int,
      rewardAmount: json['rewardAmount'] as int,
      isCompleted: json['isCompleted'] as bool,
    );
  }
}

/// Model for referral statistics
class ReferralStats {
  final int totalReferrals;
  final int nextRewardAmount;
  final List<ProgressiveReward> progressiveRewards;
  final int totalCreditsEarned;

  ReferralStats({
    required this.totalReferrals,
    required this.nextRewardAmount,
    required this.progressiveRewards,
    required this.totalCreditsEarned,
  });

  factory ReferralStats.fromJson(Map<String, dynamic> json) {
    return ReferralStats(
      totalReferrals: json['totalReferrals'] as int,
      nextRewardAmount: json['nextRewardAmount'] as int,
      progressiveRewards: (json['progressiveRewards'] as List)
          .map((item) => ProgressiveReward.fromJson(item))
          .toList(),
      totalCreditsEarned: json['totalCreditsEarned'] as int,
    );
  }
}

/// Widget that displays referral progress with milestone goals
class ReferralProgressWidget extends StatefulWidget {
  const ReferralProgressWidget({Key? key}) : super(key: key);

  @override
  State<ReferralProgressWidget> createState() => _ReferralProgressWidgetState();
}

class _ReferralProgressWidgetState extends State<ReferralProgressWidget> {
  ReferralStats? _stats;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadReferralStats();
  }

  Future<void> _loadReferralStats() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        setState(() {
          _error = context.tr('referral_system.salon_id_error');
          _isLoading = false;
        });
        return;
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/referral/$salonId/stats',
      );

      if (response.success && response.data != null) {
        setState(() {
          _stats = ReferralStats.fromJson(response.data!);
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? context.tr('referral_system.stats_load_error');
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = '${context.tr('referral_system.stats_load_error')}: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (_error != null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(Icons.error, color: Colors.red, size: 32),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: _loadReferralStats,
                child: Text(context.tr('referral_system.try_again')),
              ),
            ],
          ),
        ),
      );
    }

    if (_stats == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildProgressGrid(),
            if (_stats!.totalReferrals > 0) ...[
              const SizedBox(height: 16),
              _buildSummary(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.emoji_events, color: Colors.amber, size: 24),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.tr('referral_system.progress_title'),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
              Text(
                context.tr('referral_system.progress_description'),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressGrid() {
    // Show first 8 milestones in a 4x2 grid (2 columns, 4 rows)
    // Use the progressive rewards from backend (which now start at 200)
    final milestones = _stats!.progressiveRewards.take(8).toList();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // Changed from 4 to 2 for better space utilization
        childAspectRatio: 1.4, // Changed from 1.0 to 1.4 for wider cards
        crossAxisSpacing: 12, // Increased spacing for better visual separation
        mainAxisSpacing: 12,
      ),
      itemCount: milestones.length,
      itemBuilder: (context, index) {
        final milestone = milestones[index];
        return _buildMilestoneCard(milestone);
      },
    );
  }

  Widget _buildMilestoneCard(ProgressiveReward milestone) {
    final isCompleted = milestone.isCompleted;
    final isNext = !isCompleted && milestone.referralNumber == _stats!.totalReferrals + 1;

    return Container(
      decoration: BoxDecoration(
        color: isCompleted
            ? Colors.green.withOpacity(0.1)
            : isNext
                ? Colors.orange.withOpacity(0.1)
                : Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCompleted
              ? Colors.green
              : isNext
                  ? Colors.orange
                  : Colors.grey.withOpacity(0.3),
          width: isNext ? 2 : 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0), // Added padding to prevent edge overflow
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
              color: isCompleted
                  ? Colors.green
                  : isNext
                      ? Colors.orange
                      : Colors.grey,
              size: 18, // Slightly reduced icon size
            ),
            const SizedBox(height: 4),
            Flexible( // Added Flexible to prevent overflow
              child: Text(
                '${milestone.referralNumber} ${context.tr('referral_system.referral_singular')}',
                style: TextStyle(
                  fontSize: 11, // Reduced font size
                  fontWeight: FontWeight.bold,
                  color: isCompleted
                      ? Colors.green.shade700
                      : isNext
                          ? Colors.orange.shade700
                          : Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis, // Handle text overflow
                maxLines: 1,
              ),
            ),
            const SizedBox(height: 2),
            Flexible( // Added Flexible to prevent overflow
              child: Text(
                '${milestone.rewardAmount} ${context.tr('referral_system.messages_count')}',
                style: TextStyle(
                  fontSize: 12, // Reduced font size
                  color: isCompleted
                      ? Colors.green.shade600
                      : isNext
                          ? Colors.orange.shade600
                          : Colors.grey.shade500,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis, // Handle text overflow
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummary() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem(
            context.tr('referral_system.referrals_label'),
            '${_stats!.totalReferrals}',
            Icons.people,
            Colors.blue,
          ),
          _buildSummaryItem(
            context.tr('referral_system.credits_earned_label'),
            '${_stats!.totalCreditsEarned}',
            Icons.sms,
            Colors.green,
          ),
          _buildSummaryItem(
            context.tr('referral_system.next_reward_label'),
            '${_stats!.nextRewardAmount}',
            Icons.star,
            Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
