import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';

import '../../services/api_service.dart';
import '../../services/auth/auth_service.dart';
import '../../services/ui_notification_service.dart';
import '../../l10n/app_localizations.dart';
import 'referral_progress_widget.dart';

/// Reusable widget for the referral system that can be used in any screen
class ReferralSystemWidget extends StatefulWidget {
  const ReferralSystemWidget({super.key});

  @override
  State<ReferralSystemWidget> createState() => _ReferralSystemWidgetState();
}

class _ReferralSystemWidgetState extends State<ReferralSystemWidget> {
  // Referral system state
  String? _salonReferralCode; // The salon's unique referral code
  final TextEditingController _referralCodeController = TextEditingController();
  bool _isClaimingCode = false;
  bool _isValidatingCode = false;
  String? _validationMessage;
  bool? _isCodeValid;
  Timer? _validationTimer;
  StateSetter? _dialogSetState; // Keep reference to dialog setState
  bool? _hasClaimedAnyCode; // Whether salon has ever claimed any referral code
  bool _isLoadingClaimStatus = true; // Loading state for claim status

  @override
  void initState() {
    super.initState();
    print('DEBUG: ReferralSystemWidget initState called');
    _loadSalonReferralCode();
    _loadClaimStatus();

    // Add listener for real-time validation
    _referralCodeController.addListener(_onReferralCodeChanged);
  }

  @override
  void dispose() {
    _referralCodeController.dispose();
    _validationTimer?.cancel();
    super.dispose();
  }

  /// Real-time validation of referral code as user types
  void _onReferralCodeChanged() {
    final code = _referralCodeController.text.trim().toUpperCase();
    
    // Cancel previous timer
    _validationTimer?.cancel();
    
    if (code.isEmpty) {
      setState(() {
        _isValidatingCode = false;
        _validationMessage = null;
        _isCodeValid = null;
      });
      _dialogSetState?.call(() {});
      return;
    }
    
    // Basic format validation
    if (code.length != 8 || !RegExp(r'^[A-Z0-9]{8}$').hasMatch(code)) {
      setState(() {
        _isValidatingCode = false;
        _validationMessage = context.tr('referral_system.code_format_error');
        _isCodeValid = false;
      });
      _dialogSetState?.call(() {});
      return;
    }
    
    // Set loading state
    setState(() {
      _isValidatingCode = true;
      _validationMessage = null;
      _isCodeValid = null;
    });
    _dialogSetState?.call(() {});
    
    // Debounce API call
    _validationTimer = Timer(const Duration(milliseconds: 800), () {
      _performReferralCodeValidation(code);
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.card_giftcard,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('referral_system.title'),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.green, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      context.tr('referral_system.description'),
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Your Unique Referral Code Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.qr_code, color: Colors.blue, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        context.tr('referral_system.your_referral_code'),
                        style: TextStyle(
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Debug info
                  const SizedBox(height: 8),

                  // Referral Code Display
                  if (_salonReferralCode != null) ...[
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? Colors.grey[800]
                            : Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isDarkMode
                              ? Colors.grey[600]!
                              : Colors.grey[300]!
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _salonReferralCode!,
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 2,
                              fontFamily: 'monospace',
                              color: Theme.of(context).textTheme.bodyLarge?.color,
                            ),
                          ),
                          Row(
                            children: [
                              IconButton(
                                onPressed: () => _copyReferralCode(_salonReferralCode!),
                                icon: const Icon(Icons.copy),
                                tooltip: context.tr('referral_system.copy_code_tooltip'),
                              ),
                              IconButton(
                                onPressed: () => _shareReferralCodeFromDialog(_salonReferralCode!),
                                icon: const Icon(Icons.share),
                                tooltip: context.tr('referral_system.share_code_tooltip'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      context.tr('referral_system.share_message'),
                      style: TextStyle(
                        color: Colors.blue.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ] else ...[
                    // Loading state
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? Colors.grey[800]
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isDarkMode
                              ? Colors.grey[600]!
                              : Colors.grey[300]!
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            context.tr('referral_system.loading_code'),
                            style: TextStyle(
                              color: Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Claim Code Button (full width)
            SizedBox(
              width: double.infinity,
              child: _isLoadingClaimStatus
                  ? OutlinedButton.icon(
                      onPressed: null,
                      icon: const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      label: Text(context.tr('referral_system.loading_button')),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    )
                  : OutlinedButton.icon(
                      onPressed: _hasClaimedAnyCode == true ? null : _claimReferralCode,
                      icon: Icon(
                        _hasClaimedAnyCode == true ? Icons.check_circle : Icons.redeem,
                        color: _hasClaimedAnyCode == true ? Colors.green : null,
                      ),
                      label: Text(_hasClaimedAnyCode == true ? context.tr('referral_system.code_claimed_button') : context.tr('referral_system.claim_code_button')),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        foregroundColor: _hasClaimedAnyCode == true ? Colors.green : null,
                        side: _hasClaimedAnyCode == true
                            ? const BorderSide(color: Colors.green)
                            : null,
                      ),
                    ),
            ),

            // Info message if already claimed
            if (_hasClaimedAnyCode == true) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.green, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        context.tr('referral_system.already_claimed_info'),
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 20),

            // Referral Progress Widget
            const ReferralProgressWidget(),

          ],
        ),
      ),
    );
  }

  /// Load claim status to check if salon has ever claimed any referral code
  void _loadClaimStatus() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        print('DEBUG: No salon ID found for claim status');
        if (mounted) {
          setState(() {
            _isLoadingClaimStatus = false;
            _hasClaimedAnyCode = false;
          });
        }
        return;
      }

      print('DEBUG: Loading claim status for salon: $salonId');
      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/referral/$salonId/has-claimed',
      );

      print('DEBUG: Claim status response - success: ${response.success}, data: ${response.data}, error: ${response.error}');

      if (mounted) {
        if (response.success && response.data != null) {
          final hasClaimed = response.data!['hasClaimed'] as bool? ?? false;
          print('DEBUG: Salon has claimed any code: $hasClaimed');

          setState(() {
            _hasClaimedAnyCode = hasClaimed;
            _isLoadingClaimStatus = false;
          });
        } else {
          print('DEBUG: Failed to load claim status - success: ${response.success}, error: ${response.error}');
          setState(() {
            _hasClaimedAnyCode = false;
            _isLoadingClaimStatus = false;
          });
        }
      }
    } catch (e) {
      print('DEBUG: Exception loading claim status: $e');
      if (mounted) {
        setState(() {
          _hasClaimedAnyCode = false;
          _isLoadingClaimStatus = false;
        });
      }
    }
  }

  /// Load salon's unique referral code
  void _loadSalonReferralCode() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        print('DEBUG: No salon ID found');
        return;
      }

      print('DEBUG: Loading referral code for salon: $salonId');
      // Call the API endpoint to get salon's unique referral code
      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/referral/$salonId/code',
      );

      print('DEBUG: Referral code response - success: ${response.success}, data: ${response.data}, error: ${response.error}');

      if (response.success && response.data != null) {
        print('DEBUG: Response data type: ${response.data.runtimeType}');
        print('DEBUG: Response data content: ${response.data}');

        final code = response.data!['code'] as String?;
        print('DEBUG: Extracted code: $code');

        if (mounted && code != null) {
          setState(() {
            _salonReferralCode = code;
          });
          print('DEBUG: Set salon referral code to: $_salonReferralCode');
        }
      } else {
        print('DEBUG: Failed to load referral code - success: ${response.success}, error: ${response.error}');
      }
    } catch (e) {
      print('DEBUG: Exception loading referral code: $e');
      // Handle error silently
    }
  }

  /// Copy referral code to clipboard
  void _copyReferralCode(String code) {
    Clipboard.setData(ClipboardData(text: code));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.tr('referral_system.code_copied').replaceAll('{0}', code)),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// Share referral code from dialog
  void _shareReferralCodeFromDialog(String code) async {
    try {
      final message = context.tr('referral_system.share_invitation_message').replaceAll('{0}', code);

      await Share.share(
        message,
        subject: context.tr('referral_system.share_subject').replaceAll('{0}', code),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('referral_system.share_error')),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Handle claim referral code action
  void _claimReferralCode() {
    // Double-check if salon has already claimed a code
    if (_hasClaimedAnyCode == true) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('referral_system.already_claimed_warning')),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      return;
    }

    _showClaimReferralDialog();
  }

  /// Show dialog to claim referral code
  void _showClaimReferralDialog() {
    _referralCodeController.clear();

    // Reset validation state
    setState(() {
      _isValidatingCode = false;
      _validationMessage = null;
      _isCodeValid = null;
      _isClaimingCode = false;
    });

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, dialogSetState) {
            // Save reference to dialog setState
            _dialogSetState = dialogSetState;
            return AlertDialog(
              title: Row(
                children: [
                  const Icon(Icons.redeem, color: Colors.orange),
                  const SizedBox(width: 8),
                  Text(context.tr('referral_system.claim_dialog_title')),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    context.tr('referral_system.claim_dialog_description'),
                  ),
                  const SizedBox(height: 20),
                  TextField(
                    controller: _referralCodeController,
                    decoration: InputDecoration(
                      labelText: context.tr('referral_system.referral_code_label'),
                      hintText: context.tr('referral_system.referral_code_hint'),
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.confirmation_number),
                      enabled: !_isClaimingCode,
                      suffixIcon: _isValidatingCode
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: Padding(
                                padding: EdgeInsets.all(12.0),
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                            )
                          : _isCodeValid != null
                              ? Icon(
                                  _isCodeValid! ? Icons.check_circle : Icons.error,
                                  color: _isCodeValid! ? Colors.green : Colors.red,
                                )
                              : null,
                      helperText: _validationMessage,
                      helperStyle: TextStyle(
                        color: _isCodeValid == false ? Colors.red : Colors.green,
                      ),
                    ),
                    textCapitalization: TextCapitalization.characters,
                    maxLength: 8,
                    onChanged: (value) {
                      // Trigger validation on change and update dialog state
                      dialogSetState(() {});
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: _isClaimingCode ? null : () => Navigator.of(context).pop(),
                  child: Text(context.tr('referral_system.cancel_button')),
                ),
                ElevatedButton(
                  onPressed: (_isClaimingCode || _isCodeValid != true)
                      ? null
                      : () => _processReferralCode(dialogSetState),
                  child: _isClaimingCode
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(context.tr('referral_system.claim_button')),
                ),
              ],
            );
          },
        );
      },
    ).then((_) {
      // Reset dialog setState reference when dialog closes
      _dialogSetState = null;
    });
  }

  /// Process the referral code and award SMS credits
  void _processReferralCode(StateSetter dialogSetState) async {
    final code = _referralCodeController.text.trim().toUpperCase();

    // Basic validation
    if (code.isEmpty || code.length != 8 || !RegExp(r'^[A-Z0-9]{8}$').hasMatch(code)) {
      if (mounted) {
        UINotificationService.showWarning(
          context: context,
          title: context.tr('referral_system.invalid_code_title'),
          message: context.tr('referral_system.invalid_code_message'),
        );
      }
      return;
    }

    // Check if we already know the code is invalid from real-time validation
    if (_isCodeValid == false) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('referral_system.invalid_code_title'),
          message: _validationMessage ?? context.tr('referral_system.code_invalid'),
        );
      }
      return;
    }

    dialogSetState(() {
      _isClaimingCode = true;
    });

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        if (mounted) {
          UINotificationService.showError(
            context: context,
            title: context.tr('referral_system.error_title'),
            message: context.tr('referral_system.salon_not_found'),
          );
        }
        dialogSetState(() {
          _isClaimingCode = false;
        });
        return;
      }

      // Validate the code first
      print('DEBUG: Making claim validation request to /api/referral/$salonId/validate with code: $code');
      final validationResponse = await ApiService.post<Map<String, dynamic>>(
        '/api/referral/$salonId/validate',
        body: {'code': code},
      );

      print('DEBUG: Claim validation response: ${validationResponse.success}, data: ${validationResponse.data}, error: ${validationResponse.error}');
      if (validationResponse.success && validationResponse.data != null) {
        final isValid = validationResponse.data!['isValid'] as bool? ?? false;
        if (!isValid) {
          final message = validationResponse.data!['message'] as String? ?? context.tr('referral_system.code_invalid');
          if (mounted) {
            UINotificationService.showError(
              context: context,
              title: 'Cod invalid',
              message: message,
              duration: const Duration(seconds: 5),
            );
          }
          dialogSetState(() {
            _isClaimingCode = false;
          });
          return;
        }
      } else {
        if (mounted) {
          UINotificationService.showError(
            context: context,
            title: context.tr('referral_system.validation_error'),
            message: '${context.tr('referral_system.validation_error_generic')}: ${validationResponse.error ?? context.tr('referral_system.unknown_error')}',
            duration: const Duration(seconds: 5),
          );
        }
        dialogSetState(() {
          _isClaimingCode = false;
        });
        return;
      }

      // If validation passed, proceed to claim the referral code
      print('DEBUG: Making claim request to /api/referral/$salonId/claim with code: $code');
      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/referral/$salonId/claim',
        body: {'code': code},
      );

      print('DEBUG: Claim response: ${response.success}, data: ${response.data}, error: ${response.error}');
      if (response.success && response.data != null) {
        final success = response.data!['success'] as bool? ?? false;

        if (success) {
          final smsCreditsAwarded = response.data!['smsCreditsAwarded'] as int? ?? 100;

          if (mounted) {
            Navigator.of(context).pop(); // Close dialog

            // Update claim status since we just successfully claimed a code
            setState(() {
              _hasClaimedAnyCode = true;
            });

            _showSuccessDialog(code, smsCreditsAwarded);
          }
        } else {
          final message = response.data!['message'] as String? ?? context.tr('referral_system.code_invalid');

          if (mounted) {
            UINotificationService.showError(
              context: context,
              title: 'Cod invalid',
              message: message,
              duration: const Duration(seconds: 5),
            );
          }
        }
      } else {
        if (mounted) {
          UINotificationService.showError(
            context: context,
            title: context.tr('referral_system.error_title'),
            message: '${context.tr('referral_system.claim_error')}: ${response.error ?? context.tr('referral_system.unknown_error')}',
            duration: const Duration(seconds: 5),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('referral_system.error_title'),
          message: '${context.tr('referral_system.claim_error')}: $e',
          duration: const Duration(seconds: 5),
        );
      }
    } finally {
      dialogSetState(() {
        _isClaimingCode = false;
      });
    }
  }

  /// Show success dialog after claiming referral code
  void _showSuccessDialog(String code, [int smsCreditsAwarded = 100]) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.celebration, color: Colors.green, size: 32),
              const SizedBox(width: 12),
              Text(context.tr('referral_system.success_title')),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                context.tr('referral_system.success_message'),
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.sms, color: Colors.green, size: 24),
                        const SizedBox(width: 8),
                        Text(
                          context.tr('referral_system.sms_credits_awarded').replaceAll('{0}', smsCreditsAwarded.toString()),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      context.tr('referral_system.credits_added'),
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                context.tr('referral_system.code_claimed_label').replaceAll('{0}', code),
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: Text(context.tr('referral_system.excellent_button')),
            ),
          ],
        );
      },
    );
  }

  /// Perform the actual referral code validation API call.
  Future<void> _performReferralCodeValidation(String code) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      print('DEBUG: Current salon ID: $salonId');
      if (salonId == null) {
        if (mounted) {
          setState(() {
            _isValidatingCode = false;
            _validationMessage = context.tr('referral_system.salon_identification_error');
            _isCodeValid = false;
          });
          _dialogSetState?.call(() {});
        }
        return;
      }

      print('DEBUG: Making validation request to /api/referral/$salonId/validate with code: $code');
      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/referral/$salonId/validate',
        body: {'code': code},
      );

      print('DEBUG: Validation response: ${response.success}, data: ${response.data}, error: ${response.error}');
      if (mounted) {
        if (response.success && response.data != null) {
          final isValid = response.data!['isValid'] as bool? ?? false;
          final message = response.data!['message'] as String?;

          print('DEBUG 1: Extracted isValid: $isValid, message: $message');

          setState(() {
            _isValidatingCode = false;
            _isCodeValid = isValid;
            _validationMessage = message ?? (isValid ? context.tr('referral_system.code_valid') : context.tr('referral_system.code_invalid'));
          });

          // Also update dialog state if dialog is open
          _dialogSetState?.call(() {});

          print('DEBUG 1 AFTER setState: _isCodeValid: $_isCodeValid, _validationMessage: $_validationMessage');
        } else {
          print('DEBUG 2: Response failed - success: ${response.success}, error: ${response.error}');

          setState(() {
            _isValidatingCode = false;
            _validationMessage = context.tr('referral_system.validation_error_generic');
            _isCodeValid = false;
          });

          // Also update dialog state if dialog is open
          _dialogSetState?.call(() {});

          print('DEBUG 2 AFTER setState: _isCodeValid: $_isCodeValid, _validationMessage: $_validationMessage');
        }
      }
    } catch (e) {
      print('DEBUG 3: Exception during validation: $e');
      if (mounted) {
        setState(() {
          _isValidatingCode = false;
          _validationMessage = '${context.tr('referral_system.validation_error_generic')}: $e';
          _isCodeValid = false;
        });

        // Also update dialog setState if dialog is open
        _dialogSetState?.call(() {});
      }
    }
  }
}
