import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/user_role.dart';
import '../../services/staff_service.dart';
import '../../services/ui_notification_service.dart';
import '../../utils/debug_logger.dart';
import '../../utils/formatters/phone_number_utils.dart';

class AddStaffDialog extends StatefulWidget {
  final VoidCallback? onSuccess;

  const AddStaffDialog({
    super.key,
    this.onSuccess,
  });

  @override
  State<AddStaffDialog> createState() => _AddStaffDialogState();
}

class _AddStaffDialogState extends State<AddStaffDialog> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _nicknameController = TextEditingController();
  final _notesController = TextEditingController();

  GroomerRole _selectedRole = GroomerRole.groomer;
  ClientDataPermission _selectedPermission = ClientDataPermission.fullAccess;
  bool _isLoading = false;
  bool _createDirectly = false;

  // Phone number state management
  String _completePhoneNumber = '';
  String _initialCountryCode = 'RO';
  static const String _lastCountryCodeKey = 'last_staff_country_code';

  @override
  void initState() {
    super.initState();
    _loadLastCountryCode();
  }

  // Load last used country code from SharedPreferences
  Future<void> _loadLastCountryCode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedCode = prefs.getString(_lastCountryCodeKey);
      if (savedCode != null && savedCode.isNotEmpty) {
        if (mounted) {
          setState(() {
            _initialCountryCode = savedCode;
          });
        }
      }
    } catch (e) {
      // Ignore errors, use default
    }
  }

  // Save last used country code to SharedPreferences
  Future<void> _saveLastCountryCode(String countryCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastCountryCodeKey, countryCode);
    } catch (e) {
      // Ignore errors
    }
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _nicknameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.person_add,

                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    context.tr('add_staff_dialog.title'),
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildCreateDirectlyToggle(),
                      const SizedBox(height: 24),
                      if (!_createDirectly) ...[
                        _buildPhoneSection(),
                        const SizedBox(height: 24),
                      ],
                      _buildNicknameSection(),
                      const SizedBox(height: 24),
                      _buildRoleSection(),
                      const SizedBox(height: 24),
                      if (!_createDirectly) ...[
                        _buildPermissionsSection(),
                        const SizedBox(height: 24),
                      ],
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
            ),
            // Footer
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                    child: Text(context.tr('add_staff_dialog.cancel_button')),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _handleSave,
                    style: ElevatedButton.styleFrom(

                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(context.tr('add_staff_dialog.add_button')),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateDirectlyToggle() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            _createDirectly ? Icons.person_add : Icons.mail_outline,

            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('add_staff_dialog.create_directly_title'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,

                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _createDirectly
                      ? context.tr('add_staff_dialog.create_directly_enabled_subtitle')
                      : context.tr('add_staff_dialog.create_directly_disabled_subtitle'),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _createDirectly,
            onChanged: (value) {
              setState(() {
                _createDirectly = value;
                if (value) {
                  // Clear phone when creating directly
                  _phoneController.clear();
                }
              });
            },

          ),
        ],
      ),
    );
  }

  Widget _buildPhoneSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('add_staff_dialog.phone_section_title'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,

          ),
        ),
        const SizedBox(height: 8),
        IntlPhoneField(
          decoration: InputDecoration(
            hintText: '731 234 567',
            prefixIcon: const Icon(Icons.phone),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
          ),
          initialCountryCode: _initialCountryCode,
          dropdownIconPosition: IconPosition.trailing,
          flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8.0),
          dropdownTextStyle: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
          ),
          showDropdownIcon: true,
          disableLengthCheck: false,
          keyboardType: TextInputType.phone,
          //  TODO TRANSLATE
          onChanged: (phone) {
            setState(() {
              _completePhoneNumber = phone.completeNumber;
            });
            // Save the selected country code for next time
            _saveLastCountryCode(phone.countryISOCode);
          },
          validator: (value) {
            if (_createDirectly) return null; // No phone validation when creating directly
            if (value == null || value.number.isEmpty) {
              return 'The phone number is required';
            }
            // Basic validation - accept international numbers with at least 6 digits
            if (value.number.length < 6) {
              return 'The phone number is too short';
            }
            return null;
          },
        ),
        const SizedBox(height: 8),
        Text(
          context.tr('add_staff_dialog.phone_invitation_info'),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildNicknameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _createDirectly ? context.tr('add_staff_dialog.nickname_required_title') : context.tr('add_staff_dialog.nickname_optional_title'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,

          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _nicknameController,
          keyboardType: TextInputType.text,
          decoration: InputDecoration(
            hintText: context.tr('add_staff_dialog.nickname_placeholder'),
            prefixIcon: Icon(Icons.badge),
            border: OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(),
            ),
            helperText: context.tr('add_staff_dialog.nickname_helper_text'),
          ),
          validator: (value) {
            // When creating directly, nickname is required
            if (_createDirectly) {
              if (value == null || value.trim().isEmpty) {
                return context.tr('add_staff_dialog.nickname_required_error');
              }
            }
            // Nickname validation when provided
            if (value != null && value.trim().isNotEmpty) {
              if (value.trim().length < 2) {
                return context.tr('add_staff_dialog.nickname_min_length_error');
              }
              if (value.trim().length > 50) {
                return context.tr('add_staff_dialog.nickname_max_length_error');
              }
            }
            return null;
          },
          textCapitalization: TextCapitalization.words,
        ),
        const SizedBox(height: 8),
        Text(
          _createDirectly
              ? context.tr('add_staff_dialog.nickname_direct_info')
              : context.tr('add_staff_dialog.nickname_invitation_info'),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildRoleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('add_staff_dialog.role_section_title'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,

          ),
        ),
        const SizedBox(height: 12),
        ...GroomerRole.values
            .where((role) => role != GroomerRole.regularGroomer && role != GroomerRole.chiefGroomer) // Exclude deprecated role
            .map((role) => RadioListTile<GroomerRole>(
          title: Text(role.localizedDisplayName(context)),
          subtitle: Text(role.localizedDescription(context)),
          value: role,
          groupValue: _selectedRole,

          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedRole = value;
                // Auto-adjust permissions based on role
                if (value.hasManagementAccess) {
                  _selectedPermission = ClientDataPermission.fullAccess;
                }
              });
            }
          },
        )),
      ],
    );
  }

  Widget _buildPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('add_staff_dialog.permissions_section_title'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,

          ),
        ),
        const SizedBox(height: 12),
        ...ClientDataPermission.values.map((permission) => RadioListTile<ClientDataPermission>(
          title: Text(permission.localizedDisplayName(context)),
          subtitle: Text(permission.localizedDescription(context)),
          value: permission,
          groupValue: _selectedPermission,

          onChanged: _selectedRole.hasManagementAccess
              ? null // Chief groomers always have full access
              : (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPermission = value;
                    });
                  }
                },
        )),
        if (_selectedRole.hasManagementAccess) ...[
          const SizedBox(height: 8),
          Text(
            context.tr('add_staff_dialog.permissions_chief_info'),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('add_staff_dialog.notes_section_title'),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,

          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: context.tr('add_staff_dialog.notes_placeholder'),
            border: OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(),
            ),
          ),
          textCapitalization: TextCapitalization.sentences,
        ),
      ],
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = _createDirectly
          ? CreateStaffDirectlyRequest.fromInput(
              nickname: _nicknameController.text.trim(),
              groomerRole: _selectedRole,
              notes: _notesController.text.trim().isNotEmpty
                  ? _notesController.text.trim()
                  : null,
            )
          : AddStaffRequest.fromInput(
              phoneNumber: _completePhoneNumber.isNotEmpty
                  ? _completePhoneNumber
                  : _phoneController.text.trim(),
              nickname: _nicknameController.text.trim().isNotEmpty
                  ? _nicknameController.text.trim()
                  : null,
              groomerRole: _selectedRole,
              clientDataPermission: _selectedPermission,
              notes: _notesController.text.trim().isNotEmpty
                  ? _notesController.text.trim()
                  : null,
            );

      DebugLogger.logVerbose('🔄 AddStaffDialog: Sending request - createDirectly: $_createDirectly');
      if (!_createDirectly) {
        DebugLogger.logVerbose('   - Phone: ${_completePhoneNumber.isNotEmpty ? _completePhoneNumber : _phoneController.text.trim()}');
        DebugLogger.logVerbose('   - Role: ${_selectedRole.name}');
        DebugLogger.logVerbose('   - Permission: ${_selectedPermission.name}');
      }
      
      final response = _createDirectly
          ? await StaffService.createStaffDirectlyInCurrentSalon(request as CreateStaffDirectlyRequest)
          : await StaffService.addStaffToCurrentSalon(request as AddStaffRequest);

      DebugLogger.logVerbose('🔄 AddStaffDialog: Response - success: ${response.success}, error: ${response.error}');
      
      if (response.success) {
        if (mounted) {
          Navigator.of(context).pop();
          UINotificationService.showSuccess(
            context: context,
            title: _createDirectly ? context.tr('add_staff_dialog.success_member_created_title') : context.tr('add_staff_dialog.success_invitation_sent_title'),
            message: _createDirectly
                ? context.tr('add_staff_dialog.success_member_created_message', params: {'name': _nicknameController.text.trim()})
                : context.tr('add_staff_dialog.success_invitation_sent_message', params: {'phone': _completePhoneNumber.isNotEmpty ? _completePhoneNumber : _phoneController.text.trim()}),
          );
          widget.onSuccess?.call();
        }
      } else {
        if (mounted) {
          UINotificationService.showError(
            context: context,
            title: context.tr('add_staff_dialog.error_title'),
            message: response.error ?? context.tr('add_staff_dialog.error_generic_message'),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('add_staff_dialog.error_unexpected_title'),
          message: context.tr('add_staff_dialog.error_unexpected_message'),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

/// Custom phone number formatter for Romanian numbers
class _PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Don't format if user is deleting
    if (newValue.text.length < oldValue.text.length) {
      return newValue;
    }

    // Don't format if text is empty
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // Apply formatting
    final formatted = PhoneNumberUtils.formatForInput(newValue.text);

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
