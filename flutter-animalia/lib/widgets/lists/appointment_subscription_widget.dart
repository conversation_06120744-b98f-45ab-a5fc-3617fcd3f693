import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/appointment_subscription.dart';
import '../../models/appointment.dart';
import '../../models/client.dart';
import '../../services/appointment_subscription_service.dart';
import '../../utils/snack_bar_utils.dart';
import '../dialogs/appointment_subscription_details_dialog.dart';
import '../dialogs/subscription_appointments_dialog.dart';
import '../../l10n/app_localizations.dart';

class AppointmentSubscriptionWidget extends StatefulWidget {
  final Client client;
  final String salonId;
  final VoidCallback? onSubscriptionChanged;

  const AppointmentSubscriptionWidget({
    super.key,
    required this.client,
    required this.salonId,
    this.onSubscriptionChanged,
  });

  @override
  State<AppointmentSubscriptionWidget> createState() => _AppointmentSubscriptionWidgetState();
}

class _AppointmentSubscriptionWidgetState extends State<AppointmentSubscriptionWidget> {
  List<AppointmentSubscription> _subscriptions = [];
  bool _isLoading = true;
  String? _error;
  Map<String, List<Appointment>> _subscriptionAppointments = {};
  Set<String> _expandedSubscriptions = {};

  @override
  void initState() {
    super.initState();
    _loadSubscriptions();
  }

  Future<void> _loadSubscriptions() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final subscriptions = await AppointmentSubscriptionService.getClientSubscriptions(
        widget.salonId,
        widget.client.id,
      );

      setState(() {
        _subscriptions = subscriptions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadSubscriptionAppointments(String subscriptionId) async {
    if (_subscriptionAppointments.containsKey(subscriptionId)) {
      return; // Already loaded
    }

    try {
      final appointments = await AppointmentSubscriptionService.getSubscriptionAppointments(
        widget.salonId,
        subscriptionId,
      );

      setState(() {
        _subscriptionAppointments[subscriptionId] = appointments;
      });
    } catch (e) {
      // Handle error silently or show a snackbar
      if (mounted) {
      }
    }
  }

  void _toggleSubscriptionExpansion(String subscriptionId) {
    setState(() {
      if (_expandedSubscriptions.contains(subscriptionId)) {
        _expandedSubscriptions.remove(subscriptionId);
      } else {
        _expandedSubscriptions.add(subscriptionId);
        _loadSubscriptionAppointments(subscriptionId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                context.tr('subscriptions.error_loading_subscriptions'),
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadSubscriptions,
                child: Text(context.tr('subscriptions.retry')),
              ),
            ],
          ),
        ),
      );
    }

    if (_subscriptions.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.repeat_outlined,
                size: 64,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 16),
              Text(
                context.tr('subscriptions.no_recurring_subscriptions'),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                context.tr('subscriptions.recurring_subscriptions_description'),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadSubscriptions,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _subscriptions.length,
        itemBuilder: (context, index) {
          final subscription = _subscriptions[index];
          return _buildSubscriptionCard(subscription);
        },
      ),
    );
  }

  Widget _buildSubscriptionCard(AppointmentSubscription subscription) {
    final theme = Theme.of(context);
    final statusColor = _getStatusColor(subscription.status);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _showSubscriptionDetails(subscription),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          subscription.frequencyDescription,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          context.tr('subscriptions.appointments_completed', params: {
                            'completed': '${subscription.totalRepetitions - subscription.remainingRepetitions}',
                            'total': '${subscription.totalRepetitions}'
                          }),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: statusColor.withOpacity(0.3)),
                    ),
                    child: Text(
                      subscription.status,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: statusColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Progress bar
              LinearProgressIndicator(
                value: subscription.progressPercentage,
                backgroundColor: theme.colorScheme.surfaceVariant,
                valueColor: AlwaysStoppedAnimation<Color>(statusColor),
              ),
              
              const SizedBox(height: 12),
              
              // Time and details
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${DateFormat('HH:mm').format(subscription.startTime)} - ${DateFormat('HH:mm').format(subscription.endTime)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  if (subscription.remainingRepetitions > 0) ...[
                    Icon(
                      Icons.schedule,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      context.tr('subscriptions.remaining', params: {'count': '${subscription.remainingRepetitions}'}),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),

              // Related appointments section
              const SizedBox(height: 12),
              _buildAppointmentsSection(subscription),

              // Notes if available
              if (subscription.notes != null && subscription.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  subscription.notes!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppointmentsSection(AppointmentSubscription subscription) {
    final theme = Theme.of(context);
    final isExpanded = _expandedSubscriptions.contains(subscription.id);
    final appointments = _subscriptionAppointments[subscription.id];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Appointments header with expand/collapse button
        InkWell(
          onTap: () => _toggleSubscriptionExpansion(subscription.id),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                Icon(
                  Icons.event,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    context.tr('subscriptions.subscription_appointments'),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
                Icon(
                  isExpanded ? Icons.expand_less : Icons.expand_more,
                  color: theme.colorScheme.primary,
                ),
              ],
            ),
          ),
        ),

        // Appointments list (when expanded)
        if (isExpanded) ...[
          const SizedBox(height: 8),
          if (appointments == null) ...[
            // Loading state
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            ),
          ] else if (appointments.isEmpty) ...[
            // Empty state
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    context.tr('subscriptions.no_appointments_for_subscription'),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            // Appointments list
            Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  // Show first 3 appointments
                  ...appointments.take(3).map((appointment) => _buildCompactAppointmentItem(appointment)),

                  // Show "View all" button if there are more than 3 appointments
                  if (appointments.length > 3) ...[
                    const Divider(height: 1),
                    InkWell(
                      onTap: () => _showAllAppointments(subscription),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              context.tr('subscriptions.view_all_appointments', params: {'count': '${appointments.length}'}),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.arrow_forward,
                              size: 14,
                              color: theme.colorScheme.primary,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ],
    );
  }

  Widget _buildCompactAppointmentItem(Appointment appointment) {
    final theme = Theme.of(context);
    final isToday = _isToday(appointment.startTime);
    final isPast = appointment.startTime.isBefore(DateTime.now());
    final isCompleted = appointment.status.toLowerCase() == 'completed';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.1),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Date indicator
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getAppointmentStatusColor(appointment.status).withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: _getAppointmentStatusColor(appointment.status).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  DateFormat('dd').format(appointment.startTime),
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: _getAppointmentStatusColor(appointment.status),
                  ),
                ),
                Text(
                  DateFormat('MMM').format(appointment.startTime).toUpperCase(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontSize: 8,
                    color: _getAppointmentStatusColor(appointment.status),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 12),

          // Appointment details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        appointment.petName,
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isPast && !isCompleted
                              ? theme.colorScheme.onSurfaceVariant
                              : null,
                        ),
                      ),
                    ),
                    if (appointment.sequenceNumber != null) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '#${appointment.sequenceNumber}',
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontSize: 9,
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      '${DateFormat('HH:mm').format(appointment.startTime)} - ${DateFormat('HH:mm').format(appointment.endTime)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontSize: 10,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    if (isToday) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          context.tr('subscriptions.today'),
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontSize: 9,
                            fontWeight: FontWeight.w600,
                            color: Colors.orange,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // Status indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getAppointmentStatusColor(appointment.status).withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: _getAppointmentStatusColor(appointment.status).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Text(
              _getAppointmentStatusDisplayText(appointment.status),
              style: theme.textTheme.bodySmall?.copyWith(
                fontSize: 9,
                fontWeight: FontWeight.w600,
                color: _getAppointmentStatusColor(appointment.status),
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }

  Color _getAppointmentStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return Colors.blue;
      case 'confirmed':
        return Colors.green;
      case 'in_progress':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'no_show':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getAppointmentStatusDisplayText(String status) {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return 'Programat';
      case 'confirmed':
        return 'Confirmat';
      case 'in_progress':
        return 'În curs';
      case 'completed':
        return 'Finalizat';
      case 'cancelled':
        return 'Anulat';
      case 'no_show':
        return 'Absent';
      default:
        return status;
    }
  }

  void _showAllAppointments(AppointmentSubscription subscription) {
    showDialog(
      context: context,
      builder: (context) => SubscriptionAppointmentsDialog(
        subscription: subscription,
        salonId: widget.salonId,
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'paused':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      case 'completed':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _showSubscriptionDetails(AppointmentSubscription subscription) {
    showDialog(
      context: context,
      builder: (context) => AppointmentSubscriptionDetailsDialog(
        subscription: subscription,
        salonId: widget.salonId,
        onSubscriptionChanged: () {
          _loadSubscriptions();
          widget.onSubscriptionChanged?.call();
        },
      ),
    );
  }
}
