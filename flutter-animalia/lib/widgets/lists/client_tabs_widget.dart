import 'package:animaliaproject/models/appointment.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../config/theme/app_theme.dart';
import '../../models/client.dart';
import '../../models/pet.dart';
import '../../models/subscription.dart';
import '../../utils/debug_logger.dart';
import '../../utils/genie_animation.dart';
import '../../l10n/app_localizations.dart';
import '../cards/pet_profile_card.dart';
import '../common/custom_bottom_sheet.dart';
import '../../screens/clients/animal_details_screen.dart';
import '../dialogs/appointment_details_dialog.dart';
import 'appointment_subscription_widget.dart';
import '../../services/auth/auth_service.dart';

class ClientTabsWidget extends StatefulWidget {
  final Client client;
  final List<Appointment> appointments;
  final List<Subscription> subscriptions;
  final List<Pet> pets;
  final bool isLoading;
  final VoidCallback? onAddPet; // Callback for adding pets
  final VoidCallback? onPetDeleted; // Callback for when a pet is deleted

  const ClientTabsWidget({
    super.key,
    required this.client,
    required this.appointments,
    required this.subscriptions,
    required this.pets,
    this.isLoading = false,
    this.onAddPet,
    this.onPetDeleted,
  });

  @override
  State<ClientTabsWidget> createState() => _ClientTabsWidgetState();
}

class _ClientTabsWidgetState extends State<ClientTabsWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Ensure we have a minimum height to work with
        final availableHeight = constraints.maxHeight;
        if (availableHeight < 100) {
          // If height is too constrained, return a simple loading indicator
          return Center(
            child: CircularProgressIndicator(color: Theme.of(context).colorScheme.onSurface),
          );
        }

        return SizedBox(
          height: availableHeight,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Tab bar with flexible height
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: TabBar(
                  controller: _tabController,
                  labelColor: Theme.of(context).colorScheme.primary,
                  unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
                  indicatorColor: Theme.of(context).colorScheme.primary,
                  indicatorWeight: 3,
                  labelStyle: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 11,
                  ),
                  isScrollable: false,
                  labelPadding: const EdgeInsets.symmetric(horizontal: 4),
                  tabs: [
                    Tab(
                      text: AppLocalizations.of(context).translate('pets.pets'),
                      icon: Icon(Icons.pets, size: 16),
                    ),
                    Tab(
                      text: AppLocalizations.of(context).translate('appointments.appointments'),
                      icon: Icon(Icons.calendar_today, size: 16),
                    ),
                    Tab(
                      text: AppLocalizations.of(context).translate('subscriptions.subscriptions'),
                      icon: Icon(Icons.card_membership, size: 16),
                    ),
                  ],
                ),
              ),

              // Tab content with proper constraints
              Expanded(
                child: ClipRect(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildPetsTab(),
                      _buildAppointmentsTab(),
                      _buildSubscriptionsTab(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPetsTab() {
    if (widget.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (widget.pets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.pets,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: 16),
            Text(
              AppLocalizations.of(context).translate('pets.no_pets_registered'),
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: widget.onAddPet,
              icon:  Icon(Icons.pets),
              label: Text(AppLocalizations.of(context).translate('pets.add_first_pet')),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Big visible "Add Pet" button at the top
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          child: ElevatedButton.icon(
            onPressed: widget.onAddPet,
            icon:  Icon(Icons.add),
            label: Text(AppLocalizations.of(context).translate('pets.add_new_pet')),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: AppColors.white,
              padding: const EdgeInsets.symmetric(vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        ),

        // Pets list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: widget.pets.length,
            itemBuilder: (context, index) {
              final pet = widget.pets[index];
              return PetProfileCard(
                pet: pet,
                onTap: () async {
                  final result = await Navigator.of(context).push<bool>(
                    MaterialPageRoute(
                      builder: (context) => AnimalDetailsScreen(pet: pet),
                    ),
                  );

                  // If pet was deleted, call the callback to refresh the list
                  if (result == true && widget.onPetDeleted != null) {
                    widget.onPetDeleted!();
                  }
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAppointmentsTab() {
    if (widget.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (widget.appointments.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.calendar_today,
        title: AppLocalizations.of(context).translate('appointments.no_appointments'),
      );
    }

    // Group appointments by status
    final upcoming = widget.appointments
        .where((apt) => apt.startTime.isAfter(DateTime.now()))
        .toList();
    final past = widget.appointments
        .where((apt) => apt.startTime.isBefore(DateTime.now()))
        .toList();

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        if (upcoming.isNotEmpty) ...[
          _buildSectionHeader(AppLocalizations.of(context).translate('appointments.upcoming_appointments'), upcoming.length),
          ...upcoming.map((apt) => _buildAppointmentCard(apt)),
          SizedBox(height: 16),
        ],
        if (past.isNotEmpty) ...[
          _buildSectionHeader(AppLocalizations.of(context).translate('appointments.appointment_history'), past.length),
          ...past.map((apt) => _buildAppointmentCard(apt)),
        ],
      ],
    );
  }

  Widget _buildSubscriptionsTab() {
    // Show both regular subscriptions and appointment subscriptions
    return Column(
      children: [
        // Regular subscriptions section (existing)
        if (widget.subscriptions.isNotEmpty) ...[
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    AppLocalizations.of(context).translate('subscriptions.service_subscriptions'),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: widget.subscriptions.length,
                    itemBuilder: (context, index) {
                      final subscription = widget.subscriptions[index];
                      return _buildSubscriptionCard(subscription);
                    },
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
        ],

        // Appointment subscriptions section (new)
        Expanded(
          flex: widget.subscriptions.isEmpty ? 1 : 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.repeat,
                      size: 20,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context).translate('subscriptions.recurring_appointments'),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: FutureBuilder<String?>(
                  future: _getCurrentSalonId(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    final salonId = snapshot.data;
                    if (salonId == null) {
                      return Center(
                        child: Text(AppLocalizations.of(context).translate('subscriptions.salon_info_error')),
                      );
                    }

                    return AppointmentSubscriptionWidget(
                      client: widget.client,
                      salonId: salonId,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }



  Widget _buildSectionHeader(String title, int count) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurface,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(
                color: Theme.of(context).colorScheme.surface,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentCard(Appointment appointment) {
    final isUpcoming = appointment.startTime.isAfter(DateTime.now());

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        onTap: () => _showAppointmentSummary(appointment),
        leading: CircleAvatar(
          backgroundColor: isUpcoming ? Theme.of(context).colorScheme.primary : AppColors.taupe,
          child: Icon(
            isUpcoming ? Icons.schedule : Icons.check,
            color: Theme.of(context).colorScheme.onPrimary,
            size: 20,
          ),
        ),
        title: Text(
          appointment.service ?? AppLocalizations.of(context).translate('appointments.unknown_service'),
          style: TextStyle(fontWeight: FontWeight.w600),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${appointment.petName} • ${DateFormat('dd MMM yyyy, HH:mm').format(appointment.startTime)}',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            if (appointment.notes.isNotEmpty)
              Text(
                appointment.notes,
                style: TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              appointment.status ?? AppLocalizations.of(context).translate('appointments.unknown_status'),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: _getStatusColor(appointment.status ?? ''),
              ),
            ),
            if (appointment.isPaid)
              Icon(
                Icons.check_circle,
                color: Colors.green, // Keep green for paid status
                size: 16,
              ),
          ],
        ),
      ),
    );
  }

  void _showAppointmentSummary(Appointment appointment) {
    GenieAnimation.showGenieDialog(
      context: context,
      dialog: AppointmentDetailsDialog(appointment: appointment),
    );
  }

  Widget _buildSubscriptionCard(Subscription subscription) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    subscription.name,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getSubscriptionStatusColor(subscription.status),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    subscription.status,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 6),
            Text(
              subscription.description,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontSize: 12,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '${AppLocalizations.of(context).translate('subscriptions.sessions')}: ${subscription.sessionsUsed}/${subscription.sessionsIncluded}',
                        style: TextStyle(fontSize: 11),
                      ),
                      SizedBox(height: 3),
                      LinearProgressIndicator(
                        value: subscription.usagePercentage / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          subscription.usagePercentage > 80
                              ? Colors.red
                              : Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  '${subscription.price.toStringAsFixed(0)} RON',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }



  Color _getStatusColor(String status) {
    return AppTheme.getStatusColor(context, status);
  }

  Color _getSubscriptionStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'activ':
        return AppTheme.getStatusColor(context, 'success');
      case 'expirat':
        return AppTheme.getStatusColor(context, 'error');
      case 'epuizat':
        return AppTheme.getStatusColor(context, 'warning');
      case 'inactiv':
        return Theme.of(context).colorScheme.onSurfaceVariant;
      default:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  /// Helper method to get current salon ID
  Future<String?> _getCurrentSalonId() async {
    try {
      return await AuthService.getCurrentSalonId();
    } catch (e) {
      DebugLogger.logError('Error getting salon ID: $e');
      return null;
    }
  }
}
