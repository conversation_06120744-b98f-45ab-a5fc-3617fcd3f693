import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/locale_provider.dart';

/// A reusable language switcher widget that displays available languages
/// as a list, making it easy to add new languages by just adding translation files
class LanguageSwitcher extends StatelessWidget {
  final bool showLabel;
  final bool useDropdown;
  final Color? backgroundColor;
  final Color? textColor;

  const LanguageSwitcher({
    Key? key,
    this.showLabel = true,
    this.useDropdown = false,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use dropdown for compact display (e.g., app bar)
    if (useDropdown) {
      return _buildDropdownSwitcher(context);
    }
    // Use list view for settings screen
    return _buildListView(context);
  }

  /// Build a list view of language options
  Widget _buildListView(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final currentLocale = localeProvider.locale;

    return Column(
      children: _supportedLocales.map((locale) {
        final languageInfo = _getLanguageInfo(locale.languageCode);
        final isSelected = currentLocale.languageCode == locale.languageCode;
        final isAvailable = _isLanguageAvailable(locale.languageCode);

        return Card(
          elevation: isSelected ? 2 : 0,
          color: isSelected
              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
              : Colors.transparent,
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Colors.grey.withValues(alpha: 0.2),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: ListTile(
            enabled: isAvailable,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            leading: Text(
              languageInfo['flag']!,
              style: TextStyle(
                fontSize: 32,
                color: isAvailable ? null : Colors.grey,
              ),
            ),
            title: Text(
              languageInfo['name']!,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: 16,
                color: isAvailable
                    ? (isSelected
                        ? Theme.of(context).primaryColor
                        : Theme.of(context).colorScheme.onSurface)
                    : Colors.grey,
              ),
            ),
            subtitle: !isAvailable
                ? Text(
                    'Coming soon',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  )
                : null,
            trailing: isSelected
                ? Icon(
                    Icons.check_circle,
                    color: Theme.of(context).primaryColor,
                    size: 28,
                  )
                : (isAvailable
                    ? Icon(
                        Icons.circle_outlined,
                        color: Colors.grey.shade400,
                        size: 28,
                      )
                    : Icon(
                        Icons.lock_outline,
                        color: Colors.grey.shade400,
                        size: 24,
                      )),
            onTap: isAvailable && !isSelected
                ? () => _changeLanguage(context, locale)
                : null,
          ),
        );
      }).toList(),
    );
  }

  /// Build a dropdown-style language switcher (for compact display)
  Widget _buildDropdownSwitcher(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final currentLocale = localeProvider.locale;

    return Container(
      constraints: const BoxConstraints(minWidth: 120),
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<Locale>(
          value: currentLocale,
          icon: Icon(
            Icons.arrow_drop_down,
            color: textColor ?? Theme.of(context).colorScheme.onSurface,
            size: 20,
          ),
          style: TextStyle(
            color: textColor ?? Theme.of(context).colorScheme.onSurface,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          dropdownColor: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          isDense: true,
          selectedItemBuilder: (BuildContext context) {
            return _supportedLocales.map<Widget>((Locale locale) {
              final languageInfo = _getLanguageInfo(locale.languageCode);
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    languageInfo['flag']!,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      languageInfo['name']!,
                      style: TextStyle(
                        color: textColor ?? Theme.of(context).colorScheme.onSurface,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              );
            }).toList();
          },
          items: _supportedLocales.map((locale) {
            final languageInfo = _getLanguageInfo(locale.languageCode);
            final isAvailable = _isLanguageAvailable(locale.languageCode);

            return DropdownMenuItem<Locale>(
              value: locale,
              enabled: isAvailable,
              child: Container(
                constraints: const BoxConstraints(minWidth: 150),
                child: Row(
                  children: [
                    Text(
                      languageInfo['flag']!,
                      style: TextStyle(
                        fontSize: 18,
                        color: isAvailable ? null : Colors.grey,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        languageInfo['name']!,
                        style: TextStyle(
                          color: isAvailable
                              ? Theme.of(context).colorScheme.onSurface
                              : Colors.grey,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (!isAvailable) ...[
                      const SizedBox(width: 8),
                      Text(
                        '(Soon)',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey.shade600,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            );
          }).toList(),
          onChanged: (Locale? newLocale) {
            if (newLocale != null &&
                newLocale != currentLocale &&
                _isLanguageAvailable(newLocale.languageCode)) {
              _changeLanguage(context, newLocale);
            }
          },
        ),
      ),
    );
  }

  /// Change the app language
  void _changeLanguage(BuildContext context, Locale newLocale) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    await localeProvider.setLocale(newLocale);

    // Show a brief confirmation
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _getLanguageChangeMessage(newLocale.languageCode),
          ),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// Check if a language translation file is available
  bool _isLanguageAvailable(String languageCode) {
    // Currently available languages (have translation files)
    return languageCode == 'ro' || languageCode == 'en';
  }

  /// Get language information (flag and name)
  Map<String, String> _getLanguageInfo(String languageCode) {
    switch (languageCode) {
      case 'ro':
        return {'flag': '🇷🇴', 'name': 'Română'};
      case 'en':
        return {'flag': '🇬🇧', 'name': 'English'};
      case 'es':
        return {'flag': '🇪🇸', 'name': 'Español'};
      case 'fr':
        return {'flag': '🇫🇷', 'name': 'Français'};
      case 'de':
        return {'flag': '🇩🇪', 'name': 'Deutsch'};
      case 'it':
        return {'flag': '🇮🇹', 'name': 'Italiano'};
      case 'pt':
        return {'flag': '🇵🇹', 'name': 'Português'};
      case 'nl':
        return {'flag': '🇳🇱', 'name': 'Nederlands'};
      case 'pl':
        return {'flag': '🇵🇱', 'name': 'Polski'};
      case 'ru':
        return {'flag': '🇷🇺', 'name': 'Русский'};
      case 'hu':
        return {'flag': '🇭🇺', 'name': 'Magyar'};
      case 'cs':
        return {'flag': '🇨🇿', 'name': 'Čeština'};
      case 'bg':
        return {'flag': '🇧🇬', 'name': 'Български'};
      case 'uk':
        return {'flag': '🇺🇦', 'name': 'Українська'};
      case 'tr':
        return {'flag': '🇹🇷', 'name': 'Türkçe'};
      case 'el':
        return {'flag': '🇬🇷', 'name': 'Ελληνικά'};
      case 'sr':
        return {'flag': '🇷🇸', 'name': 'Српски'};
      case 'hr':
        return {'flag': '🇭🇷', 'name': 'Hrvatski'};
      case 'sk':
        return {'flag': '🇸🇰', 'name': 'Slovenčina'};
      case 'sl':
        return {'flag': '🇸🇮', 'name': 'Slovenščina'};
      default:
        return {'flag': '🌐', 'name': languageCode.toUpperCase()};
    }
  }

  /// Get language change confirmation message
  String _getLanguageChangeMessage(String languageCode) {
    final info = _getLanguageInfo(languageCode);
    return '${info['flag']} Language changed to ${info['name']}';
  }

  /// List of supported locales
  /// To add a new language:
  /// 1. Add the Locale here
  /// 2. Create assets/translations/{code}.json file
  /// 3. Update _isLanguageAvailable() to include the language code
  /// The language info is automatically retrieved from _getLanguageInfo()
  static const List<Locale> _supportedLocales = [
    Locale('ro'), // Romanian - Active ✓
    Locale('en'), // English - Active ✓
    Locale('es'), // Spanish - Placeholder
    Locale('fr'), // French - Placeholder
    Locale('de'), // German - Placeholder
    Locale('it'), // Italian - Placeholder
    Locale('pt'), // Portuguese - Placeholder
    Locale('nl'), // Dutch - Placeholder
    Locale('pl'), // Polish - Placeholder
    Locale('ru'), // Russian - Placeholder
    Locale('hu'), // Hungarian - Placeholder
    Locale('cs'), // Czech - Placeholder
    Locale('bg'), // Bulgarian - Placeholder
    Locale('uk'), // Ukrainian - Placeholder
    Locale('tr'), // Turkish - Placeholder
    Locale('el'), // Greek - Placeholder
    Locale('sr'), // Serbian - Placeholder
    Locale('hr'), // Croatian - Placeholder
    Locale('sk'), // Slovak - Placeholder
    Locale('sl'), // Slovenian - Placeholder
  ];
}
