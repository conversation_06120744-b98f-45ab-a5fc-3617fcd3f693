import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';
import '../../models/client.dart';
import '../../providers/mass_sms_provider.dart';
import '../../services/auth/auth_service.dart';

class ClientSelectionStep extends StatefulWidget {
  final List<Client> selectedClients;
  final Map<String, dynamic> filterCriteria;
  final Function(List<Client>) onClientsChanged;
  final Function(Map<String, dynamic>) onFilterChanged;

  const ClientSelectionStep({
    super.key,
    required this.selectedClients,
    required this.filterCriteria,
    required this.onClientsChanged,
    required this.onFilterChanged,
  });

  @override
  State<ClientSelectionStep> createState() => _ClientSelectionStepState();
}

class _ClientSelectionStepState extends State<ClientSelectionStep> {
  final TextEditingController _searchController = TextEditingController();
  bool _showFilters = false;
  
  // Filter values
  List<String> _selectedPetSpecies = [];
  DateTimeRange? _lastVisitRange;
  
  // Customer loyalty filter values
  int? _minAppointments;
  String? _loyaltyTimePeriod;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    _applyFilters();
  }

  void _applyFilters() async {
    final filters = <String, dynamic>{
      'hasPhone': true,
      'canReceiveSms': true,
    };

    if (_searchController.text.isNotEmpty) {
      filters['searchQuery'] = _searchController.text;
    }

    if (_selectedPetSpecies.isNotEmpty) {
      filters['petSpecies'] = _selectedPetSpecies;
    }

    if (_lastVisitRange != null) {
      filters['lastVisitFrom'] = _lastVisitRange!.start.toIso8601String();
      filters['lastVisitTo'] = _lastVisitRange!.end.toIso8601String();
    }

    if (_minAppointments != null && _loyaltyTimePeriod != null) {
      filters['loyaltyMinAppointments'] = _minAppointments;
      filters['loyaltyTimePeriod'] = _loyaltyTimePeriod;
    }

    final provider = Provider.of<MassSmsProvider>(context, listen: false);

    // Check if we have any actual filter criteria beyond the defaults
    final hasActiveFilters = _searchController.text.isNotEmpty ||
                            _selectedPetSpecies.isNotEmpty ||
                            _lastVisitRange != null ||
                            (_minAppointments != null && _loyaltyTimePeriod != null);

    if (hasActiveFilters) {
      // Check if we need backend filtering (for loyalty or complex filters)
      final needsBackendFiltering = (_minAppointments != null && _loyaltyTimePeriod != null);
      
      if (needsBackendFiltering) {
        // Use backend filtering for loyalty and other complex filters
        final salonId = await AuthService.getCurrentSalonId();
        if (salonId != null && mounted) {
          await provider.filterClients(salonId, filters);
        }
      } else {
        // Apply filters locally using client pet information
        _applyLocalFilters(provider);
      }
    } else {
      // No active filters, show all clients
      provider.clearFilters();
    }

    // Always notify parent widget
    widget.onFilterChanged(filters);
  }

  void _applyLocalFilters(MassSmsProvider provider) {
    var filteredClients = List<Client>.from(provider.allClients);

    // Filter by search query (name, phone, email)
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      filteredClients = filteredClients.where((client) {
        return client.name.toLowerCase().contains(query) ||
               client.phone.toLowerCase().contains(query) ||
               client.email.toLowerCase().contains(query);
      }).toList();
    }

    // Filter by pet species
    if (_selectedPetSpecies.isNotEmpty) {
      filteredClients = filteredClients.where((client) {
        return client.pets.any((pet) {
          final petSpecies = pet.species?.toLowerCase();
          if (petSpecies == null) return false;

          // Check both English and Romanian values
          return _selectedPetSpecies.any((selectedSpecies) {
            return _speciesMatches(petSpecies, selectedSpecies);
          });
        });
      }).toList();
    }

    // Apply filtered clients to provider
    provider.setFilteredClients(filteredClients);
  }

  String _getSpeciesDisplayName(String species) {
    switch (species.toLowerCase()) {
      case 'dog':
        return 'câine';
      case 'cat':
        return 'pisică';
      case 'other':
        return 'altele';
      case 'câine':
        return 'câine';
      case 'pisică':
        return 'pisică';
      case 'altele':
        return 'altele';
      default:
        return species;
    }
  }

  bool _speciesMatches(String petSpecies, String selectedSpecies) {
    // Convert selected filter to both English and Romanian equivalents
    final equivalents = _getSpeciesEquivalents(selectedSpecies);
    return equivalents.contains(petSpecies);
  }

  List<String> _getSpeciesEquivalents(String selectedSpecies) {
    switch (selectedSpecies.toLowerCase()) {
      case 'dog':
        return ['dog', 'câine', 'caine'];
      case 'cat':
        return ['cat', 'pisică', 'pisica'];
      case 'other':
        return ['other', 'altele'];
      default:
        return [selectedSpecies.toLowerCase()];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MassSmsProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                context.tr('mass_sms.client_selection_title'),
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                context.tr('mass_sms.client_selection_subtitle'),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Search and filters
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // Search bar
                      TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: context.tr('mass_sms.search_clients_placeholder'),
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          suffixIcon: IconButton(
                            onPressed: () {
                              setState(() {
                                _showFilters = !_showFilters;
                              });
                            },
                            icon: Icon(
                              _showFilters ? Icons.filter_list_off : Icons.filter_list,
                            ),
                          ),
                        ),
                      ),
                      
                      // Filters
                      if (_showFilters) ...[
                        const SizedBox(height: 16),
                        _buildFiltersSection(),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Client count and actions
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              context.tr('mass_sms.clients_found_count').replaceAll('{0}', '${provider.filteredClients.length}'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              context.tr('mass_sms.selected_count').replaceAll('{0}', '${widget.selectedClients.length}'),
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            height: 32,
                            child: ElevatedButton(
                              onPressed: provider.filteredClients.isNotEmpty
                                  ? _selectAllClients
                                  : null,
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                                minimumSize: const Size(0, 32),
                              ),
                              child: Text(
                                context.tr('mass_sms.select_all_button'),
                                style: const TextStyle(fontSize: 12),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          SizedBox(
                            height: 32,
                            child: OutlinedButton(
                              onPressed: widget.selectedClients.isNotEmpty
                                  ? _clearSelection
                                  : null,
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                                minimumSize: const Size(0, 32),
                              ),
                              child: Text(
                                context.tr('mass_sms.clear_selection_button'),
                                style: const TextStyle(fontSize: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Client list
              if (provider.isLoadingClients)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: CircularProgressIndicator(),
                  ),
                )
              else if (provider.clientsError != null)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        const Icon(Icons.error, color: Colors.red, size: 48),
                        const SizedBox(height: 8),
                        Text(
                          'Eroare la încărcarea clienților',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(provider.clientsError!),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            // Retry loading clients
                          },
                          child: const Text('Încearcă din nou'),
                        ),
                      ],
                    ),
                  ),
                )
              else if (provider.filteredClients.isEmpty)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      children: [
                        const Icon(Icons.people_outline, size: 48, color: Colors.grey),
                        const SizedBox(height: 16),
                        const Text(
                          'Nu s-au găsit clienți',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Încearcă să modifici criteriile de căutare.',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                )
              else
                _buildClientsList(provider.filteredClients),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFiltersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('mass_sms.advanced_filters_title'),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 16),
        
        // Customer loyalty filter
        Text(context.tr('mass_sms.customer_loyalty_label')),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: DropdownButtonFormField<int>(
                value: _minAppointments,
                decoration: InputDecoration(
                  labelText: context.tr('mass_sms.appointments_label'),
                  border: const OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: [0, 1, 3, 5, 10, 15, 20].map((count) {
                  return DropdownMenuItem(
                    value: count,
                    child: Text(count == 0 ? '0' : '$count+'),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _minAppointments = value;
                  });
                  if (value != null && _loyaltyTimePeriod != null) {
                    _applyFilters();
                  }
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 3,
              child: DropdownButtonFormField<String>(
                value: _loyaltyTimePeriod,
                decoration: InputDecoration(
                  labelText: context.tr('mass_sms.period_label'),
                  border: const OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: const [
                  DropdownMenuItem(value: '1_week', child: Text('1 săptămână')),
                  DropdownMenuItem(value: '1_month', child: Text('1 lună')),
                  DropdownMenuItem(value: '3_months', child: Text('3 luni')),
                  DropdownMenuItem(value: '6_months', child: Text('6 luni')),
                  DropdownMenuItem(value: '1_year', child: Text('1 an')),
                ],
                onChanged: (value) {
                  setState(() {
                    _loyaltyTimePeriod = value;
                  });
                  if (_minAppointments != null && value != null) {
                    _applyFilters();
                  }
                },
              ),
            ),
          ],
        ),
        
        // Loyalty filter description
        if (_minAppointments != null && _loyaltyTimePeriod != null) ...[
          const SizedBox(height: 8),
          Text(
            _getLoyaltyFilterDescription(),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
        
        const SizedBox(height: 16),
        
        // Clear filters button
        Align(
          alignment: Alignment.centerRight,
          child: TextButton(
            onPressed: _clearFilters,
            child: const Text('Șterge filtrele'),
          ),
        ),
      ],
    );
  }

  Widget _buildClientsList(List<Client> clients) {
    return Card(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(Icons.people),
                const SizedBox(width: 8),
                Text(
                  context.tr('mass_sms.clients_list_title'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: clients.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final client = clients[index];
              final isSelected = widget.selectedClients.any((c) => c.id == client.id);
              
              return CheckboxListTile(
                value: isSelected,
                onChanged: (selected) {
                  _toggleClientSelection(client, selected ?? false);
                },
                title: Text(client.name),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(client.phone),
                    if (client.email.isNotEmpty)
                      Text(client.email, style: TextStyle(color: Colors.grey[600])),
                    if (client.pets.isNotEmpty)
                      Text(
                        '${client.pets.first.name} (${client.pets.first.species != null ? _getSpeciesDisplayName(client.pets.first.species!) : 'necunoscut'})',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                  ],
                ),
                secondary: CircleAvatar(
                  child: Text(client.name.isNotEmpty ? client.name[0].toUpperCase() : '?'),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _toggleClientSelection(Client client, bool selected) {
    final updatedSelection = List<Client>.from(widget.selectedClients);
    
    if (selected) {
      if (!updatedSelection.any((c) => c.id == client.id)) {
        updatedSelection.add(client);
      }
    } else {
      updatedSelection.removeWhere((c) => c.id == client.id);
    }
    
    widget.onClientsChanged(updatedSelection);
  }

  void _selectAllClients() {
    final provider = Provider.of<MassSmsProvider>(context, listen: false);
    widget.onClientsChanged(List.from(provider.filteredClients));
  }

  void _clearSelection() {
    widget.onClientsChanged([]);
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedPetSpecies.clear();
      _lastVisitRange = null;
      _minAppointments = null;
      _loyaltyTimePeriod = null;
    });

    // Clear filters in provider to show all clients
    final provider = Provider.of<MassSmsProvider>(context, listen: false);
    provider.clearFilters();

    // Notify parent widget with empty filters
    widget.onFilterChanged({
      'hasPhone': true,
      'canReceiveSms': true,
    });
  }

  String _getLoyaltyFilterDescription() {
    if (_minAppointments == null || _loyaltyTimePeriod == null) return '';
    
    final periodText = _getPeriodText(_loyaltyTimePeriod!);
    
    if (_minAppointments == 0) {
      return 'Clienții care au fost inactivi în $periodText';
    } else if (_minAppointments == 1) {
      return 'Clienții care au avut cel puțin o programare în $periodText';
    } else {
      return 'Clienții care au avut cel puțin $_minAppointments programări în $periodText';
    }
  }

  String _getPeriodText(String period) {
    switch (period) {
      case '1_week': return 'ultima săptămână';
      case '1_month': return 'ultima lună';
      case '3_months': return 'ultimele 3 luni';
      case '6_months': return 'ultimele 6 luni';
      case '1_year': return 'ultimul an';
      default: return 'perioada selectată';
    }
  }
}
