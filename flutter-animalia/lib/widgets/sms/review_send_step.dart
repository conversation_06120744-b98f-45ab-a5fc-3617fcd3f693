import 'package:flutter/material.dart';
import '../../models/client.dart';
import '../../providers/mass_sms_provider.dart';
import '../../services/sms_usage_service.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';

class ReviewSendStep extends StatefulWidget {
  final List<Client> selectedClients;
  final String message;
  final VoidCallback onSend;

  const ReviewSendStep({
    Key? key,
    required this.selectedClients,
    required this.message,
    required this.onSend,
  }) : super(key: key);

  @override
  State<ReviewSendStep> createState() => _ReviewSendStepState();
}

class _ReviewSendStepState extends State<ReviewSendStep> {
  int? _totalSmsQuota;
  bool _isLoadingSmsQuota = true;

  @override
  void initState() {
    super.initState();
    _loadSmsQuota();
  }

  Future<void> _loadSmsQuota() async {
    try {
      final response = await SmsUsageService.getSmsUsage();
      if (response.success && response.data != null) {
        setState(() {
          _totalSmsQuota = response.data!.remainingSms; // Changed from totalAllowed to remainingSms
          _isLoadingSmsQuota = false;
        });
      } else {
        setState(() {
          _isLoadingSmsQuota = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingSmsQuota = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MassSmsProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // Summary Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr('mass_sms.review_summary_title'),
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    _buildSummaryRow(context.tr('mass_sms.recipients_label'), '${widget.selectedClients.length}'),
                    _buildSummaryRow(context.tr('mass_sms.message_length_label'), context.tr('mass_sms.characters_text').replaceAll('{0}', '${widget.message.length}')),
                    _buildSummaryRow(context.tr('mass_sms.estimated_sms_label'), '${_calculateSmsCount()}'),
                    _buildSummaryRow(
                      context.tr('mass_sms.total_sms_label'),
                      _isLoadingSmsQuota
                          ? context.tr('mass_sms.loading_text')
                          : _totalSmsQuota != null
                              ? '$_totalSmsQuota'
                              : 'N/A',
                    ),
                    // _buildSummaryRow('Cost estimat:', '${_calculateCost()} RON'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Message Preview
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr('mass_sms.message_preview_title'),
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Text(
                        widget.message,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Recipients List
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr('mass_sms.recipients_list_title').replaceAll('{0}', '${widget.selectedClients.length}'),
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ListView.builder(
                        itemCount: widget.selectedClients.length,
                        itemBuilder: (context, index) {
                          final client = widget.selectedClients[index];
                          return ListTile(
                            dense: true,
                            leading: const Icon(Icons.person),
                            title: Text(client.name),
                            subtitle: Text(client.phone),
                            trailing: const Icon(Icons.sms, color: Colors.green),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  int _calculateSmsCount() {
    // Simple SMS count calculation (160 chars per SMS)
    return (widget.message.length / 160).ceil();
  }
}
