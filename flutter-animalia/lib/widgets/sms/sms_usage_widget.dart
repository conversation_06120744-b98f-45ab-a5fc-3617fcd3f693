import 'package:flutter/material.dart';

import '../../screens/subscription/subscription_purchase_screen.dart';
import '../../services/auth/auth_service.dart';
import '../../services/subscription_limit_service.dart';
import '../../l10n/app_localizations.dart';

/// Widget that displays SMS usage information with upgrade prompts
class SmsUsageWidget extends StatefulWidget {
  final String? salonId;
  final bool showUpgradePrompt;
  final bool compact;

  const SmsUsageWidget({
    super.key,
    this.salonId,
    this.showUpgradePrompt = true,
    this.compact = false,
  });

  @override
  State<SmsUsageWidget> createState() => _SmsUsageWidgetState();
}

class _SmsUsageWidgetState extends State<SmsUsageWidget> {
  SubscriptionLimits? _limits;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadSmsUsage();
  }

  Future<void> _loadSmsUsage() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final salonId = widget.salonId ?? await AuthService.getCurrentSalonId();
      if (salonId == null) {
        setState(() {
          _error = 'No salon ID available';
          _isLoading = false;
        });
        return;
      }

      final limits = await SubscriptionLimitService.getLimits(salonId);
      if (mounted) {
        setState(() {
          _limits = limits;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.compact 
        ? const SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        : const Center(
            child: CircularProgressIndicator(),
          );
    }

    if (_error != null || _limits == null) {
      return widget.compact 
        ? const SizedBox.shrink()
        : Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Theme.of(context).colorScheme.error,
                    size: 48,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.tr('sms_usage.loading'),
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _loadSmsUsage,
                    child: Text(context.tr('common.try_again')),
                  ),
                ],
              ),
            ),
          );
    }

    if (widget.compact) {
      return _buildCompactView();
    }

    return _buildFullView();
  }

  Widget _buildCompactView() {
    final percentage = _limits!.getSmsUsagePercentage();
    final color = _getUsageColor(percentage);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.sms,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 6),
          Text(
            _limits!.getSmsUsageText(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullView() {
    final percentage = _limits!.getSmsUsagePercentage();
    final color = _getUsageColor(percentage);
    final isApproachingLimit = _limits!.isSmsLimitApproaching();
    final isAtLimit = !_limits!.canSendSms();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.sms,
                  color: color,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('sms_usage.title'),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _limits!.getSmsUsageText(),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.showUpgradePrompt && (isApproachingLimit || isAtLimit))
                  TextButton(
                    onPressed: _navigateToUpgrade,
                    child: const Text('Upgrade'),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Progress bar
            LinearProgressIndicator(
              value: percentage / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 8,
            ),
            const SizedBox(height: 8),

            // Percentage text
            Text(
              '${percentage.toStringAsFixed(1)}% ${context.tr('common.used')}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
              ),
            ),

            // Warning or upgrade prompt
            if (widget.showUpgradePrompt && (isApproachingLimit || isAtLimit)) ...[
              const SizedBox(height: 16),
              _buildWarningMessage(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWarningMessage() {
    final isAtLimit = !_limits!.canSendSms();
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isAtLimit 
          ? Colors.red.withOpacity(0.1)
          : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isAtLimit 
            ? Colors.red.withOpacity(0.3)
            : Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isAtLimit ? Icons.error : Icons.warning,
                color: isAtLimit ? Colors.red : Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  isAtLimit 
                    ? context.tr('sms_usage.quota_exhausted')
                    : context.tr('sms_usage.quota_almost_exhausted'),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isAtLimit ? Colors.red : Colors.orange,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            isAtLimit
              ? context.tr('sms_usage.no_more_sms_message')
              : context.tr('sms_usage.approaching_limit_message'),
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _navigateToUpgrade,
              style: ElevatedButton.styleFrom(
                backgroundColor: isAtLimit ? Colors.red : Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: Text(
                isAtLimit ? context.tr('sms_usage.upgrade_now') : context.tr('sms_usage.view_plans'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getUsageColor(double percentage) {
    if (percentage >= 100) return Colors.red;
    if (percentage >= 80) return Colors.orange;
    if (percentage >= 60) return Colors.yellow[700]!;
    return Colors.green;
  }

  void _navigateToUpgrade() async {
    final salonId = widget.salonId ?? await AuthService.getCurrentSalonId();
    if (salonId != null && mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => SubscriptionPurchaseScreen(
            salonId: salonId,
          ),
        ),
      );
    }
  }
}
