import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../models/mass_sms_template.dart';
import '../../providers/mass_sms_provider.dart';
import '../../utils/sms_length_calculator.dart';
import '../../utils/template_variable_replacer.dart';
import '../../l10n/app_localizations.dart';

class MessageCompositionStep extends StatefulWidget {
  final String message;
  final MassSmsTemplate? selectedTemplate;
  final Function(String) onMessageChanged;
  final Function(MassSmsTemplate?) onTemplateSelected;

  const MessageCompositionStep({
    super.key,
    required this.message,
    required this.selectedTemplate,
    required this.onMessageChanged,
    required this.onTemplateSelected,
  });

  @override
  State<MessageCompositionStep> createState() => _MessageCompositionStepState();
}

class _MessageCompositionStepState extends State<MessageCompositionStep> {
  final TextEditingController _messageController = TextEditingController();
  final FocusNode _messageFocusNode = FocusNode();
  bool _showTemplates = false;

  @override
  void initState() {
    super.initState();
    _messageController.text = widget.message;
    _messageController.addListener(_onMessageChanged);
  }

  @override
  void dispose() {
    _messageController.removeListener(_onMessageChanged);
    _messageController.dispose();
    _messageFocusNode.dispose();
    super.dispose();
  }

  void _onMessageChanged() {
    widget.onMessageChanged(_messageController.text);
  }

  @override
  Widget build(BuildContext context) {
    final smsInfo = SmsLengthCalculator.calculateSmsInfo(_messageController.text);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            context.tr('mass_sms.compose_message_title'),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.tr('mass_sms.compose_message_subtitle'),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Template selection
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.text_snippet_outlined),
                      const SizedBox(width: 8),
                      Text(
                        context.tr('mass_sms.templates_title'),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _showTemplates = !_showTemplates;
                          });
                        },
                        child: Text(_showTemplates ? context.tr('mass_sms.hide_button') : context.tr('mass_sms.show_button')),
                      ),
                    ],
                  ),
                  
                  if (widget.selectedTemplate != null) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.selectedTemplate!.name,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  widget.selectedTemplate!.category,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              widget.onTemplateSelected(null);
                              _messageController.clear();
                            },
                            icon: const Icon(Icons.close),
                            iconSize: 20,
                          ),
                        ],
                      ),
                    ),
                  ],
                  
                  if (_showTemplates) ...[
                    const SizedBox(height: 16),
                    _buildTemplatesList(),
                  ],
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Message input
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.edit),
                      const SizedBox(width: 8),
                      Text(
                        context.tr('mass_sms.your_message_title'),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Text area
                  TextField(
                    controller: _messageController,
                    focusNode: _messageFocusNode,
                    maxLines: 6,
                    maxLength: 1600,
                    decoration: InputDecoration(
                      hintText: context.tr('mass_sms.message_placeholder'),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      counterText: '', // Hide default counter
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // SMS info
                  _buildSmsInfo(smsInfo),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Preview
          if (_messageController.text.isNotEmpty) _buildMessagePreview(),
        ],
      ),
    );
  }

  Widget _buildTemplatesList() {
    return Consumer<MassSmsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoadingTemplates) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (provider.templates.isEmpty) {
          return Text(
            context.tr('mass_sms.no_templates_available'),
            style: const TextStyle(color: Colors.grey),
          );
        }

        return Column(
          children: provider.templates.map((template) {
            // Convert template content to user-friendly text for display
            final displayContent = TemplateVariableReplacer.replaceVariablesWithPlaceholders(template.content);

            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                title: Text(template.name),
                subtitle: Text(
                  displayContent.length > 100
                      ? '${displayContent.substring(0, 100)}...'
                      : displayContent,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 13,
                  ),
                ),
                trailing: Chip(
                  label: Text(
                    template.category,
                    style: const TextStyle(fontSize: 10),
                  ),
                ),
                onTap: () {
                  widget.onTemplateSelected(template);
                  // Use user-friendly version in the text field
                  _messageController.text = TemplateVariableReplacer.replaceVariablesWithPlaceholders(template.content);
                  setState(() {
                    _showTemplates = false;
                  });
                },
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildSmsInfo(SmsInfo smsInfo) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('mass_sms.characters_count').replaceAll('{0}', smsInfo.characterCount.toString()),
                  style: TextStyle(
                    fontSize: 12,
                    color: smsInfo.characterCount > 1600 ? Colors.red : Colors.grey[700],
                  ),
                ),
                Text(
                  context.tr('mass_sms.sms_count').replaceAll('{0}', smsInfo.smsCount.toString()),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
          if (smsInfo.characterCount > 1600)
            const Icon(
              Icons.warning,
              color: Colors.red,
              size: 20,
            ),
        ],
      ),
    );
  }

  Widget _buildMessagePreview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.phone_android),
                const SizedBox(width: 8),
                Text(
                  context.tr('mass_sms.phone_preview_title'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Phone mockup
            Container(
              width: double.infinity,
              constraints: const BoxConstraints(maxWidth: 300),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                children: [
                  // Phone header
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.message, size: 16),
                        SizedBox(width: 8),
                        Text(
                          'SMS',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Message content
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    child: Text(
                      _messageController.text,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
