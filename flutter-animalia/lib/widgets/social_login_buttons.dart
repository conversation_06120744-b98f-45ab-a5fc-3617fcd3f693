import 'dart:io' show Platform;

import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../config/theme/app_theme.dart';
import '../l10n/app_localizations.dart';
import '../providers/auth_provider.dart';
import '../screens/main_layout.dart';
import '../utils/debug_logger.dart';

class SocialLoginButton extends StatefulWidget {
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final String text;
  final VoidCallback onPressed;

  const SocialLoginButton({
    super.key,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
    required this.text,
    required this.onPressed,
  });

  @override
  State<SocialLoginButton> createState() => _SocialLoginButtonState();
}

class _SocialLoginButtonState extends State<SocialLoginButton> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    final isLightButton = widget.backgroundColor == Theme.of(context).colorScheme.surface || widget.backgroundColor == AppColors.white;

    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        transform: Matrix4.diagonal3Values(
          _isPressed ? 0.95 : 1.0,
          _isPressed ? 0.95 : 1.0,
          1.0,
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                widget.backgroundColor,
                widget.backgroundColor.withValues(alpha: 0.8),
              ],
            ),
            border: isLightButton ? Border.all(
              color: Colors.black87,
              width: 0.5,
            ) : null,
            boxShadow: [
              BoxShadow(
                color: isLightButton 
                    ? Colors.black.withValues(alpha: 0.08)
                    : widget.backgroundColor.withValues(alpha: 0.3),
                blurRadius: _isPressed ? 6 : 8,
                spreadRadius: _isPressed ? 1 : 1,
                offset: Offset(0, _isPressed ? 2 : 3),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: () {
              // Add haptic feedback
              widget.onPressed();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              foregroundColor: isLightButton ? Colors.black87 : Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FaIcon(widget.icon, color: widget.iconColor, size: 22)
                    .animate()
                    .scale(delay: const Duration(milliseconds: 200))
                    .then()
                    .shimmer(duration: const Duration(milliseconds: 1000)),
                const SizedBox(width: 16),
                Text(
                  widget.text,
                  style: TextStyle(
                    color: isLightButton ? Colors.black87 : Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                )
                    .animate()
                    .fadeIn(delay: const Duration(milliseconds: 300))
                    .slideX(begin: 0.2, end: 0),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class SocialLoginButtons extends StatelessWidget {
  const SocialLoginButtons({super.key});

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    return AnimationLimiter(
      child: Column(
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 600),
          childAnimationBuilder: (widget) => SlideAnimation(
            verticalOffset: 30.0,
            child: FadeInAnimation(
              child: widget,
            ),
          ),
          children: [
        SocialLoginButton(
          icon: FontAwesomeIcons.google,
          backgroundColor: Colors.white,
          iconColor: AppColors.googleRed,
          text: context.tr('login.google_signin'),
          onPressed: () async {
            DebugLogger.logVerbose('Google Sign In button pressed');
            try {
              DebugLogger.logVerbose('Attempting Google Sign In...');
              final success = await authProvider.signInWithGoogle();
              DebugLogger.logVerbose('Google Sign In result: $success');
              if (success && context.mounted) {
                // Navigate to home after successful login
                Navigator.pushReplacementNamed(context, '/');
              } else if (context.mounted) {
                // Show user-friendly Romanian error message
                String errorMessage = 'Autentificarea cu Google a eșuat';
                if (authProvider.error != null) {
                  // Check if user cancelled the sign-in
                  if (authProvider.error!.toLowerCase().contains('cancelled') ||
                      authProvider.error!.toLowerCase().contains('cancel')) {
                    errorMessage = 'Autentificarea cu Google a fost anulată';
                  }
                }
                showTopSnackBar(context, 
                  SnackBar(
                    content: Text(errorMessage),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            } catch (e) {
              DebugLogger.logVerbose('Google Sign In exception: $e');
              if (context.mounted) {
                String errorMessage = 'Autentificarea cu Google a eșuat';
                // Check if user cancelled the sign-in
                if (e.toString().toLowerCase().contains('cancelled') ||
                    e.toString().toLowerCase().contains('cancel')) {
                  errorMessage = 'Autentificarea cu Google a fost anulată';
                }
                showTopSnackBar(context, 
                  SnackBar(
                    content: Text(errorMessage),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            }
          },
        ),
        const SizedBox(height: 12),

        // Apple Sign-In: Available on iOS and macOS only (removed from web)
        if (!kIsWeb && (Platform.isIOS || Platform.isMacOS))
          SocialLoginButton(
            icon: FontAwesomeIcons.apple,
            backgroundColor: AppColors.appleBlack,
            iconColor: Colors.white,
            text: context.tr('login.apple_signin'),
            onPressed: () async {
              DebugLogger.logVerbose('Apple Sign In button pressed');

              try {
                DebugLogger.logVerbose('Attempting Apple Sign In...');
                final success = await authProvider.signInWithApple();
                DebugLogger.logVerbose('Apple Sign In result: $success');

                // Close loading dialog
                if (context.mounted) {
                  Navigator.of(context).pop();
                }

                if (success && context.mounted) {
                  // Navigate to home after successful login
                  Navigator.pushReplacementNamed(context, '/');
                } else if (context.mounted) {
                  // Show error from provider if sign-in was not successful
                  final errorMessage = authProvider.error ?? "Apple Sign-In is not available or not configured properly";
                  showTopSnackBar(context,
                    SnackBar(
                      content: Text(errorMessage),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 5),
                    ),
                  );
                }
              } catch (e) {
                DebugLogger.logVerbose('Apple Sign In exception: $e');

                // Close loading dialog
                if (context.mounted) {
                  Navigator.of(context).pop();
                }

                if (context.mounted) {
                  showTopSnackBar(context,
                    SnackBar(
                      content: Text('Apple Sign In failed: $e'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 5),
                    ),
                  );
                }
              }
            },
          ),

        const SizedBox(height: 12),
        SocialLoginButton(
          icon: FontAwesomeIcons.phone,
          backgroundColor: Theme.of(context).colorScheme.primary,
          iconColor: Theme.of(context).colorScheme.onError,
          text: context.tr('login.phone_signin'),
          onPressed: () {
            Navigator.pushNamed(context, '/phone-login');
          },
        ),
          ],
        ),
      ),
    );
  }
}
