import 'package:flutter/material.dart';

import '../../l10n/app_localizations.dart';
import '../../models/working_hours_settings.dart';

/// Staff-specific schedule templates for groomers
class StaffScheduleTemplates {
  /// Get all available staff schedule templates
  static List<StaffScheduleTemplate> getAllTemplates(BuildContext context) => _buildTemplates(context);

  /// Get template by name
  static StaffScheduleTemplate? getTemplateByName(BuildContext context, String name) {
    try {
      return _buildTemplates(context).firstWhere((template) => template.name == name);
    } catch (e) {
      return null;
    }
  }

  static List<StaffScheduleTemplate> _buildTemplates(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return [
      StaffScheduleTemplate(
        name: localizations.translate('staff_schedule_templates.standard_groomer_name'),
        description: localizations.translate('staff_schedule_templates.standard_groomer_desc'),
        icon: Icons.person_outline,
        color: Colors.black,
        suitableForSenior: true,
        suitableForRegular: true,
        suitableForAssistant: true,
        schedule: {
          'monday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'tuesday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'wednesday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'thursday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'friday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'saturday': const DaySchedule(
            startTime: '10:00',
            endTime: '15:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'sunday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
        },
      ),
      StaffScheduleTemplate(
        name: localizations.translate('staff_schedule_templates.part_time_name'),
        description: localizations.translate('staff_schedule_templates.part_time_desc'),
        icon: Icons.schedule,
        color: Colors.blue,
        suitableForSenior: true,
        suitableForRegular: true,
        suitableForAssistant: true,
        schedule: {
          'monday': const DaySchedule(
            startTime: '10:00',
            endTime: '16:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
          'tuesday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'wednesday': const DaySchedule(
            startTime: '10:00',
            endTime: '16:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
          'thursday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'friday': const DaySchedule(
            startTime: '10:00',
            endTime: '16:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
          'saturday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'sunday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
        },
      ),
      StaffScheduleTemplate(
        name: localizations.translate('staff_schedule_templates.weekend_name'),
        description: localizations.translate('staff_schedule_templates.weekend_desc'),
        icon: Icons.weekend,
        color: Colors.orange,
        suitableForSenior: true,
        suitableForRegular: true,
        suitableForAssistant: false,
        schedule: {
          'monday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'tuesday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'wednesday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'thursday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'friday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'saturday': const DaySchedule(
            startTime: '09:00',
            endTime: '18:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
          'sunday': const DaySchedule(
            startTime: '09:00',
            endTime: '18:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
        },
      ),
      StaffScheduleTemplate(
        name: localizations.translate('staff_schedule_templates.flexible_name'),
        description: localizations.translate('staff_schedule_templates.flexible_desc'),
        icon: Icons.access_time,
        color: Colors.purple,
        suitableForSenior: true,
        suitableForRegular: true,
        suitableForAssistant: false,
        schedule: {
          'monday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'tuesday': const DaySchedule(
            startTime: '10:00',
            endTime: '19:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
          'wednesday': const DaySchedule(
            startTime: '10:00',
            endTime: '19:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
          'thursday': const DaySchedule(
            startTime: '10:00',
            endTime: '19:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
          'friday': const DaySchedule(
            startTime: '10:00',
            endTime: '19:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
          'saturday': const DaySchedule(
            startTime: '10:00',
            endTime: '19:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
          'sunday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
        },
      ),
      StaffScheduleTemplate(
        name: localizations.translate('staff_schedule_templates.assistant_name'),
        description: localizations.translate('staff_schedule_templates.assistant_desc'),
        icon: Icons.support_agent,
        color: Colors.teal,
        suitableForSenior: false,
        suitableForRegular: false,
        suitableForAssistant: true,
        schedule: {
          'monday': const DaySchedule(
            startTime: '08:00',
            endTime: '16:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'tuesday': const DaySchedule(
            startTime: '08:00',
            endTime: '16:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'wednesday': const DaySchedule(
            startTime: '08:00',
            endTime: '16:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'thursday': const DaySchedule(
            startTime: '08:00',
            endTime: '16:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'friday': const DaySchedule(
            startTime: '08:00',
            endTime: '16:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'saturday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'sunday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
        },
      ),
      StaffScheduleTemplate(
        name: localizations.translate('staff_schedule_templates.nonstop_name'),
        description: localizations.translate('staff_schedule_templates.nonstop_desc'),
        icon: Icons.timelapse,
        color: Colors.redAccent,
        suitableForSenior: true,
        suitableForRegular: true,
        suitableForAssistant: true,
        schedule: {
          'monday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'tuesday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'wednesday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'thursday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'friday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'saturday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'sunday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
        },
      ),
    ];
  }
}

/// Model for staff schedule templates
class StaffScheduleTemplate {
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final Map<String, DaySchedule> schedule;
  final bool suitableForSenior;
  final bool suitableForRegular;
  final bool suitableForAssistant;

  const StaffScheduleTemplate({
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.schedule,
    required this.suitableForSenior,
    required this.suitableForRegular,
    required this.suitableForAssistant,
  });
}
