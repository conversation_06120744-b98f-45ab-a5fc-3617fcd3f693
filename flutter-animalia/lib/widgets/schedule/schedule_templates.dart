import 'package:flutter/material.dart';

import '../../l10n/app_localizations.dart';
import '../../models/working_hours_settings.dart';

/// Quick schedule templates for common patterns
class ScheduleTemplates extends StatefulWidget {
  final Function(Map<String, DaySchedule>) onTemplateSelected;

  const ScheduleTemplates({
    Key? key,
    required this.onTemplateSelected,
  }) : super(key: key);

  @override
  State<ScheduleTemplates> createState() => _ScheduleTemplatesState();
}

class _ScheduleTemplatesState extends State<ScheduleTemplates> {
  int? _selectedIndex;
  int? _hoverIndex;

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.translate('schedule_templates.title'),
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
         Text(
          localizations.translate('schedule_templates.subtitle'),
          style: TextStyle(
            fontSize: 15,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 20),

        // Responsive template grid
        LayoutBuilder(
          builder: (context, constraints) {
            // Determine card width based on screen size
            double cardWidth;

            if (constraints.maxWidth < 600) {
              // Mobile: full width
              cardWidth = constraints.maxWidth;
            } else if (constraints.maxWidth < 900) {
              // Tablet: 2 columns
              cardWidth = (constraints.maxWidth - 12) / 2;
            } else {
              // Desktop: 3 columns
              cardWidth = (constraints.maxWidth - 24) / 3;
            }

            return Wrap(
              spacing: 12,
              runSpacing: 12,
              children: _buildTemplates(context).asMap().entries.map((entry) {
                return SizedBox(
                  width: cardWidth,
                  child: _buildTemplateCard(entry.value, entry.key),
                );
              }).toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildTemplateCard(ScheduleTemplate template, int index) {
    final isSelected = _selectedIndex == index;
    final isHovered = _hoverIndex == index;

    return MouseRegion(
      onEnter: (_) => setState(() => _hoverIndex = index),
      onExit: (_) => setState(() => _hoverIndex = null),
      child: InkWell(
        onTap: () {
          setState(() => _selectedIndex = index);
          widget.onTemplateSelected(template.schedule);
        },
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? template.color.withValues(alpha: 0.08)
                : Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? template.color
                  : isHovered
                      ? template.color.withValues(alpha: 0.5)
                      : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              width: isSelected ? 2.5 : 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: isHovered || isSelected
                    ? template.color.withValues(alpha: 0.15)
                    : Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
                blurRadius: isHovered || isSelected ? 8 : 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Icon and title
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: template.color.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      template.icon,
                      color: template.color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      template.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).colorScheme.onSurface,
                        letterSpacing: -0.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Description
              Text(
                template.description,
                style: TextStyle(
                  fontSize: 13,
                  height: 1.4,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Working days indicator
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: _weekDays.map((day) {
                  final isWorking = template.schedule[day]?.isWorkingDay ?? false;
                  return Flexible(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 1),
                      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
                      decoration: BoxDecoration(
                        color: isWorking
                            ? template.color
                            : Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Center(
                        child: Text(
                          _getDayInitial(day, context),
                          style: TextStyle(
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                            color: isWorking
                                ? Colors.white
                                : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getDayInitial(String day, BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (day) {
      case 'monday': return localizations.translate('schedule_templates.day_monday_abbr');
      case 'tuesday': return localizations.translate('schedule_templates.day_tuesday_abbr');
      case 'wednesday': return localizations.translate('schedule_templates.day_wednesday_abbr');
      case 'thursday': return localizations.translate('schedule_templates.day_thursday_abbr');
      case 'friday': return localizations.translate('schedule_templates.day_friday_abbr');
      case 'saturday': return localizations.translate('schedule_templates.day_saturday_abbr');
      case 'sunday': return localizations.translate('schedule_templates.day_sunday_abbr');
      default: return '';
    }
  }

  List<ScheduleTemplate> _buildTemplates(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return [
      ScheduleTemplate(
        name: localizations.translate('schedule_templates.standard_schedule_name'),
        description: localizations.translate('schedule_templates.standard_schedule_desc'),
        icon: Icons.business,
        color: Colors.black,
        schedule: {
          'monday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'tuesday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'wednesday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'thursday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'friday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'saturday': const DaySchedule(
            startTime: '10:00',
            endTime: '15:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'sunday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
        },
      ),

      ScheduleTemplate(
        name: localizations.translate('schedule_templates.weekdays_only_name'),
        description: localizations.translate('schedule_templates.weekdays_only_desc'),
        icon: Icons.work,
        color: Colors.blue,
        schedule: {
          'monday': const DaySchedule(
            startTime: '08:00',
            endTime: '18:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'tuesday': const DaySchedule(
            startTime: '08:00',
            endTime: '18:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'wednesday': const DaySchedule(
            startTime: '08:00',
            endTime: '18:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'thursday': const DaySchedule(
            startTime: '08:00',
            endTime: '18:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'friday': const DaySchedule(
            startTime: '08:00',
            endTime: '18:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'saturday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'sunday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
        },
      ),

      ScheduleTemplate(
        name: localizations.translate('schedule_templates.extended_schedule_name'),
        description: localizations.translate('schedule_templates.extended_schedule_desc'),
        icon: Icons.schedule,
        color: Colors.orange,
        schedule: {
          'monday': const DaySchedule(
            startTime: '08:00',
            endTime: '20:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'tuesday': const DaySchedule(
            startTime: '08:00',
            endTime: '20:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'wednesday': const DaySchedule(
            startTime: '08:00',
            endTime: '20:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'thursday': const DaySchedule(
            startTime: '08:00',
            endTime: '20:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'friday': const DaySchedule(
            startTime: '08:00',
            endTime: '20:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'saturday': const DaySchedule(
            startTime: '08:00',
            endTime: '20:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'sunday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
        },
      ),

      ScheduleTemplate(
        name: localizations.translate('schedule_templates.weekend_focus_name'),
        description: localizations.translate('schedule_templates.weekend_focus_desc'),
        icon: Icons.weekend,
        color: Colors.purple,
        schedule: {
          'monday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'tuesday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'wednesday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'thursday': const DaySchedule(
            startTime: '10:00',
            endTime: '18:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'friday': const DaySchedule(
            startTime: '10:00',
            endTime: '20:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'saturday': const DaySchedule(
            startTime: '09:00',
            endTime: '19:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'sunday': const DaySchedule(
            startTime: '10:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
        },
      ),

      ScheduleTemplate(
        name: localizations.translate('schedule_templates.nonstop_schedule_name'),
        description: localizations.translate('schedule_templates.nonstop_schedule_desc'),
        icon: Icons.timelapse,
        color: Colors.redAccent,
        schedule: {
          'monday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'tuesday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'wednesday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'thursday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'friday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'saturday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
          'sunday': const DaySchedule(
            startTime: '00:00',
            endTime: '23:59',
            isWorkingDay: true,
            breakStart: null,
            breakEnd: null,
          ),
        },
      ),
    ];
  }

  static const List<String> _weekDays = [
    'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'
  ];
}

/// Model for schedule templates
class ScheduleTemplate {
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final Map<String, DaySchedule> schedule;

  const ScheduleTemplate({
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.schedule,
  });
}
