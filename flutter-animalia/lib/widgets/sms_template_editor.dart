import 'package:flutter/material.dart';

import '../l10n/app_localizations.dart';
import '../models/sms_template.dart';
import '../services/sms_template_service.dart';
import '../services/ui_notification_service.dart';

/// Widget for editing SMS templates with real-time preview
class SmsTemplateEditor extends StatefulWidget {
  final SmsTemplate? template;
  final SmsTemplateType templateType;
  final Function(SmsTemplate) onSaved;
  final VoidCallback? onCancel;

  const SmsTemplateEditor({
    Key? key,
    this.template,
    required this.templateType,
    required this.onSaved,
    this.onCancel,
  }) : super(key: key);

  @override
  State<SmsTemplateEditor> createState() => _SmsTemplateEditorState();
}

class _SmsTemplateEditorState extends State<SmsTemplateEditor> {
  late TextEditingController _contentController;
  late FocusNode _contentFocusNode;

  bool _isLoading = false;
  bool _showPreview = true;
  bool _showVariableHelper = false;
  bool _showProfessionalTemplates = false;
  bool _isUsingProfessionalTemplate = false;
  String? _validationError;
  String _previewText = '';
  SmsCharacterInfo? _smsCharacterInfo;
  ProfessionalTemplate? _selectedProfessionalTemplate;

  final Map<String, String> _sampleData = TemplateVariableInfo.getSampleData();

  @override
  void initState() {
    super.initState();
    _contentController = TextEditingController(
      text: widget.template?.templateContent ?? widget.templateType.getDefaultContent(),
    );
    _contentFocusNode = FocusNode();
    
    _contentController.addListener(_onContentChanged);
    _updatePreview();
  }

  @override
  void dispose() {
    _contentController.removeListener(_onContentChanged);
    _contentController.dispose();
    _contentFocusNode.dispose();
    super.dispose();
  }

  void _onContentChanged() {
    setState(() {
      _smsCharacterInfo = SmsTemplateService.getSmsCharacterInfo(_contentController.text);
      _validationError = SmsTemplateService.validateTemplateContent(_contentController.text);
      _isUsingProfessionalTemplate = false; // User is customizing
    });
    _updatePreview();
  }

  void _updatePreview() {
    if (_contentController.text.isNotEmpty) {
      String preview = _contentController.text;
      _sampleData.forEach((key, value) {
        preview = preview.replaceAll('{$key}', value);
      });
      setState(() {
        _previewText = preview;
      });
    }
  }

  Future<void> _saveTemplate() async {
    if (_validationError != null) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = UpdateSmsTemplateRequest(
        templateContent: _contentController.text,
        isActive: true,
      );

      final response = await SmsTemplateService.updateSmsTemplate(
        widget.templateType,
        request,
      );

      if (response.success && response.data != null) {
        widget.onSaved(response.data!);
        if (mounted) {
          UINotificationService.showSuccess(
            context: context,
            title: context.tr('sms_templates.editor.template_saved_title'),
            message: context.tr('sms_templates.editor.template_saved_message'),
          );
        }
      } else {
        if (mounted) {
          UINotificationService.showError(
            context: context,
            title: context.tr('sms_templates.editor.save_error_title'),
            message: response.error ?? context.tr('sms_templates.editor.save_error_message'),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('common.error'),
          message: '${context.tr('common.error')}: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _insertVariable(String variable) {
    final text = _contentController.text;
    final selection = _contentController.selection;

    // Handle invalid selection (when start is -1)
    final start = selection.start >= 0 ? selection.start : text.length;
    final end = selection.end >= 0 ? selection.end : text.length;

    final newText = text.replaceRange(
      start,
      end,
      '{$variable}',
    );

    _contentController.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(
        offset: start + variable.length + 2,
      ),
    );

    _contentFocusNode.requestFocus();
  }

  void _selectProfessionalTemplate(ProfessionalTemplate template) {
    setState(() {
      _selectedProfessionalTemplate = template;
      _isUsingProfessionalTemplate = true;
      _contentController.text = context.tr(template.contentKey);
      _showProfessionalTemplates = false;
    });
  }

  void _toggleProfessionalTemplates() {
    setState(() {
      _showProfessionalTemplates = !_showProfessionalTemplates;
      if (_showProfessionalTemplates) {
        _showVariableHelper = false;
      }
    });
  }

  void _resetToDefault() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('sms_templates.editor.reset_confirmation_title')),
        content: Text(context.tr('sms_templates.editor.reset_confirmation_message')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('sms_templates.editor.cancel')),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _contentController.text = widget.templateType.getDefaultContent();
            },
            child: Text(context.tr('sms_templates.editor.reset')),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${context.tr('sms_templates.editor.edit_title')} ${context.tr(widget.templateType.translationKey)}'),
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_showProfessionalTemplates ? Icons.auto_awesome : Icons.auto_awesome_outlined),
            onPressed: _toggleProfessionalTemplates,
            tooltip: context.tr('sms_templates.editor.professional_templates'),
          ),
          IconButton(
            icon: Icon(_showVariableHelper ? Icons.help : Icons.help_outline),
            onPressed: () {
              setState(() {
                _showVariableHelper = !_showVariableHelper;
                if (_showVariableHelper) {
                  _showProfessionalTemplates = false;
                }
              });
            },
            tooltip: context.tr('sms_templates.editor.available_variables'),
          ),
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _resetToDefault,
            tooltip: context.tr('sms_templates.editor.reset_to_default'),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Professional templates section
                  if (_showProfessionalTemplates) ...[
                    _buildProfessionalTemplatesSection(),
                    SizedBox(height: 24),
                  ],

                  // Template editor section
                  _buildEditorSection(),

                  SizedBox(height: 24),

                  // Variable helper section
                  if (_showVariableHelper) ...[
                    _buildVariableHelperSection(),
                    SizedBox(height: 24),
                  ],

                  // Preview section
                  if (_showPreview) ...[
                    _buildPreviewSection(),
                    SizedBox(height: 24),
                  ],
                ],
              ),
            ),
          ),
          
          // Action buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildEditorSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row with selected professional template badge
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  context.tr('sms_templates.editor.content_title'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                if (_isUsingProfessionalTemplate && _selectedProfessionalTemplate != null)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      context.tr(_selectedProfessionalTemplate!.nameKey),
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
              ],
            ),

            SizedBox(height: 8),

            // Character counter
            _buildSmsCharacterCounter(),
            SizedBox(height: 8),

            // Help/description
            Text(
              context.tr('sms_templates.editor.usage_instructions'),
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),

            SizedBox(height: 12),

            // Quick variables
            _buildQuickVariables(),

            SizedBox(height: 16),

            // Drag target editor area
            DragTarget<String>(
              onAcceptWithDetails: (details) => _insertVariable(details.data),
              builder: (context, candidateData, rejectedData) {
                final isDragOver = candidateData.isNotEmpty;
                return AnimatedContainer(
                  duration: Duration(milliseconds: 200),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: isDragOver
                        ? Border.all(color: Theme.of(context).colorScheme.primary.withValues(alpha: 1.0), width: 2)
                        : null,
                    color: isDragOver
                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.05)
                        : null,
                  ),
                  child: TextField(
                    controller: _contentController,
                    focusNode: _contentFocusNode,
                    maxLines: 6,
                    maxLength: 1000,
                    decoration: InputDecoration(
                      hintText: isDragOver
                          ? '📥 ${context.tr('sms_templates.editor.drag_active_hint')} {${candidateData.isNotEmpty ? candidateData.first : ''}}...'
                          : context.tr('sms_templates.editor.drag_hint'),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: isDragOver
                            ? BorderSide(color: Theme.of(context).colorScheme.primary, width: 2)
                            : BorderSide(),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Theme.of(context).colorScheme.primary, width: 2),
                      ),
                      errorText: _validationError,
                      counterText: '',
                      filled: isDragOver,
                      fillColor: isDragOver
                          ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.05)
                          : null,
                    ),
                  ),
                );
              },
            ),

            // SMS warning if applicable
            if (_smsCharacterInfo != null) ...[
              SizedBox(height: 8),
              _buildSmsWarning(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildVariableHelperSection() {
    final variables = TemplateVariableInfo.getAllVariables();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.code, size: 20, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 8),
                Text(
                  context.tr('sms_templates.editor.available_variables'),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12),
            
            Text(
              context.tr('sms_templates.editor.all_variables_description'),
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),

            SizedBox(height: 12),

            // Group variables by category
            _buildVariableGroup(context.tr('sms_templates.editor.basic_info'), [
              variables.firstWhere((v) => v.name == 'SALON_NAME'),
              variables.firstWhere((v) => v.name == 'SALON_PHONE'),
              variables.firstWhere((v) => v.name == 'SALON_ADDRESS'),
            ]),

            SizedBox(height: 12),

            _buildVariableGroup(context.tr('sms_templates.editor.client_pet_info'), [
              variables.firstWhere((v) => v.name == 'OWNER_NAME'),
              variables.firstWhere((v) => v.name == 'PET_NAME'),
            ]),

            SizedBox(height: 12),

            _buildVariableGroup(context.tr('sms_templates.editor.appointment_info'), [
              variables.firstWhere((v) => v.name == 'APPOINTMENT_DATE'),
              variables.firstWhere((v) => v.name == 'APPOINTMENT_TIME'),
              variables.firstWhere((v) => v.name == 'SERVICE_NAME'),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.phone_android,
                      size: 20,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    SizedBox(width: 8),
                    Text(
                      context.tr('sms_templates.editor.preview_title'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showPreview = !_showPreview;
                    });
                  },
                  child: Text(_showPreview ? context.tr('sms_templates.editor.hide') : context.tr('sms_templates.editor.show')),
                ),
              ],
            ),
            
            SizedBox(height: 8),
            
            Text(
              context.tr('sms_templates.editor.preview_description'),
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
            
            SizedBox(height: 12),
            
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                _previewText.isNotEmpty ? _previewText : context.tr('sms_templates.editor.preview_placeholder'),
                style: TextStyle(
                  fontSize: 14,
                  color: _previewText.isNotEmpty 
                      ? Theme.of(context).colorScheme.onSurface
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                  fontStyle: _previewText.isEmpty ? FontStyle.italic : null,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (widget.onCancel != null) ...[
            Expanded(
              child: OutlinedButton(
                onPressed: _isLoading ? null : widget.onCancel,
                child: Text(context.tr('sms_templates.editor.cancel')),
              ),
            ),
            SizedBox(width: 16),
          ],
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading || _validationError != null ? null : _saveTemplate,
              child: _isLoading
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(context.tr('sms_templates.editor.save')),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmsCharacterCounter() {
    if (_smsCharacterInfo == null) return SizedBox.shrink();

    Color getCounterColor() {
      switch (_smsCharacterInfo!.costLevel) {
        case SmsCostLevel.low:
          return Colors.green;
        case SmsCostLevel.medium:
          return Colors.orange;
        case SmsCostLevel.high:
          return Colors.red;
      }
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: getCounterColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: getCounterColor().withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _smsCharacterInfo!.smsCount == 1 ? Icons.sms : Icons.sms_outlined,
            size: 14,
            color: getCounterColor(),
          ),
          SizedBox(width: 6),
          Text(
            '${_smsCharacterInfo!.totalCharacters} caractere',
            style: TextStyle(
              fontSize: 11,
              color: getCounterColor(),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: 8),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: getCounterColor(),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '${_smsCharacterInfo!.smsCount} ${context.tr('sms_templates.sms_count')}',
              style: TextStyle(
                fontSize: 10,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmsWarning() {
    if (_smsCharacterInfo == null) return SizedBox.shrink();

    final warning = _smsCharacterInfo!.getWarningMessage();
    if (warning == null) return SizedBox.shrink();

    Color getWarningColor() {
      switch (_smsCharacterInfo!.costLevel) {
        case SmsCostLevel.low:
          return Colors.blue;
        case SmsCostLevel.medium:
          return Colors.orange;
        case SmsCostLevel.high:
          return Colors.red;
      }
    }

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: getWarningColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: getWarningColor().withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            _smsCharacterInfo!.costLevel == SmsCostLevel.high
                ? Icons.warning_outlined
                : Icons.info_outline,
            size: 16,
            color: getWarningColor(),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              warning,
              style: TextStyle(
                fontSize: 12,
                color: getWarningColor().withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfessionalTemplatesSection() {
    final templates = widget.templateType.getProfessionalTemplates();

    // Group templates by SMS count
    final Map<int, List<ProfessionalTemplate>> templatesBySmsCount = {};
    for (final template in templates) {
      final smsCount = SmsCalculator.calculateSmsCount(context.tr(template.contentKey));
      templatesBySmsCount.putIfAbsent(smsCount, () => []).add(template);
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.auto_awesome, size: 20, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 8),
                Text(
                  context.tr('sms_templates.editor.professional_templates'),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),

            SizedBox(height: 8),

            Text(
              context.tr('sms_templates.editor.professional_templates_description'),
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),

            SizedBox(height: 16),

            // Show templates grouped by SMS count
            ...templatesBySmsCount.entries.map((entry) {
              final smsCount = entry.key;
              final templatesForCount = entry.value;
              return _buildSmsLevelSection(smsCount, templatesForCount);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildProfessionalTemplateCard(ProfessionalTemplate template) {
    final isSelected = _selectedProfessionalTemplate?.id == template.id && _isUsingProfessionalTemplate;
    final smsInfo = SmsCalculator.getCharacterInfo(context.tr(template.contentKey));

    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: isSelected ? 2 : 1,
        ),
        color: isSelected
            ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.05)
            : Theme.of(context).colorScheme.surface,
      ),
      child: InkWell(
        onTap: () => _selectProfessionalTemplate(template),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      context.tr(template.nameKey),
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                  SizedBox(width: 8),
                  Text(
                    '${smsInfo.totalCharacters} ${context.tr('sms_templates.characters')}',
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  SizedBox(width: 8),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                ],
              ),

              SizedBox(height: 6),

              Text(
                context.tr(template.descriptionKey),
                style: TextStyle(
                  fontSize: 11,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),

              SizedBox(height: 8),

              Container(
                width: double.infinity,
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  context.tr(template.contentKey).length > 100
                      ? '${context.tr(template.contentKey).substring(0, 100)}...'
                      : context.tr(template.contentKey),
                  style: TextStyle(
                    fontSize: 11,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }



  Widget _buildQuickVariables() {
    // Show the most commonly used variables prominently
    final quickVariables = [
      TemplateVariableInfo.getAllVariables().firstWhere((v) => v.name == 'SALON_NAME'),
      TemplateVariableInfo.getAllVariables().firstWhere((v) => v.name == 'SALON_PHONE'),
      TemplateVariableInfo.getAllVariables().firstWhere((v) => v.name == 'SALON_ADDRESS'),
      TemplateVariableInfo.getAllVariables().firstWhere((v) => v.name == 'OWNER_NAME'),
      TemplateVariableInfo.getAllVariables().firstWhere((v) => v.name == 'PET_NAME'),
      TemplateVariableInfo.getAllVariables().firstWhere((v) => v.name == 'APPOINTMENT_DATE'),
      TemplateVariableInfo.getAllVariables().firstWhere((v) => v.name == 'APPOINTMENT_TIME'),
    ];

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.touch_app, size: 16, color: Theme.of(context).colorScheme.primary),
              SizedBox(width: 6),
              Text(
                context.tr('sms_templates.editor.frequent_variables'),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: quickVariables.map((variable) => _buildDraggableVariableChip(variable)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildVariableGroup(String title, List<TemplateVariableInfo> variables) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        SizedBox(height: 6),
        Wrap(
          spacing: 6,
          runSpacing: 6,
          children: variables.map((variable) => _buildDraggableVariableChip(variable)).toList(),
        ),
      ],
    );
  }

  Widget _buildSmsLevelSection(int smsCount, List<ProfessionalTemplate> templates) {
    Color getLevelColor() {
      switch (smsCount) {
        case 1:
          return Colors.green;
        case 2:
          return Colors.orange;
        default:
          return Colors.red;
      }
    }

    String getLevelTitle() {
      switch (smsCount) {
        case 1:
          return context.tr('sms_templates.editor.economical_templates');
        case 2:
          return context.tr('sms_templates.editor.moderate_templates');
        default:
          return '${context.tr('sms_templates.editor.complete_templates')} ($smsCount SMS)';
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: getLevelColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: getLevelColor().withValues(alpha: 0.3)),
          ),
          child: Text(
            getLevelTitle(),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: getLevelColor(),
            ),
          ),
        ),
        SizedBox(height: 8),
        ...templates.map((template) => _buildProfessionalTemplateCard(template)),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildDraggableVariableChip(TemplateVariableInfo variable) {
    return Draggable<String>(
      data: variable.name,
      feedback: Material(
        elevation: 6,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            '{${variable.name}}',
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.3,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            '{${variable.name}}',
            style: TextStyle(
              fontSize: 11,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.drag_indicator,
              size: 12,
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(width: 4),
            Text(
              '{${variable.name}}',
              style: TextStyle(
                fontSize: 11,
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
