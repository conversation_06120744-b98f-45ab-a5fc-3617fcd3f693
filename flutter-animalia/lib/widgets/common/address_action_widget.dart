import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../l10n/app_localizations.dart';

/// Widget for displaying an address with action buttons to open in Maps/Waze/Google Maps
/// Similar to <PERSON>s<PERSON><PERSON>'s address display functionality
class AddressActionWidget extends StatelessWidget {
  final String address;
  final TextStyle? textStyle;
  final Color? iconColor;
  final bool showIcon;
  final EdgeInsets? padding;

  const AddressActionWidget({
    super.key,
    required this.address,
    this.textStyle,
    this.iconColor,
    this.showIcon = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _showNavigationOptions(context),
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: padding ?? const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            if (showIcon) ...[
              Icon(
                Icons.location_on,
                size: 16,
                color: iconColor ?? Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 6),
            ],
            Expanded(
              child: Text(
                address,
                style: textStyle ??
                    TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.primary,
                      decoration: TextDecoration.underline,
                    ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Icon(
              Icons.open_in_new,
              size: 14,
              color: iconColor ?? Theme.of(context).colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }

  void _showNavigationOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Title
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('address_action.open_address_in'),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    address,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            
            const Divider(height: 1),
            
            // Google Maps option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.map, color: Colors.green),
              ),
              title: Text(context.tr('address_action.google_maps')),
              subtitle: Text(context.tr('address_action.navigate_with_google_maps')),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.pop(context);
                _openInGoogleMaps();
              },
            ),
            
            // Waze option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.navigation, color: Colors.blue),
              ),
              title: Text(context.tr('address_action.waze')),
              subtitle: Text(context.tr('address_action.navigate_with_waze')),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.pop(context);
                _openInWaze();
              },
            ),
            
            // Apple Maps option (iOS only, but we show it anyway)
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.map_outlined, color: Colors.orange),
              ),
              title: Text(context.tr('address_action.apple_maps')),
              subtitle: Text(context.tr('address_action.navigate_with_apple_maps')),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.pop(context);
                _openInAppleMaps();
              },
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Future<void> _openInGoogleMaps() async {
    final encodedAddress = Uri.encodeComponent(address);
    final url = Uri.parse('https://www.google.com/maps/search/?api=1&query=$encodedAddress');
    
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _openInWaze() async {
    final encodedAddress = Uri.encodeComponent(address);
    final url = Uri.parse('https://waze.com/ul?q=$encodedAddress&navigate=yes');
    
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      // Fallback to Google Maps if Waze is not installed
      _openInGoogleMaps();
    }
  }

  Future<void> _openInAppleMaps() async {
    final encodedAddress = Uri.encodeComponent(address);
    final url = Uri.parse('https://maps.apple.com/?q=$encodedAddress');
    
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      // Fallback to Google Maps if Apple Maps is not available
      _openInGoogleMaps();
    }
  }
}
