import 'package:flutter/material.dart';
import '../../models/appointment.dart';
import '../../models/appointment_subscription.dart';
import '../../services/appointment_subscription_service.dart';
import '../dialogs/subscription_appointments_dialog.dart';
import '../../l10n/app_localizations.dart';

/// A widget that displays a subscription indicator for appointments
/// Shows a recurring icon and allows navigation to subscription details
class SubscriptionIndicator extends StatelessWidget {
  final Appointment appointment;
  final String salonId;
  final VoidCallback? onSubscriptionTapped;

  const SubscriptionIndicator({
    super.key,
    required this.appointment,
    required this.salonId,
    this.onSubscriptionTapped,
  });

  @override
  Widget build(BuildContext context) {
    if (!appointment.hasSubscription) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: () => _showSubscriptionDetails(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.repeat,
              size: 12,
              color: Theme.of(context).colorScheme.primary,
            ),
            if (appointment.sequenceNumber != null) ...[
              const SizedBox(width: 2),
              Text(
                '#${appointment.sequenceNumber}',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showSubscriptionDetails(BuildContext context) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Fetch subscription details
      final subscription = await AppointmentSubscriptionService.getSubscriptionByAppointmentId(
        salonId,
        appointment.id,
      );

      // Close loading indicator
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (subscription != null && context.mounted) {
        // Show subscription details dialog
        showDialog(
          context: context,
          builder: (context) => SubscriptionAppointmentsDialog(
            subscription: subscription,
            salonId: salonId,
          ),
        );
      } else if (context.mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('subscription_indicator.could_not_load_details')),
            backgroundColor: Colors.red,
          ),
        );
      }

      // Call callback if provided
      onSubscriptionTapped?.call();
    } catch (e) {
      // Close loading indicator if still showing
      if (context.mounted) {
        Navigator.of(context).pop();
        
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('common.error_with_details', params: {'error': '$e'})),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// A compact version of the subscription indicator for small spaces
class CompactSubscriptionIndicator extends StatelessWidget {
  final Appointment appointment;
  final String salonId;
  final VoidCallback? onTapped;
  final Color? color; // Allow custom color to match text

  const CompactSubscriptionIndicator({
    super.key,
    required this.appointment,
    required this.salonId,
    this.onTapped,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    if (!appointment.hasSubscription) {
      return const SizedBox.shrink();
    }

    // Use provided color, or fall back to a subtle color that works with text
    final iconColor = Colors.white;

    return GestureDetector(
      onTap: onTapped,
      child: Tooltip(
        message: appointment.subscriptionDisplayInfo,
        child: Icon(
          Icons.repeat,
          size: 14,
          color: iconColor,
        ),
      ),
    );
  }
}
