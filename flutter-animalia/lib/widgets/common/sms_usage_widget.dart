import 'package:flutter/material.dart';
import '../../services/sms_usage_service.dart';
import '../../screens/subscription/subscription_purchase_screen.dart';
import '../sms/blocked_sms_credits_widget.dart';
import '../../l10n/app_localizations.dart';

/// Widget to display SMS usage information and free trial limits
class SmsUsageWidget extends StatefulWidget {
  final bool showDetails;
  final bool showUpgradeButton;
  final VoidCallback? onUpgradePressed;

  const SmsUsageWidget({
    super.key,
    this.showDetails = true,
    this.showUpgradeButton = true,
    this.onUpgradePressed,
  });

  @override
  State<SmsUsageWidget> createState() => _SmsUsageWidgetState();
}

class _SmsUsageWidgetState extends State<SmsUsageWidget> {
  SmsUsageInfo? _usageInfo;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadSmsUsage();
  }

  Future<void> _loadSmsUsage() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await SmsUsageService.getSmsUsage();
      if (response.success && response.data != null) {
        setState(() {
          _usageInfo = response.data;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load SMS usage';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading SMS usage: $e';
        _isLoading = false;
      });
    }
  }

  /// Refresh SMS usage information
  Future<void> _refreshUsageInfo() async {
    await _loadSmsUsage();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 12),
              Text(context.tr('sms_usage.loading')),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.red),
              const SizedBox(width: 12),
              Expanded(child: Text(_error!)),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _loadSmsUsage,
              ),
            ],
          ),
        ),
      );
    }

    if (_usageInfo == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 12),
            _buildUsageBar(),
            if (widget.showDetails) ...[
              const SizedBox(height: 12),
              _buildDetails(),
            ],
            if (_usageInfo!.blockedCredits.hasBlockedCredits) ...[
              const SizedBox(height: 16),
              BlockedSmsCreditsWidget(
                blockedCredits: _usageInfo!.blockedCredits,
                showDetails: widget.showDetails,
                onUnlockAttempt: _refreshUsageInfo,
              ),
            ],
            if (_usageInfo!.isFreeTrial && _usageInfo!.isNearLimit && widget.showUpgradeButton) ...[
              const SizedBox(height: 16),
              _buildUpgradeButton(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final usageInfo = _usageInfo!;
    
    return Row(
      children: [
        Icon(
          Icons.sms,
          color: usageInfo.isAtLimit ? Colors.red : 
                 usageInfo.isNearLimit ? Colors.orange : Colors.blue,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            usageInfo.isFreeTrial ? context.tr('sms_usage.free_trial') : context.tr('sms_usage.title'),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        if (usageInfo.isFreeTrial)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withOpacity(0.3)),
            ),
            child: Text(
              context.tr('sms_usage.trial_badge'),
              style: TextStyle(
                color: Colors.blue[700],
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildUsageBar() {
    final usageInfo = _usageInfo!;
    final percentage = usageInfo.usagePercentage / 100;
    
    Color barColor;
    if (usageInfo.isAtLimit) {
      barColor = Colors.red;
    } else if (usageInfo.isNearLimit) {
      barColor = Colors.orange;
    } else {
      barColor = Colors.green;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              context.tr('sms_usage.usage_count', params: {
                'used': '${usageInfo.usedSms}',
                'total': '${usageInfo.totalAllowed}'
              }),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${usageInfo.usagePercentage.toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: barColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: percentage,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(barColor),
          minHeight: 8,
        ),
        const SizedBox(height: 4),
        Text(
          context.tr('sms_usage.remaining', params: {'count': '${usageInfo.remainingSms}'}),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildDetails() {
    final usageInfo = _usageInfo!;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        Row(
          children: [
            Icon(Icons.info_outline, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                usageInfo.statusMessage,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[700],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(Icons.account_circle, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 8),
            Text(
              context.tr('sms_usage.plan', params: {'tier': usageInfo.subscriptionTier}),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUpgradeButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: widget.onUpgradePressed,
        icon: const Icon(Icons.upgrade),
        label: Text(context.tr('sms_usage.upgrade_button')),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }
}

/// Compact SMS usage indicator for app bars or status areas
class SmsUsageIndicator extends StatefulWidget {
  final VoidCallback? onTap;

  const SmsUsageIndicator({
    super.key,
    this.onTap,
  });

  @override
  State<SmsUsageIndicator> createState() => _SmsUsageIndicatorState();
}

class _SmsUsageIndicatorState extends State<SmsUsageIndicator> {
  SmsUsageInfo? _usageInfo;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSmsUsage();
  }

  Future<void> _loadSmsUsage() async {
    try {
      final response = await SmsUsageService.getSmsUsage();
      if (response.success && response.data != null) {
        setState(() {
          _usageInfo = response.data;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _usageInfo == null) {
      return const SizedBox.shrink();
    }

    final usageInfo = _usageInfo!;
    
    // Only show indicator for free trial accounts or when near/at limit
    if (!usageInfo.isFreeTrial && !usageInfo.isNearLimit) {
      return const SizedBox.shrink();
    }

    Color indicatorColor;
    IconData indicatorIcon;
    
    if (usageInfo.isAtLimit) {
      indicatorColor = Colors.red;
      indicatorIcon = Icons.sms_failed;
    } else if (usageInfo.isNearLimit) {
      indicatorColor = Colors.orange;
      indicatorIcon = Icons.warning;
    } else {
      indicatorColor = Colors.blue;
      indicatorIcon = Icons.sms;
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: indicatorColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: indicatorColor.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(indicatorIcon, size: 16, color: indicatorColor),
            const SizedBox(width: 4),
            Text(
              '${usageInfo.remainingSms}',
              style: TextStyle(
                color: indicatorColor,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
