import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/appointment.dart';
import '../../models/google_calendar_event.dart';
import '../../providers/calendar_provider.dart';
import '../../services/staff_service.dart';
import '../../models/user_role.dart';
import '../../screens/profile/team/staff_detail_screen.dart';
import '../dialogs/block_time_dialog.dart';
import '../dialogs/google_event_to_appointment_dialog.dart';
import 'animated_staff_column.dart';
import 'block_time_block.dart';
import 'draggable_appointment_block.dart';
import 'droppable_time_slot.dart';
import 'google_calendar_event_block.dart';
import 'time_slot.dart';



class DayView extends StatefulWidget {
  /// Global key to access state for scrolling from outside the widget tree
  static final GlobalKey<_DayViewState> globalKey = GlobalKey<_DayViewState>();
  final DateTime selectedDate;
  final List<Appointment> appointments;
  final Function(Appointment) onAppointmentTap;
  final Function(Map<String, dynamic>)? onBlockTap;
  final Function(DateTime, String?)? onTimeSlotTap; // Added staff ID parameter
  final Map<String, dynamic> businessHours;

  const DayView({
    Key? key,
    required this.selectedDate,
    required this.appointments,
    required this.onAppointmentTap,
    this.onBlockTap,
    this.onTimeSlotTap,
    required this.businessHours,
  }) : super(key: key);

  @override
  State<DayView> createState() => _DayViewState();
}

class _DayViewState extends State<DayView> {
  late ScrollController _horizontalScrollController;
  late ScrollController _staffHeaderScrollController;
  late ScrollController _verticalScrollController;
  late ScrollController _timeScrollController;



  @override
  void initState() {
    super.initState();

    _horizontalScrollController = ScrollController();
    _staffHeaderScrollController = ScrollController();
    _verticalScrollController = ScrollController();
    _timeScrollController = ScrollController();

    // Sync time scroll with main content scroll
    _verticalScrollController.addListener(() {
      if (_timeScrollController.hasClients) {
        _timeScrollController.jumpTo(_verticalScrollController.offset);
      }
    });

    // Sync horizontal scrolling between staff header and appointment content
    _horizontalScrollController.addListener(() {
      if (_staffHeaderScrollController.hasClients &&
          _staffHeaderScrollController.offset != _horizontalScrollController.offset) {
        _staffHeaderScrollController.jumpTo(_horizontalScrollController.offset);
      }
    });

    _staffHeaderScrollController.addListener(() {
      if (_horizontalScrollController.hasClients &&
          _horizontalScrollController.offset != _staffHeaderScrollController.offset) {
        _horizontalScrollController.jumpTo(_staffHeaderScrollController.offset);
      }
    });
  }

  @override
  void didUpdateWidget(DayView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // No special handling needed since we're not using PageView anymore
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    _staffHeaderScrollController.dispose();
    _verticalScrollController.dispose();
    _timeScrollController.dispose();
    super.dispose();
  }

  /// Handle tap on Google Calendar event
  Future<void> _handleGoogleEventTap(GoogleCalendarEvent event) async {
    // Show dialog asking if user wants to create appointment from this event
    final shouldCreate = await GoogleEventToAppointmentDialog.show(context, event);

    if (shouldCreate && mounted) {
      // Navigate to appointment creation screen with pre-filled data
      // We'll use the onTimeSlotTap callback with the event's start time
      if (widget.onTimeSlotTap != null) {
        widget.onTimeSlotTap!(event.startTime, null);
      }
    }
  }

  /// Scroll to the current time
  void scrollToCurrentTime() {
    scrollToTime(DateTime.now());
  }

  /// Navigate to today and scroll to current time
  void goToToday() {
    scrollToCurrentTime();
  }



  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final currentDisplayDate = widget.selectedDate;

        // Calculate day-specific time range instead of using week-wide business hours
        final Map<String, int> daySpecificHours = _calculateDaySpecificTimeRange(provider, currentDisplayDate);
        final openTime = provider.showFullDay ? 0 : daySpecificHours['openTime']!;
        final closeTime = provider.showFullDay ? 24 : daySpecificHours['closeTime']!;
        final totalHours = closeTime - openTime;
        final slotHeight = provider.getResponsiveTimeSlotHeight(context);

        // Calculate staff layout - show all selected staff (no 3-groomer limit)
        final staff = provider.availableStaff;
        final selectedStaffIds = provider.selectedStaff;
        final allVisibleStaff = staff.where((s) => selectedStaffIds.contains(s.id)).toList();

        final screenWidth = MediaQuery.of(context).size.width;
        const timeColumnWidth = 60.0;
        final availableWidth = screenWidth - timeColumnWidth;

        // Calculate column width with proper constraints to prevent overflow
        // Use responsive minimum width based on screen size
        final minColumnWidth = provider.getResponsiveStaffColumnWidth(context);
        double staffColumnWidth;
        double totalCalendarWidth;

        if (allVisibleStaff.isEmpty) {
          staffColumnWidth = minColumnWidth;
          totalCalendarWidth = staffColumnWidth;
        } else {
          // For day view, maintain the visual layout by calculating width as if max 3 staff
          // but allow horizontal scrolling for more staff
          final visibleStaffCount = allVisibleStaff.length.clamp(1, 3);
          final safeAvailableWidth = availableWidth - 8.0; // Reserve 8px total for safety
          final distributedWidth = safeAvailableWidth / visibleStaffCount;

          if (distributedWidth >= minColumnWidth) {
            staffColumnWidth = distributedWidth.floorToDouble(); // Use floor to prevent fractional pixels
          } else {
            staffColumnWidth = minColumnWidth;
          }

          // Total calendar width includes all staff members for horizontal scrolling
          totalCalendarWidth = staffColumnWidth * allVisibleStaff.length;
        }

        return Container(
          color: Theme.of(context).colorScheme.surface, // Use theme-aware background
          child: Column(
            children: [
              Expanded(
                child: GestureDetector(
                  onScaleUpdate: (details) {
                    // Pinch-to-zoom functionality
                    if (details.scale != 1.0) {
                      final currentHeight = provider.timeSlotHeight;
                      final newHeight = (currentHeight * details.scale).clamp(
                        provider.minTimeSlotHeight,
                        provider.maxTimeSlotHeight,
                      );
                      if ((newHeight - currentHeight).abs() > 2) {
                        provider.setTimeSlotHeight(newHeight);
                      }
                    }
                  },
                  child: Row(
                    children: [
                      // Fixed time labels column
                      _buildStaticTimeColumn(openTime, closeTime, totalHours, slotHeight, currentDisplayDate),
                      // Scrollable content area
                      Expanded(
                        child: Column(
                          children: [
                            // Staff header with horizontal scrolling
                            _buildScrollableStaffHeader(allVisibleStaff, staffColumnWidth, totalCalendarWidth),
                            // Appointment content with horizontal scrolling
                            Expanded(
                              child: _buildScrollableAppointmentContent(
                                provider, allVisibleStaff, staffColumnWidth, slotHeight,
                                totalHours, openTime, closeTime, totalCalendarWidth, currentDisplayDate
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Static time column that doesn't change during within-week navigation
  Widget _buildStaticTimeColumn(int openTime, int closeTime, int totalHours, double slotHeight, DateTime currentDate) {
    return Container(
      width: 60,
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        children: [
          // _buildTimeHeader(),
          Expanded(
            child: SingleChildScrollView(
              controller: _timeScrollController,
              physics: const NeverScrollableScrollPhysics(),
              child: Column(
                children: List.generate(totalHours, (index) {
                  final hour = openTime + index;
                  final time = DateTime(
                    currentDate.year,
                    currentDate.month,
                    currentDate.day,
                    hour,
                  );
                  final isCurrentHour = DateTime.now().hour == hour &&
                      DateTime.now().day == currentDate.day &&
                      DateTime.now().month == currentDate.month &&
                      DateTime.now().year == currentDate.year;

                  return TimeLabel(
                    time: time,
                    isCurrentHour: isCurrentHour,
                    height: slotHeight,
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Scrollable staff header with horizontal scrolling support
  Widget _buildScrollableStaffHeader(List<StaffResponse> staff, double columnWidth, double totalWidth) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      height: 32,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: isDark ? AppColors.darkSurfaceVariant : AppColors.lightGray,
            width: 0.5,
          ),
        ),
      ),
      child: SingleChildScrollView(
        controller: _staffHeaderScrollController,
        scrollDirection: Axis.horizontal,
        physics: const ClampingScrollPhysics(),
        child: SizedBox(
          width: totalWidth,
          child: Row(
            children: staff
                .map((staffMember) => SizedBox(
                      width: columnWidth,
                      child: GestureDetector(
                        onTap: () {
                          // Navigate to staff detail screen with schedule tab selected (tab index 1)
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => StaffDetailScreen(
                                staff: staffMember,
                                initialTabIndex: 1, // 1 = Schedule tab
                              ),
                            ),
                          );
                        },
                        child: Container(
                          height: 32,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            border: Border(
                              bottom: BorderSide(
                                color: AppTheme.getStaffColor(staffMember.id),
                                width: 3,
                              ),
                            ),
                          ),
                          child: Row(
                            children: [
                              // Staff name
                              Expanded(
                                child: Center(
                                  child: Text(
                                    _getStaffDisplayName(staffMember),
                                    style: TextStyle(
                                      color: AppTheme.getStaffColor(staffMember.id),
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ))
                .toList(),
          ),
        ),
      ),
    );
  }

  // Scrollable appointment content with horizontal scrolling support
  Widget _buildScrollableAppointmentContent(
    CalendarProvider provider,
    List<StaffResponse> allVisibleStaff,
    double staffColumnWidth,
    double slotHeight,
    int totalHours,
    int openTime,
    int closeTime,
    double totalCalendarWidth,
    DateTime selectedDate,
  ) {
    final lunchStart = widget.businessHours['lunchBreak']['start'] as int;
    final lunchEnd = widget.businessHours['lunchBreak']['end'] as int;
    final isWorkDay = provider.isWorkingDay(selectedDate);

    return SingleChildScrollView(
      controller: _horizontalScrollController,
      scrollDirection: Axis.horizontal,
      physics: const ClampingScrollPhysics(),
      child: SizedBox(
        width: totalCalendarWidth,
        child: SingleChildScrollView(
          controller: _verticalScrollController,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: allVisibleStaff
                .map((staffMember) => SizedBox(
                      width: staffColumnWidth,
                      child: _buildStaffColumn(
                        provider,
                        staffMember,
                        staffColumnWidth,
                        slotHeight,
                        totalHours,
                        openTime,
                        closeTime,
                        lunchStart,
                        lunchEnd,
                        isWorkDay,
                        selectedDate,
                      ),
                    ))
                .toList(),
          ),
        ),
      ),
    );
  }





  Widget _buildTimeHeader() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: 60,
      height: 48 + 32, // Match day header (48) + staff header (32) = 80 total
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          right: BorderSide(
            color: isDark ? AppColors.darkSurfaceVariant : AppColors.lightGray,
            width: 0.5,
          ),
        ),
      ),
      child: const SizedBox.shrink(), // No text, just spacing
    );
  }

  // Feature 2: Helper methods for slot management
  String _getStaffDisplayName(StaffResponse staffMember) {
    // Check if this is a slot (additional staff member linked to original groomer)
    if (staffMember.notes?.contains('SLOT_FOR:') == true) {
      final originalStaffId = staffMember.notes!.split('SLOT_FOR:')[1].split('|')[0];
      final slotNumber = _getSlotNumber(staffMember.notes!);
      final originalStaff = context.read<CalendarProvider>().getStaffById(originalStaffId);
      return '${originalStaff?.displayName ?? 'Staff'} - Slot $slotNumber';
    }
    return staffMember.displayName;
  }

  int _getSlotNumber(String notes) {
    final match = RegExp(r'SLOT_NUMBER:(\d+)').firstMatch(notes);
    return match != null ? int.parse(match.group(1)!) : 1;
  }






  void _showQuickActionMenu(DateTime slotTime, String staffId) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading:  Icon(Icons.add),
              title: Text(AppLocalizations.of(context)!.translate('calendar.new_appointment')),
              onTap: () {
                Navigator.pop(context);
                widget.onTimeSlotTap?.call(slotTime, staffId);
              },
            ),
            ListTile(
              leading:  Icon(Icons.block),
              title: Text(AppLocalizations.of(context)!.translate('calendar.block_time')),
              onTap: () {
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (_) => BlockTimeDialog(
                    selectedDate: slotTime,
                    preselectedStaffId: staffId,
                    preselectedStartTime: TimeOfDay.fromDateTime(slotTime),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildStaffColumn(
    CalendarProvider provider,
    StaffResponse staffMember,
    double columnWidth,
    double slotHeight,
    int totalHours,
    int openTime,
    int closeTime,
    int lunchStart,
    int lunchEnd,
    bool isWorkDay,
    DateTime selectedDate,
  ) {
    // Get staff-specific working hours for this specific day
    final staffSettings = provider.getStaffWorkingHoursSettings(staffMember.id);
    int staffStartHour = openTime;
    int staffEndHour = closeTime;

    if (staffSettings != null) {
      final dayOfWeek = _getDayOfWeekString(selectedDate);
      final daySchedule = staffSettings.getScheduleForDay(dayOfWeek);

      if (daySchedule != null && daySchedule.isWorkingDay &&
          daySchedule.startTime != null && daySchedule.endTime != null) {
        // Parse staff start and end hours
        final startHour = int.tryParse(daySchedule.startTime!.split(':')[0]) ?? openTime;
        final endHour = int.tryParse(daySchedule.endTime!.split(':')[0]) ?? closeTime;

        // Use staff hours directly
        staffStartHour = startHour;
        staffEndHour = endHour;
      }
    }

    final staffAppointments = widget.appointments.where((appointment) {
      // First filter by date - only show appointments for the selected date
      final appointmentDate = DateTime(
        appointment.startTime.year,
        appointment.startTime.month,
        appointment.startTime.day,
      );
      final targetDate = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
      );

      if (!appointmentDate.isAtSameMomentAs(targetDate)) {
        return false;
      }

      // Then filter by staff member
      if (appointment.groomerId != null &&
          appointment.groomerId!.isNotEmpty) {
        return appointment.groomerId == staffMember.id;
      }

      String appointmentStaffName;
      try {
        appointmentStaffName =
            appointment.assignedGroomer?.isNotEmpty == true
                ? appointment.assignedGroomer!
                : 'Ana Popescu';
      } catch (e) {
        appointmentStaffName = 'Ana Popescu';
      }

      return appointmentStaffName == staffMember.name ||
          appointmentStaffName == staffMember.displayName;
    }).toList();

    final dayBlocks = provider.getBlockedTimesForDate(selectedDate);
    final staffBlocks = dayBlocks.where((block) {
      final ids = (block['staffIds'] as List).cast<String>();
      return ids.contains(staffMember.id);
    }).toList();

    // Build the staff column content
    final columnContent = Stack(
      clipBehavior: Clip.hardEdge,
      children: [
        Column(
          children: List.generate(totalHours, (index) {
            final hour = openTime + index;
            final slotTime = DateTime(
              selectedDate.year,
              selectedDate.month,
              selectedDate.day,
              hour,
            );

            final isPastSlot = slotTime.isBefore(DateTime.now());
            final isWithinStaffHours = hour >= staffStartHour && hour < staffEndHour;
            final slotStyling = provider.getTimeSlotStyling(slotTime, staffMember.id);
            final isBusinessHour = provider.showFullDay
                ? (slotStyling.isAvailable && isWithinStaffHours)
                : (slotStyling.isAvailable && isWithinStaffHours);
            final isLunchBreak = hour >= lunchStart && hour < lunchEnd;
            final hasAppointment = staffAppointments.any((apt) =>
                apt.startTime.hour <= hour && apt.endTime.hour > hour);

            if (!isWithinStaffHours) {
              return DroppableTimeSlot(
                dateTime: slotTime,
                staffId: staffMember.id,
                isBusinessHour: false,
                isLunchBreak: false,
                isAvailable: false,
                isGreyedOut: true,
                isPastSlot: isPastSlot,
                height: slotHeight,
                isDragEnabled: true,
                isOccupied: false,
              );
            }

            return Container(
              decoration: BoxDecoration(
                color: slotStyling.isGreyedOut
                    ? Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.1)
                    : null,
              ),
              child: DroppableTimeSlot(
                dateTime: slotTime,
                staffId: staffMember.id,
                isBusinessHour: isBusinessHour && !slotStyling.isGreyedOut,
                isLunchBreak: isLunchBreak,
                isAvailable: slotStyling.isAvailable,
                isGreyedOut: slotStyling.isGreyedOut,
                isPastSlot: isPastSlot,
                height: slotHeight,
                onTap: slotStyling.isInteractive
                    ? () => widget.onTimeSlotTap?.call(slotTime, staffMember.id)
                    : null,
                onLongPress: slotStyling.isInteractive
                    ? () => _showQuickActionMenu(slotTime, staffMember.id)
                    : null,
                isDragEnabled: true,
                isOccupied: hasAppointment,
                child: slotStyling.disabledReason != null
                    ? Container(
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                        ),
                      )
                    : null,
              ),
            );
          }),
        ),
        ...staffBlocks.map((block) {
          final start = DateTime.parse(block['startTime']).toLocal();
          final end = DateTime.parse(block['endTime']).toLocal();
          final topOffset = ((start.hour - openTime) * slotHeight) +
              (start.minute / 60 * slotHeight);
          final duration = end.difference(start);
          final blockHeight = (duration.inMinutes / 60) * slotHeight;

          return Positioned(
            top: topOffset,
            left: 2,
            right: 2,
            child: BlockTimeBlock(
              block: block,
              height: blockHeight,
              onTap: () => widget.onBlockTap?.call(block),
            ),
          );
        }),
        // Display Google Calendar events first (they will be behind appointments)
        ...provider.getGoogleCalendarEventsForDate(selectedDate).map((event) {
          final startHour = event.startTime.hour;
          final startMinute = event.startTime.minute;
          final topOffset = ((startHour - openTime) * slotHeight) +
              (startMinute / 60 * slotHeight);
          final duration = event.endTime.difference(event.startTime);
          final blockHeight = (duration.inMinutes / 60) * slotHeight;

          return Positioned(
            top: topOffset,
            left: 0,
            right: 0,
            child: GoogleCalendarEventBlock(
              event: event,
              height: blockHeight,
              isCompact: false,
              onTap: () => _handleGoogleEventTap(event),
            ),
          );
        }),
        // Display appointments on top (they will be above Google Calendar events)
        ...staffAppointments.map((appointment) {
          final startHour = appointment.startTime.hour;
          final startMinute = appointment.startTime.minute;
          final topOffset = ((startHour - openTime) * slotHeight) +
              (startMinute / 60 * slotHeight);
          final duration =
              appointment.endTime.difference(appointment.startTime);
          final blockHeight = (duration.inMinutes / 60) * slotHeight;

          return Positioned(
            top: topOffset,
            left: 0,
            right: 0,
            child: DraggableAppointmentBlock(
              appointment: appointment,
              height: blockHeight,
              onTap: () => widget.onAppointmentTap(appointment),
              isCompact: false,
              isDragEnabled: true,
            ),
          );
        }),
        if (_isToday(selectedDate))
          _buildCurrentTimeIndicator(openTime, slotHeight),
      ],
    );

    // Wrap with animation for smooth slot addition
    return AnimatedStaffColumn(
      key: ValueKey(staffMember.id),
      staffId: staffMember.id,
      isNewSlot: SlotAnimationTracker.isNew(staffMember.id),
      child: columnContent,
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  Widget _buildCurrentTimeIndicator(int openTime, double slotHeight) {
    final now = DateTime.now();
    final currentHour = now.hour;
    final currentMinute = now.minute;

    // In full day mode, show indicator for any hour; in business mode, only during business hours
    final businessCloseTime = widget.businessHours['closeTime'] as int;
    final showFullDay = context.read<CalendarProvider>().showFullDay;

    if (!showFullDay && (currentHour < openTime || currentHour >= businessCloseTime)) {
      return SizedBox.shrink();
    }

    if (showFullDay && (currentHour < openTime || currentHour >= 24)) {
      return SizedBox.shrink();
    }

    final topOffset = ((currentHour - openTime) * slotHeight) +
        (currentMinute / 60 * slotHeight);

    return Positioned(
      top: topOffset,
      left: 0,
      right: 0,
      child: Container(
        height: 2,
        color: Colors.red,
        child: Row(
          children: [
            Container(
              width: 6,
              height: 6,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: Container(
                height: 2,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Helper method to get day of week string
  String _getDayOfWeekString(DateTime date) {
    const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return dayNames[date.weekday - 1];
  }

  /// Scroll vertically to show the specified time slot centered in viewport
  void scrollToTime(DateTime time) {
    final provider = context.read<CalendarProvider>();
    final openTime = provider.showFullDay
        ? 0
        : widget.businessHours['openTime'] as int;
    final slotHeight = provider.getResponsiveTimeSlotHeight(context);

    final offset = ((time.hour - openTime) * slotHeight) +
        (time.minute / 60 * slotHeight);

    if (_verticalScrollController.hasClients) {
      final max = _verticalScrollController.position.maxScrollExtent;
      final viewportHeight = _verticalScrollController.position.viewportDimension;

      // Center the appointment by subtracting half the viewport height
      final centeredOffset = offset - (viewportHeight / 2);

      _verticalScrollController.animateTo(
        centeredOffset.clamp(0.0, max),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }

    if (_timeScrollController.hasClients) {
      final max = _timeScrollController.position.maxScrollExtent;
      final viewportHeight = _timeScrollController.position.viewportDimension;

      // Center the appointment by subtracting half the viewport height
      final centeredOffset = offset - (viewportHeight / 2);

      _timeScrollController.animateTo(
        centeredOffset.clamp(0.0, max),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Convenience method to scroll to the current time
  // void scrollToCurrentTime() {
  //   scrollToTime(DateTime.now());
  // }

  /// Calculate day-specific open and close times based on business rules
  Map<String, int> _calculateDaySpecificTimeRange(CalendarProvider provider, DateTime date) {
    // Default times as fallback
    int defaultOpenTime = 9;
    int defaultCloseTime = 21;

    // If showing full day (24h mode), return full range
    if (provider.showFullDay) {
      return {'openTime': 0, 'closeTime': 24};
    }

    // Get day of week (1 = Monday, 7 = Sunday)
    int dayOfWeek = date.weekday;
    String dayKey = _getDayKeyFromWeekday(dayOfWeek);

    // Calculate time range based on selected staff working hours for this specific day
    int earliestStart = 24; // Initialize to end of day
    int latestEnd = 0;     // Initialize to start of day
    bool hasAnyWorkingStaff = false;

    // Check working hours for each selected staff member for this specific day
    for (String staffId in provider.selectedStaff) {
      final staffWorkingHours = provider.getStaffWorkingHours(staffId);
      if (staffWorkingHours != null && staffWorkingHours.weeklySchedule.containsKey(dayKey)) {
        final daySchedule = staffWorkingHours.weeklySchedule[dayKey];

        // Only consider this staff member if they're working on this day
        if (daySchedule != null && daySchedule.isWorkingDay &&
            daySchedule.startTime != null && daySchedule.endTime != null) {
          hasAnyWorkingStaff = true;

          // Parse start time
          final startHour = int.tryParse(daySchedule.startTime!.split(':')[0]) ?? defaultOpenTime;
          if (startHour < earliestStart) {
            earliestStart = startHour;
          }

          // Parse end time
          final endHour = int.tryParse(daySchedule.endTime!.split(':')[0]) ?? defaultCloseTime;
          if (endHour > latestEnd) {
            latestEnd = endHour;
          }
        }
      }
    }

    // If no staff are working on this day or no staff working hours found,
    // fall back to salon working hours settings
    if (!hasAnyWorkingStaff) {
      final workingHoursSettings = provider.workingHoursSettings;
      if (workingHoursSettings != null && workingHoursSettings.weeklySchedule.containsKey(dayKey)) {
        final salonDaySchedule = workingHoursSettings.weeklySchedule[dayKey];

        if (salonDaySchedule != null && salonDaySchedule.isWorkingDay &&
            salonDaySchedule.startTime != null && salonDaySchedule.endTime != null) {
          final openTime = int.tryParse(salonDaySchedule.startTime!.split(':')[0]) ?? defaultOpenTime;
          final closeTime = int.tryParse(salonDaySchedule.endTime!.split(':')[0]) ?? defaultCloseTime;
          return {'openTime': openTime, 'closeTime': closeTime};
        }
      }

      // Final fallback to defaults
      return {'openTime': defaultOpenTime, 'closeTime': defaultCloseTime};
    }

    // Apply padding if configured (but only if we have actual staff schedules)
    if (provider.timeRangePaddingHours > 0) {
      earliestStart = (earliestStart - provider.timeRangePaddingHours).clamp(0, 23);
      latestEnd = (latestEnd + provider.timeRangePaddingHours).clamp(1, 24);
    }

    // Ensure minimum time range
    final totalHours = latestEnd - earliestStart;
    if (totalHours < provider.minimumTimeRangeHours) {
      final additionalHours = provider.minimumTimeRangeHours - totalHours;
      final paddingBefore = additionalHours ~/ 2;
      final paddingAfter = additionalHours - paddingBefore;

      earliestStart = (earliestStart - paddingBefore).clamp(0, 23);
      latestEnd = (latestEnd + paddingAfter).clamp(1, 24);
    }

    return {'openTime': earliestStart, 'closeTime': latestEnd};
  }

  /// Convert weekday number to day key string
  String _getDayKeyFromWeekday(int weekday) {
    switch (weekday) {
      case 1: return 'monday';
      case 2: return 'tuesday';
      case 3: return 'wednesday';
      case 4: return 'thursday';
      case 5: return 'friday';
      case 6: return 'saturday';
      case 7: return 'sunday';
      default: return 'monday';
    }
  }
} // end of _DayViewState
