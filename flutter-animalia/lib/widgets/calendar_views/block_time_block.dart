import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/theme_provider.dart';

class BlockTimeBlock extends StatelessWidget {
  final Map<String, dynamic> block;
  final double height;
  final VoidCallback? onTap;

  const BlockTimeBlock({
    Key? key,
    required this.block,
    required this.height,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final start = DateTime.parse(block['startTime']).toLocal();
    final end = DateTime.parse(block['endTime']).toLocal();
    final reason = (block['customReason'] ?? block['reason'] ?? '') as String;
    final duration = end.difference(start).inMinutes;

    final tooltip =
        '$reason\n${DateFormat.Hm().format(start)} - ${DateFormat.Hm().format(end)} ($duration min)';

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isDark = Theme.of(context).brightness == Brightness.dark;

        return GestureDetector(
          onTap: onTap,
          child: Tooltip(
            message: tooltip,
            preferBelow: false,
            child: Container(
              height: height,
              padding: const EdgeInsets.all(4),
              margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                color: isDark
                    ? AppColors.darkError.withValues(alpha: 0.25)
                    : AppColors.lightError.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: isDark
                    ? AppColors.darkError.withValues(alpha: 0.8)
                    : AppColors.lightError.withValues(alpha: 0.7),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: (isDark ? AppColors.darkError : AppColors.lightError).withOpacity(0.25),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
              child: CustomPaint(
                painter: _DiagonalLinesPainter(isDark: isDark),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.block,
                        color: isDark ? AppColors.darkError : AppColors.lightError,
                        size: height > 40 ? 16 : 12,
                      ),
                      if (height > 30) ...[
                        const SizedBox(height: 2),
                        Text(
                          reason.isNotEmpty ? reason : AppLocalizations.of(context)!.translate('calendar.appointments.blocked'),
                          style: TextStyle(
                            color: isDark ? AppColors.darkText : AppColors.lightText,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _DiagonalLinesPainter extends CustomPainter {
  final bool isDark;

  const _DiagonalLinesPainter({required this.isDark});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = (isDark
          ? AppColors.darkError.withValues(alpha: 0.9)
          : AppColors.lightError.withValues(alpha: 0.1))
      ..strokeWidth = .5;

    const spacing = 8.0;
    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
