import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';
import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../utils/weekday_helper.dart';
import '../../utils/genie_animation.dart';
import '../../widgets/dialogs/appointment_details_dialog.dart';
import '../common/appointment_item_widget.dart';

enum AppointmentFilter {
  all,
  today,
  upcoming,
  thisWeek,
  thisMonth,
  completed,
  pending,
  cancelled,
}

class AppointmentsListView extends StatefulWidget {
  final AppointmentFilter initialFilter;
  final String? searchQuery;

  const AppointmentsListView({
    super.key,
    this.initialFilter = AppointmentFilter.upcoming,
    this.searchQuery,
  });

  @override
  State<AppointmentsListView> createState() => _AppointmentsListViewState();
}

class _AppointmentsListViewState extends State<AppointmentsListView> {
  final ScrollController _scrollController = ScrollController();
  late AppointmentFilter _currentFilter;
  String _searchQuery = '';
  List<Appointment> _filteredAppointments = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.initialFilter;
    _searchQuery = widget.searchQuery ?? '';
    _loadAppointments();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadAppointments() async {
    setState(() {
      _isLoading = true;
    });

    final provider = Provider.of<CalendarProvider>(context, listen: false);

    // CRITICAL FIX: Clear appointment cache to prevent cross-salon data leakage
    // This ensures old appointments from previous salon are removed
    provider.clearAppointmentCache();

    // Load appointments for a wider range to support all filters
    final now = DateTime.now();
    final startDate = now.subtract(const Duration(days: 30)); // Past month for completed
    final endDate = now.add(const Duration(days: 90)); // Next 3 months for upcoming

    // Load appointments for the entire range
    await provider.fetchAppointmentsForDateRange(startDate, endDate);

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        _updateFilteredAppointments(provider);

        return Column(
          children: [
            // Search and filter header
            _buildSearchAndFilterHeader(),

            // Quick stats
            _buildQuickStats(),

            // Appointments list
            Expanded(
              child: _buildAppointmentsList(),
            ),
          ],
        );
      },
    );
  }

  void _updateFilteredAppointments(CalendarProvider provider) {
    final now = DateTime.now();
    final allAppointments = provider.appointments;

    List<Appointment> filtered = allAppointments.where((appointment) {
      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!appointment.clientName.toLowerCase().contains(query) &&
            !appointment.petName.toLowerCase().contains(query) &&
            !appointment.service.toLowerCase().contains(query)) {
          return false;
        }
      }

      // Apply status/time filter
      switch (_currentFilter) {
        case AppointmentFilter.all:
          return true;
        case AppointmentFilter.today:
          return _isSameDay(appointment.startTime, now);
        case AppointmentFilter.upcoming:
          return appointment.startTime.isAfter(now);
        case AppointmentFilter.thisWeek:
          final weekStart = _getWeekStart(now);
          final weekEnd = weekStart.add(const Duration(days: 6));
          return appointment.startTime.isAfter(weekStart) &&
                 appointment.startTime.isBefore(weekEnd.add(const Duration(days: 1)));
        case AppointmentFilter.thisMonth:
          return appointment.startTime.year == now.year &&
                 appointment.startTime.month == now.month;
        case AppointmentFilter.completed:
          return appointment.status.toLowerCase() == 'completed';
        case AppointmentFilter.pending:
          return appointment.status.toLowerCase() == 'scheduled' ||
                 appointment.status.toLowerCase() == 'confirmed';
        case AppointmentFilter.cancelled:
          return appointment.status.toLowerCase() == 'cancelled';
      }
    }).toList();

    // Sort appointments chronologically
    filtered.sort((a, b) => a.startTime.compareTo(b.startTime));

    _filteredAppointments = filtered;
  }

  Widget _buildSearchAndFilterHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
            decoration: InputDecoration(
              hintText: context.tr('calendar.search_hint'),
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
            ),
          ),

          const SizedBox(height: 12),

          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: AppointmentFilter.values.map((filter) {
                final isSelected = _currentFilter == filter;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(_getFilterLabel(filter)),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _currentFilter = filter;
                      });
                    },
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    selectedColor: Theme.of(context).colorScheme.primaryContainer,
                    checkmarkColor: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    final now = DateTime.now();
    final todayCount = _filteredAppointments.where((apt) => _isSameDay(apt.startTime, now)).length;
    final upcomingCount = _filteredAppointments.where((apt) => apt.startTime.isAfter(now)).length;
    final completedCount = _filteredAppointments.where((apt) => apt.status.toLowerCase() == 'completed').length;
    final totalRevenue = _filteredAppointments
        .where((apt) => apt.status.toLowerCase() == 'completed')
        .fold(0.0, (sum, apt) => sum + apt.totalPrice);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              context.tr('calendar.stats_today'),
              '$todayCount',
              Icons.today,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              context.tr('calendar.stats_upcoming'),
              '$upcomingCount',
              Icons.upcoming,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              context.tr('calendar.stats_completed'),
              '$completedCount',
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              context.tr('calendar.stats_revenue'),
              '${totalRevenue.toStringAsFixed(0)} RON',
              Icons.attach_money,
              Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentsList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_filteredAppointments.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _filteredAppointments.length,
      itemBuilder: (context, index) {
        final appointment = _filteredAppointments[index];
        final showDateHeader = index == 0 ||
            !_isSameDay(appointment.startTime, _filteredAppointments[index - 1].startTime);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showDateHeader) _buildDateHeader(appointment.startTime),
            _buildAppointmentItem(appointment),
            const SizedBox(height: 8),
          ],
        );
      },
    );
  }

  Widget _buildDateHeader(DateTime date) {
    final now = DateTime.now();
    final isToday = _isSameDay(date, now);
    final isTomorrow = _isSameDay(date, now.add(const Duration(days: 1)));
    final isYesterday = _isSameDay(date, now.subtract(const Duration(days: 1)));

    String dateText;
    if (isToday) {
      dateText = context.tr('calendar.today');
    } else if (isTomorrow) {
      dateText = context.tr('calendar.tomorrow');
    } else if (isYesterday) {
      dateText = context.tr('calendar.yesterday');
    } else {
      dateText = WeekdayHelper.getFullDateWithWeekday(AppLocalizations.of(context)!, date);
    }

    return Container(
      margin: const EdgeInsets.only(top: 16, bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: isToday
            ? Theme.of(context).colorScheme.primaryContainer
            : Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        dateText,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: isToday
              ? Theme.of(context).colorScheme.onPrimaryContainer
              : Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  Widget _buildAppointmentItem(Appointment appointment) {
    return AppointmentItemWidget(
      appointment: appointment,
      showDate: false, // Date is shown in header
      isCompact: false,
      showActions: true,
      onTap: () => _showAppointmentDetails(appointment),
      onStatusChange: (status) => _changeAppointmentStatus(appointment, status),
      onCall: () => _callClient(appointment),
      onMessage: () => _messageClient(appointment),
      onEdit: () => _editAppointment(appointment),
      onCancel: () => _cancelAppointment(appointment),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    String message;
    String subtitle;
    IconData icon;

    switch (_currentFilter) {
      case AppointmentFilter.today:
        message = context.tr('calendar.empty_today');
        subtitle = context.tr('calendar.empty_today_subtitle');
        icon = Icons.today;
        break;
      case AppointmentFilter.upcoming:
        message = context.tr('calendar.empty_upcoming');
        subtitle = context.tr('calendar.empty_upcoming_subtitle');
        icon = Icons.upcoming;
        break;
      case AppointmentFilter.completed:
        message = context.tr('calendar.empty_completed');
        subtitle = context.tr('calendar.empty_completed_subtitle');
        icon = Icons.check_circle;
        break;
      case AppointmentFilter.pending:
        message = context.tr('calendar.empty_pending');
        subtitle = context.tr('calendar.empty_pending_subtitle');
        icon = Icons.pending;
        break;
      case AppointmentFilter.cancelled:
        message = context.tr('calendar.empty_cancelled');
        subtitle = context.tr('calendar.empty_cancelled_subtitle');
        icon = Icons.cancel;
        break;
      default:
        message = context.tr('calendar.empty_default');
        subtitle = _searchQuery.isNotEmpty
            ? context.tr('calendar.empty_search_subtitle')
            : context.tr('calendar.empty_filter_subtitle');
        icon = Icons.event_busy;
    }

    return SingleChildScrollView(
      child: Container(
        height: MediaQuery.of(context).size.height * 0.6,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getFilterLabel(AppointmentFilter filter) {
    switch (filter) {
      case AppointmentFilter.all:
        return context.tr('calendar.filter_all');
      case AppointmentFilter.today:
        return context.tr('calendar.filter_today');
      case AppointmentFilter.upcoming:
        return context.tr('calendar.filter_upcoming');
      case AppointmentFilter.thisWeek:
        return context.tr('calendar.filter_this_week');
      case AppointmentFilter.thisMonth:
        return context.tr('calendar.filter_this_month');
      case AppointmentFilter.completed:
        return context.tr('calendar.filter_completed');
      case AppointmentFilter.pending:
        return context.tr('calendar.filter_pending');
      case AppointmentFilter.cancelled:
        return context.tr('calendar.filter_cancelled');
    }
  }

  void _showAppointmentDetails(Appointment appointment) {
    GenieAnimation.showGenieDialog(
      context: context,
      dialog: AppointmentDetailsDialog(appointment: appointment),
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  // Business action methods
  void _changeAppointmentStatus(Appointment appointment, String newStatus) async {
    try {
      // Show confirmation dialog for status changes
      final confirmed = await _showStatusChangeConfirmation(appointment, newStatus);
      if (!confirmed) return;

      // TODO: Implement appointment status update API call
      // await AppointmentService.updateAppointmentStatus(appointment.id, newStatus);

      // Refresh the appointments list
      _loadAppointments();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Status programării actualizat la: ${_getStatusDisplayName(newStatus)}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la actualizarea statusului: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _callClient(Appointment appointment) {
    // TODO: Implement phone call functionality
    // This could use url_launcher to open the phone app
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Apelează ${appointment.clientName} la ${appointment.clientPhone}'),
        action: SnackBarAction(
          label: 'Sună',
          onPressed: () {
            // TODO: Launch phone app with number
          },
        ),
      ),
    );
  }

  void _messageClient(Appointment appointment) {
    // TODO: Implement SMS functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Trimite mesaj către ${appointment.clientName}'),
        action: SnackBarAction(
          label: 'Mesaj',
          onPressed: () {
            // TODO: Launch SMS app or show message dialog
          },
        ),
      ),
    );
  }

  void _editAppointment(Appointment appointment) {
    // TODO: Navigate to edit appointment screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Editează programarea pentru ${appointment.clientName}'),
        action: SnackBarAction(
          label: 'Editează',
          onPressed: () {
            // TODO: Navigate to edit screen
          },
        ),
      ),
    );
  }

  void _cancelAppointment(Appointment appointment) async {
    final confirmed = await _showCancelConfirmation(appointment);
    if (!confirmed) return;

    try {
      // TODO: Implement appointment cancellation API call
      // await AppointmentService.cancelAppointment(appointment.id);

      _loadAppointments();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Programarea pentru ${appointment.clientName} a fost anulată'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la anularea programării: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool> _showStatusChangeConfirmation(Appointment appointment, String newStatus) async {
    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmă schimbarea statusului'),
          content: Text(
            'Ești sigur că vrei să schimbi statusul programării pentru ${appointment.clientName} la "${_getStatusDisplayName(newStatus)}"?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Anulează'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Confirmă'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  Future<bool> _showCancelConfirmation(Appointment appointment) async {
    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmă anularea'),
          content: Text(
            'Ești sigur că vrei să anulezi programarea pentru ${appointment.clientName} din ${DateFormat('dd/MM/yyyy HH:mm').format(appointment.startTime)}?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Nu'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Da, anulează'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  String _getStatusDisplayName(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'Finalizat';
      case 'cancelled':
        return 'Anulat';
      case 'confirmed':
        return 'Confirmat';
      case 'scheduled':
        return 'Programat';
      case 'in_progress':
        return 'În desfășurare';
      case 'no_show':
        return 'Nu s-a prezentat';
      default:
        return status;
    }
  }
}
