import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';

import '../../models/salon_subscription.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/subscription_provider.dart';
import '../../services/auth/auth_service.dart';
import '../../services/calendar_preferences_service.dart';
import '../../services/staff_service.dart';
import '../../services/subscription/revenue_cat_paywall_service.dart';
import '../../services/subscription_limit_service.dart';
import '../../utils/debug_logger.dart';
import '../calendar_views/google_calendar_view.dart';
import '../forms/add_staff_dialog.dart';
import '../../services/ui_notification_service.dart';
import '../../providers/role_provider.dart';
import '../../models/user_role.dart';
import '../../l10n/app_localizations.dart';

class CalendarSettingsDrawer extends StatefulWidget {
  final CalendarViewMode currentViewMode;
  final ValueChanged<CalendarViewMode> onViewModeChanged;
  final bool monthlyViewEnabled;

  const CalendarSettingsDrawer({
    Key? key,
    required this.currentViewMode,
    required this.onViewModeChanged,
    required this.monthlyViewEnabled,
  }) : super(key: key);

  @override
  State<CalendarSettingsDrawer> createState() => _CalendarSettingsDrawerState();
}

class _CalendarSettingsDrawerState extends State<CalendarSettingsDrawer> {
  // Track which sections are expanded
  bool _isStaffSectionExpanded = true;
  bool _isDisplaySectionExpanded = false;
  bool _isZoomSectionExpanded = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        color: Theme.of(context).colorScheme.surface,
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildModernHeader(),
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  children: [
                    _buildViewModeCard(),
                    const SizedBox(height: 12),
                    _buildTimeFormatCard(),
                    const SizedBox(height: 12),
                    if (!kIsWeb) ...[
                      _buildGoogleCalendarCard(),
                      const SizedBox(height: 12),
                    ],
                    _buildStaffCard(),
                    const SizedBox(height: 12),
                    _buildDisplayOptionsCard(),
                    const SizedBox(height: 12),
                    _buildZoomCard(),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.settings_outlined,
              color: Theme.of(context).colorScheme.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('calendar.calendar_settings'),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w700,
                    fontSize: 20,
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  context.tr('calendar.calendar_settings_subtitle'),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            tooltip: context.tr('calendar.calendar_settings_close'),
            style: IconButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  // Modern card wrapper for sections
  Widget _buildCard({
    required String title,
    required Widget child,
    IconData? icon,
    bool isExpandable = false,
    bool isExpanded = false,
    VoidCallback? onExpandToggle,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: isExpandable ? onExpandToggle : null,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  if (icon != null) ...[
                    Icon(
                      icon,
                      size: 20,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 12),
                  ],
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                        letterSpacing: -0.2,
                      ),
                    ),
                  ),
                  if (isExpandable)
                    Icon(
                      isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                ],
              ),
            ),
          ),
          if (!isExpandable || isExpanded) ...[
            Divider(
              height: 1,
              thickness: 1,
              color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: child,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildViewModeCard() {
    return _buildCard(
      title: context.tr('calendar.hour_display'),
      icon: Icons.access_time_outlined,
      child: Consumer<CalendarProvider>(
        builder: (context, provider, child) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildModernSegmentedControl(
                options: [
                  SegmentedOption(
                    value: CalendarHourViewMode.businessHours,
                    icon: Icons.business_outlined,
                    label: context.tr('calendar.business_hours'),
                  ),
                  SegmentedOption(
                    value: CalendarHourViewMode.fullDay,
                    icon: Icons.schedule_outlined,
                    label: context.tr('calendar.full_day'),
                  ),
                ],
                selectedValue: provider.hourViewMode,
                onChanged: provider.setHourViewMode,
              ),
              const SizedBox(height: 12),
              _buildHintText(
                provider.hourViewMode == CalendarHourViewMode.businessHours
                    ? context.tr('calendar.business_hours_desc')
                    : context.tr('calendar.full_day_desc'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTimeFormatCard() {
    return _buildCard(
      title: context.tr('calendar.time_format'),
      icon: Icons.schedule,
      child: Consumer<CalendarProvider>(
        builder: (context, provider, child) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildModernSegmentedControl(
                options: [
                  SegmentedOption(
                    value: CalendarTimeFormat.twelveHour,
                    icon: Icons.wb_twilight,
                    label: context.tr('calendar.time_format_12h'),
                  ),
                  SegmentedOption(
                    value: CalendarTimeFormat.twentyFourHour,
                    icon: Icons.timelapse,
                    label: context.tr('calendar.time_format_24h'),
                  ),
                ],
                selectedValue: provider.timeFormat,
                onChanged: provider.setTimeFormat,
              ),
              const SizedBox(height: 12),
              _buildHintText(
                provider.timeFormat == CalendarTimeFormat.twelveHour
                    ? context.tr('calendar.time_format_12h_desc')
                    : context.tr('calendar.time_format_24h_desc'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildGoogleCalendarCard() {
    return _buildCard(
      title: 'Google Calendar',
      icon: Icons.calendar_today_outlined,
      child: _buildGoogleCalendarSyncSection(),
    );
  }

  Widget _buildStaffCard() {
    return _buildCard(
      title: context.tr('calendar.staff_filter'),
      icon: Icons.people_outline,
      isExpandable: true,
      isExpanded: _isStaffSectionExpanded,
      onExpandToggle: () {
        setState(() {
          _isStaffSectionExpanded = !_isStaffSectionExpanded;
        });
      },
      child: _buildStaffFilterSection(),
    );
  }

  Widget _buildDisplayOptionsCard() {
    return _buildCard(
      title: context.tr('calendar.display_options'),
      icon: Icons.visibility_outlined,
      isExpandable: true,
      isExpanded: _isDisplaySectionExpanded,
      onExpandToggle: () {
        setState(() {
          _isDisplaySectionExpanded = !_isDisplaySectionExpanded;
        });
      },
      child: _buildDisplayOptionsSection(),
    );
  }

  Widget _buildZoomCard() {
    return _buildCard(
      title: context.tr('calendar.zoom'),
      icon: Icons.zoom_in_outlined,
      isExpandable: true,
      isExpanded: _isZoomSectionExpanded,
      onExpandToggle: () {
        setState(() {
          _isZoomSectionExpanded = !_isZoomSectionExpanded;
        });
      },
      child: _buildZoomSection(),
    );
  }

  // Modern segmented control
  Widget _buildModernSegmentedControl<T>({
    required List<SegmentedOption<T>> options,
    required T selectedValue,
    required ValueChanged<T> onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: options.asMap().entries.map((entry) {
          final index = entry.key;
          final option = entry.value;
          final isSelected = option.value == selectedValue;
          final isFirst = index == 0;
          final isLast = index == options.length - 1;

          return Expanded(
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => onChanged(option.value),
                borderRadius: BorderRadius.horizontal(
                  left: isFirst ? const Radius.circular(7) : Radius.zero,
                  right: isLast ? const Radius.circular(7) : Radius.zero,
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary.withOpacity(0.12)
                        : Colors.transparent,
                    borderRadius: BorderRadius.horizontal(
                      left: isFirst ? const Radius.circular(7) : Radius.zero,
                      right: isLast ? const Radius.circular(7) : Radius.zero,
                    ),
                    border: !isLast
                        ? Border(
                            right: BorderSide(
                              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                            ),
                          )
                        : null,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        option.icon,
                        size: 16,
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 6),
                      Flexible(
                        child: Text(
                          option.label,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildHintText(String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          Icons.info_outline,
          size: 14,
          color: Theme.of(context).colorScheme.primary.withOpacity(0.6),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHourViewModeSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('calendar.hour_display'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            ToggleButtons(
              isSelected: [
                provider.hourViewMode == CalendarHourViewMode.businessHours,
                provider.hourViewMode == CalendarHourViewMode.fullDay,
              ],
              onPressed: (index) {
                final mode = index == 0
                    ? CalendarHourViewMode.businessHours
                    : CalendarHourViewMode.fullDay;
                provider.setHourViewMode(mode);
              },
              borderRadius: BorderRadius.circular(8),
              selectedColor: Theme.of(context).colorScheme.onPrimary,
              selectedBorderColor: Theme.of(context).colorScheme.primary,
              fillColor: Theme.of(context).colorScheme.primary,
              color: Theme.of(context).colorScheme.onSurface,
              constraints: const BoxConstraints(minWidth: 90, minHeight: 40),
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.business, size: 16),
                      const SizedBox(width: 6),
                      Text(context.tr('calendar.business_hours'), style: const TextStyle(fontSize: 13)),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.schedule, size: 16),
                      const SizedBox(width: 6),
                      Text(context.tr('calendar.full_day'), style: const TextStyle(fontSize: 13)),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              provider.hourViewMode == CalendarHourViewMode.businessHours
                  ? context.tr('calendar.business_hours_desc')
                  : context.tr('calendar.full_day_desc'),
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTimeFormatSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('calendar.time_format'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            ToggleButtons(
              isSelected: [
                provider.timeFormat == CalendarTimeFormat.twelveHour,
                provider.timeFormat == CalendarTimeFormat.twentyFourHour,
              ],
              onPressed: (index) {
                final format = index == 0
                    ? CalendarTimeFormat.twelveHour
                    : CalendarTimeFormat.twentyFourHour;
                provider.setTimeFormat(format);
              },
              borderRadius: BorderRadius.circular(8),
              selectedColor: Theme.of(context).colorScheme.onPrimary,
              selectedBorderColor: Theme.of(context).colorScheme.primary,
              fillColor: Theme.of(context).colorScheme.primary,
              color: Theme.of(context).colorScheme.onSurface,
              constraints: const BoxConstraints(minWidth: 90, minHeight: 40),
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.schedule, size: 16),
                      const SizedBox(width: 6),
                      Text(context.tr('calendar.time_format_12h'), style: const TextStyle(fontSize: 13)),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.timelapse, size: 16),
                      const SizedBox(width: 6),
                      Text(context.tr('calendar.time_format_24h'), style: const TextStyle(fontSize: 13)),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              provider.timeFormat == CalendarTimeFormat.twelveHour
                  ? context.tr('calendar.time_format_12h_desc')
                  : context.tr('calendar.time_format_24h_desc'),
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildGoogleCalendarSyncSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Modern switch tile
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: SwitchListTile(
                value: provider.googleCalendarSyncEnabled,
                onChanged: (value) {
                  provider.setGoogleCalendarSyncEnabled(value);
                },
                title: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: const Color(0xFF4285F4),
                          width: 1.5,
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '31',
                          style: TextStyle(
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF4285F4),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Sincronizare activă',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                activeColor: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            _buildHintText(context.tr('calendar.google_calendar_sync')),

            if (provider.googleCalendarSyncEnabled) ...[
              const SizedBox(height: 16),
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SwitchListTile(
                  value: provider.showGoogleCalendarEvents,
                  onChanged: (value) {
                    provider.setShowGoogleCalendarEvents(value);
                  },
                  title: const Text(
                    'Afișează evenimente',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  subtitle: const Text(
                    'Actualizare automată la 30s',
                    style: TextStyle(fontSize: 11),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildStaffFilterSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final staff = provider.availableStaff;
        final selectedStaff = provider.selectedStaff;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Select all/deselect all button
            Consumer<CalendarProvider>(
              builder: (context, calendarProvider, child) {
                final allSelected = selectedStaff.length == staff.length && staff.isNotEmpty;

                return TextButton.icon(
                  onPressed: staff.isEmpty ? null : () {
                    if (allSelected) {
                      provider.clearStaffSelection();
                    } else {
                      provider.selectAllStaff();
                    }
                  },
                  icon: Icon(
                    allSelected ? Icons.check_box : Icons.check_box_outline_blank,
                    size: 18,
                  ),
                  label: Text(
                    allSelected ? context.tr('calendar.deselect_all') : context.tr('calendar.select_all'),
                    style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                );
              },
            ),

            const SizedBox(height: 8),

            if (staff.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        context.tr('calendar.no_staff_available'),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            else
              ...staff.map((staffMember) => _buildModernStaffOption(staffMember, provider)),

            const SizedBox(height: 16),
            _buildCreateStaffButton(),
          ],
        );
      },
    );
  }

  Widget _buildModernStaffOption(StaffResponse staffMember, CalendarProvider provider) {
    final isSelected = provider.selectedStaff.contains(staffMember.id);
    final staffColor = provider.getStaffColor(staffMember.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected
            ? Theme.of(context).colorScheme.primary.withOpacity(0.08)
            : Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withOpacity(0.3)
              : Theme.of(context).colorScheme.outline.withOpacity(0.15),
          width: isSelected ? 1.5 : 1,
        ),
      ),
      child: CheckboxListTile(
        value: isSelected,
        onChanged: (value) {
          if (value == true) {
            provider.selectStaff(staffMember.id);
          } else {
            provider.deselectStaff(staffMember.id);
          }
        },
        title: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: staffColor,
                shape: BoxShape.circle,
                border: Border.all(
                  color: staffColor.computeLuminance() > 0.5
                      ? Colors.black.withOpacity(0.2)
                      : Colors.white.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    staffMember.displayName,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (staffMember.nickname != null &&
                      staffMember.nickname!.isNotEmpty &&
                      staffMember.nickname != staffMember.name)
                    Text(
                      '(${staffMember.name})',
                      style: TextStyle(
                        fontSize: 11,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                ],
              ),
            ),
            Consumer<RoleProvider>(
              builder: (context, roleProvider, child) {
                final canSeeDelete = roleProvider.permissions?.groomerRole == GroomerRole.chiefGroomer;
                final isStaffChief = staffMember.groomerRole == GroomerRole.chiefGroomer;

                if (!canSeeDelete || isStaffChief) {
                  return const SizedBox.shrink();
                }

                return IconButton(
                  onPressed: () => _showDeleteStaffConfirmation(staffMember, provider),
                  icon: const Icon(Icons.delete_outline, size: 18),
                  color: Theme.of(context).colorScheme.error,
                  tooltip: 'Șterge membru',
                  splashRadius: 18,
                  padding: const EdgeInsets.all(8),
                  constraints: const BoxConstraints(),
                );
              },
            ),
          ],
        ),
        activeColor: Theme.of(context).colorScheme.primary,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        controlAffinity: ListTileControlAffinity.leading,
      ),
    );
  }

  /// Show confirmation dialog and delete staff using existing StaffService
  void _showDeleteStaffConfirmation(StaffResponse staffMember, CalendarProvider provider) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            Icon(Icons.delete_forever, color: Theme.of(context).colorScheme.error),
            const SizedBox(width: 12),
            Expanded(child: Text('Șterge ${staffMember.name}?')),
          ],
        ),
        content: Text('Ești sigur că vrei să ștergi acest membru din echipă? Această acțiune nu poate fi anulată.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Anulează', style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant)),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // Haptic feedback to make action feel snappy
              try {
                await HapticFeedback.mediumImpact();
              } catch (_) {}

              // Perform deletion
              try {
                final response = await StaffService.removeStaffFromCurrentSalon(staffMember.id);

                if (response.success) {
                  // Refresh calendar provider staff list and selections
                  try {
                    provider.deselectStaff(staffMember.id);
                    await provider.refreshStaffData();
                    await provider.forceCalendarRefresh(reason: 'Staff removed from settings drawer');
                  } catch (e) {
                    DebugLogger.logVerbose('⚠️ Error refreshing provider after staff delete: $e');
                  }

                  UINotificationService.showSuccess(
                    context: context,
                    title: 'Membru șters',
                    message: '${staffMember.name} a fost eliminat din echipă cu succes',
                  );
                } else {
                  UINotificationService.showError(
                    context: context,
                    title: 'Eroare ștergere',
                    message: response.error ?? 'Nu s-a putut șterge membrul din echipă',
                    actionLabel: 'Încearcă din nou',
                    onActionPressed: () => _showDeleteStaffConfirmation(staffMember, provider),
                  );
                }
              } catch (e) {
                UINotificationService.showError(
                  context: context,
                  title: 'Eroare ștergere',
                  message: 'A apărut o eroare neașteptată: $e',
                  actionLabel: 'Încearcă din nou',
                  onActionPressed: () => _showDeleteStaffConfirmation(staffMember, provider),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('Șterge'),
          ),
        ],
      ),
    );
  }



  /// Handle add staff with intelligent upgrade check
  Future<void> _handleAddStaffWithUpgradeCheck(SubscriptionProvider subscriptionProvider) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) return;

      // Get current subscription and limits
      final currentTier = subscriptionProvider.currentTier;
      final limits = await SubscriptionLimitService.getLimits(salonId);
      final currentStaffCount = limits.currentStaffCount;
      final maxStaff = limits.maxStaff;

      // Check if user can add more staff
      if (maxStaff == -1 || currentStaffCount < maxStaff) {
        // User can add staff, show dialog directly
        _showAddStaffDialog();
        return;
      }

      // User has reached limit, show intelligent upgrade prompt
      if (mounted) {
        _showIntelligentUpgradePrompt(currentTier, salonId);
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error checking staff limits: $e');
      // Fallback to showing dialog
      _showAddStaffDialog();
    }
  }

  /// Show intelligent upgrade prompt based on current tier
  void _showIntelligentUpgradePrompt(SubscriptionTier? currentTier, String salonId) {
    SubscriptionTier targetTier;

    switch (currentTier) {
      case SubscriptionTier.free:
        targetTier = SubscriptionTier.freelancer;
        break;
      case SubscriptionTier.freelancer:
        targetTier = SubscriptionTier.enterprise;
        break;
      case SubscriptionTier.team:
        targetTier = SubscriptionTier.enterprise;
        break;
      case SubscriptionTier.enterprise:
        targetTier = SubscriptionTier.enterprise;
        break;
      case null:
        // No subscription
        targetTier = SubscriptionTier.team;
        break;
    }

    if (mounted) {
      RevenueCatPaywallService.showPaywall(
        context: context,
        defaultTier: targetTier,
        salonId: salonId,
      );
    }
  }

  void _showAddStaffDialog() {
    showDialog(
      context: context,
      builder: (context) => AddStaffDialog(
        onSuccess: () async {
          DebugLogger.logVerbose('🔄 CalendarSettingsDrawer: Staff added successfully, refreshing calendar data...');

          // Refresh staff data with multiple attempts to ensure new staff appears
          await _refreshStaffDataWithRetry();
        },
      ),
    );
  }

  /// Refresh staff data with retry mechanism to ensure new staff appears
  Future<void> _refreshStaffDataWithRetry() async {
    if (!mounted) return;

    final calendarProvider = context.read<CalendarProvider>();
    const maxAttempts = 3;
    const delayBetweenAttempts = Duration(milliseconds: 800);

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      DebugLogger.logVerbose('🔄 CalendarSettingsDrawer: Refreshing staff data (attempt $attempt/$maxAttempts)');

      // Get staff count before refresh
      final staffCountBefore = calendarProvider.availableStaff.length;

      // Refresh staff data
      await calendarProvider.refreshStaffData();

      // Check if new staff appeared
      final staffCountAfter = calendarProvider.availableStaff.length;

      if (staffCountAfter > staffCountBefore) {
        DebugLogger.logVerbose('✅ CalendarSettingsDrawer: New staff detected ($staffCountBefore -> $staffCountAfter)');

        // Auto-select the new staff member if it's the first one or if user preference
        await _autoSelectNewStaff(calendarProvider, staffCountBefore);
        break;
      }

      if (attempt < maxAttempts) {
        DebugLogger.logVerbose('⏳ CalendarSettingsDrawer: No new staff detected, waiting before retry...');
        await Future.delayed(delayBetweenAttempts);
      } else {
        DebugLogger.logVerbose('⚠️ CalendarSettingsDrawer: Staff refresh completed but no new staff detected after $maxAttempts attempts');
      }
    }
  }

  /// Auto-select newly added staff member
  Future<void> _autoSelectNewStaff(CalendarProvider calendarProvider, int previousStaffCount) async {
    try {
      final currentStaff = calendarProvider.availableStaff;

      if (currentStaff.length > previousStaffCount) {
        // Get the newly added staff (assuming it's the last one in the list)
        final newStaff = currentStaff.last;

        DebugLogger.logVerbose('🎯 CalendarSettingsDrawer: Auto-selecting new staff: ${newStaff.displayName} (${newStaff.id})');

        // Select the new staff member
        calendarProvider.selectStaff(newStaff.id);

        // Initialize working hours for the new staff member
        await _initializeNewStaffWorkingHours(newStaff.id, calendarProvider);

        // Force calendar refresh to ensure new staff appears in calendar view
        await calendarProvider.forceCalendarRefresh(reason: 'New staff added and selected');

        DebugLogger.logVerbose('✅ CalendarSettingsDrawer: New staff auto-selected and initialized');
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ CalendarSettingsDrawer: Error auto-selecting new staff: $e');
    }
  }

  /// Initialize working hours for newly added staff
  Future<void> _initializeNewStaffWorkingHours(String staffId, CalendarProvider calendarProvider) async {
    try {
      DebugLogger.logVerbose('🔧 CalendarSettingsDrawer: Initializing working hours for new staff: $staffId');

      // Force refresh staff working hours cache for the new staff member
      await calendarProvider.refreshStaffWorkingHours(reason: 'New staff added from calendar drawer');

      DebugLogger.logVerbose('✅ CalendarSettingsDrawer: Working hours initialized for new staff');
    } catch (e) {
      DebugLogger.logVerbose('❌ CalendarSettingsDrawer: Error initializing working hours for new staff: $e');
    }
  }



  Widget _buildCreateStaffButton() {
    return Consumer<SubscriptionProvider>(
      builder: (context, subscriptionProvider, child) {
        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _handleAddStaffWithUpgradeCheck(subscriptionProvider),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    Theme.of(context).colorScheme.primary.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                  width: 1.5,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.person_add_outlined,
                      size: 16,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      context.tr('calendar.add_new_member'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                        letterSpacing: -0.2,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.6),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }



  Widget _buildDisplayOptionsSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildModernCheckbox(
              value: provider.showCanceledAppointments,
              onChanged: (value) {
                provider.setShowCanceledAppointments(value ?? false);
              },
              title: context.tr('calendar.show_cancelled'),
              icon: Icons.event_busy_outlined,
            ),
            const SizedBox(height: 12),
            _buildModernCheckbox(
              value: provider.showRevenueSummary,
              onChanged: (value) {
                provider.toggleRevenueSummaryVisibility(value ?? false);
              },
              title: context.tr('calendar.show_revenue_summary'),
              icon: Icons.attach_money_outlined,
            ),
          ],
        );
      },
    );
  }

  Widget _buildModernCheckbox({
    required bool value,
    required ValueChanged<bool?> onChanged,
    required String title,
    IconData? icon,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: value
            ? Theme.of(context).colorScheme.primary.withOpacity(0.08)
            : Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: value
              ? Theme.of(context).colorScheme.primary.withOpacity(0.3)
              : Theme.of(context).colorScheme.outline.withOpacity(0.15),
          width: value ? 1.5 : 1,
        ),
      ),
      child: CheckboxListTile(
        value: value,
        onChanged: onChanged,
        title: Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 18,
                color: value
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 10),
            ],
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
        activeColor: Theme.of(context).colorScheme.primary,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        controlAffinity: ListTileControlAffinity.leading,
      ),
    );
  }

  Widget _buildZoomSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Zoom controls
            _buildSliderControl(
              label: context.tr('calendar.slot_height'),
              value: provider.timeSlotHeight,
              min: provider.minTimeSlotHeight,
              max: provider.maxTimeSlotHeight,
              divisions: 8,
              icon: Icons.height,
              onChanged: provider.setTimeSlotHeight,
              onDecrease: provider.timeSlotHeight > provider.minTimeSlotHeight ? provider.zoomOut : null,
              onIncrease: provider.timeSlotHeight < provider.maxTimeSlotHeight ? provider.zoomIn : null,
              onReset: provider.resetZoom,
            ),

            const SizedBox(height: 20),
            Divider(color: Theme.of(context).colorScheme.outline.withOpacity(0.2)),
            const SizedBox(height: 20),

            // Opacity control
            Text(
              context.tr('calendar.unavailable_slot_opacity'),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            _buildHintText(context.tr('calendar.unavailable_slot_opacity_desc')),
            const SizedBox(height: 16),

            _buildSliderControl(
              label: 'Opacitate',
              value: provider.unavailableSlotOpacity,
              min: provider.minUnavailableSlotOpacity,
              max: provider.maxUnavailableSlotOpacity,
              divisions: 16,
              icon: Icons.opacity,
              onChanged: provider.setUnavailableSlotOpacity,
              formatLabel: (v) => '${(v * 100).round()}%',
            ),

            const SizedBox(height: 20),
            Divider(color: Theme.of(context).colorScheme.outline.withOpacity(0.2)),
            const SizedBox(height: 20),

            // Color selector
            Text(
              context.tr('calendar.unavailable_slot_color'),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            _buildHintText(context.tr('calendar.unavailable_slot_color_desc')),
            const SizedBox(height: 16),

            _buildColorSelector(provider),
          ],
        );
      },
    );
  }

  Widget _buildSliderControl({
    required String label,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required IconData icon,
    required ValueChanged<double> onChanged,
    VoidCallback? onDecrease,
    VoidCallback? onIncrease,
    VoidCallback? onReset,
    String Function(double)? formatLabel,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: Theme.of(context).colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const Spacer(),
              Text(
                formatLabel != null ? formatLabel(value) : '${value.round()}px',
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              if (onDecrease != null)
                IconButton(
                  onPressed: onDecrease,
                  icon: const Icon(Icons.remove_circle_outline),
                  iconSize: 20,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  color: Theme.of(context).colorScheme.primary,
                ),
              Expanded(
                child: SliderTheme(
                  data: SliderThemeData(
                    trackHeight: 3,
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                    overlayShape: const RoundSliderOverlayShape(overlayRadius: 14),
                  ),
                  child: Slider(
                    value: value,
                    min: min,
                    max: max,
                    divisions: divisions,
                    onChanged: onChanged,
                  ),
                ),
              ),
              if (onIncrease != null)
                IconButton(
                  onPressed: onIncrease,
                  icon: const Icon(Icons.add_circle_outline),
                  iconSize: 20,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  color: Theme.of(context).colorScheme.primary,
                ),
            ],
          ),
          if (onReset != null) ...[
            const SizedBox(height: 4),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton.icon(
                onPressed: onReset,
                icon: const Icon(Icons.refresh, size: 14),
                label: Text(
                  context.tr('calendar.reset'),
                  style: const TextStyle(fontSize: 12),
                ),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildColorSelector(CalendarProvider provider) {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: UnavailableSlotColor.values.map((color) {
        final isSelected = provider.unavailableSlotColor == color;
        final colorValue = _getColorForSlotColor(color);

        return GestureDetector(
          onTap: () => provider.setUnavailableSlotColor(color),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: 70,
            height: 50,
            decoration: BoxDecoration(
              color: colorValue,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : colorValue.computeLuminance() > 0.5
                        ? Colors.black.withOpacity(0.1)
                        : Colors.white.withOpacity(0.2),
                width: isSelected ? 3 : 1.5,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                        blurRadius: 8,
                        spreadRadius: 0,
                      ),
                    ]
                  : null,
            ),
            child: Stack(
              children: [
                Center(
                  child: Text(
                    context.tr('calendar.colors.${color.englishName.toLowerCase()}'),
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: _getTextColorForBackground(colorValue),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                if (isSelected)
                  Positioned(
                    top: 4,
                    right: 4,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.check,
                        size: 12,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Color _getColorForSlotColor(UnavailableSlotColor color) {
    switch (color) {
      case UnavailableSlotColor.gray:
        return Colors.grey.shade400;
      case UnavailableSlotColor.red:
        return Colors.red.shade400;
      case UnavailableSlotColor.blue:
        return Colors.blue.shade400;
      case UnavailableSlotColor.purple:
        return Colors.purple.shade400;
      case UnavailableSlotColor.orange:
        return Colors.orange.shade400;
      case UnavailableSlotColor.brown:
        return Colors.brown.shade400;
    }
  }

  Color _getTextColorForBackground(Color backgroundColor) {
    // Use a simple luminance-based approach to determine text color
    final luminance = backgroundColor.computeLuminance();
    return luminance < 0.5 ? Colors.white : Colors.black;
  }
}

// Helper class for segmented control
class SegmentedOption<T> {
  final T value;
  final IconData icon;
  final String label;

  SegmentedOption({
    required this.value,
    required this.icon,
    required this.label,
  });
}
