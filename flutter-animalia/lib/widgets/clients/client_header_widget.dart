import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';

import '../../models/client.dart';
import '../../models/client_statistics.dart';
import '../../services/client/client_service.dart';
import '../../services/url_launcher_service.dart';
import '../../l10n/app_localizations.dart';
import 'client_summary_widget.dart';

class ClientHeaderWidget extends StatefulWidget {
  final Client client;

  const ClientHeaderWidget({
    super.key,
    required this.client,
  });

  @override
  State<ClientHeaderWidget> createState() => _ClientHeaderWidgetState();
}

class _ClientHeaderWidgetState extends State<ClientHeaderWidget> {
  ClientStatistics? _statistics;
  bool _isLoadingStats = true;
  DateTime? _firstAppointmentDate;
  DateTime? _lastAppointmentDate;

  // Statistics variables
  double _totalRevenue = 0.0;
  int _totalAppointments = 0;
  int _noShowAppointments = 0;
  int _cancelledAppointments = 0;

  @override
  void initState() {
    super.initState();
    _loadClientStats();
  }

  Future<void> _loadClientStats() async {
    try {
      final statsResponse = await ClientService.getClientStats(widget.client.id);
      if (statsResponse.success && statsResponse.data != null) {
        _statistics = ClientStatistics.fromJson(statsResponse.data!);

        // Populate statistics variables
        _totalRevenue = _statistics!.totalRevenue;
        _totalAppointments = _statistics!.totalAppointments;
        _noShowAppointments = _statistics!.noShowAppointments;
        _cancelledAppointments = _statistics!.cancelledAppointments;
      }

      // Load client appointments to find first and last appointment dates
      final appointmentsResponse = await ClientService.getClientAppointments(widget.client.id);
      if (appointmentsResponse.success && appointmentsResponse.data != null) {
        final appointments = appointmentsResponse.data!;

        // Filter completed appointments
        final completedAppointments = appointments
            .where((appointment) => appointment.isCompleted)
            .toList();

        if (completedAppointments.isNotEmpty) {
          // Sort by start time to get earliest and latest
          completedAppointments.sort((a, b) => a.startTime.compareTo(b.startTime));
          _firstAppointmentDate = completedAppointments.first.startTime;
          _lastAppointmentDate = completedAppointments.last.startTime;
        }
      }

      if (mounted) {
        setState(() {
          _isLoadingStats = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingStats = false;
        });
      }
    }
  }

  String _formatRevenue(double revenue) {
    return '${revenue.toStringAsFixed(0)} RON';
  }

  String _formatDate(DateTime date) {
    try {
      return DateFormat('MMM yyyy', 'ro_RO').format(date);
    } catch (e) {
      // Fallback to default format if Romanian locale is not available
      return DateFormat('MMM yyyy').format(date);
    }
  }

  String _formatRecentDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return AppLocalizations.of(context).translate('time.today');
    } else if (difference.inDays == 1) {
      return AppLocalizations.of(context).translate('time.yesterday');
    } else if (difference.inDays < 7) {
      return AppLocalizations.of(context).translate('time.days_ago').replaceAll('{count}', difference.inDays.toString());
    } else if (difference.inDays < 30) {
      return AppLocalizations.of(context).translate('time.weeks_ago').replaceAll('{count}', (difference.inDays / 7).floor().toString());
    } else {
      try {
        return DateFormat('dd MMM yyyy', 'ro_RO').format(date);
      } catch (e) {
        return DateFormat('dd MMM yyyy').format(date);
      }
    }
  }

  bool _isNewClient() {
    if (_firstAppointmentDate == null) return false;
    final now = DateTime.now();
    final difference = now.difference(_firstAppointmentDate!);
    return difference.inDays < 30;
  }

  String _getClientSinceText() {
    if (_firstAppointmentDate == null) return AppLocalizations.of(context).translate('clients.new_client');
    return AppLocalizations.of(context).translate('clients.client_since').replaceAll('{date}', _formatDate(_firstAppointmentDate!));
  }

  String _getLastVisitText() {
    if (_lastAppointmentDate == null) return AppLocalizations.of(context).translate('clients.no_visits');
    return AppLocalizations.of(context).translate('clients.last_visit').replaceAll('{date}', _formatRecentDate(_lastAppointmentDate!));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 4),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Profile section with enhanced design
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: isDark ? 0.1 : 0.15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Enhanced profile photo with new client badge
                Stack(
                  children: [
                    Container(
                      width: 64, // Increased from 48 for better visibility
                      height: 64,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: theme.colorScheme.surface,
                        boxShadow: [
                          BoxShadow(
                            color: theme.colorScheme.shadow.withValues(alpha: 0.15),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: CircleAvatar(
                        radius: 32, // Increased from 16
                        backgroundColor: theme.colorScheme.primary,
                        child: Text(
                          widget.client.name.isNotEmpty ? widget.client.name[0].toUpperCase() : '?',
                          style: TextStyle(
                            color: theme.colorScheme.onPrimary,
                            fontSize: 32, // Increased from 24
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                // Statistics info instead of phone
                Expanded(
                  child: _isLoadingStats
                    ? _buildLoadingStats(theme)
                    : _buildStatsDisplay(theme),
                ),
              ],
            ),
          ),

          const SizedBox(height: 8),
          statsRow(theme, false),
          const SizedBox(height: 16),
          // Enhanced action buttons
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  context,
                  icon: Icons.phone,
                  color: Colors.green,
                  onPressed: () => _makePhoneCall(context),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionButton(
                  context,
                  icon: Icons.message,
                  color: Colors.blue,
                  onPressed: () => _sendSMS(context),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionButton(
                  context,
                  icon: FontAwesomeIcons.whatsapp,
                  color: const Color(0xFF25D366),
                  onPressed: () => _openWhatsApp(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingStats(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            AppLocalizations.of(context).translate('common.loading'),
            style: TextStyle(
              fontSize: 12,
              color: theme.colorScheme.onSurfaceVariant,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsDisplay(ThemeData theme) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isVerySmallScreen = constraints.maxWidth < 250;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Revenue and client since row
            Flex(
              direction: isVerySmallScreen ? Axis.vertical : Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Revenue display
                Flexible(
                  flex: isVerySmallScreen ? 0 : 2,
                  child: Container(
                    width: isVerySmallScreen ? double.infinity : null,
                    padding: EdgeInsets.symmetric(
                      horizontal: constraints.maxWidth < 300 ? 6 : 8,
                      vertical: 4
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFD36135).withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Row(
                      mainAxisSize: isVerySmallScreen ? MainAxisSize.max : MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.account_balance_wallet,
                          color: const Color(0xFFD36135),
                          size: constraints.maxWidth < 300 ? 12 : 14,
                        ),
                        SizedBox(width: constraints.maxWidth < 300 ? 3 : 4),
                        Flexible(
                          child: Text(
                            '${AppLocalizations.of(context).translate('clients.total_revenue')}: ${_formatRevenue(_totalRevenue)}',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface,
                              fontSize: constraints.maxWidth < 300 ? 16 : 20,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  width: isVerySmallScreen ? 0 : (constraints.maxWidth < 300 ? 4 : 6),
                  height: isVerySmallScreen ? 4 : 0,
                ),
              ],
            ),
            SizedBox(height: constraints.maxWidth < 300 ? 4 : 6),
            // Last visit information
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: constraints.maxWidth < 300 ? 6 : 8,
                vertical: 4
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.schedule,
                    color: theme.colorScheme.onSurfaceVariant,
                    size: constraints.maxWidth < 300 ? 10 : 12,
                  ),
                  SizedBox(width: constraints.maxWidth < 300 ? 3 : 4),
                  Expanded(
                    child: Text(
                      _getLastVisitText(),
                      style: TextStyle(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontSize: constraints.maxWidth < 300 ? 12 : 16,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: constraints.maxWidth < 300 ? 4 : 6),
            // Descriptive statistics row
          ],
        );
      },
    );
  }

  Row statsRow(ThemeData theme, bool isSmallScreen) {
    return Row(
            children: [
              _buildDescriptiveStatChip(
                icon: Icons.event,
                label: AppLocalizations.of(context).translate('clients.total_appointments'),
                value: _totalAppointments.toString(),
                color: const Color(0xFF4CAF50),
                theme: theme,
                isSmallScreen: isSmallScreen,
              ),
              SizedBox(width: isSmallScreen ? 2 : 4),
              _buildDescriptiveStatChip(
                icon: Icons.cancel,
                label: AppLocalizations.of(context).translate('clients.cancelled_appointments'),
                value: _cancelledAppointments.toString(),
                color: const Color(0xFFFF9800),
                theme: theme,
                isSmallScreen: isSmallScreen,
              ),
            ],
          );
  }

  Widget _buildDescriptiveStatChip({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required ThemeData theme,
    required bool isSmallScreen,
  }) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isSmallScreen ? 3 : 6,
          vertical: isSmallScreen ? 3 : 4
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: isSmallScreen ? 10 : 12,
                ),
                SizedBox(width: isSmallScreen ? 2 : 3),
                Flexible(
                  child: Text(
                    value,
                    style: TextStyle(
                      color: theme.colorScheme.onSurface,
                      fontSize: isSmallScreen ? 16 : 20,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            Text(
              label,
              style: TextStyle(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontSize: isSmallScreen ? 12 : 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: theme.colorScheme.primary,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          splashColor: theme.colorScheme.onPrimary.withValues(alpha: 0.2),
          highlightColor: theme.colorScheme.onPrimary.withValues(alpha: 0.1),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.onPrimary.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(3),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.onPrimary.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 16,
                    color: theme.colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _makePhoneCall(BuildContext context) async {
    final success = await UrlLauncherService.makePhoneCall(widget.client.phone);
    if (!success) {
      UrlLauncherService.showLaunchError(context, 'aplicația de telefon');
    }
  }

  void _sendSMS(BuildContext context) async {
    final success = await UrlLauncherService.sendSMS(
      widget.client.phone,
      message: 'Bună ziua ${widget.client.name}, ',
    );
    if (!success) {
      UrlLauncherService.showLaunchError(context, 'aplicația de mesaje');
    }
  }

  void _openWhatsApp(BuildContext context) async {
    final success = await UrlLauncherService.openWhatsApp(
      widget.client.phone,
      message: 'Bună ziua ${widget.client.name}, ',
    );
    if (!success) {
      UrlLauncherService.showLaunchError(context, 'WhatsApp');
    }
  }
}
