import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/inactive_client.dart';
import '../../providers/client_provider.dart';
import '../../utils/date_formatter.dart';
import '../common/custom_bottom_sheet.dart';

/// Widget for displaying a list of inactive clients
class InactiveClientsList extends StatefulWidget {
  final Function(String)? onClientTap;
  final int daysSinceLastAppointment;

  const InactiveClientsList({
    super.key,
    this.onClientTap,
    this.daysSinceLastAppointment = 30,
  });

  @override
  State<InactiveClientsList> createState() => _InactiveClientsListState();
}

class _InactiveClientsListState extends State<InactiveClientsList> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInactiveClients();
    });
  }

  Future<void> _loadInactiveClients() async {
    final clientProvider = context.read<ClientProvider>();
    await clientProvider.loadInactiveClients(
      daysSinceLastAppointment: widget.daysSinceLastAppointment,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ClientProvider>(
      builder: (context, clientProvider, child) {
        if (clientProvider.isLoadingInactiveClients) {
          return LoadingWidget(message: context.tr('clients.loading_inactive_clients'));
        }

        if (clientProvider.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: AppDimensions.spacingStandard),
                Text(
                  context.tr('clients.error_loading_inactive_clients'),
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: AppDimensions.spacingSmall),
                Text(
                  clientProvider.error ?? 'Eroare necunoscută',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppDimensions.spacingStandard),
                ElevatedButton(
                  onPressed: _loadInactiveClients,
                  child: Text(context.tr('common.try_again')),
                ),
              ],
            ),
          );
        }

        if (!clientProvider.hasInactiveClients) {
          return EmptyStateWidget(
            icon: Icons.celebration,
            title: context.tr('clients.no_inactive_clients_title'),
            subtitle: context.tr('clients.no_inactive_clients_message', params: {'days': widget.daysSinceLastAppointment.toString()}),
            action: ElevatedButton(
              onPressed: _loadInactiveClients,
              child: Text(context.tr('clients.refresh_inactive_clients')),
            ),
          );
        }

        final inactiveClients = clientProvider.filteredInactiveClients;

        return RefreshIndicator(
          onRefresh: _loadInactiveClients,
          child: Column(
            children: [
              // Summary header
              _buildSummaryHeader(clientProvider),
              
              // Client list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(AppDimensions.spacingStandard),
                  itemCount: inactiveClients.length,
                  itemBuilder: (context, index) {
                    final inactiveClient = inactiveClients[index];
                    return _buildInactiveClientCard(inactiveClient);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryHeader(ClientProvider clientProvider) {
    final priorityCounts = clientProvider.getInactiveClientsByPriority();
    final totalCount = clientProvider.inactiveClients.length;

    return Container(
      margin: const EdgeInsets.all(AppDimensions.spacingStandard),
      padding: const EdgeInsets.all(AppDimensions.spacingStandard),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.schedule,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.spacingSmall),
              Text(
                'Clienți inactivi (${widget.daysSinceLastAppointment}+ zile)',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingSmall),
          Text(
            'Total: $totalCount clienți',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          if (priorityCounts.isNotEmpty) ...[
            const SizedBox(height: AppDimensions.spacingSmall),
            Wrap(
              spacing: AppDimensions.spacingSmall,
              children: priorityCounts.entries.map((entry) {
                final priority = entry.key;
                final count = entry.value;
                return _buildPriorityChip(priority, count);
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPriorityChip(int priority, int count) {
    Color color;
    String label;
    
    switch (priority) {
      case 5:
        color = Colors.red;
        label = 'Critic';
        break;
      case 4:
        color = Colors.orange;
        label = 'Urgent';
        break;
      case 3:
        color = Colors.yellow.shade700;
        label = 'Important';
        break;
      case 2:
        color = Colors.blue;
        label = 'Moderat';
        break;
      case 1:
      default:
        color = Colors.grey;
        label = 'Scăzut';
        break;
    }

    return Chip(
      label: Text(
        '$label: $count',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: color,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildInactiveClientCard(InactiveClient inactiveClient) {
    final client = inactiveClient.client;
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingStandard),
      child: InkWell(
        onTap: widget.onClientTap != null
            ? () => widget.onClientTap!(client.id)
            : null,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.spacingStandard),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with name and priority
              Row(
                children: [
                  Expanded(
                    child: Text(
                      client.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildPriorityBadge(inactiveClient),
                ],
              ),
              
              const SizedBox(height: AppDimensions.spacingSmall),

              // Contact info
              if (client.phone.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      Icons.phone,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: AppDimensions.spacingSmall),
                    Text(
                      client.phone,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacingTiny),
              ],

              // Pet names with breed info
              if (client.pets.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      Icons.pets,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: AppDimensions.spacingSmall),
                    Expanded(
                      child: Text(
                        '${client.pets.first.name} (${client.pets.first.species != null ? _getSpeciesDisplayName(client.pets.first.species!) : 'necunoscut'})',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacingTiny),
              ] else if (client.petNames.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      Icons.pets,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: AppDimensions.spacingSmall),
                    Expanded(
                      child: Text(
                        client.petNames.join(', '),
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacingTiny),
              ],

              // Inactivity info
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: Colors.orange,
                  ),
                  const SizedBox(width: AppDimensions.spacingSmall),
                  Expanded(
                    child: Text(
                      inactiveClient.inactivityDescription,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              
              // Last appointment date if available
              if (inactiveClient.lastAppointmentDate != null) ...[
                const SizedBox(height: AppDimensions.spacingTiny),
                Row(
                  children: [
                    Icon(
                      Icons.event,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: AppDimensions.spacingSmall),
                    Text(
                      'Ultima programare: ${DateFormatter.formatDate(inactiveClient.lastAppointmentDate!)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityBadge(InactiveClient inactiveClient) {
    Color color;
    
    switch (inactiveClient.priorityLevel) {
      case 5:
        color = Colors.red;
        break;
      case 4:
        color = Colors.orange;
        break;
      case 3:
        color = Colors.yellow.shade700;
        break;
      case 2:
        color = Colors.blue;
        break;
      case 1:
      default:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingSmall,
        vertical: AppDimensions.spacingTiny,
      ),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusSmall),
      ),
      child: Text(
        inactiveClient.priorityLabel,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _getSpeciesDisplayName(String species) {
    switch (species.toLowerCase()) {
      case 'dog':
        return 'câine';
      case 'cat':
        return 'pisică';
      case 'other':
        return 'altele';
      default:
        return species;
    }
  }
}
