import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';
import '../../models/salon_subscription.dart';
import '../../providers/subscription_provider.dart';
import '../../screens/subscription/subscription_purchase_screen.dart';
import '../../services/subscription_limit_service.dart';
import 'usage_indicator.dart';

/// Widget that displays subscription usage dashboard
class UsageDashboard extends StatefulWidget {
  final String salonId;
  final bool showUpgradePrompts;
  final bool compact;

  const UsageDashboard({
    super.key,
    required this.salonId,
    this.showUpgradePrompts = true,
    this.compact = false,
  });

  @override
  State<UsageDashboard> createState() => _UsageDashboardState();
}

class _UsageDashboardState extends State<UsageDashboard> {
  SubscriptionLimits? _limits;
  bool _isLoading = true;
  String? _error;
  SubscriptionProvider? _subscriptionProvider;
  bool _isDisposing = false;

  @override
  void initState() {
    super.initState();
    _loadLimits();

    // Listen to subscription changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _subscriptionProvider = context.read<SubscriptionProvider>();
        _subscriptionProvider?.addListener(_onSubscriptionChanged);
      }
    });
  }

  @override
  void dispose() {
    // Set disposing flag to prevent any further setState calls
    _isDisposing = true;

    // Remove subscription listener safely
    _subscriptionProvider?.removeListener(_onSubscriptionChanged);
    super.dispose();
  }

  void _onSubscriptionChanged() {
    // Reload limits when subscription changes
    if (mounted && !_isDisposing) {
      // Use a post-frame callback to ensure the widget is still mounted
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_isDisposing) {
          _loadLimits();
        }
      });
    }
  }

  /// Public method to refresh limits (can be called from parent widgets)
  Future<void> refresh() async {
    if (mounted && !_isDisposing) {
      await _loadLimits();
    }
  }

  Future<void> _loadLimits() async {
    if (!mounted || _isDisposing) return;

    // Double-check mounted state and disposing flag right before setState
    if (!mounted || _isDisposing) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Clear cache to ensure we get fresh data
      SubscriptionLimitService.clearCache(widget.salonId);

      final limits = await SubscriptionLimitService.getLimits(widget.salonId);
      if (mounted) {
        setState(() {
          _limits = limits;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.compact) {
      return _buildCompactDashboard();
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactDashboard() {
    if (_isLoading || _limits == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          CompactUsageIndicator(
            label: context.tr('usage_dashboard.staff_label'),
            current: _limits!.currentStaffCount,
            max: _limits!.maxStaff,
            icon: Icons.group,
          ),
          const SizedBox(width: 8),
          CompactUsageIndicator(
            label: context.tr('usage_dashboard.pets_label'),
            current: _limits!.currentClientCount,
            max: _limits!.maxClients,
            icon: Icons.pets,
          ),
          const SizedBox(width: 8),
          CompactUsageIndicator(
            label: 'SMS',
            current: _limits!.currentSmsUsage,
            max: _limits!.smsQuota,
            icon: Icons.sms,
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.dashboard,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          'Utilizare abonament',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: _loadLimits,
          icon: Icon(
            Icons.refresh,
            size: 20,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          tooltip: 'Actualizează',
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null || _limits == null) {
      return _buildErrorState();
    }

    return Column(
      children: [
        // Staff usage
        UsageIndicator(
          label: 'Angajați',
          current: _limits!.currentStaffCount,
          max: _limits!.maxStaff,
          percentage: _limits!.getStaffUsagePercentage(),
          usageText: _limits!.getStaffUsageText(),
          icon: Icons.group,
        ),
        const SizedBox(height: 12),
        
        // Client usage
        UsageIndicator(
          label: 'Animale',
          current: _limits!.currentClientCount,
          max: _limits!.maxClients,
          percentage: _limits!.getClientUsagePercentage(),
          usageText: _limits!.getClientUsageText(),
          icon: Icons.pets,
        ),
        const SizedBox(height: 12),
        
        // SMS usage
        UsageIndicator(
          label: 'SMS (luna aceasta)',
          current: _limits!.currentSmsUsage,
          max: _limits!.smsQuota,
          percentage: _limits!.getSmsUsagePercentage(),
          usageText: _limits!.getSmsUsageText(),
          icon: Icons.sms,
        ),
        
        if (widget.showUpgradePrompts && _shouldShowUpgradePrompt()) ...[
          const SizedBox(height: 16),
          _buildUpgradePrompt(),
        ],
      ],
    );
  }

  Widget _buildErrorState() {
    return Column(
      children: [
        Icon(
          Icons.error_outline,
          color: Theme.of(context).colorScheme.error,
          size: 48,
        ),
        const SizedBox(height: 8),
        Text(
          'Eroare la încărcarea datelor',
          style: TextStyle(
            color: Theme.of(context).colorScheme.error,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          _error ?? 'Eroare necunoscută',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        ElevatedButton.icon(
          onPressed: _loadLimits,
          icon: const Icon(Icons.refresh),
          label: const Text('Reîncarcă'),
        ),
      ],
    );
  }

  bool _shouldShowUpgradePrompt() {
    if (_limits == null) return false;
    
    // Show upgrade prompt if any usage is above 80% or at limit
    return _limits!.getStaffUsagePercentage() > 80 ||
           _limits!.getClientUsagePercentage() > 80 ||
           _limits!.getSmsUsagePercentage() > 80 ||
           !_limits!.canAddStaff() ||
           !_limits!.canAddClients() ||
           !_limits!.canSendSms();
  }

  Widget _buildUpgradePrompt() {
    String message = '';
    SubscriptionTier suggestedTier = SubscriptionTier.team;
    
    if (!_limits!.canAddStaff() || _limits!.isStaffLimitApproaching()) {
      message = 'Vă apropiați de limita de angajați. ';
      suggestedTier = SubscriptionTier.team;
    } else if (!_limits!.canAddClients() || _limits!.isClientLimitApproaching()) {
      message = 'Vă apropiați de limita de animale. ';
      suggestedTier = SubscriptionTier.team;
    } else if (!_limits!.canSendSms() || _limits!.isSmsLimitApproaching()) {
      message = 'Vă apropiați de limita de SMS. ';
      suggestedTier = SubscriptionTier.team;
    }
    
    message += 'Considerați un upgrade pentru acces nelimitat?';

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primaryContainer,
            Theme.of(context).colorScheme.primaryContainer.withOpacity(0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.trending_up,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Upgrade recomandat',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  // Dismiss the prompt
                  if (mounted) {
                    setState(() {
                      // Could implement a way to temporarily hide the prompt
                    });
                  }
                },
                child: Text(
                  'Mai târziu',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.7),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                onPressed: () => _navigateToUpgrade(),
                icon: const Icon(Icons.upgrade, size: 16),
                label: const Text('Upgrade'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _navigateToUpgrade() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SubscriptionPurchaseScreen(
          salonId: widget.salonId,
        ),
      ),
    );
  }
}

/// Simple usage summary widget for headers/toolbars
class UsageSummary extends StatefulWidget {
  final String salonId;

  const UsageSummary({
    super.key,
    required this.salonId,
  });

  @override
  State<UsageSummary> createState() => _UsageSummaryState();
}

class _UsageSummaryState extends State<UsageSummary> {
  SubscriptionLimits? _limits;

  @override
  void initState() {
    super.initState();
    _loadLimits();
  }

  Future<void> _loadLimits() async {
    try {
      final limits = await SubscriptionLimitService.getLimits(widget.salonId);
      if (mounted) {
        setState(() {
          _limits = limits;
        });
      }
    } catch (e) {
      // Silently fail for summary widget
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_limits == null) {
      return const SizedBox.shrink();
    }

    final hasWarnings = _limits!.isStaffLimitApproaching() ||
                       _limits!.isClientLimitApproaching() ||
                       _limits!.isSmsLimitApproaching() ||
                       !_limits!.canAddStaff() ||
                       !_limits!.canAddClients() ||
                       !_limits!.canSendSms();

    if (!hasWarnings) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.warning_amber,
            size: 14,
            color: Colors.orange,
          ),
          const SizedBox(width: 4),
          Text(
            'Limite aproape atinse',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.orange.shade800,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
