import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../config/theme/app_theme.dart';
import '../../models/report_data.dart';
import '../../l10n/app_localizations.dart';

/// Common widget for displaying charts in reports
class ReportChartWidget extends StatelessWidget {
  final ReportData data;
  final bool showLegend;
  final double height;

  const ReportChartWidget({
    super.key,
    required this.data,
    this.showLegend = true,
    this.height = 300,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: double.infinity,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark 
              ? Colors.grey[850] 
              : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              data.title,
              style:  TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SizedBox(
                width: double.infinity,
                child: _buildChart(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChart(BuildContext context) {
    if (data is BarChartReportData) {
      return _buildBarChart(data as BarChartReportData, context);
    } else if (data is LineChartReportData) {
      return _buildLineChart(data as LineChartReportData, context);
    } else {
      return Center(
        child: Text(context.tr('chart_widget.unsupported_chart_type')),
      );
    }
  }

  Widget _buildBarChart(BarChartReportData chartData, BuildContext context) {
    // Safety check for empty data
    if (chartData.items.isEmpty) {
      return Center(
        child: Text(
          context.tr('chart_widget.no_data_to_display'),
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    final maxY = chartData.items.map((e) => e.value).reduce((a, b) => a > b ? a : b);

    // Safety check for zero or negative values
    if (maxY <= 0) {
      return Center(
        child: Text(
          context.tr('chart_widget.no_valid_values'),
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceEvenly, // Better spacing for readability
        maxY: maxY * 1.2, // Add some padding at the top
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (group) => Colors.grey[800]!,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final item = chartData.items[group.x.toInt()];
              return BarTooltipItem(
                '${item.label}\n${item.value.toStringAsFixed(1)}',
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < chartData.items.length) {
                  final label = chartData.items[value.toInt()].label;
                  final shortLabel = _getShortLabel(label);

                  return Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Transform.rotate(
                      angle: -0.5, // Rotate text for better readability
                      child: Text(
                        shortLabel,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  );
                }
                return const Text('');
              },
              reservedSize: 60, // Increased space for rotated text
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: chartData.items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: item.value,
                color: item.color ?? chartData.primaryColor,
                width: 16, // Slightly narrower bars for better spacing
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          );
        }).toList(),
        gridData: FlGridData(
          show: true,
          drawHorizontalLine: true,
          drawVerticalLine: false,
          horizontalInterval: maxY / 5,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey[300]!,
              strokeWidth: 1,
            );
          },
        ),
      ),
    );
  }

  Widget _buildLineChart(LineChartReportData chartData, BuildContext context) {
    // Safety check for empty data
    if (chartData.points.isEmpty) {
      return Center(
        child: Text(
          context.tr('chart_widget.no_data_to_display'),
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    final spots = chartData.points.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.value);
    }).toList();

    final maxY = chartData.points.map((e) => e.value).reduce((a, b) => a > b ? a : b);
    final minY = chartData.points.map((e) => e.value).reduce((a, b) => a < b ? a : b);

    // Safety check for invalid range
    if (maxY == minY) {
      return Center(
        child: Text(
          context.tr('chart_widget.insufficient_data_variation'),
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawHorizontalLine: true,
          drawVerticalLine: true,
          horizontalInterval: (maxY - minY) / 5,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey[300]!,
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey[300]!,
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              interval: (chartData.points.length / 5).ceilToDouble(),
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < chartData.points.length) {
                  final date = chartData.points[index].date;
                  return Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      DateFormat('dd/MM').format(date),
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 50,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey[300]!),
        ),
        minX: 0,
        maxX: (chartData.points.length - 1).toDouble(),
        minY: minY * 0.9,
        maxY: maxY * 1.1,
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: chartData.lineColor,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: chartData.lineColor,
                  strokeWidth: 2,
                  strokeColor: Colors.white,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              color: chartData.lineColor.withOpacity(0.1),
            ),
          ),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (touchedSpot) => Colors.grey[800]!,
            getTooltipItems: (touchedSpots) {
              return touchedSpots.map((touchedSpot) {
                final index = touchedSpot.x.toInt();
                if (index >= 0 && index < chartData.points.length) {
                  final point = chartData.points[index];
                  return LineTooltipItem(
                    '${DateFormat('dd/MM/yyyy').format(point.date)}\n${point.value.toStringAsFixed(1)}',
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }
                return null;
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  /// Helper method to create short, readable labels for charts
  String _getShortLabel(String label) {
    // Common service abbreviations for Romanian pet grooming
    final Map<String, String> serviceAbbreviations = {
      'Tuns și aranjat': 'Tuns',
      'Baie completă': 'Baie',
      'Toaletare completă': 'Toaletare',
      'Tăiat unghii': 'Unghii',
      'Curățat urechi': 'Urechi',
      'Spălat dinți': 'Dinți',
      'Tratament anti-purici': 'Anti-purici',
      'Golden Retriever': 'Golden',
      'German Shepherd': 'German Sh.',
      'Bichon Frise': 'Bichon',
      'Pisică Persană': 'P. Persană',
      'Pisică Europeană': 'P. Europeană',
    };

    // Check if we have a specific abbreviation
    if (serviceAbbreviations.containsKey(label)) {
      return serviceAbbreviations[label]!;
    }

    // For other labels, apply smart truncation
    if (label.length <= 8) {
      return label;
    }

    // Try to break at word boundaries
    final words = label.split(' ');
    if (words.length > 1) {
      // Take first word and first letter of subsequent words
      String result = words[0];
      for (int i = 1; i < words.length && result.length < 8; i++) {
        if (result.length + words[i].length + 1 <= 8) {
          result += ' ${words[i]}';
        } else {
          result += ' ${words[i][0]}.';
        }
      }
      return result;
    }

    // Fallback: simple truncation with ellipsis
    return '${label.substring(0, 6)}..';
  }
}
