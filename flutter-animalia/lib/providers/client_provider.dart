import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../models/api_response.dart';
import '../models/client.dart';
import '../models/inactive_client.dart';
import '../services/auth/auth_service.dart';
import '../services/client/client_service.dart';
import '../utils/debug_logger.dart';
import 'base_provider.dart';

/// Provider for managing client data with salon-specific isolation
class ClientProvider extends BaseProvider {
  List<Client> _clients = [];
  List<Client> _filteredClients = [];
  List<InactiveClient> _inactiveClients = [];
  List<InactiveClient> _filteredInactiveClients = [];
  String _searchQuery = '';
  String? _currentSalonId;
  bool _isLoadingInactiveClients = false;

  // Getters
  List<Client> get clients => _clients;
  List<Client> get filteredClients => _filteredClients;
  List<InactiveClient> get inactiveClients => _inactiveClients;
  List<InactiveClient> get filteredInactiveClients => _filteredInactiveClients;
  String get searchQuery => _searchQuery;
  String? get currentSalonId => _currentSalonId;
  bool get hasClients => _clients.isNotEmpty;
  bool get hasInactiveClients => _inactiveClients.isNotEmpty;
  bool get isLoadingInactiveClients => _isLoadingInactiveClients;

  @override
  Future<void> initialize() async {
    await executeVoidAsync(
      () async {
        await _loadCurrentSalonId();
        await _loadClients();
        setInitialized(true);
      },
      errorMessage: 'Failed to initialize client provider',
    );
  }

  @override
  Future<void> refresh() async {
    await executeVoidAsync(
      () async {
        await _loadCurrentSalonId();
        await _loadClients();
        DebugLogger.logVerbose('✅ ClientProvider: Refresh completed');
      },
      errorMessage: 'Failed to refresh client data',
    );
  }

  @override
  void clear() {
    DebugLogger.logVerbose('🧹 ClientProvider: Clearing all client data...');
    _clients.clear();
    _filteredClients.clear();
    _inactiveClients.clear();
    _filteredInactiveClients.clear();
    _searchQuery = '';
    _currentSalonId = null;
    _isLoadingInactiveClients = false;
    super.clear();
    DebugLogger.logVerbose('✅ ClientProvider: All client data cleared');
  }

  /// Clear client data for salon switching (with comprehensive logging)
  Future<void> clearForSalonSwitch(String newSalonId) async {

    // Clear all cached data
    final previousClientCount = _clients.length;
    _clients.clear();
    _filteredClients.clear();
    _searchQuery = '';
    _currentSalonId = newSalonId;

    // Notify listeners immediately after clearing data
    notifyListeners();


    // Load fresh data for new salon
    await _loadClients();

  }

  /// Load current salon ID
  Future<void> _loadCurrentSalonId() async {
    _currentSalonId = await AuthService.getCurrentSalonId();
  }

  /// Load clients for current salon
  Future<void> _loadClients() async {
    if (_currentSalonId == null) {
      return;
    }


    final response = await ClientService.getClients();

    if (response.success && response.data != null) {
      _clients = response.data!;
      _filteredClients = response.data!;
    } else {
      _clients = [];
      _filteredClients = [];
      final errorMsg = response.error ?? 'Unknown error loading clients';
      setError('Failed to load clients: $errorMsg');
    }

    // Notify listeners that the data has changed
    notifyListeners();
  }

  /// Search clients by query
  void searchClients(String query) {
    _searchQuery = query;
    
    if (query.isEmpty) {
      _filteredClients = List.from(_clients);
    } else {
      _filteredClients = _clients.where((client) {
        final searchLower = query.toLowerCase();
        final phoneNormalized = client.phone.replaceAll(RegExp(r'[\s\-\+]'), ''); // Elimină spații, - și +
        final queryNormalized = query.replaceAll(RegExp(r'[\s\-\+]'), ''); // Elimină spații, - și +
        final petNameMatch = client.petNames.any((petName) => petName.toLowerCase().contains(searchLower));
        final backendPetMatch =
            client.matchingPetNames.any((petName) => petName.toLowerCase().contains(searchLower));

        return client.name.toLowerCase().contains(searchLower) ||
               client.phone.toLowerCase().contains(searchLower) ||
               phoneNormalized.contains(queryNormalized) || // Caută în numărul normalizat
               client.email.toLowerCase().contains(searchLower) ||
               petNameMatch ||
               backendPetMatch;
      }).toList();
    }
    
    notifyListeners();
  }

  /// Add a new client
  Future<bool> addClient(Client client) async {

    final response = await ClientService.createClient(client);
    
    if (response.success && response.data != null) {
      _clients.add(response.data!);
      searchClients(_searchQuery); // Refresh filtered list
      return true;
    } else {
      final errorMsg = response.error ?? 'Failed to add client';
      setError(errorMsg);
      return false;
    }
  }

  /// Update an existing client
  Future<bool> updateClient(Client client) async {

    final response = await ClientService.updateClient(client.id, client);
    
    if (response.success && response.data != null) {
      final index = _clients.indexWhere((c) => c.id == client.id);
      if (index != -1) {
        _clients[index] = response.data!;
        searchClients(_searchQuery); // Refresh filtered list
        return true;
      }
    }
    
    final errorMsg = response.error ?? 'Failed to update client';
    setError(errorMsg);
    return false;
  }

  /// Delete a client
  Future<bool> deleteClient(String clientId) async {

    final response = await ClientService.deleteClient(clientId);

    if (response.success) {
      _clients.removeWhere((c) => c.id == clientId);
      searchClients(_searchQuery); // Refresh filtered list
      return true;
    } else {
      final errorMsg = response.error ?? 'Failed to delete client';
      setError(errorMsg);
      return false;
    }
  }

  /// Delete multiple clients
  Future<Map<String, dynamic>> deleteMultipleClients(List<String> clientIds) async {
    final response = await ClientService.deleteMultipleClients(clientIds);

    if (response.success && response.data != null) {
      final result = response.data!;
      final deletedCount = result['deletedCount'] as int? ?? 0;

      // Remove successfully deleted clients from local list
      if (deletedCount > 0) {
        _clients.removeWhere((c) => clientIds.contains(c.id));
        searchClients(_searchQuery); // Refresh filtered list
      }

      return result;
    } else {
      final errorMsg = response.error ?? 'Failed to delete clients';
      setError(errorMsg);
      return {
        'deletedCount': 0,
        'failedCount': clientIds.length,
        'errors': [errorMsg],
      };
    }
  }

  /// Add multiple clients in batch
  Future<ApiResponse<List<Client>>> addMultipleClients(List<Client> clients) async {
    final response = await ClientService.createMultipleClients(clients);
    
    if (response.success && response.data != null) {
      _clients.addAll(response.data!);
      searchClients(_searchQuery); // Refresh filtered list
    }
    
    return response;
  }

  /// Get client by ID
  Client? getClientById(String clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      return null;
    }
  }

  /// Load inactive clients (clients who haven't had appointments in 30+ days)
  Future<void> loadInactiveClients({int daysSinceLastAppointment = 30}) async {
    _isLoadingInactiveClients = true;
    notifyListeners();

    try {
      DebugLogger.logVerbose('🔄 ClientProvider: Loading inactive clients (${daysSinceLastAppointment}+ days)...');

      // For now, we'll calculate inactive clients locally since the backend endpoint might not exist yet
      await _calculateInactiveClientsLocally(daysSinceLastAppointment);

      DebugLogger.logVerbose('✅ ClientProvider: Loaded ${_inactiveClients.length} inactive clients');
    } catch (e) {
      DebugLogger.logError('❌ ClientProvider: Error loading inactive clients: $e');
      setError('Failed to load inactive clients: $e');
    } finally {
      _isLoadingInactiveClients = false;
      notifyListeners();
    }
  }

  /// Calculate inactive clients locally by checking each client's last appointment
  Future<void> _calculateInactiveClientsLocally(int daysSinceLastAppointment) async {
    final now = DateTime.now();
    final cutoffDate = now.subtract(Duration(days: daysSinceLastAppointment));
    final inactiveClients = <InactiveClient>[];

    for (final client in _clients) {
      try {
        // Get the client's last appointment date
        final lastAppointmentResponse = await ClientService.getClientLastAppointmentDate(client.id);

        DateTime? lastAppointmentDate;
        if (lastAppointmentResponse.success && lastAppointmentResponse.data != null) {
          lastAppointmentDate = lastAppointmentResponse.data;
        }

        // Check if client is inactive
        bool isInactive = false;
        if (lastAppointmentDate == null) {
          // Client has never had an appointment
          isInactive = true;
        } else if (lastAppointmentDate.isBefore(cutoffDate)) {
          // Client's last appointment was before the cutoff date
          isInactive = true;
        }

        if (isInactive) {
          final inactiveClient = InactiveClient.fromClient(
            client,
            lastAppointmentDate: lastAppointmentDate,
          );
          inactiveClients.add(inactiveClient);
        }
      } catch (e) {
        DebugLogger.logError('❌ Error checking client ${client.name}: $e');
        // Continue with next client
      }
    }

    // Sort by priority (highest first) and then by days since last appointment (most days first)
    inactiveClients.sort((a, b) {
      final priorityComparison = b.priorityLevel.compareTo(a.priorityLevel);
      if (priorityComparison != 0) return priorityComparison;
      return b.daysSinceLastAppointment.compareTo(a.daysSinceLastAppointment);
    });

    _inactiveClients = inactiveClients;
    _filteredInactiveClients = List.from(_inactiveClients);
  }

  /// Search inactive clients
  void searchInactiveClients(String query) {
    _searchQuery = query;

    if (query.isEmpty) {
      _filteredInactiveClients = List.from(_inactiveClients);
    } else {
      final lowerQuery = query.toLowerCase();
      _filteredInactiveClients = _inactiveClients.where((inactiveClient) {
        final client = inactiveClient.client;
        return client.name.toLowerCase().contains(lowerQuery) ||
               client.phone.toLowerCase().contains(lowerQuery) ||
               client.email.toLowerCase().contains(lowerQuery) ||
               client.petNames.any((petName) => petName.toLowerCase().contains(lowerQuery));
      }).toList();
    }

    notifyListeners();
  }

  /// Get inactive clients count by priority level
  Map<int, int> getInactiveClientsByPriority() {
    final priorityCounts = <int, int>{};

    for (final inactiveClient in _inactiveClients) {
      final priority = inactiveClient.priorityLevel;
      priorityCounts[priority] = (priorityCounts[priority] ?? 0) + 1;
    }

    return priorityCounts;
  }

  /// Clear inactive clients data
  void clearInactiveClients() {
    _inactiveClients.clear();
    _filteredInactiveClients.clear();
    _isLoadingInactiveClients = false;
    notifyListeners();
  }
}
