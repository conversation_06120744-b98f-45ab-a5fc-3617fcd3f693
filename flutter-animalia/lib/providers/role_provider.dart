import 'package:animaliaproject/services/salon_service.dart';
import 'package:animaliaproject/utils/debug_logger.dart';

import '../models/user_role.dart';
import '../models/user_salon_association.dart';
import '../services/auth/auth_service.dart';
import 'base_provider.dart';

/// Provider for managing user roles and permissions throughout the app
class RoleProvider extends BaseProvider {
  UserRole? _currentRole;
  GroomerPermissions? _permissions;
  List<String> _accessibleFeatures = [];

  // Getters
  UserRole? get currentRole => _currentRole;
  GroomerPermissions? get permissions => _permissions;
  List<String> get accessibleFeatures => _accessibleFeatures;

  // Role checks
  bool get isAdmin => _currentRole?.isAdmin ?? false;
  bool get canBeGroomer => _currentRole?.canBeGroomer ?? false;
  bool get isActiveGroomer => canBeGroomer && (_permissions?.isActive ?? false);
  bool get hasManagementAccess => isAdmin || (_permissions?.hasManagementAccess ?? false);

  // Salon association check
  bool get hasSalonAssociation => _permissions != null;

  // Fixed: Only allow client data access if user has valid permissions AND explicit access
  bool get canAccessClientData {
    if (isAdmin) return true;
    if (_permissions == null) return false; // No permissions = no access
    return _permissions!.canAccessClientData;
  }

  bool get canSeeFullClientDetails {
    if (isAdmin) return true;
    if (_permissions == null) return false; // No permissions = no access
    return _permissions!.canSeeFullClientDetails;
  }

  // Sales data access requires management access
  bool canViewSalesData() {
    return hasManagementAccess;
  }

  /// Initialize role and permissions
  @override
  Future<void> initialize() async {
    DebugLogger.logInit('🔄 RoleProvider.initialize() started');
    try {
      // Add timeout to prevent hanging
      await executeVoidAsync(
        () async {
          DebugLogger.logInit('🔄 RoleProvider: Loading role and permissions...');
          await _loadRoleAndPermissions().timeout(
            const Duration(seconds: 30),
            onTimeout: () {
              DebugLogger.logInit('⏰ RoleProvider: _loadRoleAndPermissions timed out');
              throw Exception('Role loading timed out');
            },
          );
          DebugLogger.logInit('🔄 RoleProvider: Loading accessible features...');
          await _loadAccessibleFeatures();
          DebugLogger.logInit('✅ RoleProvider: Setting initialized to true');
          setInitialized(true);
          DebugLogger.logInit('✅ RoleProvider.initialize() completed successfully');
          DebugLogger.logInit('🔍 RoleProvider: hasSalonAssociation = $hasSalonAssociation');
        },
        errorMessage: 'Failed to initialize roles and permissions',
      ).timeout(
        const Duration(seconds: 45),
        onTimeout: () {
          DebugLogger.logInit('⏰ RoleProvider: Full initialization timed out');
          // Set basic state to prevent infinite loading
          setInitialized(true);
          setError('Initialization timed out');
        },
      );
    } catch (e) {
      DebugLogger.logInit('❌ RoleProvider: Initialize failed: $e');
      // Ensure we're marked as initialized even on error to prevent infinite loading
      setInitialized(true);
      setError('Failed to initialize: $e');
    }
  }

  /// Load user role and permissions from storage/API
  Future<void> _loadRoleAndPermissions() async {
    _currentRole = await _getCurrentUserRole();
    _permissions = await _loadPermissionsFromSalonAssociation();
  }

  /// Load list of accessible features
  Future<void> _loadAccessibleFeatures() async {
    _accessibleFeatures = await _getAccessibleFeatures();
  }

  /// Refresh role and permissions from server
  @override
  Future<void> refresh() async {
    DebugLogger.logInit('🔄 RoleProvider.refresh() called');
    await executeVoidAsync(
      () async {
        DebugLogger.logInit('🔄 RoleProvider: Starting role and permissions reload...');
        await _loadRoleAndPermissions();
        DebugLogger.logInit('🔄 RoleProvider: Role and permissions loaded, hasSalonAssociation = $hasSalonAssociation');
        await _loadAccessibleFeatures();
        DebugLogger.logInit('🔄 RoleProvider.refresh() completed successfully');
        DebugLogger.logInit('🔍 RoleProvider: Final state - hasSalonAssociation = $hasSalonAssociation');
      },
      errorMessage: 'Failed to refresh roles and permissions',
    );
  }

  /// Clear all role data (call on logout)
  @override
  void clear() {
    _currentRole = null;
    _permissions = null;
    _accessibleFeatures = [];
    super.clear();
  }

  /// Check if user can access a specific feature
  bool canAccessFeature(String featureKey) {
    return _accessibleFeatures.contains(featureKey);
  }

  /// Permission check methods for UI
  bool canManageTeam() => canAccessFeature('team_management');
  bool canManageServices() => canAccessFeature('services_management');
  bool canViewReports() => canAccessFeature('reports');
  bool canManageSalonSettings() => canAccessFeature('salon_settings');
  bool canInviteGroomers() => hasManagementAccess;
  bool canViewAllAppointments() => canAccessFeature('calendar_all');
  bool canOnlyViewOwnAppointments() => canAccessFeature('calendar_own') && !canAccessFeature('calendar_all');
  bool canManageBusinessHours() => hasManagementAccess;
  bool canSendSmsReminders() => hasManagementAccess;
  // bool canViewSalesData() => canAccessFeature('sales');
  bool canManageReviews() => canAccessFeature('reviews');

  /// Get role display information (fallback - use localized versions in UI)
  String get roleDisplayName {
    if (isAdmin) {
      return 'Administrator';
    } else if (isActiveGroomer && _permissions != null) {
      return _permissions!.groomerRole.displayName;
    } else {
      return _currentRole?.displayName ?? 'Necunoscut';
    }
  }

  String get roleDescription {
    if (isAdmin) {
      return 'Acces complet la toate funcționalitățile';
    } else if (isActiveGroomer && _permissions != null) {
      return _permissions!.groomerRole.description;
    } else {
      return _currentRole?.description ?? '';
    }
  }

  /// Get permission display information (fallback - use localized versions in UI)
  String get clientDataPermissionName => _permissions?.clientDataPermission.displayName ?? 'Necunoscut';
  String get clientDataPermissionDescription => _permissions?.clientDataPermission.description ?? '';

  /// Check if user has pending invitations
  Future<bool> hasPendingInvitations() async {
    try {
      final userId = await AuthService.getCurrentUserId();
      if (userId == null) return false;

      // This would be implemented to check for pending salon invitations
      // For now, return false as placeholder
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Accept salon invitation
  Future<bool> acceptSalonInvitation(String invitationId) async {
    try {
      // Implementation would call API to accept invitation
      // and then refresh permissions
      await refresh();
      return true;
    } catch (e) {
      setError(e.toString());
      return false;
    }
  }

  /// Decline salon invitation
  Future<bool> declineSalonInvitation(String invitationId) async {
    try {
      // Implementation would call API to decline invitation
      return true;
    } catch (e) {
      setError(e.toString());
      return false;
    }
  }

  /// Clear error (inherited from BaseProvider)
  // clearError() method is already available from BaseProvider

  /// Get current user role from AuthService
  Future<UserRole?> _getCurrentUserRole() async {
    try {
      final userRole = await AuthService.getCurrentUserRole();
      if (userRole != null) {
        return UserRole.fromString(userRole);
      }
      return null;
    } catch (e) {
      DebugLogger.logInit('❌ Error getting user role: $e');
      return null;
    }
  }

  /// Load permissions directly from salon associations
  Future<GroomerPermissions?> _loadPermissionsFromSalonAssociation() async {
    try {
      DebugLogger.logInit('🔄 RoleProvider: Loading permissions from salon association...');
      final userId = await AuthService.getCurrentUserId();
      final salonId = await AuthService.getCurrentSalonId();

      DebugLogger.logInit('🔍 RoleProvider: userId = $userId, salonId = $salonId');

      if (userId == null) {
        DebugLogger.logInit('🔍 RoleProvider: Missing userId for permissions');
        return null;
      }

      // Get user's salon associations first with timeout
      DebugLogger.logInit('🔄 RoleProvider: Fetching user salons from API...');
      final salonResponse = await SalonService.getUserSalons().timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          DebugLogger.logInit('⏰ RoleProvider: getUserSalons API call timed out');
          throw Exception('API call timed out');
        },
      );

      if (!salonResponse.success || salonResponse.data == null) {
        DebugLogger.logInit('❌ RoleProvider: Failed to fetch user salons: ${salonResponse.error}');
        return null;
      }

      final userSalons = salonResponse.data!;

      // If user has no salons, return null
      if (userSalons.isEmpty) {
        DebugLogger.logInit('🔍 RoleProvider: User has no salon associations');
        if (salonId != null) {
          // Clear invalid salon ID
          DebugLogger.logInit('🔄 RoleProvider: Clearing invalid salon ID');
          await AuthService.updateCurrentSalonId(null);
        }
        return null;
      }

      // If no current salon ID is set, or current salon ID is invalid, switch to first available salon
      if (salonId == null || !userSalons.any((salon) => salon.salonId == salonId)) {
        final newSalonId = userSalons.first.salonId;
        DebugLogger.logInit('🔄 RoleProvider: Current salon ID invalid or missing, switching to: $newSalonId');
        await AuthService.updateCurrentSalonId(newSalonId);
        // Update local variable for the rest of this method
        final updatedSalonId = newSalonId;

        // Find the salon association for the new salon ID
        final salonAssociation = userSalons.firstWhere((salon) => salon.salonId == updatedSalonId);
        return _createPermissionsFromAssociation(userId, updatedSalonId, salonAssociation);
      }

      // Current salon ID is valid, find the association
      final salonAssociation = userSalons.firstWhere(
        (salon) => salon.salonId == salonId,
        orElse: () => throw Exception('Salon association not found'),
      );

      return _createPermissionsFromAssociation(userId, salonId, salonAssociation);
    } catch (e) {
      DebugLogger.logInit('❌ RoleProvider: Error loading permissions from salon association: $e');
      return null;
    }
  }

  /// Create GroomerPermissions from salon association
  GroomerPermissions _createPermissionsFromAssociation(
    String userId,
    String salonId,
    UserSalonAssociation salonAssociation,
  ) {
    DebugLogger.logInit('✅ RoleProvider: Found salon association for salon: $salonId');
    DebugLogger.logInit('🔍 RoleProvider: Role = ${salonAssociation.groomerRole}, Permission = ${salonAssociation.clientDataPermission}');

    final permissions = GroomerPermissions(
      userId: userId,
      salonId: salonId,
      groomerRole: salonAssociation.groomerRole,
      clientDataPermission: salonAssociation.clientDataPermission,
      isActive: true,
      updatedAt: DateTime.now(),
    );

    DebugLogger.logInit('✅ RoleProvider: Created permissions for salon: $salonId');
    return permissions;
  }

  /// Get list of features accessible to current user
  Future<List<String>> _getAccessibleFeatures() async {
    final List<String> features = [];

    // Debug logging for troubleshooting
    DebugLogger.logInit('🔍 RoleProvider._getAccessibleFeatures() called');
    DebugLogger.logInit('🔍 hasManagementAccess: $hasManagementAccess');
    DebugLogger.logInit('🔍 _permissions: $_permissions');
    DebugLogger.logInit('🔍 _permissions?.groomerRole: ${_permissions?.groomerRole}');
    DebugLogger.logInit('🔍 hasSalonAssociation: $hasSalonAssociation');

    // If user has no salon association, they get no features except basic profile access
    if (!hasSalonAssociation) {
      DebugLogger.logInit('🔍 No salon association - user needs to create or join a salon');
      return features; // Return empty features list
    }

    // Calendar access logic - ALL users with salon association should have calendar access
    // regardless of client data permission level
    if (hasManagementAccess || (_permissions?.groomerRole == GroomerRole.chiefGroomer)) {
      features.add('calendar_all');
      DebugLogger.logInit('🔍 Added calendar_all access');
    } else {
      features.add('calendar_own');
      DebugLogger.logInit('🔍 Added calendar_own access');
    }

    // Client data access - only add if user has appropriate permission
    if (canAccessClientData) {
      features.add('clients');
      DebugLogger.logInit('🔍 Added clients access');
    }

    if (canViewSalesData()) {
      features.add('sales');
      DebugLogger.logInit('🔍 Added sales access');
    }

    // Team management - check permissions directly
    if (hasManagementAccess) {
      features.add('team_management');
      DebugLogger.logInit('🔍 Added team_management access');
    }

    // Services management - check permissions directly
    if (hasManagementAccess) {
      features.add('services_management');
      DebugLogger.logInit('🔍 Added services_management access');
    }

    // Reports - check permissions directly
    if (hasManagementAccess) {
      features.add('reports');
      DebugLogger.logInit('🔍 Added reports access');
    }

    // Salon settings - check permissions directly
    if (hasManagementAccess) {
      features.add('salon_settings');
      DebugLogger.logInit('🔍 Added salon_settings access');
    }

    if (hasManagementAccess) {
      features.add('sms_reminders');
      DebugLogger.logInit('🔍 Added sms_reminders access');
    }

    // Reviews - check permissions directly
    if (hasManagementAccess) {
      features.add('reviews');
      DebugLogger.logInit('🔍 Added reviews access');
    }

    DebugLogger.logInit('🔍 Final accessible features: $features');
    return features;
  }
}
