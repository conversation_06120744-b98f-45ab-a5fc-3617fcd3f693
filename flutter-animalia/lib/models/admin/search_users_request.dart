/// Request model for searching users
class SearchUsersRequest {
  final String searchTerm;
  final bool? isActive;
  final int limit;
  final int offset;

  const SearchUsersRequest({
    required this.searchTerm,
    this.isActive,
    this.limit = 20,
    this.offset = 0,
  });

  /// Create SearchUsersRequest from JSON
  factory SearchUsersRequest.fromJson(Map<String, dynamic> json) {
    return SearchUsersRequest(
      searchTerm: json['searchTerm'] as String,
      isActive: json['isActive'] as bool?,
      limit: json['limit'] as int? ?? 20,
      offset: json['offset'] as int? ?? 0,
    );
  }

  /// Convert SearchUsersRequest to JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'searchTerm': searchTerm,
      'limit': limit,
      'offset': offset,
    };

    if (isActive != null) {
      json['isActive'] = isActive;
    }

    return json;
  }

  /// Create a copy with updated values
  SearchUsersRequest copyWith({
    String? searchTerm,
    bool? isActive,
    int? limit,
    int? offset,
  }) {
    return SearchUsersRequest(
      searchTerm: searchTerm ?? this.searchTerm,
      isActive: isActive ?? this.isActive,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
    );
  }

  /// Create a request for the next page
  SearchUsersRequest nextPage() {
    return copyWith(offset: offset + limit);
  }

  /// Reset to first page
  SearchUsersRequest resetToFirstPage() {
    return copyWith(offset: 0);
  }

  /// Validate the search request
  bool get isValid {
    return searchTerm.trim().length >= 2 && limit > 0 && offset >= 0;
  }

  /// Get validation error message
  String? get validationError {
    if (searchTerm.trim().length < 2) {
      return 'Termenul de căutare trebuie să aibă cel puțin 2 caractere';
    }
    if (limit <= 0) {
      return 'Limita trebuie să fie mai mare decât 0';
    }
    if (offset < 0) {
      return 'Offset-ul nu poate fi negativ';
    }
    return null;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchUsersRequest &&
          runtimeType == other.runtimeType &&
          searchTerm == other.searchTerm &&
          isActive == other.isActive &&
          limit == other.limit &&
          offset == other.offset;

  @override
  int get hashCode =>
      searchTerm.hashCode ^ isActive.hashCode ^ limit.hashCode ^ offset.hashCode;

  @override
  String toString() {
    return 'SearchUsersRequest{searchTerm: $searchTerm, isActive: $isActive, limit: $limit, offset: $offset}';
  }
}
