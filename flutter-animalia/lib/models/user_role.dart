import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

/// Application-level user roles
enum UserRole {
  /// Regular user - basic app access
  user('USER'),

  /// Groomer - can work in salons
  groomer('GROOMER'),

  /// Admin - full system access
  admin('ADMIN');

  const UserRole(this.value);
  final String value;

  /// Create UserRole from string value
  static UserRole fromString(String value) {
    switch (value.toUpperCase()) {
      case 'USER':
        return UserRole.user;
      case 'GROOMER':
        return UserRole.groomer;
      case 'ADMIN':
        return UserRole.admin;
      default:
        throw ArgumentError('Unknown user role: $value');
    }
  }

  /// Check if this role has admin privileges
  bool get isAdmin => this == UserRole.admin;

  /// Check if this role can work as a groomer
  bool get canBeGroomer => this == UserRole.groomer || this == UserRole.admin;

  /// Get display name in Romanian
  String get displayName {
    switch (this) {
      case UserRole.user:
        return 'Utilizator';
      case UserRole.groomer:
        return 'Groomer';
      case UserRole.admin:
        return 'Administrator';
    }
  }

  /// Get role description in Romanian
  String get description {
    switch (this) {
      case UserRole.user:
        return 'Utilizator obișnuit al aplicației';
      case UserRole.groomer:
        return 'Poate lucra în saloane de toaletaj';
      case UserRole.admin:
        return 'Acces complet la toate funcționalitățile';
    }
  }
}

/// Salon-specific groomer roles (matches backend StaffRole enum)
enum GroomerRole {
  /// Chief groomer with management permissions within the salon
  chiefGroomer('CHIEF_GROOMER'),

  /// Regular groomer with limited permissions within the salon
  groomer('GROOMER'),

  /// Assistant groomer with basic permissions
  assistant('ASSISTANT'),

  /// Legacy regular groomer (mapped to GROOMER for backward compatibility)
  @Deprecated('Use groomer instead')
  regularGroomer('REGULAR_GROOMER');

  const GroomerRole(this.value);
  final String value;

  /// Create GroomerRole from string value
  static GroomerRole fromString(String value) {
    switch (value.toUpperCase()) {
      case 'CHIEF_GROOMER':
        return GroomerRole.chiefGroomer;
      case 'GROOMER':
        return GroomerRole.groomer;
      case 'ASSISTANT':
        return GroomerRole.assistant;
      default:
        throw ArgumentError('Unknown groomer role: $value');
    }
  }

  /// Check if this groomer role has management privileges within the salon
  bool get hasManagementAccess => this == GroomerRole.chiefGroomer;

  /// Get display name in Romanian (fallback)
  String get displayName {
    switch (this) {
      case GroomerRole.chiefGroomer:
        return 'Chief Groomer';
      case GroomerRole.groomer:
        return 'Groomer';
      case GroomerRole.assistant:
        return 'Assistant';
      // ignore: deprecated_member_use_from_same_package
      case GroomerRole.regularGroomer:
        return 'Groomer'; // Legacy support
    }
  }

  /// Get role description in Romanian (fallback)
  String get description {
    switch (this) {
      case GroomerRole.chiefGroomer:
        return 'Poate gestiona echipa și programările salonului';
      case GroomerRole.groomer:
        return 'Poate gestiona propriile programări';
      case GroomerRole.assistant:
        return 'Asistă la serviciile de toaletaj';
      // ignore: deprecated_member_use_from_same_package
      case GroomerRole.regularGroomer:
        return 'Poate gestiona propriile programări'; // Legacy support
    }
  }
}

/// Client data access permission levels for groomers (matches backend ClientDataAccess enum)
enum ClientDataPermission {
  /// No access to client data - can only see appointment times
  noAccess('NO_ACCESS'),

  /// Full access - can see and edit all client information
  fullAccess('FULL_ACCESS');

  const ClientDataPermission(this.value);
  final String value;

  /// Create ClientDataPermission from string value
  static ClientDataPermission fromString(String value) {
    switch (value.toUpperCase()) {
      // Handle backend ClientDataAccess enum values
      case 'NONE':
        return ClientDataPermission.noAccess;
      case 'LIMITED':
        return ClientDataPermission.fullAccess; // Map old LIMITED to FULL
      case 'FULL':
        return ClientDataPermission.fullAccess;
      // Handle frontend enum values for backward compatibility
      case 'NO_ACCESS':
        return ClientDataPermission.noAccess;
      case 'LIMITED_ACCESS':
        return ClientDataPermission.fullAccess; // Map old LIMITED_ACCESS to FULL
      case 'FULL_ACCESS':
        return ClientDataPermission.fullAccess;
      default:
        throw ArgumentError('Unknown client data permission: $value');
    }
  }

  /// Get display name in Romanian (fallback)
  String get displayName {
    switch (this) {
      case ClientDataPermission.noAccess:
        return 'No access';
      case ClientDataPermission.fullAccess:
        return 'Full access';
    }
  }

  /// Get permission description in Romanian (fallback)
  String get description {
    switch (this) {
      case ClientDataPermission.noAccess:
        return 'Poate vedea doar orele programărilor';
      case ClientDataPermission.fullAccess:
        return 'Poate vedea și edita toate datele clienților';
    }
  }
}

/// Groomer permissions model
class GroomerPermissions {
  final String userId;
  final String salonId;
  final GroomerRole groomerRole;
  final ClientDataPermission clientDataPermission;
  final bool isActive;
  final DateTime updatedAt;

  GroomerPermissions({
    required this.userId,
    required this.salonId,
    required this.groomerRole,
    required this.clientDataPermission,
    required this.isActive,
    required this.updatedAt,
  });

  /// Create from JSON
  factory GroomerPermissions.fromJson(Map<String, dynamic> json) {
    return GroomerPermissions(
      userId: json['userId'],
      salonId: json['salonId'],
      groomerRole: GroomerRole.fromString(json['groomerRole']),
      clientDataPermission: ClientDataPermission.fromString(json['clientDataPermission']),
      isActive: json['isActive'] ?? true,
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'salonId': salonId,
      'groomerRole': groomerRole.value,
      'clientDataPermission': clientDataPermission.value,
      'isActive': isActive,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Copy with method for immutable updates
  GroomerPermissions copyWith({
    String? userId,
    String? salonId,
    GroomerRole? groomerRole,
    ClientDataPermission? clientDataPermission,
    bool? isActive,
    DateTime? updatedAt,
  }) {
    return GroomerPermissions(
      userId: userId ?? this.userId,
      salonId: salonId ?? this.salonId,
      groomerRole: groomerRole ?? this.groomerRole,
      clientDataPermission: clientDataPermission ?? this.clientDataPermission,
      isActive: isActive ?? this.isActive,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if this groomer can access client data
  bool get canAccessClientData => clientDataPermission != ClientDataPermission.noAccess;

  /// Check if this groomer can see full client details
  bool get canSeeFullClientDetails => clientDataPermission == ClientDataPermission.fullAccess;

  /// Check if this groomer has management privileges within the salon
  bool get hasManagementAccess => groomerRole.hasManagementAccess;
}

/// Extensions for localized display names
extension GroomerRoleLocalization on GroomerRole {
  /// Get localized display name
  String localizedDisplayName(BuildContext context) {
    switch (this) {
      case GroomerRole.chiefGroomer:
        return context.tr('user_roles.groomer_role_chief_groomer');
      case GroomerRole.groomer:
        return context.tr('user_roles.groomer_role_groomer');
      case GroomerRole.assistant:
        return context.tr('user_roles.groomer_role_assistant');
      // ignore: deprecated_member_use_from_same_package
      case GroomerRole.regularGroomer:
        return context.tr('user_roles.groomer_role_groomer'); // Legacy support
    }
  }

  /// Get localized role description
  String localizedDescription(BuildContext context) {
    switch (this) {
      case GroomerRole.chiefGroomer:
        return context.tr('user_roles.groomer_role_chief_description');
      case GroomerRole.groomer:
        return context.tr('user_roles.groomer_role_groomer_description');
      case GroomerRole.assistant:
        return context.tr('user_roles.groomer_role_assistant_description');
      // ignore: deprecated_member_use_from_same_package
      case GroomerRole.regularGroomer:
        return context.tr('user_roles.groomer_role_groomer_description'); // Legacy support
    }
  }
}

extension ClientDataPermissionLocalization on ClientDataPermission {
  /// Get localized display name
  String localizedDisplayName(BuildContext context) {
    switch (this) {
      case ClientDataPermission.noAccess:
        return context.tr('user_roles.client_permission_no_access');
      case ClientDataPermission.fullAccess:
        return context.tr('user_roles.client_permission_full_access');
    }
  }

  /// Get localized permission description
  String localizedDescription(BuildContext context) {
    switch (this) {
      case ClientDataPermission.noAccess:
        return context.tr('user_roles.client_permission_no_access_description');
      case ClientDataPermission.fullAccess:
        return context.tr('user_roles.client_permission_full_description');
    }
  }
}
