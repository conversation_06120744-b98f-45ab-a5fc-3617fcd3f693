/// SMS template model for customizable message templates
class SmsTemplate {
  final String id;
  final String salonId;
  final SmsTemplateType templateType;
  final String templateContent;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SmsTemplate({
    required this.id,
    required this.salonId,
    required this.templateType,
    required this.templateContent,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create SmsTemplate from JSON
  factory SmsTemplate.fromJson(Map<String, dynamic> json) {
    return SmsTemplate(
      id: json['id'] as String,
      salonId: json['salonId'] as String,
      templateType: SmsTemplateType.fromString(json['templateType'] as String),
      templateContent: json['templateContent'] as String,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert SmsTemplate to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'salonId': salonId,
      'templateType': templateType.name,
      'templateContent': templateContent,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated values
  SmsTemplate copyWith({
    String? id,
    String? salonId,
    SmsTemplateType? templateType,
    String? templateContent,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SmsTemplate(
      id: id ?? this.id,
      salonId: salonId ?? this.salonId,
      templateType: templateType ?? this.templateType,
      templateContent: templateContent ?? this.templateContent,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get all template variables used in this template
  Set<String> getUsedVariables() {
    final variableRegex = RegExp(r'\{([A-Z_]+)\}');
    return variableRegex
        .allMatches(templateContent)
        .map((match) => match.group(1)!)
        .toSet();
  }

  /// Render template with provided variables
  String renderMessage(Map<String, String> variables) {
    String rendered = templateContent;
    variables.forEach((key, value) {
      rendered = rendered.replaceAll('{$key}', value);
    });
    return rendered;
  }

  /// Validate template content
  String? validateContent() {
    if (templateContent.trim().isEmpty) {
      return 'Conținutul template-ului nu poate fi gol';
    }
    
    if (templateContent.length > 1000) {
      return 'Conținutul template-ului nu poate depăși 1000 de caractere';
    }

    // Check for valid variables
    final variableRegex = RegExp(r'\{([A-Z_]+)\}');
    final usedVariables = variableRegex
        .allMatches(templateContent)
        .map((match) => match.group(1)!)
        .toSet();

    const validVariables = {
      'SALON_NAME', 'OWNER_NAME', 'PET_NAME',
      'APPOINTMENT_DATE', 'APPOINTMENT_TIME',
      'SALON_ADDRESS', 'SALON_PHONE', 'SERVICE_NAME'
    };

    final invalidVariables = usedVariables.difference(validVariables);
    if (invalidVariables.isNotEmpty) {
      return 'Variabile invalide: ${invalidVariables.join(', ')}';
    }

    return null;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SmsTemplate &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SmsTemplate(id: $id, templateType: $templateType, isActive: $isActive)';
  }
}

/// Enum for SMS template types
enum SmsTemplateType {
  appointmentConfirmation('APPOINTMENT_CONFIRMATION'),
  appointmentCancellation('APPOINTMENT_CANCELLATION'),
  appointmentReschedule('APPOINTMENT_RESCHEDULE'),
  reminder('REMINDER'),
  appointmentCompletion('APPOINTMENT_COMPLETION'),
  followUp('FOLLOW_UP');

  const SmsTemplateType(this.name);

  final String name;

  /// Get translation key for display name
  String get translationKey {
    switch (this) {
      case SmsTemplateType.appointmentConfirmation:
        return 'sms_templates.appointment_confirmation';
      case SmsTemplateType.appointmentCancellation:
        return 'sms_templates.appointment_cancellation';
      case SmsTemplateType.appointmentReschedule:
        return 'sms_templates.appointment_reschedule';
      case SmsTemplateType.reminder:
        return 'sms_templates.reminder';
      case SmsTemplateType.appointmentCompletion:
        return 'sms_templates.appointment_completion';
      case SmsTemplateType.followUp:
        return 'sms_templates.follow_up';
    }
  }

  /// Get display name (fallback for when localization is not available)
  String get displayName {
    switch (this) {
      case SmsTemplateType.appointmentConfirmation:
        return 'Confirmare Programare';
      case SmsTemplateType.appointmentCancellation:
        return 'Anulare Programare';
      case SmsTemplateType.appointmentReschedule:
        return 'Reprogramare';
      case SmsTemplateType.reminder:
        return 'Reminder';
      case SmsTemplateType.appointmentCompletion:
        return 'Finalizare Programare';
      case SmsTemplateType.followUp:
        return 'Follow-up';
    }
  }

  static SmsTemplateType fromString(String value) {
    return SmsTemplateType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => throw ArgumentError('Unknown SMS template type: $value'),
    );
  }

  /// Get default template content
  String getDefaultContent() {
    switch (this) {
      case SmsTemplateType.appointmentConfirmation:
        return '{SALON_NAME}: Salut {OWNER_NAME}! Programarea pentru {PET_NAME} e confirmată pe {APPOINTMENT_DATE} la {APPOINTMENT_TIME}. Te așteptăm!';
      case SmsTemplateType.appointmentCancellation:
        return '{SALON_NAME}: Salut {OWNER_NAME}! Programarea pentru {PET_NAME} din {APPOINTMENT_DATE} a fost anulată. Pentru reprogramare: {SALON_PHONE}';
      case SmsTemplateType.appointmentReschedule:
        return '{SALON_NAME}: Salut {OWNER_NAME}! Programarea pentru {PET_NAME} s-a mutat pe {APPOINTMENT_DATE} la {APPOINTMENT_TIME}. Te așteptăm!';
      case SmsTemplateType.reminder:
        return '{SALON_NAME}: Salut {OWNER_NAME}! la {APPOINTMENT_TIME} îl așteptăm pe {PET_NAME}. Ne bucurăm să vă vedem!';
      case SmsTemplateType.appointmentCompletion:
        return '{SALON_NAME}: Salut {OWNER_NAME}! {PET_NAME} a fost grozav astăzi. Sperăm să ne revedem curând! 🐾';
      case SmsTemplateType.followUp:
        return '{SALON_NAME}: Salut {OWNER_NAME}! Cum se simte {PET_NAME} după vizită? Mulțumim că ne-ai ales! Pentru întrebări: {SALON_PHONE}';
    }
  }

  /// Get professional template options for this template type
  List<ProfessionalTemplate> getProfessionalTemplates() {
    switch (this) {
      case SmsTemplateType.appointmentConfirmation:
        return [
          ProfessionalTemplate(
            id: 'confirmation_1',
            nameKey: 'sms_templates.friendly_tone',
            contentKey: 'sms_templates.confirmation_friendly_content',
            descriptionKey: 'sms_templates.friendly_tone_desc',
            estimatedSmsCount: 1,
          ),
          ProfessionalTemplate(
            id: 'confirmation_2',
            nameKey: 'sms_templates.professional_tone',
            contentKey: 'sms_templates.confirmation_professional_content',
            descriptionKey: 'sms_templates.professional_tone_desc',
            estimatedSmsCount: 1,
          ),
          ProfessionalTemplate(
            id: 'confirmation_3',
            nameKey: 'sms_templates.with_emoji',
            contentKey: 'sms_templates.confirmation_emoji_content',
            descriptionKey: 'sms_templates.with_emoji_desc',
            estimatedSmsCount: 1,
          ),
        ];
      case SmsTemplateType.appointmentCancellation:
        return [
          ProfessionalTemplate(
            id: 'cancellation_1',
            nameKey: 'sms_templates.simple',
            contentKey: 'sms_templates.cancellation_simple_content',
            descriptionKey: 'sms_templates.simple_desc',
            estimatedSmsCount: 1,
          ),
          ProfessionalTemplate(
            id: 'cancellation_2',
            nameKey: 'sms_templates.with_apology',
            contentKey: 'sms_templates.cancellation_apology_content',
            descriptionKey: 'sms_templates.with_apology_desc',
            estimatedSmsCount: 1,
          ),
        ];
      case SmsTemplateType.appointmentReschedule:
        return [
          ProfessionalTemplate(
            id: 'reschedule_1',
            nameKey: 'sms_templates.simple',
            contentKey: 'sms_templates.reschedule_simple_content',
            descriptionKey: 'sms_templates.short_positive_desc',
            estimatedSmsCount: 1,
          ),
          ProfessionalTemplate(
            id: 'reschedule_2',
            nameKey: 'sms_templates.formal',
            contentKey: 'sms_templates.reschedule_formal_content',
            descriptionKey: 'sms_templates.professional_tone_desc',
            estimatedSmsCount: 1,
          ),
        ];
      case SmsTemplateType.reminder:
        return [
          ProfessionalTemplate(
            id: 'reminder_1',
            nameKey: 'sms_templates.short',
            contentKey: 'sms_templates.reminder_short_content',
            descriptionKey: 'sms_templates.friendly_short_desc',
            estimatedSmsCount: 1,
          ),
          ProfessionalTemplate(
            id: 'reminder_2',
            nameKey: 'sms_templates.urgent',
            contentKey: 'sms_templates.reminder_urgent_content',
            descriptionKey: 'sms_templates.urgent_desc',
            estimatedSmsCount: 1,
          ),
          ProfessionalTemplate(
            id: 'reminder_3',
            nameKey: 'sms_templates.formal',
            contentKey: 'sms_templates.reminder_formal_content',
            descriptionKey: 'sms_templates.formal_reminder_desc',
            estimatedSmsCount: 1,
          ),
        ];
      case SmsTemplateType.appointmentCompletion:
        return [
          ProfessionalTemplate(
            id: 'completion_1',
            nameKey: 'sms_templates.thank_you',
            contentKey: 'sms_templates.completion_thank_you_content',
            descriptionKey: 'sms_templates.thank_you_desc',
            estimatedSmsCount: 1,
          ),
          ProfessionalTemplate(
            id: 'completion_2',
            nameKey: 'sms_templates.professional',
            contentKey: 'sms_templates.completion_professional_content',
            descriptionKey: 'sms_templates.formal_completion_desc',
            estimatedSmsCount: 1,
          ),
          ProfessionalTemplate(
            id: 'completion_3',
            nameKey: 'sms_templates.with_review',
            contentKey: 'sms_templates.completion_review_content',
            descriptionKey: 'sms_templates.with_review_desc',
            estimatedSmsCount: 1,
          ),
        ];
      case SmsTemplateType.followUp:
        return [
          ProfessionalTemplate(
            id: 'followup_1',
            nameKey: 'sms_templates.care',
            contentKey: 'sms_templates.followup_care_content',
            descriptionKey: 'sms_templates.care_desc',
            estimatedSmsCount: 1,
          ),
          ProfessionalTemplate(
            id: 'followup_2',
            nameKey: 'sms_templates.review',
            contentKey: 'sms_templates.followup_review_content',
            descriptionKey: 'sms_templates.review_request_desc',
            estimatedSmsCount: 1,
          ),
          ProfessionalTemplate(
            id: 'followup_3',
            nameKey: 'sms_templates.simple',
            contentKey: 'sms_templates.followup_simple_content',
            descriptionKey: 'sms_templates.simple_followup_desc',
            estimatedSmsCount: 1,
          ),
        ];
    }
  }
}

/// Request model for updating SMS template
class UpdateSmsTemplateRequest {
  final String templateContent;
  final bool isActive;

  const UpdateSmsTemplateRequest({
    required this.templateContent,
    this.isActive = true,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'templateContent': templateContent,
      'isActive': isActive,
    };
  }
}

/// Request model for previewing SMS template
class PreviewSmsTemplateRequest {
  final String templateContent;
  final Map<String, String> sampleData;

  const PreviewSmsTemplateRequest({
    required this.templateContent,
    required this.sampleData,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'templateContent': templateContent,
      'sampleData': sampleData,
    };
  }
}

/// Response model for SMS template preview
class PreviewSmsTemplateResponse {
  final String preview;
  final int characterCount;

  const PreviewSmsTemplateResponse({
    required this.preview,
    required this.characterCount,
  });

  /// Create from JSON
  factory PreviewSmsTemplateResponse.fromJson(Map<String, dynamic> json) {
    return PreviewSmsTemplateResponse(
      preview: json['preview'] as String,
      characterCount: json['characterCount'] as int,
    );
  }
}

/// Professional template option
class ProfessionalTemplate {
  final String id;
  final String nameKey;
  final String contentKey;
  final String descriptionKey;
  final int estimatedSmsCount;

  const ProfessionalTemplate({
    required this.id,
    required this.nameKey,
    required this.contentKey,
    required this.descriptionKey,
    required this.estimatedSmsCount,
  });

  /// Calculate actual SMS count for this template
  int getActualSmsCount(String content) {
    return SmsCalculator.calculateSmsCount(content);
  }

  /// Get SMS cost warning level
  SmsCostLevel getCostLevel(String content) {
    return SmsCalculator.getCostLevel(content);
  }
}

/// SMS cost warning levels
enum SmsCostLevel {
  low(1, 'Scump - 1 SMS', 'green'),
  medium(2, 'Moderat - 2 SMS', 'orange'),
  high(3, 'Scump - 3+ SMS', 'red');

  const SmsCostLevel(this.smsCount, this.label, this.color);

  final int smsCount;
  final String label;
  final String color;
}

/// SMS calculation utilities
class SmsCalculator {
  static const int singleSmsLimit = 160;
  static const int multiSmsPartLimit = 153;

  /// Calculate exact SMS count
  static int calculateSmsCount(String content) {
    if (content.length <= singleSmsLimit) {
      return 1;
    }
    // For concatenated SMS, each part can hold 153 characters
    return ((content.length - singleSmsLimit) / multiSmsPartLimit).ceil() + 1;
  }

  /// Get cost warning level
  static SmsCostLevel getCostLevel(String content) {
    final smsCount = calculateSmsCount(content);
    if (smsCount == 1) return SmsCostLevel.low;
    if (smsCount == 2) return SmsCostLevel.medium;
    return SmsCostLevel.high;
  }

  /// Get character count with SMS boundary info
  static SmsCharacterInfo getCharacterInfo(String content) {
    final length = content.length;
    final smsCount = calculateSmsCount(content);
    final costLevel = getCostLevel(content);

    int remainingInCurrentSms;
    if (smsCount == 1) {
      remainingInCurrentSms = singleSmsLimit - length;
    } else {
      final usedInLastPart = (length - singleSmsLimit) % multiSmsPartLimit;
      remainingInCurrentSms = multiSmsPartLimit - usedInLastPart;
    }

    return SmsCharacterInfo(
      totalCharacters: length,
      smsCount: smsCount,
      costLevel: costLevel,
      remainingInCurrentSms: remainingInCurrentSms,
    );
  }
}

/// SMS character information
class SmsCharacterInfo {
  final int totalCharacters;
  final int smsCount;
  final SmsCostLevel costLevel;
  final int remainingInCurrentSms;

  const SmsCharacterInfo({
    required this.totalCharacters,
    required this.smsCount,
    required this.costLevel,
    required this.remainingInCurrentSms,
  });

  /// Get display text for character counter
  String getDisplayText() {
    return '$totalCharacters caractere • ${costLevel.label}';
  }

  /// Get warning message if needed
  String? getWarningMessage() {
    if (smsCount > 1) {
      return 'Atenție: Mesajul va fi trimis în $smsCount SMS-uri, ceea ce va costa mai mult.';
    }
    if (remainingInCurrentSms <= 10) {
      return 'Aproape de limita de ${SmsCalculator.singleSmsLimit} caractere pentru 1 SMS.';
    }
    return null;
  }
}

/// Template variable information
class TemplateVariableInfo {
  final String name;
  final String description;
  final String example;

  const TemplateVariableInfo({
    required this.name,
    required this.description,
    required this.example,
  });

  /// Create from JSON
  factory TemplateVariableInfo.fromJson(Map<String, dynamic> json) {
    return TemplateVariableInfo(
      name: json['name'] as String,
      description: json['description'] as String,
      example: json['example'] as String,
    );
  }

  /// Get all available template variables
  static List<TemplateVariableInfo> getAllVariables() {
    return [
      const TemplateVariableInfo(
        name: 'SALON_NAME',
        description: 'Numele salonului',
        example: 'Animalia Salon',
      ),
      const TemplateVariableInfo(
        name: 'OWNER_NAME',
        description: 'Numele proprietarului animalului',
        example: 'Maria Popescu',
      ),
      const TemplateVariableInfo(
        name: 'PET_NAME',
        description: 'Numele animalului',
        example: 'Rex',
      ),
      const TemplateVariableInfo(
        name: 'APPOINTMENT_DATE',
        description: 'Data programării',
        example: '25.12.2024',
      ),
      const TemplateVariableInfo(
        name: 'APPOINTMENT_TIME',
        description: 'Ora programării',
        example: '14:30',
      ),
      const TemplateVariableInfo(
        name: 'SALON_ADDRESS',
        description: 'Adresa salonului',
        example: 'Str. Florilor nr. 15, București',
      ),
      const TemplateVariableInfo(
        name: 'SALON_PHONE',
        description: 'Telefonul salonului',
        example: '0721 123 456',
      ),
      const TemplateVariableInfo(
        name: 'SERVICE_NAME',
        description: 'Numele serviciului',
        example: 'Tuns și spălat',
      ),
    ];
  }

  /// Get sample data for preview
  static Map<String, String> getSampleData() {
    return {
      'SALON_NAME': 'Animalia Salon',
      'OWNER_NAME': 'Maria Popescu',
      'PET_NAME': 'Rex',
      'APPOINTMENT_DATE': '25.12.2024',
      'APPOINTMENT_TIME': '14:30',
      'SALON_ADDRESS': 'Str. Florilor nr. 15, București',
      'SALON_PHONE': '0721 123 456',
      'SERVICE_NAME': 'Tuns și spălat',
    };
  }
}
