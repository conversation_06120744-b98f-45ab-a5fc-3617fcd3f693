/// Salon subscription package tiers based on SUBSCRIPTIONS.md
enum SubscriptionTier {
  free('free', 'Free', 'Funcționalități de bază'),
  freelancer('freelancer', 'Freelancer', 'Pentru groomeri independenți'),
  team('team', 'Team', 'Pentru echipe mici'),
  enterprise('enterprise', 'Enterprise', 'Pentru saloane multiple si echipe mari');

  const SubscriptionTier(this.id, this.name, this.subtitle);
  
  final String id;
  final String name;
  final String subtitle;

  /// Get subtitle translation key
  String get subtitleKey {
    switch (this) {
      case SubscriptionTier.free:
        return 'subscription_plans.tier_subtitle_free';
      case SubscriptionTier.freelancer:
        return 'subscription_plans.tier_subtitle_freelancer';
      case SubscriptionTier.team:
        return 'subscription_plans.tier_subtitle_team';
      case SubscriptionTier.enterprise:
        return 'subscription_plans.tier_subtitle_enterprise';
    }
  }

  /// Get monthly price in RON (mobile pricing)
  double get monthlyPrice {
    switch (this) {
      case SubscriptionTier.free:
        return 0.0;
      case SubscriptionTier.freelancer:
        return 99.0;
      case SubscriptionTier.team:
        return 199.0;
      case SubscriptionTier.enterprise:
        return 499.0;
    }
  }

  /// Get annual price in RON (10 months price for 12 months - mobile pricing)
  double get annualPrice {
    switch (this) {
      case SubscriptionTier.free:
        return 0.0;
      case SubscriptionTier.freelancer:
        return 990.0; // 99 * 10 months
      case SubscriptionTier.team:
        return 1990.0; // 199 * 10 months
      case SubscriptionTier.enterprise:
        return 4990.0; // 499 * 10 months
    }
  }

  /// Get monthly price for web platform in RON
  double get webMonthlyPrice {
    switch (this) {
      case SubscriptionTier.free:
        return 0.0;
      case SubscriptionTier.freelancer:
        return 80.0;
      case SubscriptionTier.team:
        return 160.0;
      case SubscriptionTier.enterprise:
        return 410.0;
    }
  }

  /// Get annual price for web platform in RON (10 months price for 12 months)
  double get webAnnualPrice {
    switch (this) {
      case SubscriptionTier.free:
        return 0.0;
      case SubscriptionTier.freelancer:
        return 800.0; // 80 * 10 months
      case SubscriptionTier.team:
        return 1600.0; // 160 * 10 months
      case SubscriptionTier.enterprise:
        return 4100.0; // 410 * 10 months
    }
  }

  /// Get maximum staff count
  int get maxStaff {
    switch (this) {
      case SubscriptionTier.free:
        return 1;
      case SubscriptionTier.freelancer:
        return 2;
      case SubscriptionTier.team:
        return 10;
      case SubscriptionTier.enterprise:
        return -1; // Unlimited
    }
  }

  /// Get maximum client count (all plans have unlimited clients now)
  int get maxClients {
    return -1; // Unlimited for all plans
  }

  /// Get SMS quota per month
  int get smsQuota {
    switch (this) {
      case SubscriptionTier.free:
        return 0;
      case SubscriptionTier.freelancer:
        return 100;
      case SubscriptionTier.team:
        return 200;
      case SubscriptionTier.enterprise:
        return 500;
    }
  }

  /// Get features list as translation keys
  List<String> get featureKeys {
    switch (this) {
      case SubscriptionTier.free:
        return [
          'subscription_plans.features.quick_booking',
          'subscription_plans.features.drag_drop_reschedule',
          'subscription_plans.features.direct_communication',
          'subscription_plans.features.mobile_app',
          'subscription_plans.features.synced_calendar',
          'subscription_plans.features.max_one_groomer',
          'subscription_plans.features.no_sms',
          'subscription_plans.features.unlimited_clients',
        ];
      case SubscriptionTier.freelancer:
        return [
          'subscription_plans.features.automated_sms_100',
          'subscription_plans.features.max_two_groomers',
          'subscription_plans.features.email_support',
        ];
      case SubscriptionTier.team:
        return [
          'subscription_plans.features.automated_sms_200',
          'subscription_plans.features.max_ten_groomers',
          'subscription_plans.features.recurring_appointments',
          'subscription_plans.features.team_management',
          'subscription_plans.features.performance_reports',
        ];
      case SubscriptionTier.enterprise:
        return [
          'subscription_plans.features.unlimited_groomers',
          'subscription_plans.features.multiple_salons',
          'subscription_plans.features.multi_location_management',
          'subscription_plans.features.automated_sms_500',
          'subscription_plans.features.custom_branding',
          'subscription_plans.features.priority_support',
          'subscription_plans.features.personal_training',
          'subscription_plans.features.auto_backup',
          'subscription_plans.features.early_access',
        ];
    }
  }

  /// Get features list in Romanian - focused on actually supported features
  List<String> get features {
    switch (this) {
      case SubscriptionTier.free:
        return [
          'Programare rapidă din doar 3 clickuri',
          'Reprogramare prin drag & drop',
          'Comunicare directă cu clientții (telefon, WhatsApp)',
          'Aplicație mobilă',
          'Calendar sincronizat',
          'Maxim 1 groomer',
          'Fără SMS-uri',
          'Clienți nelimitați',
        ];
      case SubscriptionTier.freelancer:
        return [
          'Mesaje automate SMS (100 gratuite)',
          'Maxim 2 groomeri în echipă',
          'Suport tehnic prin email',
        ];
      case SubscriptionTier.team:
        return [
          'Mesaje automate SMS (200 gratuite)',
          'Maxim 10 groomeri în echipă',
          'Programari recurente',
          'Management echipa',
          'Rapoarte de performanță',

          ];
      case SubscriptionTier.enterprise:
        return [
          'Groomeri nelimitați în echipă',
          'Creare saloane multiple',
          'Management multi-locații',
          'Mesaje automate SMS (500/lună)',
          'Branding personalizat',
          'Suport prioritar + telefon',
          'Sesiuni training personalizate',
          'Backup automat date',
          'Acces prioritar la funcții noi',
        ];
    }
  }

  /// Create from string ID (case-insensitive)
  /// Supports both Flutter enum IDs and backend enum names
  static SubscriptionTier fromId(String id) {
    final lowerId = id.toLowerCase();

    // Handle backend enum names
    switch (lowerId) {
      case 'freelancer':
        return SubscriptionTier.freelancer;
      case 'team':
        return SubscriptionTier.team;
      case 'enterprise':
        return SubscriptionTier.enterprise;
      case 'free':
        return SubscriptionTier.free;
    }

    // Handle Flutter enum IDs
    return SubscriptionTier.values.firstWhere(
      (tier) => tier.id.toLowerCase() == lowerId,
      orElse: () => SubscriptionTier.free
    );
  }
}

/// Subscription status
enum SubscriptionStatus {
  active('active'),
  trial('trial'),
  expired('expired'),
  cancelled('cancelled'),
  pending('pending');

  const SubscriptionStatus(this.value);
  final String value;

  static SubscriptionStatus fromString(String value) {
    return SubscriptionStatus.values.firstWhere(
      (status) => status.value.toLowerCase() == value.toLowerCase(),
      orElse: () => SubscriptionStatus.expired,
    );
  }
}

/// Salon subscription model
class SalonSubscription {
  final String id;
  final String userId;
  final String salonId;
  final SubscriptionTier tier;
  final SubscriptionStatus status;
  final DateTime startDate;
  final DateTime? endDate;
  final DateTime? trialEndDate;
  final bool isTrialActive;
  final String? revenueCatCustomerId;
  final String? revenueCatEntitlementId;
  final Map<String, dynamic> metadata;

  SalonSubscription({
    required this.id,
    required this.userId,
    required this.salonId,
    required this.tier,
    required this.status,
    required this.startDate,
    this.endDate,
    this.trialEndDate,
    this.isTrialActive = false,
    this.revenueCatCustomerId,
    this.revenueCatEntitlementId,
    this.metadata = const {},
  });

  /// Create from JSON
  factory SalonSubscription.fromJson(Map<String, dynamic> json) {
    return SalonSubscription(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      salonId: json['salonId'] ?? '',
      tier: SubscriptionTier.fromId(json['tier'] ?? 'free'),
      status: SubscriptionStatus.fromString(json['status'] ?? 'expired'),
      startDate: DateTime.parse(json['startDate']),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      trialEndDate: json['trialEndDate'] != null ? DateTime.parse(json['trialEndDate']) : null,
      isTrialActive: json['isTrialActive'] ?? false,
      revenueCatCustomerId: json['revenueCatCustomerId'],
      revenueCatEntitlementId: json['revenueCatEntitlementId'],
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'salonId': salonId,
      'tier': tier.id,
      'status': status.value,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'trialEndDate': trialEndDate?.toIso8601String(),
      'isTrialActive': isTrialActive,
      'revenueCatCustomerId': revenueCatCustomerId,
      'revenueCatEntitlementId': revenueCatEntitlementId,
      'metadata': metadata,
    };
  }

  /// Check if subscription is active (including trial)
  /// A subscription is considered active if:
  /// - It's in trial period and trial hasn't ended yet
  /// - It's ACTIVE or CANCELLED and the end date hasn't passed yet
  /// - It's ACTIVE with no end date (indefinite)
  bool get isActive {
    final now = DateTime.now();

    // Trial period takes precedence - user has access until trial ends
    if (isTrialActive && trialEndDate != null) {
      return now.isBefore(trialEndDate!);
    }

    // CANCELLED subscriptions remain active until end date (preserves trial/billing period access)
    if (status == SubscriptionStatus.cancelled) {
      if (endDate != null) {
        return now.isBefore(endDate!);
      }
      if (trialEndDate != null) {
        return now.isBefore(trialEndDate!);
      }
      return false;
    }

    // ACTIVE subscriptions
    if (status == SubscriptionStatus.active) {
      if (endDate != null) {
        return now.isBefore(endDate!);
      }
      return true; // Active with no end date
    }

    return false;
  }

  /// Check if subscription has expired
  bool get isExpired {
    final now = DateTime.now();

    if (isTrialActive && trialEndDate != null) {
      return now.isAfter(trialEndDate!);
    }

    if (endDate != null) {
      return now.isAfter(endDate!);
    }

    return status == SubscriptionStatus.expired;
  }

  /// Get days remaining in trial
  int get trialDaysRemaining {
    if (!isTrialActive || trialEndDate == null) return 0;
    final now = DateTime.now();
    final difference = trialEndDate!.difference(now);
    return difference.inDays.clamp(0, double.infinity).toInt();
  }

  /// Get days remaining in subscription
  int get subscriptionDaysRemaining {
    if (endDate == null) return 0;
    final now = DateTime.now();
    final difference = endDate!.difference(now);
    return difference.inDays.clamp(0, double.infinity).toInt();
  }

  /// Check if user can access feature based on subscription tier
  bool canAccessFeature(String feature) {
    if (!isActive) return false;

    switch (feature) {
      case 'advanced_reports':
        return tier == SubscriptionTier.team || tier == SubscriptionTier.enterprise;
      case 'multi_location':
        return tier == SubscriptionTier.enterprise;
      case 'api_access':
        return tier == SubscriptionTier.enterprise;
      case 'custom_branding':
        return tier == SubscriptionTier.enterprise;
      case 'salon_creation':
        return tier == SubscriptionTier.enterprise;
      case 'inventory_management':
        return tier == SubscriptionTier.team || tier == SubscriptionTier.enterprise;
      case 'marketing_tools':
        return tier == SubscriptionTier.team || tier == SubscriptionTier.enterprise;
      case 'online_booking_widget':
        return tier == SubscriptionTier.team || tier == SubscriptionTier.enterprise;
      case 'sms_subscription':
        return tier == SubscriptionTier.freelancer || tier == SubscriptionTier.team || tier == SubscriptionTier.enterprise;
      default:
        return true; // Basic features available to all tiers
    }
  }

  /// Check if salon can add more staff
  bool canAddStaff(int currentStaffCount) {
    if (!isActive) return false;
    if (tier.maxStaff == -1) return true; // Unlimited
    return currentStaffCount < tier.maxStaff;
  }

  /// Check if salon can add more clients
  bool canAddClients(int currentClientCount) {
    if (!isActive) return false;
    if (tier.maxClients == -1) return true; // Unlimited
    return currentClientCount < tier.maxClients;
  }
}
