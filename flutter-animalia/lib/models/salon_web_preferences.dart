import 'dart:convert';

/// Model for salon website management preferences
class SalonWebPreferences {
  final String? id;
  final String salonId;
  
  // Basic Info
  final String bookingWebsiteUrl;
  final String businessName;
  final String businessDescription;
  final String businessAddress;
  final String contactPhone;
  final String contactEmail;
  final String facebookLink;
  final String instagramLink;
  final String tiktokLink;
  
  // Website Photos
  final List<String> websitePhotos;
  
  // Business Hours
  final Map<String, Map<String, dynamic>> businessHours;
  
  // Extra Info
  final CancellationPolicy cancellationPolicy;
  
  // Booking Acceptance
  final BookingAcceptance bookingAcceptance;
  
  // Metadata
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const SalonWebPreferences({
    this.id,
    required this.salonId,
    this.bookingWebsiteUrl = '',
    this.businessName = '',
    this.businessDescription = '',
    this.businessAddress = '',
    this.contactPhone = '',
    this.contactEmail = '',
    this.facebookLink = '',
    this.instagramLink = '',
    this.tiktokLink = '',
    this.websitePhotos = const [],
    this.businessHours = const {},
    this.cancellationPolicy = CancellationPolicy.hours24,
    this.bookingAcceptance = BookingAcceptance.automatic,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
  });

  /// Create a copy with updated fields
  SalonWebPreferences copyWith({
    String? id,
    String? salonId,
    String? bookingWebsiteUrl,
    String? businessName,
    String? businessDescription,
    String? businessAddress,
    String? contactPhone,
    String? contactEmail,
    String? facebookLink,
    String? instagramLink,
    String? tiktokLink,
    List<String>? websitePhotos,
    Map<String, Map<String, dynamic>>? businessHours,
    CancellationPolicy? cancellationPolicy,
    BookingAcceptance? bookingAcceptance,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SalonWebPreferences(
      id: id ?? this.id,
      salonId: salonId ?? this.salonId,
      bookingWebsiteUrl: bookingWebsiteUrl ?? this.bookingWebsiteUrl,
      businessName: businessName ?? this.businessName,
      businessDescription: businessDescription ?? this.businessDescription,
      businessAddress: businessAddress ?? this.businessAddress,
      contactPhone: contactPhone ?? this.contactPhone,
      contactEmail: contactEmail ?? this.contactEmail,
      facebookLink: facebookLink ?? this.facebookLink,
      instagramLink: instagramLink ?? this.instagramLink,
      tiktokLink: tiktokLink ?? this.tiktokLink,
      websitePhotos: websitePhotos ?? this.websitePhotos,
      businessHours: businessHours ?? this.businessHours,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
      bookingAcceptance: bookingAcceptance ?? this.bookingAcceptance,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create from JSON
  factory SalonWebPreferences.fromJson(Map<String, dynamic> json) {
    return SalonWebPreferences(
      id: json['id'],
      salonId: json['salon_id'] ?? '',
      bookingWebsiteUrl: json['booking_website_url'] ?? '',
      businessName: json['business_name'] ?? '',
      businessDescription: json['business_description'] ?? '',
      businessAddress: json['business_address'] ?? '',
      contactPhone: json['contact_phone'] ?? '',
      contactEmail: json['contact_email'] ?? '',
      facebookLink: json['facebook_link'] ?? '',
      instagramLink: json['instagram_link'] ?? '',
      tiktokLink: json['tiktok_link'] ?? '',
      websitePhotos: _parsePhotos(json['website_photos']),
      businessHours: _parseBusinessHours(json['business_hours']),
      cancellationPolicy: _parseCancellationPolicy(json['cancellation_policy']),
      bookingAcceptance: _parseBookingAcceptance(json['booking_acceptance']),
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'salon_id': salonId,
      'booking_website_url': bookingWebsiteUrl,
      'business_name': businessName,
      'business_description': businessDescription,
      'business_address': businessAddress,
      'contact_phone': contactPhone,
      'contact_email': contactEmail,
      'facebook_link': facebookLink,
      'instagram_link': instagramLink,
      'tiktok_link': tiktokLink,
      'website_photos': jsonEncode(websitePhotos),
      'business_hours': jsonEncode(businessHours),
      'cancellation_policy': cancellationPolicy.name.toUpperCase(),
      'booking_acceptance': bookingAcceptance.name.toUpperCase(),
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Helper methods for parsing JSON fields
  static List<String> _parsePhotos(dynamic photosJson) {
    if (photosJson == null || photosJson.isEmpty) return [];
    try {
      if (photosJson is String) {
        final List<dynamic> photosList = jsonDecode(photosJson);
        return photosList.cast<String>();
      } else if (photosJson is List) {
        return photosJson.cast<String>();
      }
    } catch (e) {
      // Handle parsing error
    }
    return [];
  }

  static Map<String, Map<String, dynamic>> _parseBusinessHours(dynamic hoursJson) {
    if (hoursJson == null || hoursJson.isEmpty) return {};
    try {
      if (hoursJson is String) {
        final Map<String, dynamic> hoursMap = jsonDecode(hoursJson);
        return hoursMap.map((key, value) => MapEntry(key, Map<String, dynamic>.from(value)));
      } else if (hoursJson is Map) {
        return Map<String, Map<String, dynamic>>.from(hoursJson);
      }
    } catch (e) {
      // Handle parsing error
    }
    return {};
  }

  static CancellationPolicy _parseCancellationPolicy(dynamic policy) {
    if (policy == null) return CancellationPolicy.hours24;
    final policyStr = policy.toString().toLowerCase();
    switch (policyStr) {
      case 'hours_24':
        return CancellationPolicy.hours24;
      case 'hours_48':
        return CancellationPolicy.hours48;
      case 'hours_72':
        return CancellationPolicy.hours72;
      case 'no_changes':
        return CancellationPolicy.noChanges;
      default:
        return CancellationPolicy.hours24;
    }
  }

  static BookingAcceptance _parseBookingAcceptance(dynamic acceptance) {
    if (acceptance == null) return BookingAcceptance.automatic;
    final acceptanceStr = acceptance.toString().toLowerCase();
    switch (acceptanceStr) {
      case 'automatic':
        return BookingAcceptance.automatic;
      case 'manual':
        return BookingAcceptance.manual;
      default:
        return BookingAcceptance.automatic;
    }
  }

  @override
  String toString() {
    return 'SalonWebPreferences(id: $id, salonId: $salonId, businessName: $businessName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SalonWebPreferences && other.id == id && other.salonId == salonId;
  }

  @override
  int get hashCode => id.hashCode ^ salonId.hashCode;
}

/// Cancellation policy options
enum CancellationPolicy {
  hours24,
  hours48,
  hours72,
  noChanges,
}

/// Booking acceptance options
enum BookingAcceptance {
  automatic,
  manual,
}
