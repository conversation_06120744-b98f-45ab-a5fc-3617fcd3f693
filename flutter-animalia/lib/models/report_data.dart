import 'package:flutter/material.dart';

/// Base class for all report data
abstract class ReportData {
  final String title;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime generatedAt;

  ReportData({
    required this.title,
    required this.startDate,
    required this.endDate,
    DateTime? generatedAt,
  }) : generatedAt = generatedAt ?? DateTime.now();
}

/// Data for bar chart reports
class BarChartReportData extends ReportData {
  final List<BarChartItem> items;
  final String xAxisLabel;
  final String yAxisLabel;
  final Color primaryColor;

  BarChartReportData({
    required super.title,
    required super.startDate,
    required super.endDate,
    required this.items,
    required this.xAxisLabel,
    required this.yAxisLabel,
    this.primaryColor = Colors.blue,
    super.generatedAt,
  });
}

/// Individual item in a bar chart
class BarChartItem {
  final String label;
  final double value;
  final Color? color;
  final Map<String, dynamic>? metadata;

  BarChartItem({
    required this.label,
    required this.value,
    this.color,
    this.metadata,
  });
}

/// Data for line chart reports (revenue over time)
class LineChartReportData extends ReportData {
  final List<LineChartPoint> points;
  final String xAxisLabel;
  final String yAxisLabel;
  final Color lineColor;

  LineChartReportData({
    required super.title,
    required super.startDate,
    required super.endDate,
    required this.points,
    required this.xAxisLabel,
    required this.yAxisLabel,
    this.lineColor = Colors.green,
    super.generatedAt,
  });
}

/// Point in a line chart
class LineChartPoint {
  final DateTime date;
  final double value;
  final Map<String, dynamic>? metadata;

  LineChartPoint({
    required this.date,
    required this.value,
    this.metadata,
  });
}

/// Staff performance report data
class StaffPerformanceReportData extends ReportData {
  final String staffId;
  final String staffName;
  final int totalAppointments;
  final double totalRevenue;
  final double averageRating;
  final int completedAppointments;
  final int cancelledAppointments;
  final double utilizationRate;
  final List<BarChartItem> serviceBreakdown;
  final List<LineChartPoint> revenueOverTime;

  StaffPerformanceReportData({
    required super.title,
    required super.startDate,
    required super.endDate,
    required this.staffId,
    required this.staffName,
    required this.totalAppointments,
    required this.totalRevenue,
    required this.averageRating,
    required this.completedAppointments,
    required this.cancelledAppointments,
    required this.utilizationRate,
    required this.serviceBreakdown,
    required this.revenueOverTime,
    super.generatedAt,
  });
}

/// Pet size/breed report data
class PetReportData extends ReportData {
  final List<BarChartItem> sizeBreakdown;
  final List<BarChartItem> breedBreakdown;
  final int totalPets;

  PetReportData({
    required super.title,
    required super.startDate,
    required super.endDate,
    required this.sizeBreakdown,
    required this.breedBreakdown,
    required this.totalPets,
    super.generatedAt,
  });
}

/// Service performance report data
class ServiceReportData extends ReportData {
  final List<BarChartItem> serviceRequests;
  final List<BarChartItem> serviceRevenue;
  final int totalServices;
  final double totalRevenue;

  ServiceReportData({
    required super.title,
    required super.startDate,
    required super.endDate,
    required this.serviceRequests,
    required this.serviceRevenue,
    required this.totalServices,
    required this.totalRevenue,
    super.generatedAt,
  });
}

/// Client performance report data
class ClientReportData extends ReportData {
  final List<BarChartItem> topClients;
  final int totalClients;
  final double averageSpending;
  final int newClients;
  final int returningClients;

  ClientReportData({
    required super.title,
    required super.startDate,
    required super.endDate,
    required this.topClients,
    required this.totalClients,
    required this.averageSpending,
    required this.newClients,
    required this.returningClients,
    super.generatedAt,
  });
}

/// Revenue report data
class RevenueReportData extends ReportData {
  final List<LineChartPoint> dailyRevenue;
  final double totalRevenue;
  final double averageDailyRevenue;
  final double growthRate;
  final List<BarChartItem> revenueByService;

  RevenueReportData({
    required super.title,
    required super.startDate,
    required super.endDate,
    required this.dailyRevenue,
    required this.totalRevenue,
    required this.averageDailyRevenue,
    required this.growthRate,
    required this.revenueByService,
    super.generatedAt,
  });
}

/// Report type enumeration
enum ReportType {
  petSizeBreed('Animale pe mărime/rasă'),
  serviceRequests('Servicii solicitate'),
  topClients('Top clienți'),
  staffPerformance('Performanță staff'),
  revenue('Venituri');

  const ReportType(this.displayName);
  final String displayName;
}

/// Date range presets
enum DateRangePreset {
  lastWeek('date_range_selector.last_week'),
  lastMonth('date_range_selector.last_month'),
  last3Months('date_range_selector.last_3_months'),
  last6Months('date_range_selector.last_6_months'),
  lastYear('date_range_selector.last_year'),
  custom('date_range_selector.custom');

  const DateRangePreset(this.translationKey);
  final String translationKey;

  DateRange getDateRange() {
    final now = DateTime.now();
    switch (this) {
      case DateRangePreset.lastWeek:
        return DateRange(
          start: now.subtract(const Duration(days: 7)),
          end: now,
        );
      case DateRangePreset.lastMonth:
        return DateRange(
          start: DateTime(now.year, now.month - 1, now.day),
          end: now,
        );
      case DateRangePreset.last3Months:
        return DateRange(
          start: DateTime(now.year, now.month - 3, now.day),
          end: now,
        );
      case DateRangePreset.last6Months:
        return DateRange(
          start: DateTime(now.year, now.month - 6, now.day),
          end: now,
        );
      case DateRangePreset.lastYear:
        return DateRange(
          start: DateTime(now.year - 1, now.month, now.day),
          end: now,
        );
      case DateRangePreset.custom:
        return DateRange(start: now, end: now);
    }
  }
}

/// Date range helper class
class DateRange {
  final DateTime start;
  final DateTime end;

  DateRange({required this.start, required this.end});

  int get daysDifference => end.difference(start).inDays;
  
  bool contains(DateTime date) {
    return date.isAfter(start) && date.isBefore(end);
  }
}
