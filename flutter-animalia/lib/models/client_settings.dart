import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

/// Client management settings model
class ClientSettings {
  final int inactiveClientThresholdDays;
  final bool showInactiveClientsTab;
  final bool enableInactiveClientNotifications;
  final int criticalInactiveThresholdDays;

  const ClientSettings({
    this.inactiveClientThresholdDays = 30,
    this.showInactiveClientsTab = true,
    this.enableInactiveClientNotifications = false,
    this.criticalInactiveThresholdDays = 90,
  });

  /// Default settings
  static const ClientSettings defaultSettings = ClientSettings();

  /// Create from JSON
  factory ClientSettings.fromJson(Map<String, dynamic> json) {
    return ClientSettings(
      inactiveClientThresholdDays: json['inactiveClientThresholdDays'] as int? ?? 30,
      showInactiveClientsTab: json['showInactiveClientsTab'] as bool? ?? true,
      enableInactiveClientNotifications: json['enableInactiveClientNotifications'] as bool? ?? false,
      criticalInactiveThresholdDays: json['criticalInactiveThresholdDays'] as int? ?? 90,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'inactiveClientThresholdDays': inactiveClientThresholdDays,
      'showInactiveClientsTab': showInactiveClientsTab,
      'enableInactiveClientNotifications': enableInactiveClientNotifications,
      'criticalInactiveThresholdDays': criticalInactiveThresholdDays,
    };
  }

  /// Copy with method for immutable updates
  ClientSettings copyWith({
    int? inactiveClientThresholdDays,
    bool? showInactiveClientsTab,
    bool? enableInactiveClientNotifications,
    int? criticalInactiveThresholdDays,
  }) {
    return ClientSettings(
      inactiveClientThresholdDays: inactiveClientThresholdDays ?? this.inactiveClientThresholdDays,
      showInactiveClientsTab: showInactiveClientsTab ?? this.showInactiveClientsTab,
      enableInactiveClientNotifications: enableInactiveClientNotifications ?? this.enableInactiveClientNotifications,
      criticalInactiveThresholdDays: criticalInactiveThresholdDays ?? this.criticalInactiveThresholdDays,
    );
  }

  /// Get user-friendly description of the threshold
  String thresholdDescription(BuildContext context) {
    if (inactiveClientThresholdDays == 1) {
      return context.tr('client_settings.period_1_day');
    } else if (inactiveClientThresholdDays < 7) {
      return context.tr('client_settings.period_days', params: {'days': inactiveClientThresholdDays.toString()});
    } else if (inactiveClientThresholdDays == 7) {
      return context.tr('client_settings.period_1_week');
    } else if (inactiveClientThresholdDays < 30) {
      final weeks = (inactiveClientThresholdDays / 7).round();
      return context.tr('client_settings.period_weeks', params: {'weeks': weeks.toString()});
    } else if (inactiveClientThresholdDays == 30) {
      return context.tr('client_settings.period_1_month');
    } else if (inactiveClientThresholdDays < 365) {
      final months = (inactiveClientThresholdDays / 30).round();
      return context.tr('client_settings.period_months', params: {'months': months.toString()});
    } else {
      final years = (inactiveClientThresholdDays / 365).round();
      return context.tr('client_settings.period_years', params: {'years': years.toString()});
    }
  }

  /// Get tab title with threshold
  String tabTitle(BuildContext context) => context.tr('clients.inactive_clients_title') + ' (${thresholdDescription(context)}+)';

  /// Predefined threshold options
  static const List<int> predefinedThresholds = [
    7,   // 1 week
    14,  // 2 weeks
    21,  // 3 weeks
    30,  // 1 month
    45,  // 1.5 months
    60,  // 2 months
    90,  // 3 months
    120, // 4 months
    180, // 6 months
    365, // 1 year
  ];

  /// Get predefined threshold options with descriptions
  static List<Map<String, dynamic>> thresholdOptions(BuildContext context) {
    return predefinedThresholds.map((days) {
      final settings = ClientSettings(inactiveClientThresholdDays: days);
      final description = settings.thresholdDescription(context);
      return {
        'value': days,
        'label': description,
        'description': context.tr('client_settings.threshold_description', params: {'period': description}),
      };
    }).toList();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClientSettings &&
        other.inactiveClientThresholdDays == inactiveClientThresholdDays &&
        other.showInactiveClientsTab == showInactiveClientsTab &&
        other.enableInactiveClientNotifications == enableInactiveClientNotifications &&
        other.criticalInactiveThresholdDays == criticalInactiveThresholdDays;
  }

  @override
  int get hashCode {
    return Object.hash(
      inactiveClientThresholdDays,
      showInactiveClientsTab,
      enableInactiveClientNotifications,
      criticalInactiveThresholdDays,
    );
  }

  @override
  String toString() {
    return 'ClientSettings('
        'inactiveClientThresholdDays: $inactiveClientThresholdDays, '
        'showInactiveClientsTab: $showInactiveClientsTab, '
        'enableInactiveClientNotifications: $enableInactiveClientNotifications, '
        'criticalInactiveThresholdDays: $criticalInactiveThresholdDays'
        ')';
  }
}
