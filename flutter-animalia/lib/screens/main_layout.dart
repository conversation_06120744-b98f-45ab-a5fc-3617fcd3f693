import 'package:animaliaproject/screens/profile/settings/profile_screen.dart';
import 'package:animaliaproject/screens/profile/settings/user_settings_screen.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:showcaseview/showcaseview.dart';

import '../models/user_role.dart';
import '../providers/layout_provider.dart';
import '../providers/role_provider.dart';
import '../services/feature_toggle_service.dart';
import '../services/new_salon_tour_service.dart';
// Legacy tour system removed - using smart tour system only
import '../services/onboarding/onboarding_tour_service.dart';
import '../services/overdue_appointment_service.dart';
import '../services/tour_keys.dart';
import '../widgets/analytics/comprehensive_analytics_mixin.dart';
import '../widgets/admin/impersonation_banner.dart';
import '../widgets/navigation/animated_bottom_navigation.dart';
import '../widgets/navigation/adaptive_navigation.dart';
import '../widgets/common/responsive_layout_wrapper.dart';
import '../l10n/app_localizations.dart';
import 'appointments/calendar_screen.dart';
import 'clients/clients_screen.dart';
import 'notifications_screen.dart';
import 'onboarding/salon_onboarding_screen.dart';

class MainLayout extends StatefulWidget {
  const MainLayout({super.key});

  /// Global key to access state from outside the widget tree
  static final GlobalKey<_MainLayoutState> globalKey =
      GlobalKey<_MainLayoutState>();


  /// Convenience method to open an appointment in the calendar tab
  static void openAppointmentInCalendar(String appointmentId) {
    globalKey.currentState?._openAppointmentInCalendar(appointmentId);
  }

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout>
    with ComprehensiveAnalyticsMixin {
  // Analytics mixin implementation
  @override
  String get screenName => 'main_layout';

  @override
  String get screenCategory => 'navigation';

  @override
  String? get entryPoint => 'app_launch';

  int _currentIndex = 0;
  bool _sellScreenEnabled = false;
  bool? _previousHasSalonAssociation; // Track previous salon association state
  final ValueNotifier<int> _unreadCount = ValueNotifier<int>(0);
  // Legacy tour system removed
  bool _tourNavigationLocked = false; // Prevent automatic navigation during tour
  BuildContext? _showcaseContext; // Store the ShowCaseWidget context

  // Back button handling
  DateTime? _lastBackPressed;
  static const Duration _backPressThreshold = Duration(seconds: 2);

  /// Get the showcase context for external use
  BuildContext? getShowcaseContext() {
    return _showcaseContext;
  }

  @override
  void initState() {
    super.initState();
    _loadFeatureToggles();

    // Initialize overdue appointment service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      OverdueAppointmentService.initialize(context);
      _checkAndStartTour();
    });
  }



  /// Check if tour should be started for new users
  Future<void> _checkAndStartTour() async {
    try {
      // Check if this is a first-time user who should see the tour
      final shouldShowTour = await OnboardingTourService.shouldShowTour();

      if (shouldShowTour && mounted && _showcaseContext != null) {
        DebugLogger.logShowcase('MainLayout: Starting tour for new user');

        // Small delay to ensure UI is ready
        await Future.delayed(const Duration(milliseconds: 1000));

        if (mounted && _showcaseContext != null) {
          startNewSalonTour();
        }
      } else {
        DebugLogger.logShowcase('MainLayout: Tour not needed - shouldShow: $shouldShowTour, mounted: $mounted, context: ${_showcaseContext != null}');
      }
    } catch (e) {
      DebugLogger.logShowcase('MainLayout: Error checking tour status: $e');
    }
  }

  /// Handle page navigation with showcase context
  void _handlePageNavigationWithShowcase(int index, BuildContext showcaseContext) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final roleProvider = context.read<RoleProvider>();
      final layoutProvider = context.read<LayoutProvider>();
      final availableNavItems = _getAvailableNavItems(roleProvider);

      if (index >= availableNavItems.length) return;

      final selectedItem = availableNavItems[index];
      final pageName = selectedItem.label ?? '';

      // Check if we're navigating to calendar by checking the internal label key
      final isCalendar = selectedItem.label == 'Calendar';
      layoutProvider.setCalendarActive(isCalendar);

      DebugLogger.logShowcase('MainLayout: Page navigation to "$pageName", calendar active: $isCalendar');
    });
  }



  Future<void> _loadFeatureToggles() async {
    final enabled = await FeatureToggleService.isSellScreenEnabled();
    if (mounted) {
      setState(() {
        _sellScreenEnabled = enabled;
      });
    }
  }

  @override
  void dispose() {
    OverdueAppointmentService.dispose();
    _unreadCount.dispose();
    super.dispose();
  }

  /// Handle back button press with exit confirmation
  Future<bool> _handleBackButton() async {
    final now = DateTime.now();

    // Track back button usage
    trackBackButton();

    // If we're not on the first tab (calendar/home), navigate to first tab
    if (_currentIndex != 0) {
      setState(() {
        _currentIndex = 0;
      });
      return false; // Don't exit app, just switch to home tab
    }

    // If we're on the home tab, show exit confirmation
    if (_lastBackPressed == null ||
        now.difference(_lastBackPressed!) > _backPressThreshold) {
      _lastBackPressed = now;

      // Show "press back again to exit" message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.tr('navigation.exit_app_message')),
          duration: _backPressThreshold,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
        ),
      );

      return false; // Don't exit yet
    }

    // User pressed back twice within threshold, exit app
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // We'll handle the back button ourselves
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final shouldExit = await _handleBackButton();
          if (shouldExit) {
            // Exit the app properly
            SystemNavigator.pop();
          }
        }
      },
      child: ShowCaseWidget(
      onStart: (index, key) {
        DebugLogger.logShowcase('MainLayout: ShowCaseWidget tour started - step $index');
      },
      onComplete: (index, key) {
        DebugLogger.logShowcase('MainLayout: ShowCaseWidget tour step $index completed');
      },
      onFinish: () async {
        DebugLogger.logShowcase('MainLayout: ShowCaseWidget tour finished');

        // Check if this is part of the new salon tour
        if (NewSalonTourService.isTourActive) {
          NewSalonTourService.onShowcaseFinished();
          return;
        }

        // Mark tour as completed
        await OnboardingTourService.markTourCompleted();
        DebugLogger.logShowcase('MainLayout: Showcase tour finished and marked as completed');
      },
      builder: (showcaseContext) {
        // Store the showcase context for tour initialization
        _showcaseContext = showcaseContext;
        // Store the showcase context for later use
        _showcaseContext = showcaseContext;

        return Consumer<RoleProvider>(
          builder: (context, roleProvider, child) {
          final availableScreens = _getAvailableScreens(roleProvider);
          final availableNavItems = _getAvailableNavItems(roleProvider);

          // Handle salon association changes
          final currentHasSalonAssociation = roleProvider.hasSalonAssociation;

          // Only handle automatic navigation if tour navigation is not locked
          if (!_tourNavigationLocked) {
            // Check if user just gained salon association (completed onboarding)
            if (_previousHasSalonAssociation == false && currentHasSalonAssociation == true) {
              _currentIndex = 0; // Calendar is always first when available
            }

            // Update the previous state for next comparison
            _previousHasSalonAssociation = currentHasSalonAssociation;

            if (!currentHasSalonAssociation) {
              // Find the profile tab index (it's always the last tab)
              final profileIndex = availableScreens.length - 1;
              _currentIndex = profileIndex;
            } else {
              // Ensure current index is valid for available screens
              if (_currentIndex >= availableScreens.length) {
                _currentIndex = 0;
              }
            }
          } else {
            DebugLogger.logShowcase('MainLayout: Navigation locked, skipping automatic tab switching');
          }

          final notificationsIndex =
              availableScreens.indexWhere((s) => s is NotificationsScreen);

          // Use adaptive navigation for responsive design
          if (!roleProvider.hasSalonAssociation) {
            // During onboarding, show simple scaffold without navigation
            return Scaffold(
              body: Column(
                children: [
                  const ImpersonationBanner(),
                  Expanded(
                    child: IndexedStack(
                      index: _currentIndex,
                      children: availableScreens,
                    ),
                  ),
                ],
              ),
            );
          }

          // Main app with adaptive navigation
          return AdaptiveNavigation(
            currentIndex: _currentIndex,
            onTap: (index) {
              // Track tab navigation
              final tabNames = ['calendar', 'clients', 'notifications', 'profile'];
              if (index < tabNames.length) {
                trackButtonClick('tab_navigation', context: {
                  'tab_name': tabNames[index],
                  'previous_tab': _currentIndex < tabNames.length ? tabNames[_currentIndex] : 'unknown',
                  'tab_index': index,
                });

                trackNavigationTo('${tabNames[index]}_tab', method: 'tab_navigation');
              }

              setState(() => _currentIndex = index);
              if (index == notificationsIndex) {
                NotificationsScreen.loadHistory();
              }

              // Handle page navigation with showcase context
              _handlePageNavigationWithShowcase(index, showcaseContext);
            },
            items: _buildAnimatedNavItems(availableNavItems),
            body: Column(
              children: [
                const ImpersonationBanner(),
                Expanded(
                  child: IndexedStack(
                    index: _currentIndex,
                    children: availableScreens,
                  ),
                ),
              ],
            ),
          );
          },
        );
      },
      ),
    );
  }

  List<Widget> _getAvailableScreens(RoleProvider roleProvider) {
    final List<Widget> screens = [];

    // If user doesn't have salon association, show onboarding screen only
    if (!roleProvider.hasSalonAssociation) {
      screens.add(const SalonOnboardingScreen());
      return screens;
    }

    // Calendar is only available if user has salon association
    if (roleProvider.hasSalonAssociation) {
      screens.add(CalendarScreen(key: CalendarScreen.globalKey));
    }

    // Clients screen - requires client data access AND salon association
    if (roleProvider.hasSalonAssociation && roleProvider.canAccessClientData) {
      screens.add(const ClientsScreen());
    }

    // Notifications is always available
    screens.add(NotificationsScreen(
      key: NotificationsScreen.globalKey,
      unreadCountNotifier: _unreadCount,
    ));

    // Profile is only available for chief groomers
    if (roleProvider.permissions?.groomerRole == GroomerRole.chiefGroomer) {
      screens.add(const ProfileScreen());
    }

    // Settings is always available
    screens.add(const UserSettingsScreen());
    return screens;
  }

  List<BottomNavigationBarItem> _getAvailableNavItems(RoleProvider roleProvider) {
    final List<BottomNavigationBarItem> items = [];

    // Calendar is only available if user has salon association
    if (roleProvider.hasSalonAssociation) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.calendar_today),
        label: 'Calendar',
      ));
    }

    // Clients screen - requires client data access AND salon association
    if (roleProvider.hasSalonAssociation && roleProvider.canAccessClientData) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.people),
        label: 'Clients',
      ));
    }

    // Sales screen - requires management access, salon association and toggle
    if (_sellScreenEnabled &&
        roleProvider.hasSalonAssociation &&
        roleProvider.canViewSalesData()) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.shopping_cart),
        label: 'Sales',
      ));
    }

    // Notifications is always available
    items.add(BottomNavigationBarItem(
      icon: _buildNotificationsIcon(),
      label: 'Notifications',
    ));

    // Profile is only available for chief groomers
    if (roleProvider.permissions?.groomerRole == GroomerRole.chiefGroomer) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.person),
        label: 'Salon',
      ));
    }

    // Settings is always available
    items.add(const BottomNavigationBarItem(
      icon: Icon(Icons.settings),
      label: 'Settings',
    ));

    return items;
  }

  /// Build animated navigation items with iOS-style icons and proper internationalized labels
  List<AnimatedBottomNavigationItem> _buildAnimatedNavItems(List<BottomNavigationBarItem> items) {
    final animatedItems = <AnimatedBottomNavigationItem>[];

    for (final item in items) {
      late IconData icon;
      late IconData activeIcon;
      late String label;
      Widget? badge;
      GlobalKey? showcaseKey;

      // Map to iOS-style icons, internationalized labels, and tour keys
      switch (item.label) {
        case 'Calendar':
          icon = CupertinoIcons.calendar;
          activeIcon = CupertinoIcons.calendar_today;
          label = context.tr('navigation.calendar');
          showcaseKey = TourKeys.calendarTabKey;
          break;
        case 'Clients':
          icon = CupertinoIcons.person_2;
          activeIcon = CupertinoIcons.person_2_fill;
          label = context.tr('navigation.clients');
          showcaseKey = TourKeys.clientsTabKey;
          break;
        case 'Sales':
          icon = CupertinoIcons.shopping_cart;
          activeIcon = CupertinoIcons.cart_fill;
          label = context.tr('navigation.sales');
          break;
        case 'Notifications':
          icon = CupertinoIcons.bell;
          activeIcon = CupertinoIcons.bell_fill;
          label = context.tr('navigation.notifications');
          showcaseKey = TourKeys.notificationsTabKey;
          // Check if there are unread notifications
          final hasUnread = _unreadCount.value > 0;
          badge = hasUnread ? _buildNotificationsBadge() : null;
          break;
        case 'Salon':
          icon = CupertinoIcons.building_2_fill;
          activeIcon = CupertinoIcons.building_2_fill;
          label = context.tr('navigation.salon');
          showcaseKey = TourKeys.salonTabKey;
          break;
        case 'Settings':
          icon = CupertinoIcons.profile_circled;
          activeIcon = CupertinoIcons.profile_circled;
          label = context.tr('navigation.settings');
          showcaseKey = TourKeys.settingsTabKey;
          break;
        default:
          icon = CupertinoIcons.circle;
          activeIcon = CupertinoIcons.circle_fill;
          label = item.label ?? '';
          showcaseKey = null;
      }

      animatedItems.add(AnimatedBottomNavigationItem(
        icon: icon,
        activeIcon: activeIcon,
        label: label,
        badge: badge,
        showcaseKey: showcaseKey,
      ));
    }

    return animatedItems;
  }


  Widget _buildNotificationsIcon() {
    return ValueListenableBuilder<int>(
      valueListenable: _unreadCount,
      builder: (context, count, child) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            const Icon(Icons.notifications),
            if (count > 0)
              Positioned(
                right: -6,
                top: -4,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
                  child: Text(
                    '$count',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// Build notifications badge for the animated navigation
  Widget _buildNotificationsBadge() {
    return ValueListenableBuilder<int>(
      valueListenable: _unreadCount,
      builder: (context, count, child) {
        if (count <= 0) return const SizedBox.shrink();

        return Container(
          padding: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(8),
          ),
          constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
          child: Text(
            '$count',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }

  /// Switch to the calendar tab and open the given appointment
  void _openAppointmentInCalendar(String appointmentId) {
    setState(() {
      _currentIndex = 0; // Calendar tab is always first when available
    });
    CalendarScreen.openAppointment(appointmentId);
  }

  /// Navigation helper methods for tour
  void switchToSettingsTab() {
    final roleProvider = context.read<RoleProvider>();
    final availableNavItems = _getAvailableNavItems(roleProvider);

    // Find settings tab index (always last)
    final settingsIndex = availableNavItems.length - 1;
    setState(() {
      _currentIndex = settingsIndex;
    });
  }

  void switchToCalendarTab() {
    setState(() {
      _currentIndex = 0; // Calendar is always first when available
    });
  }

  void switchToClientsTab() {
    final roleProvider = context.read<RoleProvider>();
    if (roleProvider.hasSalonAssociation && roleProvider.canAccessClientData) {
      setState(() {
        _currentIndex = 1; // Clients is typically second
      });
    }
  }

  void switchToProfileTab() {
    final roleProvider = context.read<RoleProvider>();
    final availableNavItems = _getAvailableNavItems(roleProvider);

    // Find profile/salon tab index
    int profileIndex = -1;
    for (int i = 0; i < availableNavItems.length; i++) {
      if (availableNavItems[i].label == 'Salon') {
        profileIndex = i;
        break;
      }
    }

    if (profileIndex >= 0) {
      setState(() {
        _currentIndex = profileIndex;
        _tourNavigationLocked = true; // Lock navigation during tour
      });
      DebugLogger.logShowcase('MainLayout: Switched to profile tab and locked navigation');
    }
  }

  /// Lock navigation during tour
  void lockTourNavigation() {
    _tourNavigationLocked = true;
    DebugLogger.logShowcase('MainLayout: Navigation locked for tour');
  }

  /// Unlock navigation after tour
  void unlockTourNavigation() {
    _tourNavigationLocked = false;
    DebugLogger.logShowcase('MainLayout: Navigation unlocked after tour');
  }

  void switchToNotificationsTab() {
    final roleProvider = context.read<RoleProvider>();
    final availableNavItems = _getAvailableNavItems(roleProvider);

    // Find notifications tab index
    int notificationsIndex = -1;
    for (int i = 0; i < availableNavItems.length; i++) {
      if (availableNavItems[i].label == 'Notificari') {
        notificationsIndex = i;
        break;
      }
    }

    if (notificationsIndex >= 0) {
      setState(() {
        _currentIndex = notificationsIndex;
      });
    }
  }

  /// Navigate to specific tab by index
  void navigateToTab(int index) {
    final roleProvider = context.read<RoleProvider>();
    final availableScreens = _getAvailableScreens(roleProvider);

    if (index >= 0 && index < availableScreens.length) {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  /// Start the new salon tour with the correct ShowCaseWidget context
  void startNewSalonTour() {
    DebugLogger.logVerbose('🎯 MainLayout: Starting new salon tour...');

    if (_showcaseContext != null && _showcaseContext!.mounted) {
      DebugLogger.logVerbose('🎯 MainLayout: Using stored ShowCaseWidget context');
      NewSalonTourService.startNewSalonTour(_showcaseContext!);
    } else {
      DebugLogger.logVerbose('🎯 MainLayout: ShowCaseWidget context not available');
      // Try to find it in the next frame
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _showcaseContext != null && _showcaseContext!.mounted) {
          DebugLogger.logVerbose('🎯 MainLayout: Retrying with ShowCaseWidget context');
          NewSalonTourService.startNewSalonTour(_showcaseContext!);
        }
      });
    }
  }

}
