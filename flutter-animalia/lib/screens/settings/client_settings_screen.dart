import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/client_settings.dart';
import '../../providers/client_settings_provider.dart';
import '../../widgets/common/loading_widget.dart';

class ClientSettingsScreen extends StatefulWidget {
  const ClientSettingsScreen({super.key});

  @override
  State<ClientSettingsScreen> createState() => _ClientSettingsScreenState();
}

class _ClientSettingsScreenState extends State<ClientSettingsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ClientSettingsProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('client_settings.title')),
        elevation: 0,
      ),
      body: Consumer<ClientSettingsProvider>(
        builder: (context, provider, child) {
          if (!provider.isInitialized) {
            return const Center(child: LoadingWidget());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppDimensions.spacingStandard),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInactiveClientsSection(provider),
                const SizedBox(height: AppDimensions.spacingLarge),
                _buildNotificationsSection(provider),
                const SizedBox(height: AppDimensions.spacingLarge),
                _buildAdvancedSection(provider),
                const SizedBox(height: AppDimensions.spacingLarge),
                _buildResetSection(provider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInactiveClientsSection(ClientSettingsProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.spacingStandard),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppDimensions.spacingSmall),
                Text(
                  context.tr('client_settings.inactive_clients_title'),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacingSmall),
            Text(
              context.tr('client_settings.inactive_clients_subtitle'),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingStandard),

            // Threshold selector
            _buildThresholdSelector(provider),
            const SizedBox(height: AppDimensions.spacingStandard),
          ],
        ),
      ),
    );
  }

  Widget _buildThresholdSelector(ClientSettingsProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('client_settings.threshold_period_title'),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingSmall),
        Text(
          context.tr('client_settings.threshold_description', params: {'period': provider.settings.thresholdDescription(context)}),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingStandard),

        // Predefined options
        Wrap(
          spacing: AppDimensions.spacingSmall,
          runSpacing: AppDimensions.spacingSmall,
          children: ClientSettings.thresholdOptions(context).map((option) {
            final isSelected = provider.inactiveClientThresholdDays == option['value'];
            return FilterChip(
              label: Text(option['label']),
              selected: isSelected,
              onSelected: provider.isLoading ? null : (selected) {
                if (selected) {
                  provider.setInactiveClientThreshold(option['value']);
                }
              },
              selectedColor: Theme.of(context).colorScheme.primaryContainer,
              checkmarkColor: Theme.of(context).colorScheme.onPrimaryContainer,
            );
          }).toList(),
        ),

        const SizedBox(height: AppDimensions.spacingStandard),

        // Custom threshold input
        Row(
          children: [
            Expanded(
              child: TextFormField(
                initialValue: provider.inactiveClientThresholdDays.toString(),
                decoration: InputDecoration(
                  labelText: context.tr('client_settings.custom_days_label'),
                  hintText: context.tr('client_settings.custom_days_hint'),
                  suffixText: context.tr('client_settings.custom_days_suffix'),
                ),
                keyboardType: TextInputType.number,
                enabled: !provider.isLoading,
                onFieldSubmitted: (value) {
                  final days = int.tryParse(value);
                  if (days != null && days >= 1 && days <= 365) {
                    provider.setInactiveClientThreshold(days);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(context.tr('client_settings.custom_days_error')),
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNotificationsSection(ClientSettingsProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.spacingStandard),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.notifications,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppDimensions.spacingSmall),
                Text(
                  context.tr('client_settings.notifications_title'),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacingSmall),
            Text(
              context.tr('client_settings.notifications_subtitle'),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingStandard),

            // TODO: Implement notification system
            // SwitchListTile(
            //   title: const Text('Notificări pentru clienți inactivi'),
            //   subtitle: const Text('Primește notificări când clienții devin inactivi'),
            //   value: provider.enableInactiveClientNotifications,
            //   onChanged: provider.isLoading ? null : (value) {
            //     provider.setEnableInactiveClientNotifications(value);
            //   },
            // ),

            Container(
              padding: const EdgeInsets.all(AppDimensions.spacingStandard),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                  const SizedBox(width: AppDimensions.spacingSmall),
                  Expanded(
                    child: Text(
                      context.tr('client_settings.notifications_coming_soon'),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSection(ClientSettingsProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.spacingStandard),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppDimensions.spacingSmall),
                Text(
                  context.tr('client_settings.advanced_title'),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacingStandard),

            // TODO: Implement critical threshold business logic
            // ListTile(
            //   title: const Text('Prag critic de inactivitate'),
            //   subtitle: Text('${provider.criticalInactiveThresholdDays} zile - clienții cu prioritate critică'),
            //   trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            //   onTap: () => _showCriticalThresholdDialog(provider),
            // ),

            Container(
              padding: const EdgeInsets.all(AppDimensions.spacingStandard),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                  const SizedBox(width: AppDimensions.spacingSmall),
                  Expanded(
                    child: Text(
                      context.tr('client_settings.advanced_coming_soon'),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResetSection(ClientSettingsProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.spacingStandard),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.restore,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(width: AppDimensions.spacingSmall),
                Text(
                  context.tr('client_settings.reset_title'),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacingStandard),

            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: provider.isLoading ? null : () => _showResetDialog(provider),
                icon: const Icon(Icons.restore),
                label: Text(context.tr('client_settings.reset_button')),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                  side: BorderSide(color: Theme.of(context).colorScheme.error),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCriticalThresholdDialog(ClientSettingsProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('client_settings.critical_threshold_title')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(context.tr('client_settings.critical_threshold_description')),
            const SizedBox(height: 16),
            TextFormField(
              initialValue: provider.criticalInactiveThresholdDays.toString(),
              decoration: InputDecoration(
                labelText: context.tr('client_settings.critical_threshold_label'),
                suffixText: context.tr('client_settings.custom_days_suffix'),
              ),
              keyboardType: TextInputType.number,
              onFieldSubmitted: (value) {
                final days = int.tryParse(value);
                if (days != null && days >= 1 && days <= 365) {
                  provider.setCriticalInactiveThreshold(days);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('client_settings.cancel')),
          ),
        ],
      ),
    );
  }

  void _showResetDialog(ClientSettingsProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('client_settings.reset_dialog_title')),
        content: Text(context.tr('client_settings.reset_dialog_message')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('client_settings.cancel')),
          ),
          TextButton(
            onPressed: () {
              provider.resetToDefaults();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(context.tr('client_settings.reset_success'))),
              );
            },
            child: Text(context.tr('client_settings.reset')),
          ),
        ],
      ),
    );
  }
}
