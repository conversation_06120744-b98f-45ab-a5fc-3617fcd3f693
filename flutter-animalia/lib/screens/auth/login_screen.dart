import 'package:flutter/material.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../widgets/language_switcher.dart';
import '../../widgets/social_login_buttons.dart';
import '../../widgets/common/responsive_layout_wrapper.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _logoController;
  late AnimationController _cardController;

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _cardController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Start animations
    _startAnimationSequence();
  }

  void _startAnimationSequence() async {
    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) _logoController.forward();
    await Future.delayed(const Duration(milliseconds: 800));
    if (mounted) _cardController.forward();
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _logoController.dispose();
    _cardController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // Main content with responsive layout
          SafeArea(
            child: Center(
              child: SingleChildScrollView(
                child: ResponsiveFormWrapper(
                  child: AnimationLimiter(
                      child: Column(
                        children: AnimationConfiguration.toStaggeredList(
                          duration: const Duration(milliseconds: 600),
                          childAnimationBuilder: (widget) => SlideAnimation(
                            verticalOffset: 50.0,
                            child: FadeInAnimation(child: widget),
                          ),
                          children: [
                            // Animated pet characters

                            const SizedBox(height: 20),

                            // Main login card
                            _buildAnimatedLoginCard(colorScheme, isDark),
                          ],
                        ),
                      ),
                    ),
                  ),
              ),
            ),
          ),

          // Language switcher in top-right corner
          Positioned(
            top: 16,
            right: 16,
            child: const LanguageSwitcher(
              showLabel: false,
              useDropdown: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedLoginCard(ColorScheme colorScheme, bool isDark) {
    return AnimatedBuilder(
      animation: _cardController,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (_cardController.value * 0.2),
          child: Opacity(
            opacity: _cardController.value,
            child: Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              color: colorScheme.surface,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: isDark
                        ? [
                            AppColors.darkSurface,
                            AppColors.darkSurfaceAlt,
                          ]
                        : [
                            AppColors.pureWhite,
                            AppColors.salonWhite.withValues(alpha: 0.8),
                          ],
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildAnimatedLogo(isDark),
                      const SizedBox(height: 24),
                      _buildAnimatedWelcomeText(isDark),
                      const SizedBox(height: 16),
                      _buildAnimatedSubtitle(isDark),
                      const SizedBox(height: 32),
                      const SocialLoginButtons(),
                    ],
                  ),
                ),
              ),
            ),
          ));
      },
    );
  }

  Widget _buildAnimatedLogo(bool isDark) {
    return AnimatedBuilder(
      animation: _logoController,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.5 + (_logoController.value * 0.5),
          child: Transform.rotate(
            angle: (1 - _logoController.value) * 0.1,
            child: Opacity(
              opacity: _logoController.value,
              child: Container(
                height: 180,
                width: 180,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Image.asset(isDark
                      ? 'assets/images/logo-no_bg-dark.png'
                      : 'assets/images/logo-no_bg.png',
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          ));
      },
    );
  }

  Widget _buildAnimatedWelcomeText(bool isDark) {
    return AnimatedTextKit(
      animatedTexts: [
        WavyAnimatedText(
          context.tr('login.welcome'),
          textAlign: TextAlign.center,
          textStyle: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: isDark ? AppColors.darkText : AppColors.white,
          ),
          speed: const Duration(milliseconds: 100),
        ),
      ],
      totalRepeatCount: 1,
      pause: const Duration(milliseconds: 1000),
    );
  }

  Widget _buildAnimatedSubtitle(bool isDark) {
    return Text(
      context.tr('login.subtitle'),
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        height: 1.4,
        color: isDark ? AppColors.darkTextSecondary : AppColors.secondaryText,
      ),
    );
  }
}
