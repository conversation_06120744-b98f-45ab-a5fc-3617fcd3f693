import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';

import '../../models/salon_subscription.dart';
import '../../providers/subscription_provider.dart';
import '../../services/auth/auth_service.dart';

import '../../services/subscription/stripe_subscription_service.dart';
import '../../services/ui_notification_service.dart';
import '../../services/url_launcher_service.dart';
import '../../utils/debug_logger.dart';
import '../../widgets/common/custom_bottom_sheet.dart';
import '../../l10n/app_localizations.dart';

/// Screen for purchasing subscription packages
class SubscriptionPurchaseScreen extends StatefulWidget {
  final String salonId;
  final bool showTrialOption;
  final SubscriptionTier? initialTier;

  const SubscriptionPurchaseScreen({
    super.key,
    required this.salonId,
    this.showTrialOption = true,
    this.initialTier,
  });

  @override
  State<SubscriptionPurchaseScreen> createState() => _SubscriptionPurchaseScreenState();
}

class _SubscriptionPurchaseScreenState extends State<SubscriptionPurchaseScreen>
    with TickerProviderStateMixin {
  bool _isAnnualBilling = true; // Default to annual for 2 months free
  SubscriptionTier? _selectedTier;
  late TabController _tabController;
  bool _isShowingCancellationDialog = false; // Track if cancellation dialog is shown

  // Legal compliance URLs - Platform-specific requirements
  static const String _privacyPolicyUrl = 'https://animalia-programari.ro/privacy-policy.html';

  // Apple App Store EULA requirement - use Apple's standard EULA for iOS
  static const String _appleEulaUrl = 'https://www.apple.com/legal/internet-services/itunes/dev/stdeula/';

  // Custom Terms of Service for web and Android
  static const String _customTermsUrl = 'https://animalia-programari.ro/terms-of-service.html';

  // Subscription tiers in order - including Free for tabs
  static const List<SubscriptionTier> _tiers = [
    SubscriptionTier.free,
    SubscriptionTier.freelancer,
    SubscriptionTier.team,
    SubscriptionTier.enterprise,
  ];

  @override
  void initState() {
    super.initState();

    // Determine initial index based on initialTier parameter or default to Team
    final int initialIndex = widget.initialTier != null
        ? _tiers.indexOf(widget.initialTier!)
        : 2; // Default to Team (index 2) if no initialTier specified

    // Initialize tab controller with 4 tabs (Free, Freelancer, Team, Enterprise)
    _tabController = TabController(
      length: 4,
      vsync: this,
      initialIndex: initialIndex >= 0 ? initialIndex : 2, // Fallback to Team if tier not found
    );

    // Set initial selected tier based on initialTier parameter or default to Team
    _selectedTier = widget.initialTier ?? SubscriptionTier.team;

    // Listen to tab changes
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        setState(() {
          _selectedTier = _tiers[_tabController.index];
        });
      }
    });

    // Use addPostFrameCallback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSubscriptions();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeSubscriptions() async {
    final subscriptionProvider = context.read<SubscriptionProvider>();

    if (!subscriptionProvider.isInitialized) {
      final userId = await AuthService.getCurrentUserId();
      if (userId != null) {
        await subscriptionProvider.initializeWithUserId(userId);
      }
    }

    // Load current subscription for user (applies to all their salons)
    await subscriptionProvider.loadUserSubscription();

    // IMPORTANT: Do NOT override the tab if initialTier was explicitly provided
    // Only auto-navigate to current tier when opening subscription screen without context
    if (mounted && widget.initialTier == null && subscriptionProvider.currentTier != null) {
      final currentTier = subscriptionProvider.currentTier!;
      final currentTierIndex = _tiers.indexOf(currentTier);
      setState(() {
        _selectedTier = currentTier;
      });
      // Update tab controller to show current tier
      if (currentTierIndex >= 0) {
        _tabController.animateTo(currentTierIndex);
      }
    }
    // If initialTier was provided, keep it - don't change tabs
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          context.tr('subscription_plans.title'),
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        centerTitle: true,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(80),
          child: Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              tabs: _tiers.map((tier) => Tab(
                height: 80,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getTierColor(tier).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(_getTierIcon(tier), size: 20),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      tier.name,
                      style: const TextStyle(fontSize: 11, fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
              )).toList(),
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey.shade600,
              indicatorColor: Theme.of(context).primaryColor,
              indicatorWeight: 3,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.grey.shade200,
            ),
          ),
        ),
      ),
      body: Consumer<SubscriptionProvider>(
        builder: (context, subscriptionProvider, child) {
          if (subscriptionProvider.isLoading) {
            return LoadingWidget(
              message: context.tr('subscription_plans.loading_plans'),
            );
          }

          // Don't show error state if we're showing cancellation dialog
          if (subscriptionProvider.hasError && !_isShowingCancellationDialog) {
            return _buildErrorState(subscriptionProvider.error!);
          }

          return _buildTabBasedSubscriptionInterface(subscriptionProvider);
        },
      ),
    );
  }

  Widget _buildTabBasedSubscriptionInterface(SubscriptionProvider subscriptionProvider) {
    return Column(
      children: [
        // Main content area with tab view
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: _tiers.map((tier) => _buildTierTabContent(tier, subscriptionProvider)).toList(),
          ),
        ),

        // Purchase section with billing toggle
        _buildModernPurchaseSection(subscriptionProvider),
      ],
    );
  }

  Widget _buildTierTabContent(SubscriptionTier tier, SubscriptionProvider subscriptionProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16), // Reduced top padding since card has margin now
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600), // Constrain width for better readability
        child: Center(
          child: _buildModernSubscriptionCard(
            tier,
            subscriptionProvider,
            isCenter: true, // Always center in tab view
            isPopular: tier == SubscriptionTier.team,
          ),
        ),
      ),
    );
  }



  /// Discrete bottom billing toggle
  Widget _buildBottomBillingToggle() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildDiscreteBillingOption(
              text: context.tr('subscription_plans.billing_monthly'),
              isSelected: !_isAnnualBilling,
              onTap: () => setState(() => _isAnnualBilling = false),
            ),
          ),
          Expanded(
            child: _buildDiscreteBillingOption(
              text: context.tr('subscription_plans.billing_annual'),
              isSelected: _isAnnualBilling,
              onTap: () => setState(() => _isAnnualBilling = true),
            ),
          )
        ],
      ),
    );
  }

  /// Discrete billing option
  Widget _buildDiscreteBillingOption({
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          boxShadow: isSelected ? [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Text(
          text,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected ? Colors.black87 : Colors.grey.shade600,
            fontSize: 14,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }









  /// Modern subscription card with enhanced design
  Widget _buildModernSubscriptionCard(
      SubscriptionTier tier,
      SubscriptionProvider subscriptionProvider, {
        required bool isCenter,
        bool isPopular = false,
      }) {
    final isCurrentSubscription = subscriptionProvider.currentTier == tier;
    final tierColor = _getTierColor(tier);

    return Container(
      margin: const EdgeInsets.only(top: 16), // Space for popular badge
      child: Stack(
        clipBehavior: Clip.none, // Allow badge to overflow
        children: [
          // Main card
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
              border: isCenter
                  ? Border.all(color: tierColor, width: 2)
                  : Border.all(color: Colors.grey.shade200),
              boxShadow: [
                BoxShadow(
                  color: isCenter
                      ? tierColor.withValues(alpha: 0.2)
                      : Colors.black.withValues(alpha: 0.08),
                  blurRadius: isCenter ? 20 : 12,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  _buildCardHeader(tier, tierColor, isCurrentSubscription),
                  const SizedBox(height: 16),

                  // Pricing
                  _buildCardPricing(tier, tierColor, isCurrentSubscription: isCurrentSubscription),
                  const SizedBox(height: 20),

                  // Features
                  _buildCardFeatures(tier),
                ],
              ),
            ),
          ),

          // Popular badge
          if (isPopular) _buildPopularBadge(),

          // Current subscription badge
          if (isCurrentSubscription) _buildCurrentBadge(tierColor),
        ],
      ),
    );
  }

  /// Card header with tier info
  Widget _buildCardHeader(SubscriptionTier tier, Color tierColor, bool isCurrentSubscription) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: tierColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(
            _getTierIcon(tier),
            color: tierColor,
            size: 28,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                tier.name,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: tierColor,
                ),
              ),
              Text(
                isCurrentSubscription ? context.tr('subscription_plans.active_plan') : context.tr(tier.subtitleKey),
                style: TextStyle(
                  fontSize: 14,
                  color: isCurrentSubscription ? tierColor : Colors.grey.shade600,
                  fontWeight: isCurrentSubscription ? FontWeight.w600 : FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Card pricing section
  Widget _buildCardPricing(SubscriptionTier tier, Color tierColor, {bool isCurrentSubscription = false}) {
    // Use platform-specific pricing
    final monthlyPrice = kIsWeb ? tier.webMonthlyPrice : tier.monthlyPrice;
    final annualPrice = kIsWeb ? tier.webAnnualPrice : tier.annualPrice;

    final price = _isAnnualBilling ? annualPrice : monthlyPrice;
    final monthlyEquivalent = _isAnnualBilling ? annualPrice / 12 : monthlyPrice;
    final monthlySavings = _isAnnualBilling ? monthlyPrice - monthlyEquivalent : 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_isAnnualBilling) ...[
          // For annual billing: Show monthly equivalent BIG
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${monthlyEquivalent.toInt()}',
                style: TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: tierColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'RON',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: tierColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '/${context.tr('subscription_plans.per_month')}',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          // Show annual total and original monthly price
          Row(
            children: [
              Text(
                '${price.toInt()} RON/${context.tr('subscription_plans.per_year')}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '(${monthlyPrice.toInt()} RON/${context.tr('subscription_plans.per_month')})',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                  decoration: TextDecoration.lineThrough,
                ),
              ),
            ],
          ),
        ] else ...[
          // For monthly billing: Show monthly price BIG
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${price.toInt()}',
                style: TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: tierColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'RON',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: tierColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '/${context.tr('subscription_plans.per_month')}',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ],

        if (_isAnnualBilling) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isCurrentSubscription ? tierColor.withValues(alpha: 0.1) : Colors.green.shade100,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              context.tr('subscription_plans.savings_message', params: {
                'amount': monthlySavings.toInt().toString(),
              }),
              style: TextStyle(
                color: isCurrentSubscription ? tierColor : Colors.green.shade700,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],

        // Special message for current subscription
        if (isCurrentSubscription) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  tierColor.withValues(alpha: 0.1),
                  tierColor.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: tierColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.favorite,
                  color: tierColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    context.tr('subscription_plans.current_plan_message'),
                    style: TextStyle(
                      color: tierColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      height: 1.3,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Card features section
  Widget _buildCardFeatures(SubscriptionTier tier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Key benefits highlight
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _getTierColor(tier).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getTierColor(tier).withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildBenefitItem(
                  Icons.group,
                  tier.maxStaff == -1 
                    ? context.tr('subscription_plans.benefit_staff_unlimited')
                    : tier.maxStaff == 1 
                      ? context.tr('subscription_plans.benefit_staff_single')
                      : context.tr('subscription_plans.benefit_staff_count', params: {'count': tier.maxStaff.toString()}),
                  _getTierColor(tier)
              ),
              _buildBenefitItem(
                  Icons.sms,
                  tier.smsQuota == 0 
                    ? context.tr('subscription_plans.benefit_sms_none')
                    : context.tr('subscription_plans.benefit_sms_count', params: {'count': tier.smsQuota.toString()}),
                  _getTierColor(tier)
              ),
              if (tier == SubscriptionTier.enterprise)
                _buildBenefitItem(Icons.business, context.tr('subscription_plans.benefit_salons_multiple'), _getTierColor(tier))
              else
                _buildBenefitItem(Icons.pets, context.tr('subscription_plans.benefit_clients_unlimited'), _getTierColor(tier)),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Features list
        Text(
          _getFeatureTitle(tier),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade800,
          ),
        ),
        const SizedBox(height: 8),
        ...tier.featureKeys.map((featureKey) => Padding(
          padding: const EdgeInsets.only(bottom: 6),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 2),
                width: 14,
                height: 14,
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(7),
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 10,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  context.tr(featureKey),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade700,
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  String _getFeatureTitle(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return context.tr('subscription_plans.tier_features_free');
      case SubscriptionTier.freelancer:
        return context.tr('subscription_plans.tier_features_freelancer');
      case SubscriptionTier.team:
        return context.tr('subscription_plans.tier_features_team');
      case SubscriptionTier.enterprise:
        return context.tr('subscription_plans.tier_features_enterprise');
    }
  }

  Widget _buildBenefitItem(IconData icon, String text, Color color) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 6),
          Text(
            text,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Popular badge with improved styling
  Widget _buildPopularBadge() {
    return Positioned(
      top: -12,
      right: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.orange.shade400, Colors.orange.shade600],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.withValues(alpha: 0.4),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.star,
              color: Colors.white,
              size: 14,
            ),
            const SizedBox(width: 4),
            Text(
              context.tr('subscription_plans.popular_badge'),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 11,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Current subscription badge
  Widget _buildCurrentBadge(Color tierColor) {
    return Positioned(
      top: 16,
      left: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: tierColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          context.tr('subscription_plans.current_plan_badge'),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// Modern purchase section with billing toggle and responsive design
  Widget _buildModernPurchaseSection(SubscriptionProvider subscriptionProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -8),
          ),
        ],
      ),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600), // Constrain width for better UX
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Billing toggle buttons (only show for paid tiers)
            if (_selectedTier != SubscriptionTier.free) ...[
              _buildBottomBillingToggle(),
              const SizedBox(height: 16),
            ],

            // Purchase button (hide for Free tier)
            if (_selectedTier != SubscriptionTier.free)
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: _selectedTier != null && !subscriptionProvider.isLoading
                      ? () => _purchaseSubscription(subscriptionProvider)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _selectedTier != null
                        ? (_selectedTier == subscriptionProvider.currentTier
                        ? Colors.grey.shade600
                        : _getTierColor(_selectedTier!))
                        : Colors.grey.shade400,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                  ),
                  child: subscriptionProvider.isLoading
                      ? const SizedBox(
                    height: 24,
                    width: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                      : Text(
                    _selectedTier == subscriptionProvider.currentTier
                        ? '✨ ${context.tr('subscription_plans.active_plan')} ✨'
                        : _getUpgradeButtonText(subscriptionProvider),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

            // Cancel subscription button (only show for active subscriptions)
            if (_shouldShowCancelButton(subscriptionProvider)) ...[
              const SizedBox(height: 12),
              _buildCancelSubscriptionButton(subscriptionProvider),
            ],

            // Free tier message
            if (_selectedTier == SubscriptionTier.free)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _getTierColor(SubscriptionTier.free).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: _getTierColor(SubscriptionTier.free).withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.favorite,
                      color: _getTierColor(SubscriptionTier.free),
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      context.tr('subscription_plans.free_plan_title'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: _getTierColor(SubscriptionTier.free),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      context.tr('subscription_plans.free_plan_subtitle'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 12),

            // Apple App Store required subscription information
            _buildSubscriptionDisclosure(),

            const SizedBox(height: 12),

            // Compact footer links - Apple App Store compliance s
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton(
                      onPressed: _openTermsOfUseEula,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        _getPlatformTermsDisplayName(),
                        style: TextStyle(
                          fontSize: 10,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                    Text(
                      '•',
                      style: TextStyle(color: Colors.grey.shade400, fontSize: 10),
                    ),
                    TextButton(
                      onPressed: () => _restorePurchases(subscriptionProvider),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        context.tr('subscription_plans.restore_purchases'),
                        style: TextStyle(
                          fontSize: 10,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                    Text(
                      '•',
                      style: TextStyle(color: Colors.grey.shade400, fontSize: 10),
                    ),
                    TextButton(
                      onPressed: _openPrivacyPolicy,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        context.tr('subscription_plans.privacy_policy'),
                        style: TextStyle(
                          fontSize: 10,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    )
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Get tier-specific color with enhanced modern palette
  Color _getTierColor(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return const Color(0xFF4CAF50); // Modern green
      case SubscriptionTier.freelancer:
        return const Color(0xFFCD7F32); // Bronze color
      case SubscriptionTier.team:
        return const Color(0xFF2196F3); // Blue color
      case SubscriptionTier.enterprise:
        return const Color(0xFFefbf04); // Gold color
    }
  }

  /// Get tier-specific icon with better visual hierarchy
  IconData _getTierIcon(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return Icons.favorite_border; // Heart outline for free
      case SubscriptionTier.freelancer:
        return Icons.person; // Single person for freelancer
      case SubscriptionTier.team:
        return Icons.group; // Group for team
      case SubscriptionTier.enterprise:
        return Icons.business; // Business building for enterprise
    }
  }

  /// Get appropriate button text for upgrade/downgrade
  String _getUpgradeButtonText(SubscriptionProvider subscriptionProvider) {
    if (subscriptionProvider.currentTier == null) {
      return context.tr('subscription_plans.trial_button');
    }

    final currentTier = subscriptionProvider.currentTier!;
    final selectedTier = _selectedTier!;

    final currentIndex = _tiers.indexOf(currentTier);
    final selectedIndex = _tiers.indexOf(selectedTier);

    if (selectedIndex > currentIndex) {
      return context.tr('subscription_plans.upgrade_button', params: {'tier': selectedTier.name});
    } else if (selectedIndex < currentIndex) {
      return context.tr('subscription_plans.downgrade_button', params: {'tier': selectedTier.name});
    } else {
      return context.tr('subscription_plans.current_plan_button');
    }
  }

  /// Error state widget
  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              context.tr('subscription_plans.error_loading_plans'),
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _initializeSubscriptions,
              child: Text(context.tr('subscription_plans.try_again')),
            ),
          ],
        ),
      ),
    );
  }

  /// Purchase subscription with enhanced cancellation handling
  Future<void> _purchaseSubscription(SubscriptionProvider subscriptionProvider) async {
    if (_selectedTier == null) return;

    // Add comprehensive logging for debugging
    DebugLogger.logInit('🛒 Starting subscription purchase from UI:');
    DebugLogger.logInit('   - Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
    DebugLogger.logInit('   - Selected tier: ${_selectedTier!.name}');
    DebugLogger.logInit('   - Billing: ${_isAnnualBilling ? 'Annual' : 'Monthly'}');
    DebugLogger.logInit('   - Salon ID: ${widget.salonId}');

    // Handle web vs mobile packages differently
    if (kIsWeb) {
      DebugLogger.logInit('🌐 Web platform detected - using Stripe Checkout');
      DebugLogger.logInit('📦 Selected tier: ${_selectedTier!.name}');
      DebugLogger.logInit('📅 Billing period: ${_isAnnualBilling ? 'Annual' : 'Monthly'}');

      // Purchase subscription using Stripe Checkout
      await _purchaseStripeSubscription(_selectedTier!);
      return;
    }

    // Mobile platform - use regular packages
    final package = subscriptionProvider.getPackage(_selectedTier!, isAnnual: _isAnnualBilling);
    if (package == null) {
      DebugLogger.logInit('❌ Package not found for tier: ${_selectedTier!.name}');
      DebugLogger.logInit('   - Available packages: ${subscriptionProvider.availablePackages.length}');

      UINotificationService.showError(
        context: context,
        title: context.tr('common.error'),
        message: context.tr('subscription_plans.package_not_available'),
      );
      return;
    }

    DebugLogger.logInit('✅ Package found: ${package.storeProduct.identifier}');
    DebugLogger.logInit('   - Package title: ${package.storeProduct.title}');
    DebugLogger.logInit('   - Package price: ${package.storeProduct.priceString}');

    try {
      DebugLogger.logInit('🔄 Calling subscription provider purchase method...');

      final success = await subscriptionProvider.purchaseSubscription(
        package: package,
        salonId: widget.salonId,
      );

      DebugLogger.logInit('📋 Purchase result: $success');
      DebugLogger.logInit('   - Provider error: ${subscriptionProvider.error ?? 'None'}');
      DebugLogger.logInit('   - Current subscription: ${subscriptionProvider.currentSubscription?.tier.name ?? 'None'}');

      if (mounted) {
        if (success) {
          final newSubscription = subscriptionProvider.currentSubscription;
          final tierName = newSubscription?.tier.name ?? 'Unknown';

          DebugLogger.logInit('✅ Purchase successful - showing success message');
          UINotificationService.showSuccess(
            context: context,
            title: context.tr('subscription_plans.purchase_success_title'),
            message: context.tr('subscription_plans.purchase_success_message', params: {'tier': tierName}),
          );
          Navigator.of(context).pop(true);
        } else {
          // Check if the error is due to user cancellation
          final error = subscriptionProvider.error ?? '';
          DebugLogger.logInit('❌ Purchase failed with error: $error');

          if (_isPurchaseCancellation(error)) {
            DebugLogger.logInit('ℹ️ Detected user cancellation - showing cancellation dialog');
            _showCancellationMessage(subscriptionProvider);
          } else {
            DebugLogger.logInit('🚨 Showing error notification to user');
            UINotificationService.showError(
              context: context,
              title: context.tr('subscription_plans.purchase_error_title'),
              message: error.isNotEmpty ? error : context.tr('subscription_plans.purchase_error_message'),
            );
          }
        }
      }
    } on PlatformException catch (e) {
      if (mounted) {
        // Handle RevenueCat platform exceptions
        if (_isPurchaseCancellationException(e)) {
          _showCancellationMessage(subscriptionProvider);
        } else {
          UINotificationService.showError(
            context: context,
            title: context.tr('subscription_plans.purchase_error_title'),
            message: _getErrorMessageFromPlatformException(e),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        // Check if the error message indicates cancellation
        if (_isPurchaseCancellation(e.toString())) {
          _showCancellationMessage(subscriptionProvider);
        } else {
          UINotificationService.showError(
            context: context,
            title: context.tr('subscription_plans.unexpected_error_title'),
            message: context.tr('subscription_plans.unexpected_error_message'),
          );
        }
      }
    }
  }

  /// Purchase subscription using Stripe Checkout
  Future<void> _purchaseStripeSubscription(SubscriptionTier tier) async {
    try {
      DebugLogger.logInit('🔄 Starting Stripe subscription purchase: ${tier.name}');
      DebugLogger.logInit('📅 Billing period: ${_isAnnualBilling ? 'Annual' : 'Monthly'}');

      // Create Stripe checkout session
      final response = await StripeSubscriptionService.purchaseSubscription(
        tier: tier,
        isAnnual: _isAnnualBilling,
        salonId: widget.salonId,
      );

      if (mounted) {
        if (response.success && response.data != null) {
          final checkoutUrl = response.data!;
          DebugLogger.logInit('✅ Stripe checkout session created successfully');

          UINotificationService.showSuccess(
            context: context,
            title: context.tr('subscription_plans.stripe_redirect_title'),
            message: context.tr('subscription_plans.stripe_redirect_message'),
          );

          // Launch Stripe checkout in browser
          final launched = await StripeSubscriptionService.launchCheckout(checkoutUrl);
          if (!launched) {
            UINotificationService.showError(
              context: context,
              title: context.tr('subscription_plans.stripe_error_title'),
              message: context.tr('subscription_plans.stripe_error_message'),
            );
          } else {
            // Navigate back - subscription status will be updated via webhooks
            Navigator.of(context).pop(false); // false indicates purchase initiated but not completed yet
          }
        } else {
          DebugLogger.logInit('❌ Stripe subscription purchase failed: ${response.error}');

          UINotificationService.showError(
            context: context,
            title: context.tr('subscription_plans.purchase_error_title'),
            message: response.error ?? context.tr('subscription_plans.stripe_initiate_error'),
          );
        }
      }
    } catch (e) {
      DebugLogger.logInit('❌ Stripe subscription purchase failed: $e');

      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('subscription_plans.purchase_error_title'),
          message: context.tr('subscription_plans.stripe_initiate_error'),
        );
      }
    }
  }

  /// Restore purchases
  Future<void> _restorePurchases(SubscriptionProvider subscriptionProvider) async {
    try {
      final success = await subscriptionProvider.restorePurchases(widget.salonId);

      if (mounted) {
        if (success) {
          final restoredSubscription = subscriptionProvider.currentSubscription;
          final tierName = restoredSubscription?.tier.name ?? 'Unknown';

          UINotificationService.showSuccess(
            context: context,
            title: context.tr('subscription_plans.restore_success_title'),
            message: context.tr('subscription_plans.restore_success_message', params: {'tier': tierName}),
          );
          Navigator.of(context).pop(true);
        } else {
          UINotificationService.showInfo(
            context: context,
            title: context.tr('subscription_plans.restore_no_purchases_title'),
            message: context.tr('subscription_plans.restore_no_purchases_message'),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('subscription_plans.restore_error_title'),
          message: context.tr('subscription_plans.restore_error_message'),
        );
      }
    }
  }

  /// Open privacy policy
  Future<void> _openPrivacyPolicy() async {
    final success = await UrlLauncherService.openWebUrl(_privacyPolicyUrl);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(
        context,
        'politica de confidențialitate',
      );
    }
  }



  /// Open Terms of Use (EULA) - Platform-specific implementation
  Future<void> _openTermsOfUseEula() async {
    // Use Apple's standard EULA for iOS, custom terms for other platforms
    final String termsUrl = _getPlatformSpecificTermsUrl();
    final String platformName = _getPlatformTermsDisplayName();

    final success = await UrlLauncherService.openWebUrl(termsUrl);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(
        context,
        platformName,
      );
    }
  }

  /// Get platform-specific terms URL
  String _getPlatformSpecificTermsUrl() {
    if (Theme.of(context).platform == TargetPlatform.iOS) {
      // Use Apple's standard EULA for iOS App Store compliance
      return _appleEulaUrl;
    } else {
      // Use custom terms for Android and Web
      return _customTermsUrl;
    }
  }

  /// Get platform-specific display name for terms
  String _getPlatformTermsDisplayName() {
    if (Theme.of(context).platform == TargetPlatform.iOS) {
      return context.tr('subscription_plans.terms_of_use_eula');
    } else {
      return context.tr('subscription_plans.terms_of_service');
    }
  }

  /// Build subscription disclosure - Apple App Store requirement
  Widget _buildSubscriptionDisclosure() {
    if (_selectedTier == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
    );
  }

  /// Check if error indicates purchase cancellation
  bool _isPurchaseCancellation(String error) {
    final lowerError = error.toLowerCase();
    return lowerError.contains('cancelled') ||
        lowerError.contains('canceled') ||
        lowerError.contains('user_cancelled') ||
        lowerError.contains('purchase_cancelled') ||
        lowerError.contains('anulat') ||
        lowerError.contains('anulată');
  }

  /// Check if PlatformException indicates purchase cancellation
  bool _isPurchaseCancellationException(PlatformException e) {
    // Check error code
    if (e.code == 'PURCHASE_CANCELLED' ||
        e.code == 'USER_CANCELLED' ||
        e.code == '1') { // iOS error code for user cancellation
      return true;
    }

    // Check error details
    final details = e.details;
    if (details is Map) {
      final userCancelled = details['userCancelled'];
      if (userCancelled == true) return true;
    }

    // Check error message
    return _isPurchaseCancellation(e.message ?? '');
  }

  /// Get user-friendly error message from PlatformException
  String _getErrorMessageFromPlatformException(PlatformException e) {
    switch (e.code) {
      case 'NETWORK_ERROR':
        return context.tr('subscription_plans.error_network');
      case 'PAYMENT_PENDING':
        return context.tr('subscription_plans.error_payment_pending');
      case 'INVALID_CREDENTIALS':
        return context.tr('subscription_plans.error_invalid_credentials');
      case 'PRODUCT_NOT_AVAILABLE':
        return context.tr('subscription_plans.error_product_not_available');
      case 'INSUFFICIENT_PERMISSIONS':
        return context.tr('subscription_plans.error_insufficient_permissions');
      default:
        return e.message ?? context.tr('subscription_plans.error_generic');
    }
  }

  /// Show user-friendly cancellation message and clear error state
  void _showCancellationMessage(SubscriptionProvider subscriptionProvider) {
    // Set flag to prevent error state from showing
    setState(() {
      _isShowingCancellationDialog = true;
    });

    // Clear the error state immediately to prevent error UI from showing after dialog dismissal
    subscriptionProvider.clearErrorState();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        icon: Icon(
          Icons.info_outline,
          color: Colors.blue.shade600,
          size: 48,
        ),
        title: Text(
          context.tr('subscription_plans.purchase_cancelled_title'),
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
          textAlign: TextAlign.center,
        ),
        content: Text(
          context.tr('subscription_plans.purchase_cancelled_message'),
          style: const TextStyle(
            fontSize: 16,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: Text(
              context.tr('subscription_plans.purchase_cancelled_button'),
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
        ],
      )  ,
    ).then((_) {
      // Ensure flag is cleared when dialog is dismissed by any means
      if (mounted) {
        setState(() {
          _isShowingCancellationDialog = false;
        });
      }
    });
  }

  /// Determine if the cancel subscription button should be shown
  bool _shouldShowCancelButton(SubscriptionProvider subscriptionProvider) {
    final currentSubscription = subscriptionProvider.currentSubscription;

    // Only show for active subscriptions that are not free tier
    return currentSubscription != null &&
           currentSubscription.isActive &&
           currentSubscription.tier != SubscriptionTier.free &&
           !currentSubscription.isExpired;
  }

  /// Build the cancel subscription button
  Widget _buildCancelSubscriptionButton(SubscriptionProvider subscriptionProvider) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: OutlinedButton.icon(
        onPressed: subscriptionProvider.isLoading
            ? null
            : () => _handleCancelSubscription(subscriptionProvider),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.red.shade600,
          side: BorderSide(color: Colors.red.shade300, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: Colors.red.shade50,
        ),
        icon: subscriptionProvider.isLoading
            ? SizedBox(
                height: 16,
                width: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.red.shade600),
                ),
              )
            : Icon(
                Icons.cancel_outlined,
                size: 18,
                color: Colors.red.shade600,
              ),
        label: Text(
          context.tr('subscription_plans.cancel_subscription'),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.red.shade700,
          ),
        ),
      ),
    );
  }

  /// Handle cancel subscription action
  Future<void> _handleCancelSubscription(SubscriptionProvider subscriptionProvider) async {
    try {
      DebugLogger.logInit('🔄 Starting subscription cancellation...');

      // Show confirmation dialog first
      final shouldCancel = await _showCancelConfirmationDialog();
      if (!shouldCancel) {
        DebugLogger.logInit('ℹ️ User cancelled subscription cancellation');
        return;
      }

      DebugLogger.logInit('🔄 User confirmed cancellation, proceeding...');

      // Call the subscription provider to cancel the subscription
      final success = await _cancelSubscriptionDirectly(subscriptionProvider);

      if (success && mounted) {
        DebugLogger.logInit('✅ Subscription cancelled successfully');

        UINotificationService.showSuccess(
          context: context,
          title: context.tr('subscription_plans.cancel_success_title'),
          message: context.tr('subscription_plans.cancel_success_message'),
        );

        // Navigate back to previous screen with success result
        Navigator.of(context).pop(true);
      } else if (mounted) {
        DebugLogger.logInit('❌ Subscription cancellation failed');

        UINotificationService.showError(
          context: context,
          title: context.tr('subscription_plans.cancel_error_title'),
          message: context.tr('subscription_plans.cancel_error_message'),
        );
      }
    } catch (e) {
      DebugLogger.logInit('❌ Error handling subscription cancellation: $e');

      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('common.error'),
          message: context.tr('subscription_plans.cancel_platform_specific_error'),
        );
      }
    }
  }

  /// Cancel subscription directly through RevenueCat
  Future<bool> _cancelSubscriptionDirectly(SubscriptionProvider subscriptionProvider) async {
    try {
      DebugLogger.logInit('🔄 Calling RevenueCat to cancel subscription...');

      // For RevenueCat, we need to use the customer center or handle cancellation through the store
      // Since direct cancellation through RevenueCat SDK is limited, we'll use a hybrid approach

      // First, try to present the customer center for cancellation
      await RevenueCatUI.presentCustomerCenter();

      // Wait a moment for potential changes
      await Future.delayed(const Duration(seconds: 2));

      // Refresh subscription status to check if it was cancelled
      await subscriptionProvider.refreshForUser();

      final updatedSubscription = subscriptionProvider.currentSubscription;

      // Check if subscription is now cancelled or will be cancelled at period end
      if (updatedSubscription == null || !updatedSubscription.isActive) {
        DebugLogger.logInit('✅ Subscription successfully cancelled');
        return true;
      }

      // If still active, it might be cancelled but active until period end
      // This is normal behavior for most subscription services
      DebugLogger.logInit('ℹ️ Subscription may be cancelled but active until period end');
      return true;

    } catch (e) {
      DebugLogger.logInit('❌ Error cancelling subscription: $e');
      return false;
    }
  }

  /// Show confirmation dialog before cancelling subscription
  Future<bool> _showCancelConfirmationDialog() async {
    final currentSubscription = context.read<SubscriptionProvider>().currentSubscription;
    final tierName = _getTierDisplayName(currentSubscription?.tier ?? SubscriptionTier.free);

    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.red.shade600,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  context.tr('subscription_plans.cancel_subscription_title'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.tr('subscription_plans.cancel_confirmation_message', params: {'tier': tierName}),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.red.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            context.tr('subscription_plans.cancel_what_happens'),
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    _buildFeatureItem(context.tr('subscription_plans.cancel_immediate')),
                    _buildFeatureItem(context.tr('subscription_plans.cancel_access_until_end')),
                    _buildFeatureItem(context.tr('subscription_plans.cancel_no_future_charges')),
                    _buildFeatureItem(context.tr('subscription_plans.cancel_reactivate_anytime')),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                context.tr('subscription_plans.cancel_keep_button'),
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                context.tr('subscription_plans.cancel_confirm_button'),
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// Build a feature item for the confirmation dialog
  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade700,
        ),
      ),
    );
  }

  /// Get display name for subscription tier
  String _getTierDisplayName(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return context.tr('profile_screen.tier_free');
      case SubscriptionTier.freelancer:
        return context.tr('profile_screen.tier_freelancer');
      case SubscriptionTier.team:
        return context.tr('profile_screen.tier_team');
      case SubscriptionTier.enterprise:
        return context.tr('profile_screen.tier_enterprise');
    }
  }
}
