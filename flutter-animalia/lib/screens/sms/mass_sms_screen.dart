import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../models/client.dart';
import '../../models/mass_sms_template.dart';
import '../../providers/mass_sms_provider.dart';
import '../../services/auth/auth_service.dart';
import '../../services/sms_usage_service.dart';
import '../../widgets/sms/mass_sms_step_indicator.dart';
import '../../widgets/sms/message_composition_step.dart';
import '../../widgets/sms/client_selection_step.dart';
import '../../widgets/sms/review_send_step.dart';
import '../profile/settings/sms_reminders_screen.dart';
import '../../l10n/app_localizations.dart';

class MassSmsScreen extends StatefulWidget {
  const MassSmsScreen({super.key});

  @override
  State<MassSmsScreen> createState() => _MassSmsScreenState();
}

class _MassSmsScreenState extends State<MassSmsScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  
  // Step 1: Message Composition
  String _message = '';
  MassSmsTemplate? _selectedTemplate;
  
  // Step 2: Client Selection
  List<Client> _selectedClients = [];
  Map<String, dynamic> _filterCriteria = {};
  
  // Step 3: Review & Send
  bool _isScheduled = false;
  DateTime? _scheduledDateTime;
  bool _isSending = false;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    final salonId = await AuthService.getCurrentSalonId();
    if (salonId != null && mounted) {
      final provider = Provider.of<MassSmsProvider>(context, listen: false);
      await provider.loadTemplates(salonId);
      await provider.loadClients(salonId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('mass_sms.title'),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          if (_currentStep > 0)
            TextButton(
              onPressed: _canGoBack() ? _goToPreviousStep : null,
              child: Text(context.tr('mass_sms.back_button')),
            ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Step indicator
            Container(
              padding: const EdgeInsets.all(16),
              child: MassSmsStepIndicator(
                currentStep: _currentStep,
                steps: [
                  context.tr('mass_sms.step_message'),
                  context.tr('mass_sms.step_clients'),
                  context.tr('mass_sms.step_send')
                ],
              ),
            ),

            // Step content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  setState(() {
                    _currentStep = index;
                  });
                },
                children: [
                  // Step 1: Message Composition
                  MessageCompositionStep(
                    message: _message,
                    selectedTemplate: _selectedTemplate,
                    onMessageChanged: (message) {
                      setState(() {
                        _message = message;
                      });
                    },
                    onTemplateSelected: (template) {
                      setState(() {
                        _selectedTemplate = template;
                        if (template != null) {
                          _message = template.content;
                        }
                      });
                    },
                  ),

                  // Step 2: Client Selection
                  ClientSelectionStep(
                    selectedClients: _selectedClients,
                    filterCriteria: _filterCriteria,
                    onClientsChanged: (clients) {
                      setState(() {
                        _selectedClients = clients;
                      });
                    },
                    onFilterChanged: (criteria) {
                      setState(() {
                        _filterCriteria = criteria;
                      });
                    },
                  ),

                  // Step 3: Review & Send
                  ReviewSendStep(
                    message: _message,
                    selectedClients: _selectedClients,
                    onSend: _sendMassSms,
                  ),
                ],
              ),
            ),

            // Navigation buttons with proper safe area padding
            Container(
              padding: EdgeInsets.only(
                left: 16,
                right: 16,
                top: 16,
                bottom: 16 + MediaQuery.of(context).padding.bottom,
              ),
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  if (_currentStep > 0)
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _canGoBack() ? _goToPreviousStep : null,
                        child: Text(context.tr('mass_sms.back_button')),
                      ),
                    ),
                  if (_currentStep > 0) const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _canGoNext() ? (_currentStep == 2 ? _sendMassSms : _goToNextStep) : null,
                      child: _isSending && _currentStep == 2
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(context.tr('mass_sms.sending_progress')),
                              ],
                            )
                          : Text(_getNextButtonText()),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _canGoBack() {
    return _currentStep > 0 && !_isSending;
  }

  bool _canGoNext() {
    if (_isSending) return false;

    switch (_currentStep) {
      case 0: // Message composition
        return _message.trim().isNotEmpty && _message.length <= 1600;
      case 1: // Client selection
        return _selectedClients.isNotEmpty;
      case 2: // Review & send
        return _selectedClients.isNotEmpty && _message.trim().isNotEmpty;
      default:
        return false;
    }
  }

  String _getNextButtonText() {
    switch (_currentStep) {
      case 0:
        return context.tr('mass_sms.select_clients_button');
      case 1:
        return context.tr('mass_sms.review_button');
      case 2:
        return _isScheduled ? context.tr('mass_sms.schedule_button') : context.tr('mass_sms.send_button');
      default:
        return context.tr('mass_sms.continue_button');
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToNextStep() {
    if (_currentStep < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _sendMassSms() async {
    if (_selectedClients.isEmpty || _message.trim().isEmpty) {
      _showErrorDialog(context.tr('mass_sms.message_required_error'));
      return;
    }

    // Check SMS quota before sending
    final smsRequired = _calculateSmsCount();
    final totalSmsRequired = smsRequired * _selectedClients.length;

    // Get current SMS usage
    final smsUsageResponse = await SmsUsageService.getSmsUsage();
    if (smsUsageResponse.success && smsUsageResponse.data != null) {
      final smsUsage = smsUsageResponse.data!;
      final remainingSms = smsUsage.remainingSms;

      // Check if user has enough SMS
      if (totalSmsRequired > remainingSms) {
        _showSmsQuotaExceededDialog(totalSmsRequired, remainingSms, smsUsage.totalAllowed);
        return;
      }
    }

    setState(() {
      _isSending = true;
    });

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        throw Exception(context.tr('mass_sms.salon_not_found_error'));
      }

      if (!mounted) return;
      final provider = Provider.of<MassSmsProvider>(context, listen: false);
      
      // Convert user-friendly placeholder text back to technical variables for sending
      final messageForSending = (_message);

      final result = await provider.sendMassSms(
        salonId: salonId,
        clientIds: _selectedClients.map((c) => c.id).toList(),
        message: messageForSending,
        templateId: _selectedTemplate?.id,
        scheduledAt: _isScheduled ? _scheduledDateTime : null,
      );

      if (result.success) {
        _showSuccessDialog(result);
      } else {
        _showErrorDialog(result.errorMessage ?? context.tr('mass_sms.send_error_default'));
      }
    } catch (e) {
      _showErrorDialog(context.tr('mass_sms.send_error_generic', params: {'error': e.toString()}));
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  int _calculateSmsCount() {
    // Simple SMS count calculation (160 chars per SMS)
    return (_message.length / 160).ceil();
  }

  void _showSmsQuotaExceededDialog(int required, int available, int total) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.warning, color: Colors.orange),
            const SizedBox(width: 8),
            Text(context.tr('mass_sms.quota_exceeded_title')),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('mass_sms.quota_exceeded_message'),
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            Text(context.tr('mass_sms.sms_required_label').replaceAll('{0}', required.toString())),
            Text(context.tr('mass_sms.sms_available_label').replaceAll('{0}', available.toString()).replaceAll('{1}', total.toString())),
            const SizedBox(height: 16),
            Text(
              context.tr('mass_sms.quota_exceeded_description'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('mass_sms.cancel_button')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              // Navigate to SMS Settings screen
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SmsRemindersScreen(),
                ),
              );
            },
            child: Text(context.tr('mass_sms.sms_settings_button')),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(dynamic result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.green),
            const SizedBox(width: 8),
            Text(context.tr('mass_sms.success_title')),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isScheduled)
              Text(context.tr('mass_sms.success_scheduled_message'))
            else
              Text(context.tr('mass_sms.success_sent_message')),
            const SizedBox(height: 16),
            Text(context.tr('mass_sms.total_recipients_label').replaceAll('{0}', (result.totalRecipients ?? _selectedClients.length).toString())),
            if (!_isScheduled) ...[
              Text(context.tr('mass_sms.sent_success_label').replaceAll('{0}', (result.successCount ?? 0).toString())),
              if ((result.failureCount ?? 0) > 0)
                Text(context.tr('mass_sms.failed_label').replaceAll('{0}', result.failureCount.toString()), style: const TextStyle(color: Colors.red)),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Close mass SMS screen
            },
            child: Text(context.tr('mass_sms.ok_button')),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Text(context.tr('mass_sms.error_title')),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(context.tr('mass_sms.ok_button')),
          ),
        ],
      ),
    );
  }
}
