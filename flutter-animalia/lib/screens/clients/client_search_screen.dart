import 'package:flutter/material.dart';

import '../../models/client.dart';
import '../../l10n/app_localizations.dart';

class ClientSearchScreen extends StatefulWidget {
  final List<Client> availableClients;

  const ClientSearchScreen({
    super.key,
    required this.availableClients,
  });

  @override
  State<ClientSearchScreen> createState() => _ClientSearchScreenState();
}

class _ClientSearchScreenState extends State<ClientSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Client> _filteredClients = [];
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _filteredClients = widget.availableClients;
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filteredClients = widget.availableClients.where((client) {
        final phoneNormalized = client.phone.replaceAll(RegExp(r'[\s\-\+]'), '');
        final queryNormalized = _searchController.text.replaceAll(RegExp(r'[\s\-\+]'), '');
        final matchesName = client.name.toLowerCase().contains(_searchQuery);
        final matchesPhone = client.phone.toLowerCase().contains(_searchQuery) ||
            phoneNormalized.contains(queryNormalized);
        final matchesEmail = client.email.toLowerCase().contains(_searchQuery);
        final matchesPetName = client.petNames
            .any((petName) => petName.toLowerCase().contains(_searchQuery));
        final matchesBackendPetName = client.matchingPetNames
            .any((petName) => petName.toLowerCase().contains(_searchQuery));

        return matchesName ||
            matchesPhone ||
            matchesEmail ||
            matchesPetName ||
            matchesBackendPetName;
      }).toList();
    });
  }

  void _selectClient(Client client) {
    Navigator.of(context).pop(client);
  }

  void _addNewClient() {
    Navigator.of(context).pop('new_client');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('client_search.title')),
      ),
      body: Container(
        child: Column(
          children: [
            // Search field
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                textCapitalization: TextCapitalization.sentences,
                decoration: InputDecoration(
                  labelText: context.tr('client_search.search_label'),
                  hintText: context.tr('client_search.search_placeholder'),
                  prefixIcon:  Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon:  Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                      : null,
                  border: const OutlineInputBorder(),
                  focusedBorder:  OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.onSurface),
                  ),
                ),
              ),
            ),
            
            // Add new client button
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _addNewClient,
                  icon:  Icon(Icons.person_add, color: Colors.white),
                  label:
                  Text(
                      context.tr('client_search.add_new_client'),
                    style: TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Results header
            if (_filteredClients.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Text(
                      context.tr('client_search.clients_found', params: {'count': _filteredClients.length.toString()}),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
            
            SizedBox(height: 8),
            
            // Clients list
            Expanded(
              child: _buildClientsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClientsList() {
    if (_filteredClients.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty 
                  ? context.tr('client_search.no_clients_available')
                  : context.tr('client_search.no_results_for_query', params: {'query': _searchQuery}),
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredClients.length,
      itemBuilder: (context, index) {
        final client = _filteredClients[index];
        final queryLower = _searchQuery.trim();
        final backendMatches = client.matchingPetNames
            .where((petName) => queryLower.isEmpty || petName.toLowerCase().contains(queryLower))
            .toList();
        final localMatches = client.petNames
            .where((petName) => queryLower.isNotEmpty && petName.toLowerCase().contains(queryLower))
            .toList();
        final combinedMatches = {
          ...backendMatches,
          ...localMatches,
        }.toList();
        final shouldShowPetMatches = combinedMatches.isNotEmpty && (queryLower.isNotEmpty || client.matchedViaPetName);

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: Text(
                client.name.isNotEmpty ? client.name[0].toUpperCase() : '?',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: client.name,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  if (client.pets.isNotEmpty)
                    TextSpan(
                      text: ' (${client.pets.first.name} - ${client.pets.first.species != null ? _getSpeciesDisplayName(client.pets.first.species!) : 'necunoscut'})',
                      style: TextStyle(
                        fontWeight: FontWeight.normal,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    )
                  else if (client.petNames.isNotEmpty)
                    TextSpan(
                      text: ' (${client.petNames.join(', ')})',
                      style: TextStyle(
                        fontWeight: FontWeight.normal,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                ],
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(client.phone),
                if (client.email.isNotEmpty)
                  Text(
                    client.email,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                if (shouldShowPetMatches)
                  Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(
                      context.tr('client_search.pet_match', params: {'pets': combinedMatches.join(', ')}),
                      style: TextStyle(
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
              ],
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              color: Theme.of(context).colorScheme.onSurface,
              size: 16,
            ),
            onTap: () => _selectClient(client),
          ),
        );
      },
    );
  }

  String _getSpeciesDisplayName(String species) {
    switch (species.toLowerCase()) {
      case 'dog':
        return 'câine';
      case 'cat':
        return 'pisică';
      case 'other':
        return 'altele';
      default:
        return species;
    }
  }
}
