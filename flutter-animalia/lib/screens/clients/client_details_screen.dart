import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/appointment.dart';
import '../../models/client.dart';
import '../../models/pet.dart';

import '../../models/subscription.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/client_provider.dart';
import '../../services/client/client_service.dart';
import '../../widgets/clients/client_header_widget.dart';
import '../../widgets/lists/client_tabs_widget.dart';
import 'add_pet_screen.dart';
import 'edit_client_screen.dart';

class ClientDetailsScreen extends StatefulWidget {
  final Client client;

  const ClientDetailsScreen({
    super.key,
    required this.client,
  });

  @override
  State<ClientDetailsScreen> createState() => _ClientDetailsScreenState();
}

class _ClientDetailsScreenState extends State<ClientDetailsScreen> {
  Client? _currentClient;
  final List<Subscription> _subscriptions = [];
  final List<Appointment> _appointments = [];
  List<Pet> _pets = [];

  bool _isLoadingPets = true;
  bool _isLoadingAppointments = true;

  @override
  void initState() {
    super.initState();
    _currentClient = widget.client;

    // Use post frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadClientData();
      _refreshClientFromProvider();
    });
  }

  /// Refresh client data from ClientProvider to get latest information
  void _refreshClientFromProvider() {
    DebugLogger.logVerbose('🔄 ClientDetailsScreen: Refreshing client data from provider...');
    final clientProvider = context.read<ClientProvider>();
    final updatedClient = clientProvider.getClientById(widget.client.id);

    if (updatedClient != null && mounted) {
      setState(() {
        _currentClient = updatedClient;
      });
      DebugLogger.logVerbose('✅ ClientDetailsScreen: Client data refreshed from provider');
    } else {
      DebugLogger.logVerbose('⚠️ ClientDetailsScreen: Client not found in provider, using original data');
    }
  }

  Future<void> _loadClientData() async {
    await Future.wait([
      _loadPets(),
      _loadAppointments(),
    ]);
  }

  Future<void> _loadPets() async {
    try {
      DebugLogger.logVerbose('🔄 ClientDetailsScreen: Loading pets for client: ${_currentClient?.id}');
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      // Get pets for this client using HTTP service
      final pets = await calendarProvider.calendarService.getPetsForClient(_currentClient?.id ?? widget.client.id);

      if (mounted) {
        setState(() {
          _pets = pets;
          _isLoadingPets = false;
        });
        DebugLogger.logVerbose('✅ ClientDetailsScreen: Loaded ${pets.length} pets');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingPets = false;
        });
      }
      DebugLogger.logVerbose('❌ ClientDetailsScreen: Error loading client pets: $e');
    }
  }

  Future<void> _loadAppointments() async {
    try {
      DebugLogger.logVerbose('🔄 ClientDetailsScreen: Loading appointments for client: ${_currentClient?.id}');
      final response = await ClientService.getClientAppointments(_currentClient?.id ?? widget.client.id);

      if (mounted) {
        setState(() {
          if (response.success && response.data != null) {
            _appointments
              ..clear()
              ..addAll(response.data!);
          } else {
            _appointments.clear();
          }
          _isLoadingAppointments = false;
        });
        DebugLogger.logVerbose('✅ ClientDetailsScreen: Loaded ${_appointments.length} appointments');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingAppointments = false;
        });
      }
      DebugLogger.logVerbose('❌ ClientDetailsScreen: Error loading client appointments: $e');
    }
  }

  void _editClient() async {
    DebugLogger.logVerbose('🔄 ClientDetailsScreen: Opening edit screen for client: ${_currentClient?.name}');

    final result = await Navigator.of(context).push<Client>(
      MaterialPageRoute(
        builder: (context) => EditClientScreen(client: _currentClient ?? widget.client),
      ),
    );

    // If client was successfully updated, refresh the data from provider
    if (result != null && mounted) {
      DebugLogger.logVerbose('✅ ClientDetailsScreen: Client ${result.name} updated successfully');

      // Refresh client provider to get latest data
      final clientProvider = context.read<ClientProvider>();
      await clientProvider.refresh();

      // Update local client data
      _refreshClientFromProvider();

      // Show success message
      if (mounted) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text(AppLocalizations.of(context).translate('clients.client_updated_success').replaceAll('{name}', result.name)),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    }
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).translate('clients.confirm_delete_title')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(AppLocalizations.of(context).translate('clients.confirm_delete_message').replaceAll('{name}', _currentClient?.name ?? widget.client.name)),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).translate('clients.delete_will_remove'),
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text('• ' + AppLocalizations.of(context).translate('clients.all_client_data')),
              Text('• ' + AppLocalizations.of(context).translate('clients.all_associated_pets')),
              Text('• ' + AppLocalizations.of(context).translate('clients.appointment_history')),
              Text('• ' + AppLocalizations.of(context).translate('clients.reviews_and_subscriptions')),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).translate('clients.action_cannot_be_undone'),
                style: TextStyle(
                  color: AppTheme.getStatusColor(context, 'error'),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context).translate('common.cancel')),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteClient();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.getStatusColor(context, 'error'),
                foregroundColor: Theme.of(context).colorScheme.onError,
              ),
              child: Text(AppLocalizations.of(context).translate('common.delete')),
            ),
          ],
        );
      },
    );
  }

  /// Delete the client
  Future<void> _deleteClient() async {
    final clientId = _currentClient?.id ?? widget.client.id;
    final clientName = _currentClient?.name ?? widget.client.name;

    DebugLogger.logVerbose('🗑️ ClientDetailsScreen: Starting client deletion process...');
    DebugLogger.logVerbose('📍 ClientDetailsScreen: Client ID: $clientId');
    DebugLogger.logVerbose('📍 ClientDetailsScreen: Client Name: $clientName');

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text(AppLocalizations.of(context).translate('clients.deleting_client')),
            ],
          ),
        );
      },
    );

    try {
      // Delete client using ClientProvider
      final clientProvider = context.read<ClientProvider>();
      final success = await clientProvider.deleteClient(clientId);

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (success && mounted) {
        DebugLogger.logVerbose('✅ ClientDetailsScreen: Client deleted successfully');

        // Show success message
        showTopSnackBar(context, 
          SnackBar(
            content: Text(AppLocalizations.of(context).translate('clients.client_deleted_success').replaceAll('{name}', clientName)),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );

        // Navigate back to clients list
        Navigator.of(context).pop();
      } else if (mounted) {
        DebugLogger.logVerbose('❌ ClientDetailsScreen: Failed to delete client');

        // Show error message
        showTopSnackBar(context, 
          SnackBar(
            content: Text(AppLocalizations.of(context).translate('clients.delete_error_generic') + ' "$clientName"'),
            backgroundColor: AppTheme.getStatusColor(context, 'error'),
          ),
        );
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ ClientDetailsScreen: Exception during client deletion: $e');

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();

        // Show error message
        showTopSnackBar(context, 
          SnackBar(
            content: Text(AppLocalizations.of(context).translate('clients.delete_error_generic') + ': $e'),
            backgroundColor: AppTheme.getStatusColor(context, 'error'),
          ),
        );
      }
    }
  }

  void _showAddPetDialog() async {
    final result = await Navigator.of(context).push<Pet>(
      MaterialPageRoute(
        builder: (context) => AddPetScreen(
          clientId: widget.client.id,
          clientName: widget.client.name,
        ),
      ),
    );

    // If a pet was successfully added, refresh the pets list
    if (result != null) {
      _loadPets();
    }
  }

  Widget _buildModernSpeedDial() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return SpeedDial(
      animatedIcon: AnimatedIcons.menu_close,
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.onPrimary,
      activeBackgroundColor: theme.colorScheme.primaryContainer,
      activeForegroundColor: theme.colorScheme.onPrimaryContainer,
      spacing: 3,
      childPadding: const EdgeInsets.all(5),
      spaceBetweenChildren: 4,
      tooltip: AppLocalizations.of(context).translate('clients.client_actions'),
      elevation: 8,
      overlayColor: theme.colorScheme.scrim,
      overlayOpacity: isDark ? 0.6 : 0.4,
      curve: Curves.easeInOutCubic,
      animationDuration: const Duration(milliseconds: 300),
      children: [
        SpeedDialChild(
          child: Icon(
            Icons.delete_outline,
            color: theme.colorScheme.onError,
          ),
          backgroundColor: theme.colorScheme.error,
          foregroundColor: theme.colorScheme.onError,
          label: AppLocalizations.of(context).translate('clients.delete_client'),
          labelStyle: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 15,
            color: theme.colorScheme.onSurface,
          ),
          labelBackgroundColor: theme.colorScheme.surface,
          labelShadow: [
            BoxShadow(
              color: theme.colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          elevation: 4,
          onTap: _showDeleteConfirmation,
        ),
        SpeedDialChild(
          child: Icon(
            Icons.pets_outlined,
            color: theme.colorScheme.onSecondary,
          ),
          backgroundColor: theme.colorScheme.secondary,
          foregroundColor: theme.colorScheme.onSecondary,
          label: AppLocalizations.of(context).translate('clients.add_pet'),
          labelStyle: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 15,
            color: theme.colorScheme.onSurface,
          ),
          labelBackgroundColor: theme.colorScheme.surface,
          labelShadow: [
            BoxShadow(
              color: theme.colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          elevation: 4,
          onTap: _showAddPetDialog,
        ),
        SpeedDialChild(
          child: Icon(
            Icons.edit_outlined,
            color: theme.colorScheme.onPrimary,
          ),
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          label: AppLocalizations.of(context).translate('clients.edit_client'),
          labelStyle: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 15,
            color: theme.colorScheme.onSurface,
          ),
          labelBackgroundColor: theme.colorScheme.surface,
          labelShadow: [
            BoxShadow(
              color: theme.colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          elevation: 4,
          onTap: _editClient,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final displayClient = _currentClient ?? widget.client;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          SliverAppBar(
            expandedHeight: 320, // Increased from 280 for more header space
            pinned: true,
            stretch: true,
            backgroundColor: theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            title: AnimatedOpacity(
              opacity: 1.0,
              duration: const Duration(milliseconds: 300),
              child: Text(
                displayClient.name,
                style: TextStyle(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w700,
                  fontSize: 20,
                  letterSpacing: 0.5,
                ),
              ),
            ),
            flexibleSpace: FlexibleSpaceBar(
              stretchModes: const [
                StretchMode.zoomBackground,
                StretchMode.blurBackground,
              ],
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      theme.colorScheme.surface,
                      theme.colorScheme.surface.withValues(alpha: 0.9),
                      theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.8),
                    ],
                    stops: const [0.0, 0.6, 1.0],
                  ),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        theme.colorScheme.shadow.withValues(alpha: isDark ? 0.1 : 0.05),
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 60, 16, 8),
                      child: ClientHeaderWidget(client: displayClient),
                    ),
                  ),
                ),
              ),
            ),
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.arrow_back_ios_new,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                onPressed: () => Navigator.of(context).pop(),
                splashRadius: 24,
              ),
            ),
          ),
          SliverFillRemaining(
            child: Container(
              margin: const EdgeInsets.only(top: 4),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(32)),
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.shadow.withValues(alpha: 0.08),
                    blurRadius: 20,
                    offset: const Offset(0, -4),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: theme.colorScheme.shadow.withValues(alpha: 0.04),
                    blurRadius: 40,
                    offset: const Offset(0, -8),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(32)),
                child: Container(
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface,
                    border: Border(
                      top: BorderSide(
                        color: theme.colorScheme.outline.withValues(alpha: 0.1),
                        width: 1,
                      ),
                    ),
                  ),
                  child: ClientTabsWidget(
                    client: displayClient,
                    appointments: _appointments,
                    subscriptions: _subscriptions,
                    pets: _pets,
                    isLoading: _isLoadingPets || _isLoadingAppointments,
                    onAddPet: _showAddPetDialog,
                    onPetDeleted: _loadPets, // Refresh pets when one is deleted
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: _buildModernSpeedDial(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }
}
