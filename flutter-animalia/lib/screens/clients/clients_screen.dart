import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_dimensions.dart';
import '../../l10n/app_localizations.dart';
import '../../models/client.dart';
import '../../providers/client_provider.dart';
import '../../providers/client_settings_provider.dart';
import '../../screens/clients/client_details_screen.dart';
import '../../screens/clients/clients_list_screen.dart';
import '../../screens/settings/client_settings_screen.dart';
import '../../widgets/clients/inactive_clients_list.dart';

class ClientsScreen extends StatefulWidget {
  const ClientsScreen({super.key});

  @override
  State<ClientsScreen> createState() => _ClientsScreenState();
}

class _ClientsScreenState extends State<ClientsScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _showInactiveClients = false; // Track current view

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);

    // Initialize client settings provider and load inactive clients for badge count
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;

      final settingsProvider = context.read<ClientSettingsProvider>();
      await settingsProvider.initialize();

      // Load inactive clients to show badge count
      if (mounted && settingsProvider.showInactiveClientsTab) {
        final clientProvider = context.read<ClientProvider>();
        await clientProvider.loadInactiveClients(
          daysSinceLastAppointment: settingsProvider.inactiveClientThresholdDays,
        );
      }
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text;
    final clientProvider = context.read<ClientProvider>();

    if (_showInactiveClients) {
      clientProvider.searchInactiveClients(query);
    } else {
      clientProvider.searchClients(query);
    }
  }

  void _navigateToClientDetails(String clientId) async {
    try {
      final clientProvider = context.read<ClientProvider>();
      
      // Try to find the client in the current list first
      Client? foundClient;
      
      if (_showInactiveClients) {
        // Look in inactive clients
        final inactiveClient = clientProvider.inactiveClients
            .where((ic) => ic.client.id == clientId)
            .firstOrNull;
        foundClient = inactiveClient?.client;
      } else {
        // Look in regular clients
        foundClient = clientProvider.clients
            .where((client) => client.id == clientId)
            .firstOrNull;
      }

      // If not found in current list, try to get from provider's cached clients
      if (foundClient == null) {
        foundClient = clientProvider.getClientById(clientId);

        if (foundClient == null) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(AppLocalizations.of(context).translate('clients.client_not_found'))),
            );
          }
          return;
        }
      }

      // Navigate to client details if found
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ClientDetailsScreen(client: foundClient!),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context).translate('clients.navigation_error'))),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ClientSettingsProvider>(
      builder: (context, settingsProvider, child) {
        if (!settingsProvider.isInitialized) {
          return Scaffold(
            appBar: AppBar(title: Text(AppLocalizations.of(context).translate('clients.title'))),
            body: const Center(child: CircularProgressIndicator()),
          );
        }

        final showInactiveOption = settingsProvider.showInactiveClientsTab;

        return Scaffold(
          appBar: AppBar(
            title: Text(AppLocalizations.of(context).translate('clients.title')),
            actions: [
              // Toggle button for inactive clients (only show if enabled in settings)
              if (showInactiveOption)
                Consumer<ClientProvider>(
                  builder: (context, clientProvider, child) {
                    final inactiveCount = clientProvider.inactiveClients.length;

                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            _showInactiveClients = !_showInactiveClients;
                          });
                          // Clear search when switching views
                          _searchController.clear();
                          // Trigger appropriate search
                          if (_showInactiveClients) {
                            clientProvider.searchInactiveClients('');
                          } else {
                            clientProvider.searchClients('');
                          }
                        },
                        borderRadius: BorderRadius.circular(20),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: _showInactiveClients
                                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                                : Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: _showInactiveClients
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Stack(
                                children: [
                                  Icon(
                                    _showInactiveClients ? Icons.people : Icons.person_off,
                                    size: 18,
                                    color: _showInactiveClients
                                        ? Theme.of(context).colorScheme.primary
                                        : Theme.of(context).colorScheme.onSurfaceVariant,
                                  ),

                                  // Red notification badge with count (only show when not in inactive view and count > 0)
                                  if (!_showInactiveClients && inactiveCount > 0)
                                    Positioned(
                                      right: -2,
                                      top: -2,
                                      child: Container(
                                        padding: const EdgeInsets.all(2),
                                        decoration: BoxDecoration(
                                          color: Colors.red,
                                          borderRadius: BorderRadius.circular(8),
                                          border: Border.all(
                                            color: Theme.of(context).colorScheme.surface,
                                            width: 1,
                                          ),
                                        ),
                                        constraints: const BoxConstraints(
                                          minWidth: 14,
                                          minHeight: 14,
                                        ),
                                        child: Text(
                                          inactiveCount > 99 ? '99+' : inactiveCount.toString(),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 9,
                                            fontWeight: FontWeight.bold,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                              const SizedBox(width: 6),
                              Text(
                                _showInactiveClients ? AppLocalizations.of(context).translate('clients.view_active_clients') : AppLocalizations.of(context).translate('clients.view_inactive_clients'),
                                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                  color: _showInactiveClients
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(context).colorScheme.onSurfaceVariant,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              
              // Settings button
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ClientSettingsScreen(),
                    ),
                  );
                },
                tooltip: AppLocalizations.of(context).translate('clients.settings_tooltip'),
              ),
            ],
          ),
          body: Column(
            children: [
              // Search bar
              Padding(
                padding: const EdgeInsets.all(AppDimensions.spacingStandard),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: _showInactiveClients 
                        ? AppLocalizations.of(context).translate('clients.search_inactive_clients')
                        : AppLocalizations.of(context).translate('clients.search_clients'),
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surface,
                  ),
                ),
              ),

              // View indicator chip (only show if inactive option is available)
              if (showInactiveOption)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppDimensions.spacingStandard),
                  child: Row(
                    children: [
                      Chip(
                        avatar: Icon(
                          _showInactiveClients ? Icons.person_off : Icons.people,
                          size: 18,
                        ),
                        label: Text(
                          _showInactiveClients 
                              ? AppLocalizations.of(context).translate('clients.view_inactive_clients')
                              : AppLocalizations.of(context).translate('clients.all_clients'),
                        ),
                        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: AppDimensions.spacingSmall),

              // Content
              Expanded(
                child: _showInactiveClients && showInactiveOption
                    ? InactiveClientsList(
                        daysSinceLastAppointment: settingsProvider.inactiveClientThresholdDays,
                        onClientTap: _navigateToClientDetails,
                      )
                    : const ClientsListScreen(
                        showAppBar: false,
                        showSearchBar: false,
                      ),
              ),
            ],
          ),
        );
      },
    );
  }
}
