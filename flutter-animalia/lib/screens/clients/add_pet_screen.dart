import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';

import '../../models/image_upload_response.dart';
import '../../models/pet.dart';
import '../../services/api_service.dart';
import '../../services/breed_service.dart';
import '../../services/client/pet_service.dart';
import '../../widgets/common/cross_platform_image.dart';
import '../../widgets/common/custom_bottom_sheet.dart';
import '../../l10n/app_localizations.dart';

class AddPetScreen extends StatefulWidget {
  final String clientId;
  final String clientName;

  const AddPetScreen({
    super.key,
    required this.clientId,
    required this.clientName,
  });

  @override
  State<AddPetScreen> createState() => _AddPetScreenState();
}

class _AddPetScreenState extends State<AddPetScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _breedController = TextEditingController();
  final _colorController = TextEditingController();
  final _weightController = TextEditingController();
  final _microchipController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedSpecies = 'dog';
  String _selectedGender = 'male';
  DateTime _birthDate = DateTime.now().subtract(const Duration(days: 365));
  bool _isLoading = false;
  String? _errorMessage;
  XFile? _selectedImage;
  final ImagePicker _imagePicker = ImagePicker();

  List<Map<String, dynamic>> _species = [];
  List<String> _currentBreeds = [];


  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _breedController.dispose();
    _colorController.dispose();
    _weightController.dispose();
    _microchipController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// Load initial data from backend
  Future<void> _loadInitialData() async {
    await _loadSpecies();
    await _loadBreedsForSpecies(_selectedSpecies);
  }

  /// Load species from backend
  Future<void> _loadSpecies() async {
    try {
      final species = await BreedService.getSpeciesForDisplay();
      if (mounted) {
        setState(() {
          _species = species;
        });
      }
    } catch (e) {
      print('Error loading species: $e');
      // Use fallback species if API fails
      if (mounted) {
        setState(() {
          _species = [
            {'value': 'dog', 'label': context.tr('pets.dog'), 'icon': '🐕'},
            {'value': 'cat', 'label': context.tr('pets.cat'), 'icon': '🐱'},
            {'value': 'other', 'label': context.tr('pets.other'), 'icon': '🐾'},
          ];
        });
      }
    }
  }

  /// Load breeds for selected species
  Future<void> _loadBreedsForSpecies(String species) async {
    try {
      final breeds = await BreedService.getCachedBreedsBySpecies(species);
      if (mounted) {
        setState(() {
          _currentBreeds = breeds;
        });
      }
    } catch (e) {
      print('Error loading breeds for $species: $e');
      if (mounted) {
        setState(() {
          _currentBreeds = [];
        });
      }
    }
  }

  /// Force refresh breeds for selected species
  Future<void> _refreshBreedsForSpecies(String species) async {
    try {
      final breeds = await BreedService.refreshBreedsBySpecies(species);
      if (mounted) {
        setState(() {
          _currentBreeds = breeds;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lista de rase a fost actualizată'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('Error refreshing breeds for $species: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la actualizarea raselor'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  List<String> get _currentBreedSuggestions {
    return _currentBreeds;
  }

  Future<void> _pickImage() async {
    CustomBottomSheet.show(
      context: context,
      title: context.tr('pets.select_image_source'),
      isScrollControlled: true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(Icons.camera_alt, color: Theme.of(context).colorScheme.onSurface),
            title: Text(context.tr('pets.camera')),
            onTap: () {
              Navigator.pop(context);
              _getImageFromSource(ImageSource.camera);
            },
          ),
          ListTile(
            leading: Icon(Icons.photo_library, color: Theme.of(context).colorScheme.onSurface),
            title: Text(context.tr('pets.gallery')),
            onTap: () {
              Navigator.pop(context);
              _getImageFromSource(ImageSource.gallery);
            },
          ),
          if (_selectedImage != null)
            ListTile(
              leading: Icon(Icons.delete, color: Theme.of(context).colorScheme.error),
              title: Text(context.tr('pets.delete_image')),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _selectedImage = null;
                });
              },
            ),
          // Add some bottom padding to ensure content is accessible
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Future<void> _getImageFromSource(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _selectedImage = image;
        });
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text(context.tr('pets.image_selection_error', params: {'error': e.toString()})),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _birthDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _birthDate) {
      setState(() {
        _birthDate = picked;
      });
    }
  }

  Future<void> _savePet() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      String? photoUrl;

      // Upload image if selected
      if (_selectedImage != null) {
        final uploadResponse = await ApiService.uploadXFile<ImageUploadResponse>(
          '/api/images/upload',
          _selectedImage!,
          fromJson: (data) => ImageUploadResponse.fromJson(Map<String, dynamic>.from(data)),
        );

        if (uploadResponse.success && uploadResponse.data != null) {
          photoUrl = uploadResponse.data!.url;
        } else {
          throw Exception('Failed to upload image: ${uploadResponse.message}');
        }
      }

      final newPet = Pet(
        id: '',
        name: _nameController.text.trim(),
        species: _selectedSpecies,
        breed: _breedController.text.trim().isEmpty ? context.tr('pets.metis') : _breedController.text.trim(),
        gender: _selectedGender,
        birthDate: _birthDate,
        weight: double.tryParse(_weightController.text.trim()) ?? 0.0,
        color: _colorController.text.trim().isEmpty ? context.tr('pets.unknown') : _colorController.text.trim(),
        microchipNumber: _microchipController.text.trim(),
        notes: _notesController.text.trim(),
        photoUrl: photoUrl ?? '',
        ownerId: widget.clientId,
      );

      final response = await PetService.addPetToClient(widget.clientId, newPet);

      if (response.success && response.data != null) {
        if (mounted) {
          Navigator.of(context).pop(response.data);
          
          showTopSnackBar(context, 
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(child: Text(context.tr('pets.pet_added_success', params: {'petName': newPet.name}))),
                ],
              ),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = response.message ?? context.tr('pets.save_error');
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = context.tr('pets.connection_error', params: {'error': e.toString()});
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return context.tr('pets.pet_name_required_error');
    }
    if (value.trim().length < 2) {
      return context.tr('pets.pet_name_min_length_error');
    }
    return null;
  }

  String? _validateWeight(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Weight is optional
    }
    final weight = double.tryParse(value.trim());
    if (weight == null) {
      return context.tr('pets.weight_validation_error');
    }
    if (weight <= 0) {
      return context.tr('pets.weight_positive_error');
    }
    if (weight > 200) {
      return context.tr('pets.weight_too_large_error');
    }
    return null;
  }

  Widget _buildPhotoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.photo_camera,
                  color: Theme.of(context).colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('pets.photo'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Center(
              child: GestureDetector(
                onTap: _pickImage,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: _selectedImage != null
                      ? ClipOval(
                          child: CrossPlatformImage.xFile(
                            _selectedImage!,
                            width: 120,
                            height: 120,
                            fit: BoxFit.cover,
                          ),
                        )
                      : _buildPetAvatar(),
                ),
              ),
            ),

            const SizedBox(height: 8),
            Center(
              child: Text(
                context.tr('pets.add_photo'),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPetAvatar() {
    return Icon(
      Icons.pets,
      size: 50,
      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
    );
  }

  Widget _buildBasicInfoSection() {
    // Update genders with localized labels
    final localizedGenders = [
      {'value': 'male', 'label': context.tr('pets.male'), 'icon': '♂️'},
      {'value': 'female', 'label': context.tr('pets.female'), 'icon': '♀️'},
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.pets,
                  color: Theme.of(context).colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('pets.basic_info'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Name field
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: context.tr('pets.pet_name_required'),
                hintText: context.tr('pets.pet_name_placeholder'),
                prefixIcon: Icon(Icons.pets),
                border: OutlineInputBorder(),
              ),
              textCapitalization: TextCapitalization.words,
              validator: _validateName,
              enabled: !_isLoading,
            ),

            const SizedBox(height: 16),

            // Species dropdown
            DropdownButtonFormField<String>(
              value: _species.any((s) => s['value'] == _selectedSpecies)
                  ? _selectedSpecies
                  : (_species.isNotEmpty ? _species.first['value'] : _selectedSpecies),
              decoration: InputDecoration(
                labelText: context.tr('pets.species_required'),
                prefixIcon: Icon(Icons.category),
                border: OutlineInputBorder(),
              ),
              items: _species.map((species) {
                return DropdownMenuItem<String>(
                  value: species['value'] as String,
                  child: Text(species['label']),
                );
              }).toList(),
              onChanged: _isLoading ? null : (value) async {
                if (value != null) {
                  setState(() {
                    _selectedSpecies = value;
                    // Clear breed when species changes since user might want to select a breed appropriate for the new species
                    _breedController.clear();
                  });
                  // Reload breeds for the new species
                  await _loadBreedsForSpecies(value);
                }
              },
            ),

            const SizedBox(height: 16),

            // Breed field with autocomplete
            Autocomplete<String>(
              optionsBuilder: (TextEditingValue textEditingValue) {
                // Use breeds from backend with fallback to static data
                final suggestions = _currentBreeds.isNotEmpty
                    ? _currentBreeds
                    : [context.tr('pets.metis'), context.tr('pets.unknown')]; // Simple fallback
                if (textEditingValue.text.isEmpty) {
                  return suggestions;
                }
                return suggestions.where((option) =>
                    option.toLowerCase().contains(textEditingValue.text.toLowerCase()));
              },
              onSelected: (String selection) {
                _breedController.text = selection;
              },
              fieldViewBuilder: (context, controller, focusNode, onEditingComplete) {
                // Sync the autocomplete controller with our breed controller
                if (controller.text != _breedController.text) {
                  controller.text = _breedController.text;
                }

                return TextFormField(
                  controller: controller,
                  focusNode: focusNode,
                  onEditingComplete: onEditingComplete,
                  onChanged: (value) {
                    _breedController.text = value;
                  },
                  decoration: InputDecoration(
                    labelText: context.tr('pets.breed_required'),
                    hintText: context.tr('pets.breed_placeholder'),
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  textCapitalization: TextCapitalization.words,
                  enabled: !_isLoading,
                );
              },
            ),

            const SizedBox(height: 16),

            // Gender dropdown
            DropdownButtonFormField<String>(
              value: localizedGenders.any((g) => g['value'] == _selectedGender)
                  ? _selectedGender
                  : (localizedGenders.isNotEmpty ? localizedGenders.first['value'] : _selectedGender),
              decoration: InputDecoration(
                labelText: context.tr('pets.gender_required'),
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              items: localizedGenders.map((gender) {
                return DropdownMenuItem<String>(
                  value: gender['value'] as String,
                  child: Row(
                    children: [
                      Text(gender['icon'] as String),
                      const SizedBox(width: 8),
                      Text(gender['label'] as String),
                    ],
                  ),
                );
              }).toList(),
              onChanged: _isLoading ? null : (value) {
                if (value != null) {
                  setState(() {
                    _selectedGender = value;
                  });
                }
              },
            ),

            const SizedBox(height: 16),

            // Birth date
            InkWell(
              onTap: _isLoading ? null : _selectBirthDate,
              child: InputDecorator(
                decoration: InputDecoration(
                  labelText: context.tr('pets.birth_date'),
                  prefixIcon: Icon(Icons.calendar_today),
                  border: OutlineInputBorder(),
                ),
                child: Text(
                  '${_birthDate.day}/${_birthDate.month}/${_birthDate.year}',
                  style: TextStyle(
                    color: _isLoading
                        ? Theme.of(context).disabledColor
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhysicalCharacteristicsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.fitness_center,
                  color: Theme.of(context).colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('pets.physical_characteristics'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Weight field
            TextFormField(
              controller: _weightController,
              decoration: InputDecoration(
                labelText: context.tr('pets.weight'),
                hintText: context.tr('pets.weight_placeholder'),
                prefixIcon: Icon(Icons.monitor_weight),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              validator: _validateWeight,
              enabled: !_isLoading,
            ),

            const SizedBox(height: 16),

            // Color field
            TextFormField(
              controller: _colorController,
              decoration: InputDecoration(
                labelText: context.tr('pets.color'),
                hintText: context.tr('pets.color_placeholder'),
                prefixIcon: Icon(Icons.palette),
                border: OutlineInputBorder(),
              ),
              textCapitalization: TextCapitalization.words,
              enabled: !_isLoading,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info,
                  color: Theme.of(context).colorScheme.onSurface,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('pets.additional_info'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Microchip field
            TextFormField(
              controller: _microchipController,
              decoration: InputDecoration(
                labelText: context.tr('pets.microchip_number'),
                hintText: context.tr('pets.microchip_placeholder'),
                prefixIcon: Icon(Icons.memory),
                border: OutlineInputBorder(),
              ),
              enabled: !_isLoading,
            ),

            const SizedBox(height: 16),

            // Notes field
            TextFormField(
              controller: _notesController,
              decoration: InputDecoration(
                labelText: context.tr('pets.notes'),
                hintText: context.tr('pets.notes_placeholder'),
                prefixIcon: Icon(Icons.note),
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              enabled: !_isLoading,
            ),

            const SizedBox(height: 16),

            // Required fields info
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
                border: Border.all(color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      context.tr('pets.required_fields_info'),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('pets.add_pet_title', params: {'clientName': widget.clientName})),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(12.0),
                  margin: const EdgeInsets.only(bottom: 16.0),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    border: Border.all(color: Colors.red[200]!),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: Colors.red[700]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(color: Colors.red[700]),
                        ),
                      ),
                    ],
                  ),
                ),

              _buildPhotoSection(),
              const SizedBox(height: 16),
              _buildBasicInfoSection(),
              const SizedBox(height: 16),
              _buildPhysicalCharacteristicsSection(),
              const SizedBox(height: 16),
              _buildAdditionalInfoSection(),
              const SizedBox(height: 32),

              ElevatedButton(
                onPressed: _isLoading ? null : _savePet,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
                child: _isLoading
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Theme.of(context).colorScheme.onPrimary,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(context.tr('pets.saving')),
                        ],
                      )
                    : Text(context.tr('pets.add_pet_button')),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
