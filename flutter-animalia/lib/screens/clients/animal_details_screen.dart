import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/pet.dart';
import '../../services/client/pet_service.dart';
import '../../utils/snack_bar_utils.dart';
import '../../l10n/app_localizations.dart';
import 'edit_pet_screen.dart';

class AnimalDetailsScreen extends StatefulWidget {
  final Pet pet;

  const AnimalDetailsScreen({
    super.key,
    required this.pet,
  });

  @override
  State<AnimalDetailsScreen> createState() => _AnimalDetailsScreenState();
}

class _AnimalDetailsScreenState extends State<AnimalDetailsScreen> {
  late Pet _currentPet;
  bool _isDeleting = false;
  bool _isUpdating = false;
  bool _isEditingNotes = false;
  late TextEditingController _notesController;

  @override
  void initState() {
    super.initState();
    _currentPet = widget.pet;
    _notesController = TextEditingController(text: _currentPet.notes);
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  String _getGenderText(String gender) {
    switch (gender.toLowerCase()) {
      case 'male':
        return context.tr('pets.male_with_icon');
      case 'female':
        return context.tr('pets.female_with_icon');
      default:
        return context.tr('pets.unknown');
    }
  }

  String _getSpeciesText(String species) {
    switch (species.toLowerCase()) {
      case 'dog':
        return context.tr('pets.dog_with_icon');
      case 'cat':
        return context.tr('pets.cat_with_icon');
      case 'rabbit':
        return context.tr('pets.rabbit_with_icon');
      default:
        return species;
    }
  }

  String _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final difference = now.difference(birthDate);
    final years = (difference.inDays / 365).floor();
    final months = ((difference.inDays % 365) / 30).floor();

    if (years > 0) {
      if (months > 0) {
        return context.tr('pets.age_years_months', params: {
          'years': years.toString(),
          'months': months.toString()
        });
      } else {
        return years == 1
            ? context.tr('pets.age_one_year')
            : context.tr('pets.age_years', params: {'years': years.toString()});
      }
    } else if (months > 0) {
      return months == 1
          ? context.tr('pets.age_one_month')
          : context.tr('pets.age_months', params: {'months': months.toString()});
    } else {
      final weeks = (difference.inDays / 7).floor();
      if (weeks > 0) {
        return weeks == 1
            ? context.tr('pets.age_one_week')
            : context.tr('pets.age_weeks', params: {'weeks': weeks.toString()});
      } else {
        return difference.inDays == 1
            ? context.tr('pets.age_one_day')
            : context.tr('pets.age_days', params: {'days': difference.inDays.toString()});
      }
    }
  }

  Future<void> _editPet() async {
    final result = await Navigator.of(context).push<Pet>(
      MaterialPageRoute(
        builder: (context) => EditPetScreen(pet: _currentPet),
      ),
    );

    if (result != null) {
      setState(() {
        _currentPet = result;
      });
    }
  }

  Future<void> _deletePet() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('pets.delete_pet_title', params: {'petName': _currentPet.name})),
        content: Text(context.tr('pets.delete_pet_confirmation')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.tr('common.cancel')),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text(context.tr('common.delete')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isDeleting = true;
      });

      try {
        final response = await PetService.removePetFromClient(
          _currentPet.ownerId,
          _currentPet.id,
        );

        if (response.success) {
          if (mounted) {
            Navigator.of(context).pop(true); // Return true to indicate deletion
            showTopSnackBar(
              context,
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.white),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(context.tr('pets.delete_success', params: {'petName': _currentPet.name})),
                    ),
                  ],
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            );
          }
        } else {
          throw Exception(response.message ?? context.tr('pets.delete_error_generic'));
        }
      } catch (e) {
        if (mounted) {
          showTopSnackBar(
            context,
            SnackBar(
              content: Text(context.tr('pets.delete_error', params: {'error': e.toString()})),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isDeleting = false;
          });
        }
      }
    }
  }

  Future<void> _updateHealthStatus(bool hasHealthIssues) async {
    // Prevent multiple simultaneous updates
    if (_isUpdating) return;

    if (!mounted) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      // Create a pet update that includes medicalConditions field for backend
      // The backend expects medicalConditions (string), not hasMedicalConditions (boolean)
      print('🔄 Updating health status: $hasHealthIssues for pet ${_currentPet.name}');
      print('📊 Current pet medicalConditions: ${_currentPet.medicalConditions}');
      print('📊 Current pet hasMedicalConditions: ${_currentPet.hasMedicalConditions}');

      final medicalConditionsValue = hasHealthIssues ? context.tr('pets.needs_special_attention') : null;
      print('📤 Sending medicalConditions: $medicalConditionsValue');

      final updatedPet = _currentPet.copyWith(
        medicalConditions: medicalConditionsValue,
      );

      print('📋 Updated pet medicalConditions: ${updatedPet.medicalConditions}');
      print('📋 Updated pet hasMedicalConditions: ${updatedPet.hasMedicalConditions}');
      print('📋 Pet data being sent: ${updatedPet.toJson()}');

      print('🌐 Making API call to update pet...');
      final response = await PetService.updateClientPet(_currentPet.ownerId, _currentPet.id, updatedPet);
      print('🌐 API call completed. Success: ${response.success}');

      if (response.success && response.data != null) {
        print('✅ Health status updated successfully:');
        print('   hasMedicalConditions: ${response.data!.hasMedicalConditions}');
        print('   medicalConditions: ${response.data!.medicalConditions}');
        print('📥 Full response data: ${response.data!.toJson()}');
        if (mounted) {
          setState(() {
            _currentPet = response.data!;
            _isUpdating = false;
          });
        }

        if (mounted) {
          showTopSnackBar(
            context,
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(context.tr('pets.health_status_updated')),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } else {
        throw Exception(response.message ?? context.tr('pets.update_error'));
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(
          context,
          SnackBar(
            content: Text(context.tr('pets.health_status_update_error', params: {'error': e.toString()})),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _updateNotes() async {
    // Prevent multiple simultaneous updates
    if (_isUpdating) return;

    if (!mounted) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final updatedPet = _currentPet.copyWith(
        notes: _notesController.text.trim(),
      );

      final response = await PetService.updateClientPet(_currentPet.ownerId, _currentPet.id, updatedPet);

      if (response.success && response.data != null) {
        setState(() {
          _currentPet = response.data!;
          _isEditingNotes = false;
          _isUpdating = false;
        });

        if (mounted) {
          showTopSnackBar(
            context,
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(context.tr('pets.notes_updated')),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } else {
        throw Exception(response.message ?? context.tr('pets.update_error'));
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(
          context,
          SnackBar(
            content: Text(context.tr('pets.notes_update_error', params: {'error': e.toString()})),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  void _cancelNotesEdit() {
    setState(() {
      _notesController.text = _currentPet.notes;
      _isEditingNotes = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(_currentPet.name),
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _editPet,
            icon: const Icon(Icons.edit),
            tooltip: context.tr('common.edit'),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _deletePet();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    const Icon(Icons.delete, color: Colors.red),
                    const SizedBox(width: 8),
                    Text(context.tr('common.delete'), style: const TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isDeleting
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(context.tr('pets.deleting_pet')),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPetHeader(),
                  const SizedBox(height: 24),
                  _buildPetDetails(),
                  const SizedBox(height: 24),
                  _buildHealthStatus(),
                  if (_currentPet.notes.isNotEmpty) ...[
                    const SizedBox(height: 24),
                    _buildNotesSection(),
                  ],
                ],
              ),
            ),
    );
  }

  Widget _buildPetHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Pet photo
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 38,
              backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              backgroundImage: _currentPet.photoUrl.isNotEmpty
                  ? NetworkImage(_currentPet.photoUrl)
                  : null,
              child: _currentPet.photoUrl.isEmpty
                  ? Icon(
                      Icons.pets,
                      size: 32,
                      color: Theme.of(context).colorScheme.primary,
                    )
                  : null,
            ),
          ),
          const SizedBox(width: 16),
          // Pet info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentPet.name,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getSpeciesText(_currentPet.species),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_currentPet.breed} • ${_getGenderText(_currentPet.gender)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPetDetails() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr('pets.details'),
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  icon: Icons.cake,
                  label: context.tr('pets.age'),
                  value: _calculateAge(_currentPet.birthDate),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDetailItem(
                  icon: Icons.monitor_weight,
                  label: context.tr('pets.weight'),
                  value: '${_currentPet.weight.toStringAsFixed(1)} kg',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  icon: Icons.palette,
                  label: context.tr('pets.color'),
                  value: _currentPet.color.isNotEmpty ? _currentPet.color : context.tr('pets.not_specified'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDetailItem(
                  icon: Icons.memory,
                  label: context.tr('pets.microchip'),
                  value: _currentPet.microchipNumber.isNotEmpty
                      ? _currentPet.microchipNumber
                      : context.tr('pets.not_specified'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildHealthStatus() {
    final hasHealthIssues = _currentPet.hasMedicalConditions;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: hasHealthIssues
            ? Theme.of(context).colorScheme.errorContainer.withValues(alpha: 0.3)
            : Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: hasHealthIssues
              ? Theme.of(context).colorScheme.error.withValues(alpha: 0.3)
              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Checkbox(
            value: hasHealthIssues,
            onChanged: _isUpdating ? null : (bool? value) {
              if (value != null) {
                _updateHealthStatus(value);
              }
            },
            activeColor: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        context.tr('pets.needs_special_attention'),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: hasHealthIssues
                              ? Theme.of(context).colorScheme.error
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                    if (_isUpdating)
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 2),
                Text(
                  hasHealthIssues
                      ? context.tr('pets.needs_special_attention_description')
                      : context.tr('pets.no_special_attention_needed'),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.edit_note,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  context.tr('pets.groomer_notes'),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
              if (!_isEditingNotes && !_isUpdating)
                IconButton(
                  onPressed: () {
                    setState(() {
                      _isEditingNotes = true;
                    });
                  },
                  icon: Icon(
                    Icons.edit,
                    size: 18,
                    color: theme.colorScheme.primary,
                  ),
                  tooltip: context.tr('pets.edit_notes_tooltip'),
                ),
              if (_isUpdating)
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: theme.colorScheme.primary,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          if (_isEditingNotes)
            Column(
              children: [
                TextFormField(
                  controller: _notesController,
                  maxLines: 4,
                  decoration: InputDecoration(
                    hintText: context.tr('pets.add_groomer_notes_hint'),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.all(12),
                  ),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: _isUpdating ? null : _cancelNotesEdit,
                      child: Text(context.tr('common.cancel')),
                    ),
                    const SizedBox(width: 8),
                    FilledButton(
                      onPressed: _isUpdating ? null : _updateNotes,
                      child: Text(context.tr('common.save')),
                    ),
                  ],
                ),
              ],
            )
          else
            GestureDetector(
              onTap: () {
                setState(() {
                  _isEditingNotes = true;
                });
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: theme.colorScheme.outline.withValues(alpha: 0.1),
                    width: 1,
                  ),
                ),
                child: Text(
                  _currentPet.notes.isNotEmpty
                      ? _currentPet.notes
                      : context.tr('pets.add_groomer_notes_prompt'),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: _currentPet.notes.isNotEmpty
                        ? theme.colorScheme.onSurface
                        : theme.colorScheme.onSurfaceVariant,
                    height: 1.4,
                    fontStyle: _currentPet.notes.isEmpty ? FontStyle.italic : null,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

