import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';

import '../../../l10n/app_localizations.dart';
import '../../../models/notification_settings.dart';
import '../../../services/notification_settings_service.dart';
import '../../../widgets/permission_guard.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  // Backend integration state
  NotificationSettings? _currentSettings;
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  // Local state for UI
  bool _pushNotificationsEnabled = true;
  String _soundPreference = 'default';
  bool _vibrationEnabled = true;

  // Do Not Disturb settings
  bool _dndEnabled = false;
  String _dndStart = '22:00';
  String _dndEnd = '08:00';
  bool _allowCritical = true;

  // Notification rules
  bool _newAppointments = true;
  bool _appointmentCancellations = true;
  bool _appointmentRescheduled = true;
  NotificationPriority _defaultPriority = NotificationPriority.normal;

  @override
  void initState() {
    super.initState();
    _loadNotificationSettings();
  }

  /// Load notification settings from backend
  Future<void> _loadNotificationSettings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await NotificationSettingsService.getNotificationSettings();

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _pushNotificationsEnabled = response.data!.pushNotificationsEnabled;
          _soundPreference = response.data!.soundPreference;
          _vibrationEnabled = response.data!.vibrationEnabled;

          // Do Not Disturb settings
          _dndEnabled = response.data!.doNotDisturb.enabled;
          _dndStart = response.data!.doNotDisturb.startTime;
          _dndEnd = response.data!.doNotDisturb.endTime;
          _allowCritical = response.data!.doNotDisturb.allowCritical;

          // Notification rules
          _newAppointments = response.data!.notificationRules.newAppointments;
          _appointmentCancellations = response.data!.notificationRules.appointmentCancellations;
          _appointmentRescheduled = response.data!.notificationRules.appointmentRescheduled;
          _defaultPriority = response.data!.notificationRules.defaultPriority;

          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? context.tr('notification_settings.failed_to_load_settings');
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = context.tr('notification_settings.error_loading_settings', params: {'error': e.toString()});
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('notification_settings.title'),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
      ),
      body: PermissionGuard(
        child: _isLoading
          ? Center(child: CircularProgressIndicator(color: Theme.of(context).colorScheme.onSurface))
          : _error != null
            ? _buildErrorView()
            : Column(
                children: [
                  // Scrollable content
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // General Settings Section
                          _buildSectionHeader(context.tr('notification_settings.general_settings')),
                          const SizedBox(height: 16),
                          _buildGeneralSettingsCard(),
                          const SizedBox(height: 24),

                          // Sound & Vibration Section
                          _buildSectionHeader(context.tr('notification_settings.sound_vibration')),
                          const SizedBox(height: 16),
                          _buildSoundVibrationCard(),
                          const SizedBox(height: 24),

                          // Do Not Disturb Section
                          _buildSectionHeader(context.tr('notification_settings.do_not_disturb')),
                          const SizedBox(height: 16),
                          _buildDoNotDisturbCard(),
                          const SizedBox(height: 24),

                          // Notification Rules Section
                          _buildSectionHeader(context.tr('notification_settings.notification_rules')),
                          const SizedBox(height: 16),
                          _buildNotificationRulesCard(),
                          const SizedBox(height: 80), // Space for fixed button
                        ],
                      ),
                    ),
                  ),
                  // Fixed save button at bottom
                  _buildFixedSaveButton(),
                ],
              ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  Widget _buildGeneralSettingsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            _buildSwitchTile(
              title: context.tr('notification_settings.push_notifications'),
              subtitle: context.tr('notification_settings.push_notifications_subtitle'),
              value: _pushNotificationsEnabled,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('pushNotificationsEnabled', value),
              icon: Icons.notifications,
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Theme.of(context).primaryColor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildSoundVibrationCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            _buildSwitchTile(
              title: context.tr('notification_settings.vibration'),
              subtitle: context.tr('notification_settings.vibration_subtitle'),
              value: _vibrationEnabled,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('vibrationEnabled', value),
              icon: Icons.vibration,
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildDoNotDisturbCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            _buildSwitchTile(
              title: context.tr('notification_settings.do_not_disturb_title'),
              subtitle: context.tr('notification_settings.do_not_disturb_subtitle'),
              value: _dndEnabled,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('dndEnabled', value),
              icon: Icons.do_not_disturb,
            ),
            if (_dndEnabled) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.tr('notification_settings.start_time'),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: _isSaving ? null : () => _selectTime(true),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(_dndStart),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.tr('notification_settings.end_time'),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: _isSaving ? null : () => _selectTime(false),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(_dndEnd),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildSwitchTile(
                title: context.tr('notification_settings.allow_critical'),
                subtitle: context.tr('notification_settings.allow_critical_subtitle'),
                value: _allowCritical,
                onChanged: _isSaving ? (value) {} : (value) => _updateSetting('allowCritical', value),
                icon: Icons.priority_high,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationRulesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            _buildSwitchTile(
              title: context.tr('notification_settings.new_appointments'),
              subtitle: context.tr('notification_settings.new_appointments_subtitle'),
              value: _newAppointments,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('newAppointments', value),
              icon: Icons.event_available,
            ),
            _buildSwitchTile(
              title: context.tr('notification_settings.appointment_cancellations'),
              subtitle: context.tr('notification_settings.appointment_cancellations_subtitle'),
              value: _appointmentCancellations,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('appointmentCancellations', value),
              icon: Icons.event_busy,
            ),
            _buildSwitchTile(
              title: context.tr('notification_settings.appointment_rescheduled'),
              subtitle: context.tr('notification_settings.appointment_rescheduled_subtitle'),
              value: _appointmentRescheduled,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('appointmentRescheduled', value),
              icon: Icons.update,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFixedSaveButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
    );
  }


  /// Update a specific notification setting
  Future<void> _updateSetting(String settingKey, dynamic value) async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Update local state first for immediate UI feedback
      setState(() {
        switch (settingKey) {
          case 'pushNotificationsEnabled':
            _pushNotificationsEnabled = value as bool;
            break;
          case 'vibrationEnabled':
            _vibrationEnabled = value as bool;
            break;
          case 'soundPreference':
            _soundPreference = value as String;
            break;
          case 'dndEnabled':
            _dndEnabled = value as bool;
            break;
          case 'allowCritical':
            _allowCritical = value as bool;
            break;
          case 'newAppointments':
            _newAppointments = value as bool;
            break;
          case 'appointmentCancellations':
            _appointmentCancellations = value as bool;
            break;
          case 'appointmentRescheduled':
            _appointmentRescheduled = value as bool;
            break;
          case 'defaultPriority':
            _defaultPriority = value as NotificationPriority;
            break;
        }
      });

      // Create update request with current values
      final request = UpdateNotificationSettingsRequest(
        pushNotificationsEnabled: _pushNotificationsEnabled,
        soundPreference: _soundPreference,
        vibrationEnabled: _vibrationEnabled,
        doNotDisturb: DoNotDisturbSettings(
          enabled: _dndEnabled,
          startTime: _dndStart,
          endTime: _dndEnd,
          allowCritical: _allowCritical,
        ),
        notificationRules: NotificationRules(
          newAppointments: _newAppointments,
          appointmentCancellations: _appointmentCancellations,
          appointmentRescheduled: _appointmentRescheduled,
          defaultPriority: _defaultPriority,
        ),
      );

      // Validate request
      final validationError = NotificationSettingsService.validateNotificationSettingsRequest(request);
      if (validationError != null) {
        _showErrorSnackBar(validationError);
        // Revert local state
        await _loadNotificationSettings();
        return;
      }

      // Send to backend
      final response = await NotificationSettingsService.updateNotificationSettings(request);

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });
        _showSuccessSnackBar(context.tr('notification_settings.settings_updated_success'));
      } else {
        _showErrorSnackBar(response.error ?? context.tr('notification_settings.settings_update_error'));
        // Revert local state
        await _loadNotificationSettings();
      }
    } catch (e) {
      _showErrorSnackBar(context.tr('notification_settings.settings_update_generic_error', params: {'error': e.toString()}));
      // Revert local state
      await _loadNotificationSettings();
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Select time for Do Not Disturb
  Future<void> _selectTime(bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: int.parse((isStartTime ? _dndStart : _dndEnd).split(':')[0]),
        minute: int.parse((isStartTime ? _dndStart : _dndEnd).split(':')[1]),
      ),
    );

    if (picked != null) {
      final timeString = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';

      if (isStartTime) {
        setState(() {
          _dndStart = timeString;
        });
        await _updateSetting('dndStart', timeString);
      } else {
        setState(() {
          _dndEnd = timeString;
        });
        await _updateSetting('dndEnd', timeString);
      }
    }
  }


  /// Show success message
  void _showSuccessSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.primary,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show error message
  void _showErrorSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Build error view
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              context.tr('notification_settings.error_loading_title'),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _error ?? context.tr('notification_settings.unknown_error'),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadNotificationSettings,
              style: ElevatedButton.styleFrom(),
              child: Text(context.tr('notification_settings.try_again')),
            ),
          ],
        ),
      ),
    );
  }

  /// Build no permission view
  Widget _buildNoPermissionView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
             Icon(
              Icons.lock_outline,
              size: 64,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
             Text(
              context.tr('notification_settings.restricted_access_title'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              context.tr('notification_settings.restricted_access_message'),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(),
              child: Text(context.tr('notification_settings.back_button')),
            ),
          ],
        ),
      ),
    );
  }
}