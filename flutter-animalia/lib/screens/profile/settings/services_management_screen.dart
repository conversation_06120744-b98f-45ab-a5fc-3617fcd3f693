import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';

import '../../../models/service.dart';
import '../../../services/service_management_service.dart';
import '../../../services/ui_notification_service.dart';
import '../../services/service_form_screen.dart';
import '../../../l10n/app_localizations.dart';

class ServicesManagementScreen extends StatefulWidget {
  const ServicesManagementScreen({Key? key}) : super(key: key);

  @override
  State<ServicesManagementScreen> createState() => _ServicesManagementScreenState();
}

class _ServicesManagementScreenState extends State<ServicesManagementScreen> {
  List<Service> services = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadServices();
  }

  Future<void> _loadServices() async {
    setState(() {
      isLoading = true;
    });

    try {
      final response = await ServiceManagementService.getServices();
      if (response.success && response.data != null) {
        setState(() {
          // Group services by color
          services = _groupServicesByColor(response.data!.cast<Service>());
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
        _showErrorSnackBar(context.tr('services_management.error_loading_services_with_details', params: {'error': response.error ?? ''}));
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      _showErrorSnackBar(context.tr('services_management.error_loading_services_with_details', params: {'error': e.toString()}));
    }
  }

  /// Group services by color for better visual organization
  List<Service> _groupServicesByColor(List<Service> servicesList) {
    // Create a map to group services by color
    final Map<String, List<Service>> colorGroups = {};

    for (final service in servicesList) {
      final color = service.color ?? '#9E9E9E'; // Default to grey if no color
      if (!colorGroups.containsKey(color)) {
        colorGroups[color] = [];
      }
      colorGroups[color]!.add(service);
    }

    // Sort each color group by display order, then by name
    for (final group in colorGroups.values) {
      group.sort((a, b) {
        final orderCompare = a.displayOrder.compareTo(b.displayOrder);
        if (orderCompare != 0) return orderCompare;
        return a.name.compareTo(b.name);
      });
    }

    // Flatten the groups back into a single list
    // Sort groups by color to maintain consistent order
    final sortedColors = colorGroups.keys.toList()..sort();
    final List<Service> groupedServices = [];

    for (final color in sortedColors) {
      groupedServices.addAll(colorGroups[color]!);
    }

    return groupedServices;
  }

  void _showErrorSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('services_management.title'),
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        actions: [
          IconButton(
            icon:  Icon(Icons.refresh),
            onPressed: _loadServices,
          ),
        ],
      ),
      body: Column(
        children: [
          // Header with add button
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    context.tr('services_management.services_available_count', params: {'count': services.length.toString()}),
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () => _showAddServiceScreen(),
                  child:  Icon(Icons.add),
                ),
              ],
            ),
          ),
          // Services list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: services.length,
              itemBuilder: (context, index) {
                final service = services[index];

                // Check if this is the first service or if the color changed from previous
                final bool showColorHeader = index == 0 ||
                    (services[index - 1].color ?? '#9E9E9E') != (service.color ?? '#9E9E9E');

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Add spacing before new color group (except for first item)
                    if (showColorHeader && index > 0)
                      const SizedBox(height: 16),

                    _buildServiceCard(service),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceCard(Service service) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showEditServiceScreen(service),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Color indicator (default to grey if no color)
                Container(
                  width: 4,
                  height: 48,
                  decoration: BoxDecoration(
                    color: _parseColor(service.color ?? '#9E9E9E'),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              service.name,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: service.isActive
                                  ? Theme.of(context).colorScheme.onSurface
                                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                            ),
                          ),
                          if (!service.isActive)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surfaceVariant,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                context.tr('services_management.inactive_badge'),
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                        ],
                      ),
                      SizedBox(height: 4),
                      if (service.category.isNotEmpty)
                        Text(
                          service.category,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      if (service.description.isNotEmpty) ...[
                        SizedBox(height: 4),
                        Text(
                          service.description,
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      _showEditServiceScreen(service);
                    } else if (value == 'toggle') {
                      _toggleServiceStatus(service);
                    } else if (value == 'duplicate') {
                      _duplicateService(service);
                    } else if (value == 'delete_permanent') {
                      _showPermanentDeleteConfirmation(service);
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: Theme.of(context).colorScheme.onSurface),
                          const SizedBox(width: 8),
                          Text(context.tr('services_management.edit_action')),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'toggle',
                      child: Row(
                        children: [
                          Icon(
                            service.isActive ? Icons.visibility_off : Icons.visibility,
                            color: service.isActive ? Theme.of(context).colorScheme.secondary : Theme.of(context).colorScheme.primary,
                          ),
                          SizedBox(width: 8),
                          Text(service.isActive ? context.tr('services_management.deactivate_action') : context.tr('services_management.activate_action')),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'duplicate',
                      child: Row(
                        children: [
                          Icon(Icons.copy, color: Theme.of(context).colorScheme.secondary),
                          const SizedBox(width: 8),
                          Text(context.tr('services_management.duplicate_action')),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete_permanent',
                      child: Row(
                        children: [
                          Icon(Icons.delete_forever, color: Theme.of(context).colorScheme.error),
                          const SizedBox(width: 8),
                          Text(context.tr('services_management.delete_permanent_action'), style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 12),
            // Use Wrap instead of Row to prevent overflow with long price text
            Wrap(
              spacing: 12,
              runSpacing: 8,
              children: [
                _buildInfoChip(
                  icon: Icons.access_time,
                  label: service.formattedDuration,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                _buildInfoChip(
                  icon: Icons.attach_money,
                  label: service.formattedPrice,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                if (service.requirements.isNotEmpty)
                  _buildInfoChip(
                    icon: Icons.rule,
                    label: context.tr('services_management.requirements_count', params: {'count': service.requirements.length.toString()}),
                    color: Theme.of(context).colorScheme.tertiary,
                  ),
              ],
            ),
            if (service.requirements.isNotEmpty) ...[
              SizedBox(height: 8),
              Wrap(
                spacing: 4,
                runSpacing: 4,
                children: service.requirements.take(3).map((requirement) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      requirement,
                      style: TextStyle(
                        fontSize: 10,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    ));
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          SizedBox(width: 4),
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddServiceScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ServiceFormScreen(
          onServiceSaved: (_) => _loadServices(),
        ),
      ),
    );
  }

  void _showEditServiceScreen(Service service) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ServiceFormScreen(
          service: service,
          onServiceSaved: (_) => _loadServices(),
        ),
      ),
    );
  }

  Future<void> _toggleServiceStatus(Service service) async {
    try {
      final response = await ServiceManagementService.toggleServiceStatus(
        service.id,
        !service.isActive,
      );

      if (response.success) {
        _loadServices();
        UINotificationService.showSuccess(
          context: context,
          title: context.tr('common.success'),
          message: service.isActive
              ? context.tr('services_management.service_deactivated_success', params: {'name': service.name})
              : context.tr('services_management.service_activated_success', params: {'name': service.name}),
        );
      } else {
        _showErrorSnackBar(context.tr('services_management.error_with_details', params: {'error': response.error ?? ''}));
      }
    } catch (e) {
      _showErrorSnackBar(context.tr('services_management.error_changing_status', params: {'error': e.toString()}));
    }
  }

  Future<void> _duplicateService(Service service) async {
    try {
      final response = await ServiceManagementService.duplicateService(service.id);

      if (response.success && response.data != null) {
        _loadServices();
        UINotificationService.showSuccess(
          context: context,
          title: context.tr('common.success'),
          message: context.tr('services_management.service_duplicated_success', params: {'name': response.data!.name}),
        );
      } else {
        _showErrorSnackBar(context.tr('services_management.error_with_details', params: {'error': response.error ?? ''}));
      }
    } catch (e) {
      _showErrorSnackBar(context.tr('services_management.error_duplicating_service', params: {'error': e.toString()}));
    }
  }


  void _showDeleteConfirmation(Service service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('services_management.confirm_deactivation_title')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(context.tr('services_management.confirm_deactivation_message', params: {'name': service.name})),
            SizedBox(height: 8),
            Text(
              context.tr('services_management.deactivation_explanation'),
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('common.cancel')),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteService(service);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.secondary,
              
            ),
            child: Text(context.tr('services_management.deactivate_button')),
          ),
        ],
      ),
    );
  }

  void _showPermanentDeleteConfirmation(Service service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('services_management.confirm_permanent_delete_title')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(context.tr('services_management.confirm_permanent_delete_message', params: {'name': service.name})),
            SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Theme.of(context).colorScheme.error.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning, color: Theme.of(context).colorScheme.error, size: 20),
                      SizedBox(width: 8),
                      Text(
                        context.tr('services_management.warning_attention'),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    context.tr('services_management.permanent_delete_warning'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('common.cancel')),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _permanentDeleteService(service);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(context.tr('services_management.delete_permanent_button')),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteService(Service service) async {
    try {
      final response = await ServiceManagementService.deleteService(service.id);

      if (response.success) {
        _loadServices();
        UINotificationService.showSuccess(
          context: context,
          title: context.tr('common.success'),
          message: context.tr('services_management.service_deactivated_success', params: {'name': service.name}),
        );
      } else {
        _showErrorSnackBar(context.tr('services_management.error_with_details', params: {'error': response.error ?? ''}));
      }
    } catch (e) {
      _showErrorSnackBar(context.tr('services_management.error_deactivating_service', params: {'error': e.toString()}));
    }
  }

  Future<void> _permanentDeleteService(Service service) async {
    try {
      final response = await ServiceManagementService.permanentDeleteService(service.id);

      if (response.success) {
        _loadServices();
        UINotificationService.showSuccess(
          context: context,
          title: context.tr('common.success'),
          message: context.tr('services_management.service_deleted_permanently_success', params: {'name': service.name}),
        );
      } else {
        _showErrorSnackBar(context.tr('services_management.error_with_details', params: {'error': response.error ?? ''}));
      }
    } catch (e) {
      _showErrorSnackBar(context.tr('services_management.error_deleting_permanently', params: {'error': e.toString()}));
    }
  }

  /// Parse hex color string to Color object
  Color _parseColor(String hexColor) {
    try {
      // Remove # if present
      final hex = hexColor.replaceAll('#', '');
      // Add FF for full opacity if not present
      final colorValue = int.parse('FF$hex', radix: 16);
      return Color(colorValue);
    } catch (e) {
      // Return default color if parsing fails
      return Theme.of(context).colorScheme.primary;
    }
  }
}
