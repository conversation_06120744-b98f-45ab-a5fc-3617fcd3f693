import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../l10n/app_localizations.dart';

import '../../../config/theme/app_theme.dart';
import '../../../models/user_role.dart';
import '../../../models/user_salon_association.dart';
import '../../../providers/auth_provider.dart';
import '../../../providers/calendar_provider.dart';
import '../../../providers/role_provider.dart';
import '../../../providers/subscription_provider.dart';
import '../../../providers/theme_provider.dart';
import '../../../services/auth/auth_service.dart';
import '../../../services/onboarding/onboarding_tour_service.dart';
import '../../../services/salon_service.dart';
import '../../../services/tour_keys.dart';
import '../../../services/url_launcher_service.dart';
import '../../../utils/debug_logger.dart';
import '../../../widgets/cards/revolut_salon_switcher.dart';
import '../../../widgets/language_switcher.dart';
import '../../../widgets/onboarding/tour_showcase_widget.dart';
import '../../../widgets/settings/color_selection_card.dart';
import '../../../widgets/verification/delete_account_verification_dialog.dart';
import '../../main_layout.dart';

class UserSettingsScreen extends StatefulWidget {
  const UserSettingsScreen({Key? key}) : super(key: key);

  @override
  State<UserSettingsScreen> createState() => _UserSettingsScreenState();
}

class _UserSettingsScreenState extends State<UserSettingsScreen> {
// Feature flag for translations

  // Easter egg state for ColorSelectionCard
  int _themeToggleCount = 0;
  DateTime? _firstToggleTime;
  bool _showColorSelection = false;
  static const int _requiredToggles = 10;
  static const int _timeWindowSeconds = 20;

  @override
  void initState() {
    super.initState();

    // Tour logic is now handled by the dedicated tour services
    DebugLogger.logShowcase('🎯 UserSettingsScreen: Initialized');
  }

  /// Handle theme toggle for Easter egg functionality
  void _handleThemeToggle() {
    final now = DateTime.now();

    // Reset if time window has expired
    if (_firstToggleTime != null &&
        now.difference(_firstToggleTime!).inSeconds > _timeWindowSeconds) {
      _themeToggleCount = 0;
      _firstToggleTime = null;
      _showColorSelection = false;
    }

    // Set first toggle time if not set
    if (_firstToggleTime == null) {
      _firstToggleTime = now;
    }

    _themeToggleCount++;

    // Check if Easter egg should be activated
    if (_themeToggleCount >= _requiredToggles) {
      setState(() {
        _showColorSelection = true;
      });

      // Show a subtle notification
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.tr('settings.easter_egg_activated')),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.deepPurple,
        ),
      );
    }
  }

  /// Reset Easter egg when navigating away
  @override
  void dispose() {
    _themeToggleCount = 0;
    _firstToggleTime = null;
    _showColorSelection = false;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            context.tr('settings.title'),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          elevation: 0,
          actions: [
            IconButton(
              icon: const Icon(Icons.help_outline),
              onPressed: () async {
                DebugLogger.logVerbose('🎯 UserSettingsScreen: Manual tour trigger');

                // Reset tour completion status to force tour to show
                await OnboardingTourService.resetTour();

                // Start the new salon tour
                final mainLayout = MainLayout.globalKey.currentState;
                if (mainLayout != null) {
                  mainLayout.startNewSalonTour();

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(context.tr('settings.tour_restart_success')),
                      backgroundColor: Colors.green,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(context.tr('settings.tour_restart_error')),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }
              },
              tooltip: context.tr('settings.help_tooltip'),
            ),
          ],
        ),
        body: _buildSingleChildScrollView(context),
    );
  }




  SingleChildScrollView _buildSingleChildScrollView(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [

          // DEBUG: Tour Debug Section (remove in production)
          // _buildTourDebugSection(),

          _buildSalonManagementSection(),

          // User Settings Section
          _buildSectionHeader(context.tr('settings.user_section')),
          const SizedBox(height: 16),
          _buildUserSettingsOptions(context),
          const SizedBox(height: 24),

          _buildYouTubeDemoSection(context),

          // Application Settings Section
          _buildSectionHeader(context.tr('settings.application_section')),
          const SizedBox(height: 16),
          _buildApplicationSettingsOptions(context),
          const SizedBox(height: 24),

          // Danger Zone Section
          _buildDangerZoneSection(context),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }


  Widget _buildSalonManagementSection() {
    return Consumer<RoleProvider>(
      builder: (context, roleProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader(context.tr('settings.salon_section')),
            const SizedBox(height: 16),

            // Show different content based on salon association
            FutureBuilder<String?>(
              future: AuthService.getCurrentSalonId(),
              builder: (context, snapshot) {

                // if (hasSalonId) {
                  // User has salon association - show consolidated salon management card
                  return _buildConsolidatedSalonCard(roleProvider);
                // } else {
                  // // User has no salon association - show create salon option
                  // return Column(
                  //   children: [
                  //     _buildCreateSalonCard(),
                  //     const SizedBox(height: 12),
                  //     _buildPendingInvitationsCard(),
                  //   ],
                  // );
                // }
              },
            ),
          ],
        );
      },
    );
  }





  void _showSalonSwitcher(BuildContext context) async {
    try {
      final response = await SalonService.getUserSalons();

      if (!mounted) return; // Check if widget is still mounted

      if (response.success && response.data != null) {
        // ignore: use_build_context_synchronously
        _showRevolutStyleSalonSwitcher(context, response.data!);
      } else {
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            showTopSnackBar(
              context,
              SnackBar(
                content: Text(
                  context.tr('salon_switcher.salon_info_error', params: {'error': response.error ?? 'Unknown error'}),
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        });
      }
    } catch (e) {
      if (!mounted) return; // Check if widget is still mounted

      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          showTopSnackBar(
            context,
            SnackBar(
              content: Text(context.tr('salon_switcher.salon_info_error', params: {'error': e.toString()})),
              backgroundColor: Colors.red,
            ),
          );
        }
      });
    }
  }

  void _showRevolutStyleSalonSwitcher(
      BuildContext context,
      List<UserSalonAssociation> salons,
      ) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) =>
            RevolutStyleSalonSwitcher(salons: salons),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween = Tween(
            begin: begin,
            end: end,
          ).chain(CurveTween(curve: curve));

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
    );
  }



  Future<void> _logout(BuildContext context) async {
    // Show confirmation dialog first
    final shouldLogout = await _showLogoutConfirmation(context);

    if (shouldLogout == true) {
      // Log out using the auth provider
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.signOut();

      if (context.mounted) {
        // Restart the app to ensure clean state after sign out
        Phoenix.rebirth(context);
      }
    }
  }


  Widget _buildMetricItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.9), size: 20),
        const SizedBox(height: 6),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }




  Future<void> _deleteAccount(BuildContext context) async {
    // Get current user's phone number
    final currentPhone = await AuthService.getCurrentUserPhone();

    if (currentPhone == null || currentPhone.isEmpty || currentPhone == 'Nesetat') {
      // Show error if no phone number is set
      if (context.mounted) {
        showTopSnackBar(
          context,
          const SnackBar(
            content: Text('Pentru a șterge contul, trebuie să aveți un număr de telefon setat în profil.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
      return;
    }

    // Show phone verification dialog
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => DeleteAccountVerificationDialog(
          phoneNumber: currentPhone,
          onVerified: (verificationCode) => _proceedWithAccountDeletion(context, verificationCode),
        ),
      );
    }
  }

  Future<void> _proceedWithAccountDeletion(BuildContext context, String verificationCode) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.deleteAccount("confirm"); // Still use "confirm" for backend compatibility

      if (context.mounted) {
        if (success) {
          // Account deleted successfully - perform complete cleanup

          // Clear all providers to ensure clean state
          final roleProvider = Provider.of<RoleProvider>(context, listen: false);
          final subscriptionProvider = Provider.of<SubscriptionProvider>(context, listen: false);
          final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);

          // Clear provider states
          roleProvider.clear();
          subscriptionProvider.clear();
          calendarProvider.clearStaffWorkingHoursCache();

          // Force AuthProvider to clear all data completely
          await authProvider.clearAllData();

          // Show success message
          showTopSnackBar(
            context,
            const SnackBar(
              content: Text('Contul a fost șters cu succes'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );

          // Add a small delay to show the success message
          await Future.delayed(const Duration(milliseconds: 2500));

          // Restart the entire app to ensure clean state
          if (context.mounted) {
            Phoenix.rebirth(context);
          }
        } else {
          // Show error message with better handling for database errors
          String errorMessage = authProvider.error ?? 'A apărut o eroare necunoscută';

          // Check if it's a database casting error
          if (errorMessage.contains('cannot cast type bytea to boolean')) {
            errorMessage = 'Eroare de configurare a bazei de date. Contactați suportul.';
          } else if (errorMessage.contains('JDBC exception')) {
            errorMessage = 'Eroare de bază de date. Încercați din nou mai târziu sau contactați suportul.';
          }

          showTopSnackBar(
            context,
            SnackBar(
              content: Text('Eroare la ștergerea contului: $errorMessage'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        showTopSnackBar(
          context,
          SnackBar(
            content: Text('Eroare la ștergerea contului: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }














  void _handleSalonCardTap(
      BuildContext context,
      UserSalonAssociation? currentSalon,
      bool hasMultipleSalons,
      RoleProvider roleProvider,
      ) {
    // Always show salon switcher - it now includes management actions
    _showSalonSwitcher(context);
  }


  Widget _buildConsolidatedSalonCard(RoleProvider roleProvider) {
    return FutureBuilder<List<UserSalonAssociation>>(
      future: SalonService.getUserSalons().then(
            (response) => response.data ?? [],
      ),
      builder: (context, snapshot) {
        final salons = snapshot.data ?? [];
        final currentSalon =
        salons.isNotEmpty
            ? salons.firstWhere(
              (s) => s.isCurrentSalon,
          orElse: () => salons.first,
        )
            : null;
        final hasMultipleSalons = salons.length > 1;

        return TourShowcaseWidget(
          showcaseKey: SettingsTourKeys.salonSwitcherKey,
          stepId: OnboardingTourService.salonSwitcherStep,
          title: 'Gestionare Saloane',
          description: context.tr('settings.salon_management_description'),
          child: Card(
            elevation: 3,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap:
                      () => _handleSalonCardTap(
                    context,
                    currentSalon,
                    hasMultipleSalons,
                    roleProvider,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with user name and salon info
                        FutureBuilder<String?>(
                          future: AuthService.isAuthenticated() ? AuthService.getCurrentUserName() : Future.value(null),
                          builder: (context, snapshot) {
                            final userName = snapshot.data ?? 'Utilizator';
                            return Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Icon(
                                    Icons.person,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        userName,
                                        style: const TextStyle(
                                          fontSize: 22,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(height: 2),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.business,
                                            color: Colors.white.withValues(
                                              alpha: 0.8,
                                            ),
                                            size: 16,
                                          ),
                                          const SizedBox(width: 4),
                                          Expanded(
                                            child: Text(
                                              currentSalon?.salon.name ??
                                                  'Salon de Toaletaj',
                                              style: TextStyle(
                                                fontSize: 16,
                                                color: Colors.white.withValues(
                                                  alpha: 0.9,
                                                ),
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 2),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.location_on,
                                            color: Colors.white.withValues(
                                              alpha: 0.7,
                                            ),
                                            size: 14,
                                          ),
                                          const SizedBox(width: 4),
                                          Expanded(
                                            child: Text(
                                              currentSalon?.salon.address ??
                                                  'Adresa salon',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.white.withValues(
                                                  alpha: 0.8,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                if (hasMultipleSalons) ...[
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          Icons.swap_horiz,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '${salons.length}',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ],
                            );
                          },
                        ),

                        const SizedBox(height: 20),

                        // Metrics row
                        Row(
                          children: [
                            Expanded(
                              child: _buildMetricItem(
                                context.tr('settings.salon_details.clients'),
                                '${currentSalon?.clientCount ?? 0}',
                                Icons.people,
                              ),
                            ),
                            Container(
                              width: 1,
                              height: 40,
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                            Expanded(
                              child: Consumer<CalendarProvider>(
                                builder: (context, calendarProvider, child) {
                                  // Get today's appointments count
                                  final todayAppointments =
                                      calendarProvider.appointments.where((
                                          appointment,
                                          ) {
                                    final appointmentDate = DateTime(
                                      appointment.startTime.year,
                                      appointment.startTime.month,
                                      appointment.startTime.day,
                                    );
                                    final today = DateTime(
                                      DateTime.now().year,
                                      DateTime.now().month,
                                      DateTime.now().day,
                                    );
                                    return appointmentDate.isAtSameMomentAs(
                                      today,
                                    );
                                  }).length;

                                  return _buildMetricItem(
                                    context.tr('settings.salon_details.appointments'),
                                    '$todayAppointments',
                                    Icons.calendar_today,
                                  );
                                },
                              ),
                            ),
                            Container(
                              width: 1,
                              height: 40,
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                            Expanded(
                              child: _buildMetricItem(
                                context.tr('settings.salon_details.status'),
                                context.tr('settings.salon_details.open'),
                                // todo Mock data - replace with real data
                                Icons.schedule,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Role and permissions row
                        LayoutBuilder(
                          builder: (context, constraints) {
                            final availableWidth = constraints.maxWidth;
                            final isNarrowScreen = availableWidth < 350;

                            return Column(
                              children: [
                                // First row with role and permission badges
                                Row(
                                  children: [
                                    Flexible(
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 10,
                                          vertical: 6,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withValues(
                                            alpha: 0.2,
                                          ),
                                          borderRadius: BorderRadius.circular(20),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              _getRoleIcon(
                                                currentSalon?.groomerRole,
                                              ),
                                              color: Colors.white,
                                              size: 14,
                                            ),
                                            const SizedBox(width: 4),
                                            Flexible(
                                              child: Text(
                                                currentSalon?.roleDisplayName ??
                                                    context.tr('settings.salon_details.groomer_chief'),
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 12,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Flexible(
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 10,
                                          vertical: 6,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withValues(
                                            alpha: 0.2,
                                          ),
                                          borderRadius: BorderRadius.circular(20),
                                        ),
                                        child: Text(
                                          currentSalon?.permissionDisplayName ??
                                              context.tr('settings.salon_details.full_access'),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 12,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                // Second row with action hint
                                const SizedBox(height: 8),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.touch_app,
                                      color: Colors.white.withValues(alpha: 0.6),
                                      size: 14,
                                    ),
                                    const SizedBox(width: 4),
                                    Flexible(
                                      child: Text(
                                        context.tr('settings.salon_details.tap_for_salon_options'),
                                        style: TextStyle(
                                          color: Colors.white.withValues(
                                            alpha: 0.8,
                                          ),
                                          fontSize: 11,
                                          fontStyle: FontStyle.italic,
                                        ),
                                        textAlign: TextAlign.center,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }













  IconData _getRoleIcon(GroomerRole? role) {
    switch (role) {
      case GroomerRole.chiefGroomer:
        return Icons.star;
      case GroomerRole.groomer:
        return Icons.person;
      case GroomerRole.assistant:
        return Icons.support_agent;
      case null:
        return Icons.device_unknown;
    // ignore: deprecated_member_use_from_same_package
      case GroomerRole.regularGroomer:
        return Icons.person; // Legacy support
    }
  }


  Widget _buildUserSettingsOptions(BuildContext context) {
    return Column(
      children: [
        // User name editing option
        FutureBuilder<String?>(
          future: AuthService.isAuthenticated() ? AuthService.getCurrentUserName() : Future.value(null),
          builder: (context, snapshot) {
            final currentName = snapshot.data ?? 'Utilizator';
            return TourShowcaseWidget(
              showcaseKey: SettingsTourKeys.nameEditKey,
              stepId: OnboardingTourService.nameEditStep,
              title: context.tr('settings.edit_name_title'),
              description: 'Va aparea in calendar.',
              child: _buildOptionCard(
                context,
                icon: Icons.person_outline,
                title: context.tr('settings.edit_name_title'),
                subtitle: context.tr('settings.edit_name_current', params: {'name': currentName}),
                onTap: () => _showEditNameDialog(context, currentName),
                dense: true,
              ),
            );
          },
        ),
        const SizedBox(height: 8),
        // Phone number editing option
        FutureBuilder<String?>(
          future: AuthService.isAuthenticated() ? AuthService.getCurrentUserPhone() : Future.value(null),
          builder: (context, snapshot) {
            final currentPhone = snapshot.data ?? 'Nesetat';
            return _buildOptionCard(
              context,
              icon: Icons.phone_outlined,
              title: context.tr('settings.edit_phone_title'),
              subtitle: context.tr('settings.edit_phone_current', params: {'phone': currentPhone}),
              onTap: () => _showEditPhoneDialog(context, currentPhone),
              dense: true,
            );
          },
        ),
      ],
    );
  }

  Widget _buildApplicationSettingsOptions(BuildContext context) {
    return Column(
      children: [
        // Admin options (only show for admin users)
        Consumer<RoleProvider>(
          builder: (context, roleProvider, child) {
            if (roleProvider.isAdmin) {
              return Column(
                children: [
                  _buildAdminSection(context),
                  const SizedBox(height: 16),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),

        // Theme toggle option
        TourShowcaseWidget(
          showcaseKey: SettingsTourKeys.themeToggleKey,
          stepId: OnboardingTourService.themeToggleStep,
          title: context.tr('settings.theme'),
          description: 'Schimbă între tema luminoasă și întunecată pentru confortul tău vizual.',
          child: _buildThemeToggleCard(context),
        ),
        const SizedBox(height: 8),

        // Language selection option
        _buildLanguageSelectionCard(context),
        const SizedBox(height: 8),

        // Primary Color selection (Easter egg - only show after 10 theme toggles in 20 seconds)
        if (_showColorSelection) ...[
          const ColorSelectionCard(),
          const SizedBox(height: 8),
        ],

        // Privacy Policy section
        _buildPrivacyPolicySection(context),
        const SizedBox(height: 8),

        // Terms and Conditions section
        _buildTermsAndConditionsSection(context),
        const SizedBox(height: 8),

        // Notifications settings
        // _buildNotificationsCard(context),
      ],
    );
  }

  Widget _buildThemeToggleCard(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Card(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    themeProvider.isDarkMode
                        ? Icons.dark_mode
                        : Icons.light_mode,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        themeProvider.isDarkMode ? context.tr('settings.dark_mode') : context.tr('settings.light_mode'),
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        themeProvider.isDarkMode
                            ? context.tr('settings.theme_dark_interface')
                            : context.tr('settings.theme_light_interface'),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: themeProvider.isDarkMode,
                  onChanged: (value) async {
                    await themeProvider.toggleTheme();
                    _handleThemeToggle(); // Easter egg logic

                    // Use SchedulerBinding to defer SnackBar until after frame
                    SchedulerBinding.instance.addPostFrameCallback((_) {
                      if (mounted && context.mounted) {
                      }
                    });
                  },
                  activeTrackColor: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
          ),
        );
      },
    );
  }


  Widget _buildAdminSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(context.tr('settings.admin_section')),
        const SizedBox(height: 16),
        _buildOptionCard(
          context,
          icon: Icons.admin_panel_settings,
          title: context.tr('settings.admin_user_management_title'),
          subtitle: context.tr('settings.admin_user_management_subtitle'),
          iconColor: Theme.of(context).colorScheme.error,
          onTap: () => _navigateToUserManagement(context),
        ),
        const SizedBox(height: 8),

      ],
    );
  }

  void _navigateToUserManagement(BuildContext context) {
    Navigator.of(context).pushNamed('/admin/users');
  }



  Widget _buildOptionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool dense = false,
    Color? iconColor,
  }) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: (iconColor ?? Theme.of(context).primaryColor)
                .withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: iconColor ?? Theme.of(context).primaryColor,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: 14,
          ),
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
        dense: dense,
      ),
    );
  }


  void _showEditNameDialog(BuildContext context, String currentName) {
    final TextEditingController nameController = TextEditingController(
      text: currentName,
    );
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title:  Text(
          context.tr('settings.edit_name_title'),
          style: TextStyle(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: nameController,
            decoration:  InputDecoration(
              labelText: context.tr('settings.edit_name_label'),
              hintText: context.tr('settings.edit_name_hint'),
              border: const OutlineInputBorder(),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Theme.of(context).primaryColor),
              ),
              labelStyle: TextStyle(color: Theme.of(context).primaryColor),
            ),
            textCapitalization: TextCapitalization.words,
            autofocus: true,
            validator: (value) => AuthService.validateName(value ?? ''),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              context.tr('settings.cancel'),
              style: const TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () => _updateUserName(context, nameController.text, formKey),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text(context.tr('settings.save')),
          ),
        ],
      ),
    );
  }

  Future<void> _updateUserName(BuildContext context, String newName, GlobalKey<FormState> formKey) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(context.tr('settings.edit_name_updating')),
            ],
          ),
        ),
      );

      // Update name
      final response = await AuthService.updateUserName(newName);

      if (!mounted) return; // Check if widget is still mounted

      // Close loading dialog
      Navigator.of(context).pop();

      if (response.success) {
        // Close edit dialog
        Navigator.of(context).pop();

        // Show success message and refresh the UI
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            showTopSnackBar(
              context,
              SnackBar(
                content: Text(context.tr('settings.edit_name_success')),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
            // Trigger a rebuild to refresh the name display
            setState(() {});
          }
        });
      } else {
        // Show error message
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            showTopSnackBar(
              context,
              SnackBar(
                content: Text('Eroare: ${response.error}'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        });
      }
    } catch (e) {
      if (!mounted) return; // Check if widget is still mounted

      // Close dialog
      Navigator.of(context).pop();

      // Use SchedulerBinding for exception SnackBar
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          showTopSnackBar(
            context,
            SnackBar(
              content: Text('Eroare la actualizarea numelui: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  void _showEditPhoneDialog(BuildContext context, String? currentPhone) {
    final TextEditingController phoneController = TextEditingController();
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    // Extract country code from current phone if available
    String initialCountryCode = 'RO'; // Default to Romania
    if (currentPhone != null && currentPhone != 'Nu este setat' && currentPhone.isNotEmpty) {
      initialCountryCode = _extractCountryCode(currentPhone);
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          context.tr('settings.edit_phone_title'),
          style: TextStyle(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Form(
          key: formKey,
          child: IntlPhoneField(
            decoration: InputDecoration(
              labelText: context.tr('settings.edit_phone_label'),
              hintText: context.tr('settings.edit_phone_hint'),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: const Icon(Icons.phone_outlined),
              contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Theme.of(context).primaryColor),
              ),
              labelStyle: TextStyle(color: Theme.of(context).primaryColor),
            ),
            initialCountryCode: initialCountryCode,
            dropdownIconPosition: IconPosition.trailing,
            flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8.0),
            dropdownTextStyle: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
            ),
            showDropdownIcon: true,
            disableLengthCheck: false,
            keyboardType: TextInputType.phone,
            autofocus: true,
            onChanged: (phone) {
              phoneController.text = phone.completeNumber;
              // Save the selected country code for future use
              _saveLastCountryCode(phone.countryISOCode);
            },
            validator: (value) {
              if (value == null || value.number.isEmpty) {
                return 'Numărul de telefon este obligatoriu';
              }
              // Basic validation - accept numbers with at least 6 digits
              if (value.number.length < 6) {
                return 'Numărul de telefon nu este valid';
              }
              return null;
            },
            initialValue: currentPhone != null && currentPhone != 'Nu este setat' && currentPhone.isNotEmpty
                ? currentPhone.replaceAll(RegExp(r'[^\d]'), '')
                : null,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              context.tr('settings.cancel'),
              style: const TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () => _showPhoneConfirmationDialog(
              context,
              phoneController.text,
              formKey,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text(context.tr('settings.continue')),
          ),
        ],
      ),
    );
  }

  void _showPhoneConfirmationDialog(
    BuildContext dialogContext,
    String newPhone,
    GlobalKey<FormState> formKey,
  ) {
    if (!formKey.currentState!.validate()) {
      return;
    }

    Navigator.of(dialogContext).pop(); // Close the phone edit dialog

    // Use a post-frame callback to ensure the dialog is closed before proceeding
    // Use the main widget's context instead of the dialog context
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Show loading dialog and send SMS verification code using main widget context
        _sendInitialSmsVerificationWithLoading(context, newPhone);
      }
    });
  }

  Future<void> _sendInitialSmsVerificationWithLoading(BuildContext widgetContext, String newPhone) async {
    // Show loading dialog using the main widget context
    showDialog(
      context: widgetContext,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(context.tr('settings.sms_verification_sending')),
          ],
        ),
      ),
    );

    try {
      // Send SMS verification code
      final response = await AuthService.resendPhoneConfirmationCode(newPhone);

      // Check if widget is still mounted and context is valid before proceeding
      if (!mounted || !widgetContext.mounted) {
        DebugLogger.logVerbose('⚠️ Widget disposed during SMS verification, aborting');
        return;
      }

      // Close loading dialog safely
      try {
        Navigator.of(widgetContext).pop();
      } catch (e) {
        DebugLogger.logVerbose('⚠️ Error closing loading dialog: $e');
        return;
      }

      if (response.success) {
        // Show SMS verification dialog
        _showSmsVerificationDialog(widgetContext, newPhone);
      } else {
        // Show error message
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && widgetContext.mounted) {
            showTopSnackBar(
              widgetContext,
              SnackBar(
                content: Text('Eroare la trimiterea SMS-ului: ${response.error}'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        });
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error in SMS verification: $e');

      // Check if widget is still mounted and context is valid before proceeding
      if (!mounted || !widgetContext.mounted) {
        DebugLogger.logVerbose('⚠️ Widget disposed during error handling, aborting');
        return;
      }

      // Close loading dialog safely
      try {
        Navigator.of(widgetContext).pop();
      } catch (navError) {
        DebugLogger.logVerbose('⚠️ Error closing loading dialog during error handling: $navError');
      }

      // Show error message
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted && widgetContext.mounted) {
          showTopSnackBar(
            widgetContext,
            SnackBar(
              content: Text('Eroare la trimiterea SMS-ului: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  void _showSmsVerificationDialog(BuildContext context, String newPhone) {
    // Double-check context is still valid before showing dialog
    if (!mounted || !context.mounted) {
      DebugLogger.logVerbose('⚠️ Cannot show SMS verification dialog - context invalid');
      return;
    }

    final TextEditingController codeController = TextEditingController();
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title:  Text(
          context.tr('settings.sms_verification_title'),
          style: TextStyle(
            color: Theme.of(context).primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                context.tr('settings.sms_verification_message', params: {'phone': newPhone}),
                style: const TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: codeController,
                decoration:  InputDecoration(
                  labelText: context.tr('settings.sms_verification_code_label'),
                  hintText: context.tr('settings.sms_verification_code_hint'),
                  border: const OutlineInputBorder(),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).primaryColor),
                  ),
                  labelStyle: TextStyle(color: Theme.of(context).primaryColor),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                autofocus: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.tr('settings.sms_verification_code_required');
                  }
                  if (value.length != 6) {
                    return context.tr('settings.sms_verification_code_length');
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              context.tr('settings.cancel'),
              style: const TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () => _verifyPhoneNumber(context, newPhone, codeController.text, formKey),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text(context.tr('settings.verify')),
          ),
        ],
      ),
    );
  }

  Future<void> _verifyPhoneNumber(BuildContext context, String newPhone, String code, GlobalKey<FormState> formKey) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(context.tr('settings.sms_verification_verifying')),
            ],
          ),
        ),
      );

      // Verify SMS code and update phone number
      final response = await AuthService.updateUserPhone(newPhone, code);

      if (!mounted) return;

      // Close loading dialog
      Navigator.of(context).pop();

      if (response.success) {
        // Close verification dialog
        Navigator.of(context).pop();

        // Show success message and refresh the UI
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            showTopSnackBar(
              context,
              SnackBar(
                content: Text(context.tr('settings.edit_phone_success')),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
            // Trigger a rebuild to refresh the phone display
            setState(() {});
          }
        });
      } else {
        // Show error message
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            showTopSnackBar(
              context,
              SnackBar(
                content: Text('Eroare: ${response.error}'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        });
      }
    } catch (e) {
      if (!mounted) return;

      // Close loading dialog
      Navigator.of(context).pop();

      // Show error message
      SchedulerBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          showTopSnackBar(
            context,
            SnackBar(
              content: Text('Eroare la verificarea codului: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    }
  }

  // Open privacy policy function
  Future<void> _openPrivacyPolicy(BuildContext context) async {
    const privacyPolicyUrl = 'https://animalia-programari.ro/privacy-policy.html';

    final success = await UrlLauncherService.openWebUrl(privacyPolicyUrl);
    if (!success && context.mounted) {
      UrlLauncherService.showLaunchError(
        context,
        'politica de confidențialitate',
      );
    }
  }

  // Open terms and conditions function
  Future<void> _openTermsAndConditions(BuildContext context) async {
    const termsUrl = 'https://animalia-programari.ro/terms-of-service.html';

    final success = await UrlLauncherService.openWebUrl(termsUrl);
    if (!success && context.mounted) {
      UrlLauncherService.showLaunchError(context, 'termenii și condițiile');
    }
  }



  Widget _buildPrivacyPolicySection(BuildContext context) {
    return _buildOptionCard(
      context,
      icon: Icons.privacy_tip_outlined,
      title: context.tr('settings.privacy_policy_title'),
      subtitle: context.tr('settings.privacy_policy_subtitle'),
      onTap: () => _openPrivacyPolicy(context),
    );
  }

  Widget _buildTermsAndConditionsSection(BuildContext context) {
    return _buildOptionCard(
      context,
      icon: Icons.article_outlined,
      title: context.tr('settings.terms_conditions_title'),
      subtitle: context.tr('settings.terms_conditions_subtitle'),
      onTap: () => _openTermsAndConditions(context),
    );
  }

  Widget _buildYouTubeDemoSection(BuildContext context) {
    return _buildOptionCard(
      context,
      icon: Icons.play_circle_fill,
      title: context.tr('settings.demo_video_title'),
      subtitle: context.tr('settings.demo_video_subtitle'),
      onTap: () => _openYouTubeDemo(context),
      iconColor: const Color(0xFFFF0000), // YouTube red
    );
  }

  Future<void> _openYouTubeDemo(BuildContext context) async {
    const youtubeUrl = 'https://www.youtube.com/@Animalia-Programari'; // Replace with actual demo URL
    
    final success = await UrlLauncherService.openWebUrl(youtubeUrl);
    if (!success && context.mounted) {
      UrlLauncherService.showLaunchError(context, 'demo video');
    }
  }



  Widget _buildDangerZoneSection(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ElevatedButton(
          onPressed: () => _logout(context),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red.shade700,
            foregroundColor: Colors.white,
          ),
          child: Text(context.tr('settings.logout_button')),
        ),
        ElevatedButton(
          onPressed: () => _deleteAccount(context),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red.shade700,
            foregroundColor: Colors.white,
          ),
          child: Text(context.tr('settings.delete_account_button')),
        ),
      ],
    );
  }

  // Show logout confirmation dialog
  Future<bool?> _showLogoutConfirmation(BuildContext context) async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title:  Text(
            context.tr('settings.logout_confirmation_title'),
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            context.tr('settings.logout_confirmation_message'),
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                context.tr('settings.cancel'),
                style: const TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(context.tr('settings.logout_confirmation_button')),
            ),
          ],
        );
      },
    );
  }

  /// Extract country code from international phone number
  /// Returns ISO country code (e.g., 'RO', 'MD', 'US', 'GB')
  String _extractCountryCode(String phoneNumber) {
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Map of country dial codes to ISO codes
    final countryCodeMap = {
      '+1': 'US',      // USA/Canada
      '+7': 'RU',      // Russia
      '+33': 'FR',     // France
      '+34': 'ES',     // Spain
      '+39': 'IT',     // Italy
      '+40': 'RO',     // Romania
      '+41': 'CH',     // Switzerland
      '+43': 'AT',     // Austria
      '+44': 'GB',     // United Kingdom
      '+45': 'DK',     // Denmark
      '+46': 'SE',     // Sweden
      '+47': 'NO',     // Norway
      '+48': 'PL',     // Poland
      '+49': 'DE',     // Germany
      '+90': 'TR',     // Turkey
      '+91': 'IN',     // India
      '+212': 'MA',    // Morocco
      '+213': 'DZ',    // Algeria
      '+351': 'PT',    // Portugal
      '+352': 'LU',    // Luxembourg
      '+353': 'IE',    // Ireland
      '+354': 'IS',    // Iceland
      '+355': 'AL',    // Albania
      '+356': 'MT',    // Malta
      '+357': 'CY',    // Cyprus
      '+358': 'FI',    // Finland
      '+359': 'BG',    // Bulgaria
      '+370': 'LT',    // Lithuania
      '+371': 'LV',    // Latvia
      '+372': 'EE',    // Estonia
      '+373': 'MD',    // Moldova
      '+374': 'AM',    // Armenia
      '+375': 'BY',    // Belarus
      '+376': 'AD',    // Andorra
      '+377': 'MC',    // Monaco
      '+378': 'SM',    // San Marino
      '+380': 'UA',    // Ukraine
      '+381': 'RS',    // Serbia
      '+382': 'ME',    // Montenegro
      '+383': 'XK',    // Kosovo
      '+385': 'HR',    // Croatia
      '+386': 'SI',    // Slovenia
      '+387': 'BA',    // Bosnia and Herzegovina
      '+389': 'MK',    // North Macedonia
    };

    // Try to match country codes from longest to shortest
    final sortedCodes = countryCodeMap.keys.toList()
      ..sort((a, b) => b.length.compareTo(a.length));

    for (final code in sortedCodes) {
      if (cleanPhone.startsWith(code)) {
        return countryCodeMap[code]!;
      }
    }

    // Default to Romania if no match found
    return 'RO';
  }

  /// Save the selected country code to SharedPreferences
  Future<void> _saveLastCountryCode(String countryCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_settings_country_code', countryCode);
    } catch (e) {
      // Silently fail if saving doesn't work
      DebugLogger.logVerbose('Error saving last country code: $e');
    }
  }

  Widget _buildLanguageSelectionCard(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                Icons.language,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('settings.language'),
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    context.tr('settings.language_app_selection'),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            const LanguageSwitcher(
              showLabel: false,
              useDropdown: true,
            ),
          ],
        ),
      ),
    );
  }
}
