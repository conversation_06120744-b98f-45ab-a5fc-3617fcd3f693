import 'package:flutter/material.dart';

import '../../../widgets/referral/referral_system_widget.dart';
import '../../../l10n/app_localizations.dart';

/// Dedicated screen for the referral system
class ReferralSystemScreen extends StatelessWidget {
  const ReferralSystemScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('referral_system.title'),
          style: const TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        elevation: 0,
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: ReferralSystemWidget(),
      ),
    );
  }
}
