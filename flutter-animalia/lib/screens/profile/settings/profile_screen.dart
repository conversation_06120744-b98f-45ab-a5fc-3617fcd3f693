import 'package:animaliaproject/l10n/app_localizations.dart';
import 'package:animaliaproject/screens/profile/settings/services_management_screen.dart';
import 'package:animaliaproject/screens/profile/settings/sms_reminders_screen.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../../models/salon_settings.dart';
import '../../../models/salon_subscription.dart';
import '../../../models/user_role.dart';
import '../../../providers/calendar_provider.dart';
import '../../../providers/role_provider.dart';
import '../../../providers/subscription_provider.dart';
import '../../../services/auth/auth_service.dart';
import '../../../services/feature_toggle_service.dart';
import '../../../services/settings_service.dart';
import '../../../services/subscription/revenue_cat_paywall_service.dart';
import '../../../services/tour_keys.dart';
import '../../../services/ui_notification_service.dart';
import '../../../services/url_launcher_service.dart';
import '../../../utils/debug_logger.dart';
import '../../../widgets/analytics/comprehensive_analytics_mixin.dart';
import '../../../widgets/permission_guard.dart';
import '../../reports/business_reports_screen.dart';
import '../../sms/mass_sms_screen.dart';
import '../../subscription/subscription_purchase_screen.dart';
import '../team/staff_schedule_list_screen.dart';
import '../team/team_management_screen.dart';
import 'notification_settings_screen.dart';
import 'referral_system_screen.dart';
import 'website_management_screen.dart';


class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with ComprehensiveAnalyticsMixin, WidgetsBindingObserver {
  // Analytics mixin implementation
  @override
  String get screenName => 'profile_screen';

  @override
  String get screenCategory => 'settings';

  @override
  String? get entryPoint => 'tab_navigation';

  static const String _contactPhoneNumber = '0775664748';
  final ScrollController _scrollController = ScrollController();

  SalonSettings? _salonSettings;

  @override
  void initState() {
    super.initState();
    _loadFeatureToggles();

    // Add lifecycle observer to detect when app becomes active
    WidgetsBinding.instance.addObserver(this);

    // Tour logic is now handled by the dedicated tour services
    DebugLogger.logShowcase('🎯 ProfileScreen: Initialized');

    // Refresh subscription status when screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshSubscriptionStatus();
      _loadSalonSettings();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadFeatureToggles() async {
    final reviewsEnabled = await FeatureToggleService.isReviewsEnabled();
    if (mounted) {
      setState(() {
      });
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Refresh subscription status when app becomes active
    // This handles cases where user completed purchase outside the app
    if (state == AppLifecycleState.resumed) {
      DebugLogger.logShowcase('🔄 ProfileScreen: App resumed, refreshing subscription status');
      _refreshSubscriptionStatus();
    }
  }

  /// Refresh subscription status for the current salon
  Future<void> _refreshSubscriptionStatus() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId != null && mounted) {
        final subscriptionProvider = context.read<SubscriptionProvider>();
        await subscriptionProvider.refreshForSalon(salonId);
        DebugLogger.logShowcase('🔄 ProfileScreen: Subscription status refreshed');
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ ProfileScreen: Failed to refresh subscription status: $e');
    }
  }

  /// Load salon settings to check website status
  Future<void> _loadSalonSettings() async {
    try {
      final response = await SettingsService.getSalonSettings();
      if (response.success && response.data != null && mounted) {
        setState(() {
          _salonSettings = response.data!;
        });
        DebugLogger.logShowcase('🔄 ProfileScreen: Salon settings loaded');
      }
    } catch (e) {
      DebugLogger.logShowcase('❌ ProfileScreen: Failed to load salon settings: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RoleProvider>(
      builder: (context, roleProvider, child) {
        final isChiefGroomer = roleProvider.permissions?.groomerRole == GroomerRole.chiefGroomer;

        if (!isChiefGroomer) {
          return _buildRegularGroomerView(context, roleProvider);
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(
              context.tr('profile_screen.salon_title'),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            elevation: 0,
            actions: [
              IconButton(
                icon: const Icon(Icons.help_outline),
                onPressed: () {
                  DebugLogger.logVerbose('🎯 Manual salon tour trigger');
                  final tourKeys = SalonTourKeys.getSalonTourKeys();
                  final validKeys = tourKeys.where((key) =>
                    key.currentContext != null && key.currentWidget != null
                  ).toList();

                  DebugLogger.logVerbose('🎯 ProfileScreen: Valid keys: ${validKeys.length}/${tourKeys.length}');
                  for (int i = 0; i < tourKeys.length; i++) {
                    final key = tourKeys[i];
                    DebugLogger.logVerbose('🎯 ProfileScreen: Key $i - context: ${key.currentContext != null}, widget: ${key.currentWidget != null}');
                  }

                  if (validKeys.isNotEmpty) {
                    ShowCaseWidget.of(context).startShowCase(validKeys);
                  } else {
                    DebugLogger.logVerbose('🎯 ProfileScreen: No valid keys found!');
                  }
                },
                tooltip: context.tr('profile_screen.help_tooltip'),
              ),
            ],
          ),
          body: _buildBody(roleProvider),
        );
      },
    );
  }

  Widget _buildRegularGroomerView(BuildContext context, RoleProvider roleProvider) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('profile_screen.profile_title'),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.business_center,
                size: 80,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 24),
              Text(
                context.tr('profile_screen.restricted_access_title'),
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                context.tr('profile_screen.restricted_access_message'),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBody(RoleProvider roleProvider) {
    if (roleProvider.isLoading || !roleProvider.isInitialized) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              context.tr('profile_screen.loading_profile'),
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (roleProvider.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              context.tr('profile_screen.profile_load_error_title'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              roleProvider.error ?? context.tr('profile_screen.unknown_error'),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => roleProvider.refresh(),
              icon: const Icon(Icons.refresh),
              label: Text(context.tr('profile_screen.try_again')),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return _buildProfileWithSalon(context);
  }

  Widget _buildProfileWithSalon(BuildContext context) {
    return SingleChildScrollView(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildElegantSubscriptionSection(context),
          const SizedBox(height: 24),
          PermissionGuard.management(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader(context.tr('profile_screen.management_section')),
                const SizedBox(height: 16),
                _buildManagementOptions(context),
                const SizedBox(height: 24),
              ],
            ),
          ),
          PermissionGuard.management(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader(context.tr('profile_screen.business_section')),
                const SizedBox(height: 16),
                _buildBusinessOptions(context),
                const SizedBox(height: 24),
              ],
            ),
          ),
          _buildContactSection(context),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  Widget _buildManagementOptions(BuildContext context) {
    return Column(
      children: [

        Consumer<CalendarProvider>(
          builder: (context, calendarProvider, child) {
            final hasServices = calendarProvider.services.isNotEmpty;
            final servicesCount = calendarProvider.services.length;

            return Showcase(
              key: SalonTourKeys.servicesManagementKey,
              title: context.tr('profile_screen.showcase_services_title'),
              description: context.tr('profile_screen.showcase_services_description'),
              child: _buildEnhancedOptionCard(
                context,
                icon: Icons.room_service,
                title: context.tr('profile_screen.my_services_title'),
                subtitle: hasServices
                    ? context.tr('profile_screen.manage_services_with_count', params: {'count': servicesCount.toString()})
                    : context.tr('profile_screen.manage_services'),
                onTap: () async {
                  // Track navigation to services management
                  trackButtonClick('services_management', context: {
                    'current_services_count': servicesCount,
                    'has_services': hasServices,
                  });
                  trackNavigationTo('services_management_screen', method: 'profile_menu');

                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ServicesManagementScreen(),
                    ),
                  );
                  if (context.mounted) {
                    await calendarProvider.loadServices();
                  }
                },
                showWarning: !hasServices,
                warningText: context.tr('profile_screen.no_services_warning'),
              ),
            );
          },
        ),
        const SizedBox(height: 8),
        Showcase(
          key: SalonTourKeys.teamManagementKey,
          title: context.tr('profile_screen.showcase_team_title'),
          description: context.tr('profile_screen.showcase_team_description'),
          child: _buildOptionCard(
            context,
            icon: Icons.people,
            title: context.tr('profile_screen.my_team_title'),
            subtitle: context.tr('profile_screen.my_team_subtitle'),
            onTap: () {
              // Track navigation to team management
              trackButtonClick('team_management', context: {
                'trigger_source': 'profile_menu',
              });
              trackNavigationTo('team_management_screen', method: 'profile_menu');

              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TeamManagementScreen(),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 8),
        _buildWebsiteManagementCard(context),
      ],
    );
  }

  Widget _buildWebsiteManagementCard(BuildContext context) {
    final hasWebsite = _salonSettings?.website.isNotEmpty == true;

    return _buildEnhancedOptionCard(
      context,
      icon: Icons.language,
      title: context.tr('profile_screen.programming_website_title'),
      subtitle: context.tr('profile_screen.programming_website_subtitle'),
      onTap: () async {
        // Track navigation to website management
        trackButtonClick('website_management', context: {
          'has_website': hasWebsite,
          'trigger_source': 'profile_menu',
        });
        trackNavigationTo('website_management_screen', method: 'profile_menu');

        final result = await Navigator.push<bool>(
          context,
          MaterialPageRoute(
            builder: (context) => const WebsiteManagementScreen(),
          ),
        );

        // Reload salon settings if website was updated
        if (result == true && mounted) {
          await _loadSalonSettings();
        }
      },
      showWarning: !hasWebsite,
      warningText: hasWebsite ? null : context.tr('profile_screen.no_website_warning'),
    );
  }

  Widget _buildBusinessOptions(BuildContext context) {
    return Column(
      children: [
        Showcase(
          key: SalonTourKeys.smsRemindersKey,
          title: context.tr('profile_screen.showcase_sms_title'),
          description: context.tr('profile_screen.showcase_sms_description'),
          child: _buildOptionCard(
            context,
            icon: Icons.sms,
            title: context.tr('profile_screen.sms_reminders_title'),
            subtitle: context.tr('profile_screen.sms_reminders_subtitle'),
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SmsRemindersScreen(),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Showcase(
          key: SalonTourKeys.workingHoursKey,
          title: context.tr('profile_screen.showcase_schedule_title'),
          description: context.tr('profile_screen.showcase_schedule_description'),
          child: _buildOptionCard(
            context,
            icon: Icons.schedule,
            title: context.tr('profile_screen.work_schedule_title'),
            subtitle: context.tr('profile_screen.work_schedule_subtitle'),
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const StaffScheduleListScreen(),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        _buildOptionCard(
          context,
          icon: Icons.bar_chart,
          title: context.tr('profile_screen.performance_reports_title'),
          subtitle: context.tr('profile_screen.performance_reports_subtitle'),
          onTap: () => _handleReportsAccess(context),
        ),
        const SizedBox(height: 8),
        _buildOptionCard(
          context,
          icon: Icons.notifications,
          title: context.tr('profile_screen.notifications_title'),
          subtitle: context.tr('profile_screen.notifications_subtitle'),
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const NotificationSettingsScreen(),
            ),
          ),
        ),
        const SizedBox(height: 8),
        _buildOptionCard(
          context,
          icon: Icons.sms,
          title: context.tr('profile_screen.mass_sms_title'),
          subtitle: context.tr('profile_screen.mass_sms_subtitle'),
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const MassSmsScreen(),
            ),
          ),
        ),
        const SizedBox(height: 8),
        _buildOptionCard(
          context,
          icon: Icons.card_giftcard,
          title: context.tr('profile_screen.referral_system_title'),
          subtitle: context.tr('profile_screen.referral_system_subtitle'),
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ReferralSystemScreen(),
            ),
          ),
        ),
        const SizedBox(height: 8),

      ],
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return _buildOptionCard(
      context,
      icon: Icons.support_agent,
      title: context.tr('profile_screen.quick_contact_title'),
      subtitle: context.tr('profile_screen.quick_contact_subtitle'),
      onTap: () => _showContactOptions(context),
    );
  }

  void _showContactOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.phone),
              title: Text(context.tr('profile_screen.contact_call')),
              onTap: _makePhoneCall,
            ),
            ListTile(
              leading: const Icon(Icons.message),
              title: Text(context.tr('profile_screen.contact_sms')),
              onTap: _sendSMS,
            ),
            ListTile(
              leading: const Icon(FontAwesomeIcons.whatsapp),
              title: Text(context.tr('profile_screen.contact_whatsapp')),
              onTap: _openWhatsApp,
            ),
          ],
        );
      },
    );
  }


  void _makePhoneCall() async {
    final success = await UrlLauncherService.makePhoneCall(_contactPhoneNumber);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, context.tr('profile_screen.error_phone_app'));
    }
  }

  void _sendSMS() async {
    final success = await UrlLauncherService.sendSMS(_contactPhoneNumber);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, context.tr('profile_screen.error_sms_app'));
    }
  }

  void _openWhatsApp() async {
    final success = await UrlLauncherService.openWhatsApp(_contactPhoneNumber);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(context, 'WhatsApp');
    }
  }

  Widget _buildElegantSubscriptionSection(BuildContext context) {
    return Showcase(
      key: SalonTourKeys.subscriptionKey,
      title: context.tr('profile_screen.showcase_subscription_title'),
      description: context.tr('profile_screen.showcase_subscription_description'),
      child: _buildSubscriptionCard(context),
    );
  }

  Widget _buildSubscriptionCard(BuildContext context) {
    return Consumer<SubscriptionProvider>(
      builder: (context, subscriptionProvider, child) {
        final currentSubscription = subscriptionProvider.currentSubscription;
        final hasActiveSubscription = subscriptionProvider.hasActiveSubscription;
        final theme = Theme.of(context);
        final isDarkMode = theme.brightness == Brightness.dark;

        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: theme.dividerColor.withValues(alpha: 0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.shadowColor.withValues(alpha: isDarkMode ? 0.2 : 0.08),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _navigateToSubscriptionStatic(context),
              borderRadius: BorderRadius.circular(20),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: hasActiveSubscription && currentSubscription != null
                    ? _buildActiveSubscriptionContent(context, currentSubscription, theme, isDarkMode)
                    : _buildUpgradePromptContent(context, theme, isDarkMode),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActiveSubscriptionContent(
    BuildContext context,
    SalonSubscription subscription,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final tierColor = _getTierColor(subscription.tier);
    final tierIcon = _getTierIcon(subscription.tier);
    final tierName = _getTierDisplayName(subscription.tier);

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color: tierColor.withValues(alpha: isDarkMode ? 0.2 : 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(
            tierIcon,
            color: tierColor,
            size: 28,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Flexible(
                    child: Text(
                      '${context.tr('profile_screen.subscription_plan_prefix')} $tierName',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: tierColor.withValues(alpha: isDarkMode ? 0.3 : 0.15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      tierName.toUpperCase(),
                      style: TextStyle(
                        color: tierColor,
                        fontSize: 10,
                        fontWeight: FontWeight.w700,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              Text(
                subscription.isTrialActive
                    ? '${context.tr('profile_screen.trial_period')} • ${_formatExpiryDate(subscription.endDate)}'
                    : '${context.tr('profile_screen.active')} • ${_formatExpiryDate(subscription.endDate)}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Icon(
          Icons.arrow_forward_ios,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
          size: 16,
        ),
      ],
    );
  }

  Widget _buildUpgradePromptContent(
    BuildContext context,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final accentColor = theme.colorScheme.primary;

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color: accentColor.withValues(alpha: isDarkMode ? 0.2 : 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(
            Icons.upgrade,
            color: accentColor,
            size: 28,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    context.tr('profile_screen.subscription_title'),
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: accentColor.withValues(alpha: isDarkMode ? 0.3 : 0.15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      context.tr('profile_screen.subscription_upgrade_label'),
                      style: TextStyle(
                        color: accentColor,
                        fontSize: 10,
                        fontWeight: FontWeight.w700,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              Text(
                context.tr('profile_screen.subscription_upgrade_message'),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Icon(
          Icons.arrow_forward_ios,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
          size: 16,
        ),
      ],
    );
  }

  String _getTierDisplayName(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return 'Free';
      case SubscriptionTier.freelancer:
        return 'Freelancer';
      case SubscriptionTier.team:
        return 'Team';
      case SubscriptionTier.enterprise:
        return 'Enterprise';
    }
  }

  Color _getTierColor(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return Colors.grey.shade400;
      case SubscriptionTier.freelancer:
        return Colors.brown;
      case SubscriptionTier.team:
        return Colors.grey.shade600;
      case SubscriptionTier.enterprise:
        return Colors.amber.shade700;
    }
  }

  IconData _getTierIcon(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return Icons.star_outline;
      case SubscriptionTier.freelancer:
        return Icons.star_border;
      case SubscriptionTier.team:
        return Icons.star_half;
      case SubscriptionTier.enterprise:
        return Icons.star;
    }
  }

  String _formatExpiryDate(DateTime? endDate) {
    if (endDate == null) return '';

    final now = DateTime.now();
    final difference = endDate.difference(now).inDays;

    if (difference < 0) {
      return context.tr('profile_screen.subscription_expired');
    } else if (difference == 0) {
      return context.tr('profile_screen.subscription_expires_today');
    } else if (difference == 1) {
      return context.tr('profile_screen.subscription_expires_tomorrow');
    } else if (difference < 30) {
      return difference == 1
        ? context.tr('profile_screen.expires_in_day', params: {'days': difference.toString()})
        : context.tr('profile_screen.expires_in_days', params: {'days': difference.toString()});
    } else {
      final months = (difference / 30).floor();
      return months == 1
        ? context.tr('profile_screen.expires_in_month', params: {'months': months.toString()})
        : context.tr('profile_screen.expires_in_months', params: {'months': months.toString()});
    }
  }

  /// Handle reports access with Team plan guard
  Future<void> _handleReportsAccess(BuildContext context) async {
    final subscriptionProvider = context.read<SubscriptionProvider>();
    final currentTier = subscriptionProvider.currentTier;

    // Check if user has Team plan or higher
    if (currentTier != null &&
        (currentTier == SubscriptionTier.team || currentTier == SubscriptionTier.enterprise)) {
      // User has access, navigate to reports
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const BusinessReportsScreen(),
        ),
      );
    } else {
      // User needs Team plan, show paywall
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId != null) {
        RevenueCatPaywallService.showPaywall(
          context: context,
          defaultTier: SubscriptionTier.team,
          salonId: salonId,
        );
      } else {
        UINotificationService.showError(
          context: context,
          title: context.tr('profile_screen.salon_not_found_title'),
          message: context.tr('profile_screen.salon_not_found_message'),
        );
      }
    }
  }

  void _navigateToSubscriptionStatic(BuildContext context) async {
    final salonId = await AuthService.getCurrentSalonId();
    if (salonId != null) {
      final subscriptionProvider = context.read<SubscriptionProvider>();
      final hasActiveSubscription = subscriptionProvider.hasActiveSubscription;

      // Track subscription interaction
      trackButtonClick('subscription_management', context: {
        'has_active_subscription': hasActiveSubscription,
        'current_tier': subscriptionProvider.currentTier?.name ?? 'free',
        'trigger_source': 'profile_screen',
      });

      if (hasActiveSubscription) {
        // Track navigation to purchase screen
        trackNavigationTo('subscription_purchase_screen', method: 'profile_subscription_card');

        // Navigate to purchase screen for new subscriptions
        final result = await Navigator.push<bool>(
          context,
          MaterialPageRoute(
            builder: (context) => SubscriptionPurchaseScreen(
              salonId: salonId,
            ),
          ),
        );

        if (result == true && mounted) {
          await subscriptionProvider.refreshForSalon(salonId);
          // Force a UI refresh to show the updated subscription status
          setState(() {});
          DebugLogger.logShowcase('🔄 ProfileScreen: Subscription refreshed after purchase');
        } else if (mounted) {
          // Even if result is not true, refresh to ensure we have the latest status
          // This handles cases where the purchase was completed but result wasn't properly returned
          await _refreshSubscriptionStatus();
        }
      }
    } else {
      UINotificationService.showError(
        context: context,
        title: context.tr('profile_screen.error_title'),
        message: context.tr('profile_screen.error_salon_not_found'),
      );
    }
  }

  Widget _buildOptionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool dense = false,
    Color? cardColor,
    Color? iconColor,
    bool isRestricted = false,
  }) {
    return _buildEnhancedOptionCard(
      context,
      icon: icon,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      dense: dense,
      cardColor: cardColor,
      iconColor: iconColor,
      showWarning: isRestricted,
    );
  }

  Widget _buildEnhancedOptionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool showWarning = false,
    String? warningText,
    bool dense = false,
    Color? cardColor,
    Color? iconColor,
  }) {
    return Card(
      elevation: showWarning ? 3 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: showWarning
            ? const BorderSide(color: Colors.orange, width: 1.5)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: showWarning
              ? BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    colors: [
                      Colors.orange.withValues(alpha: 0.05),
                      Theme.of(context).colorScheme.surface,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                )
              : cardColor != null
                  ? BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: cardColor,
                    )
                  : null,
          child: Padding(
            padding: dense ? const EdgeInsets.all(12) : const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: showWarning
                            ? Colors.orange.withValues(alpha: 0.1)
                            : iconColor?.withValues(alpha: 0.1) ??
                                Theme.of(context).primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        icon,
                        color: showWarning
                            ? Colors.orange
                            : iconColor ?? Theme.of(context).primaryColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            subtitle,
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (showWarning) ...[ 
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(
                          Icons.warning,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    Icon(
                      Icons.arrow_forward_ios,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      size: 16,
                    ),
                  ],
                ),
                if (showWarning && warningText != null) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.orange.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          color: Colors.orange,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            warningText,
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.orange,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
