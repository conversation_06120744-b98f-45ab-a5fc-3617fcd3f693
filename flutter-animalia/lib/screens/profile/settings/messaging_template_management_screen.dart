import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../models/sms_template.dart';
import '../../../models/whatsapp_template.dart';
import '../../../services/sms_template_service.dart';
import '../../../services/whatsapp_template_service.dart';
import '../../../widgets/permission_guard.dart';
import 'package:shimmer/shimmer.dart';
import '../../../widgets/sms_template_editor.dart';
import '../../../l10n/app_localizations.dart';

/// Modern messaging template management screen
/// Supports both editable SMS templates and fixed WhatsApp templates
class MessagingTemplateManagementScreen extends StatefulWidget {
  const MessagingTemplateManagementScreen({Key? key}) : super(key: key);

  @override
  State<MessagingTemplateManagementScreen> createState() =>
      _MessagingTemplateManagementScreenState();
}

class _MessagingTemplateManagementScreenState
    extends State<MessagingTemplateManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  String? _error;

  List<SmsTemplate> _smsTemplates = [];
  Map<String, List<WhatsAppTemplate>> _whatsappTemplates = {};
  Map<String, WhatsAppTemplatePreference> _whatsappPreferences = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load SMS templates
      final smsResponse = await SmsTemplateService.getSmsTemplates();

      // Load WhatsApp templates and preferences
      final whatsappResponse = await WhatsAppTemplateService.getAvailableTemplates();
      final preferencesResponse = await WhatsAppTemplateService.getTemplatePreferences();

      // Debug logging to help diagnose empty UI
      debugPrint('WhatsApp available response: success=${whatsappResponse.success}, count=${whatsappResponse.data?.length ?? 0}');
      debugPrint('WhatsApp preferences response: success=${preferencesResponse.success}, count=${preferencesResponse.data?.length ?? 0}');

      if (smsResponse.success && smsResponse.data != null) {
        _smsTemplates = smsResponse.data!;
      }

      if (whatsappResponse.success && whatsappResponse.data != null) {
        // Group WhatsApp templates by type
        _whatsappTemplates.clear();
        for (var template in whatsappResponse.data!) {
          final key = template.templateType.trim().toUpperCase();
          _whatsappTemplates.putIfAbsent(key, () => []);
          _whatsappTemplates[key]!.add(template);
        }
        debugPrint('Grouped whatsapp template types: ${_whatsappTemplates.keys.toList()}');
      }

      if (preferencesResponse.success && preferencesResponse.data != null) {
        // Map preferences by template type
        _whatsappPreferences.clear();
        for (var pref in preferencesResponse.data!) {
          final key = pref.templateType.trim().toUpperCase();
          _whatsappPreferences[key] = pref;
        }
        debugPrint('Loaded whatsapp preferences keys: ${_whatsappPreferences.keys.toList()}');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading templates: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: colorScheme.surface,
        elevation: 0,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations.translate('messaging_template_management.screen_title'),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
                color: colorScheme.onSurface,
              ),
            ),
            Text(
              localizations.translate('messaging_template_management.screen_subtitle'),
              style: TextStyle(
                fontSize: 12,
                color: colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: colorScheme.primary,
          labelColor: colorScheme.primary,
          unselectedLabelColor: colorScheme.onSurfaceVariant,
          labelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          tabs: [
            Tab(
              icon: Icon(Icons.sms),
              text: localizations.translate('messaging_template_management.sms_tab'),
            ),
            Tab(
              icon: Icon(FontAwesomeIcons.whatsapp),
              text: localizations.translate('messaging_template_management.whatsapp_tab'),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.help_outline, color: colorScheme.primary),
            onPressed: _showHelpDialog,
            tooltip: localizations.translate('messaging_template_management.help_tooltip'),
          ),
        ],
      ),
      body: PermissionGuard(
        requireManagementAccess: true,
        fallback: _buildNoPermissionView(),
        child: _isLoading
            ? _buildLoadingView()
            : _error != null
                ? _buildErrorView()
                : RefreshIndicator(
                    onRefresh: _loadData,
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildSmsTemplatesTab(),
                        _buildWhatsAppTemplatesTab(),
                      ],
                    ),
                  ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: 6,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Card(
            margin: EdgeInsets.only(bottom: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            child: Container(
              height: 120,
              padding: EdgeInsets.all(16),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNoPermissionView() {
    final colorScheme = Theme.of(context).colorScheme;
    final localizations = AppLocalizations.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lock_outline, size: 80, color: colorScheme.primary.withValues(alpha: 0.5)),
            SizedBox(height: 24),
            Text(
              localizations.translate('messaging_template_management.no_permission_title'),
              style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Text(
              localizations.translate('messaging_template_management.no_permission_message'),
              style: TextStyle(fontSize: 14, color: colorScheme.onSurfaceVariant),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    final colorScheme = Theme.of(context).colorScheme;
    final localizations = AppLocalizations.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: colorScheme.error),
            SizedBox(height: 24),
            Text(
              localizations.translate('messaging_template_management.error_loading_title'),
              style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Text(
              _error ?? localizations.translate('messaging_template_management.unknown_error'),
              style: TextStyle(fontSize: 14, color: colorScheme.onSurfaceVariant),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadData,
              icon: Icon(Icons.refresh),
              label: Text(localizations.translate('messaging_template_management.retry_button')),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSmsTemplatesTab() {
    final colorScheme = Theme.of(context).colorScheme;
    final localizations = AppLocalizations.of(context);

    return ListView(
      padding: EdgeInsets.all(16),
      children: [
        // Info banner
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [colorScheme.primaryContainer, colorScheme.primaryContainer.withValues(alpha: 0.7)],
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            children: [
              Icon(Icons.edit_note, size: 32, color: colorScheme.primary),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      localizations.translate('messaging_template_management.fully_editable_title'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onPrimaryContainer,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      localizations.translate('messaging_template_management.fully_editable_message'),
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 24),

        // SMS Templates list
        ...SmsTemplateType.values.map((type) {
          final template = _smsTemplates.firstWhere(
            (t) => t.templateType == type,
            orElse: () => SmsTemplate(
              id: '',
              salonId: '',
              templateType: type,
              templateContent: type.getDefaultContent(),
              isActive: true,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          return _buildSmsTemplateCard(template);
        }).toList(),
      ],
    );
  }

  Widget _buildSmsTemplateCard(SmsTemplate template) {
    final colorScheme = Theme.of(context).colorScheme;
    final localizations = AppLocalizations.of(context);

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: colorScheme.outlineVariant, width: 1),
      ),
      child: InkWell(
        onTap: () => _editSmsTemplate(template),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.sms, color: colorScheme.primary, size: 20),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      template.templateType.displayName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ),
                  Icon(Icons.edit, color: colorScheme.primary, size: 20),
                ],
              ),
              SizedBox(height: 12),
              Text(
                template.templateContent.length > 120
                    ? '${template.templateContent.substring(0, 120)}...'
                    : template.templateContent,
                style: TextStyle(
                  fontSize: 13,
                  color: colorScheme.onSurfaceVariant,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 12),
              Row(
                children: [
                  _buildStatusChip(
                    template.isActive
                      ? localizations.translate('messaging_template_management.status_active')
                      : localizations.translate('messaging_template_management.status_inactive'),
                    template.isActive ? Colors.green : Colors.grey,
                  ),
                  SizedBox(width: 8),
                  _buildInfoChip(
                    '${template.templateContent.length} ${localizations.translate('messaging_template_management.characters')}',
                    Icons.text_fields,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWhatsAppTemplatesTab() {
    final colorScheme = Theme.of(context).colorScheme;
    final localizations = AppLocalizations.of(context);

    return ListView(
      padding: EdgeInsets.all(16),
      children: [
        // Info banner
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFF25D366).withValues(alpha: 0.2),
                Color(0xFF25D366).withValues(alpha: 0.1)
              ],
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            children: [
              Icon(Icons.lock_outline, size: 32, color: Color(0xFF25D366)),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      localizations.translate('messaging_template_management.fixed_templates_title'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      localizations.translate('messaging_template_management.fixed_templates_message'),
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 24),

        // WhatsApp Templates by type
        ...SmsTemplateType.values.map((type) {
          final key = type.name.trim().toUpperCase();
          final templates = _whatsappTemplates[key] ?? [];
          final selectedPreference = _whatsappPreferences[key];

          return _buildWhatsAppTemplateTypeCard(type, templates, selectedPreference);
        }).toList(),
      ],
    );
  }

  Widget _buildWhatsAppTemplateTypeCard(
    SmsTemplateType type,
    List<WhatsAppTemplate> templates,
    WhatsAppTemplatePreference? selectedPreference,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final localizations = AppLocalizations.of(context);
    final selectedTemplate = templates.firstWhereOrNull(
      (t) => t.id == selectedPreference?.whatsappTemplateId,
    );

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: colorScheme.outlineVariant, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Color(0xFF25D366).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(FontAwesomeIcons.whatsapp, color: Color(0xFF25D366), size: 20),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    type.displayName,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
                if (selectedTemplate != null)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: Color(0xFF25D366).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.check_circle, color: Color(0xFF25D366), size: 14),
                        SizedBox(width: 4),
                        Text(
                          localizations.translate('messaging_template_management.selected'),
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF25D366),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          Divider(height: 1),

          // Template options
          if (templates.isEmpty)
            Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                localizations.translate('messaging_template_management.no_templates_for_type'),
                style: TextStyle(
                  fontSize: 13,
                  color: colorScheme.onSurfaceVariant,
                  fontStyle: FontStyle.italic,
                ),
              ),
            )
          else
            ...templates.map((template) {
              final isSelected = template.id == selectedPreference?.whatsappTemplateId;
              return _buildWhatsAppTemplateOption(template, isSelected, type);
            }).toList(),
        ],
      ),
    );
  }

  Widget _buildWhatsAppTemplateOption(
    WhatsAppTemplate template,
    bool isSelected,
    SmsTemplateType type,
  ) {
    final colorScheme = Theme.of(context).colorScheme;

    return InkWell(
      onTap: () => _selectWhatsAppTemplate(type, template),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? Color(0xFF25D366).withValues(alpha: 0.05)
              : Colors.transparent,
          border: Border(
            top: BorderSide(color: colorScheme.outlineVariant.withValues(alpha: 0.3)),
          ),
        ),
        child: Row(
          children: [
            // Selection indicator
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Color(0xFF25D366) : colorScheme.outlineVariant,
                  width: 2,
                ),
                color: isSelected ? Color(0xFF25D366) : Colors.transparent,
              ),
              child: isSelected
                  ? Icon(Icons.check, color: Colors.white, size: 16)
                  : null,
            ),
            SizedBox(width: 12),

            // Template info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        template.languageFlag,
                        style: TextStyle(fontSize: 16),
                      ),
                      SizedBox(width: 6),
                      Text(
                        template.displayName,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 6),
                  Text(
                    template.previewText,
                    style: TextStyle(
                      fontSize: 13,
                      color: colorScheme.onSurfaceVariant,
                      height: 1.45,
                    ),
                    // allow full multi-line preview for WhatsApp templates
                    softWrap: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String label, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String label, IconData icon) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: colorScheme.onSurfaceVariant),
          SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  void _editSmsTemplate(SmsTemplate template) {
    // Navigate to SMS template editor (reuse existing editor)
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SmsTemplateEditor(
          template: template,
          templateType: template.templateType,
          onSaved: (updatedTemplate) {
            setState(() {
              final index = _smsTemplates.indexWhere((t) => t.templateType == template.templateType);
              if (index >= 0) {
                _smsTemplates[index] = updatedTemplate;
              } else {
                _smsTemplates.add(updatedTemplate);
              }
            });
            Navigator.of(context).pop();
          },
          onCancel: () => Navigator.of(context).pop(),
        ),
      ),
    ).then((_) => _loadData());
  }

  Future<void> _selectWhatsAppTemplate(
    SmsTemplateType type,
    WhatsAppTemplate template,
  ) async {
    final localizations = AppLocalizations.of(context);

    try {
      final response = await WhatsAppTemplateService.updateTemplatePreference(
        templateType: type.name,
        whatsappTemplateId: template.id,
      );

      if (response.success && response.data != null) {
        setState(() {
          // store normalized key
          _whatsappPreferences[type.name.trim().toUpperCase()] = response.data!;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 12),
                Expanded(
                  child: Text(localizations.translate('messaging_template_management.whatsapp_updated_success')),
                ),
              ],
            ),
            backgroundColor: Color(0xFF25D366),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      } else {
        throw Exception(response.error ?? 'Failed to update');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${localizations.translate('messaging_template_management.error_prefix')} $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      );
    }
  }

  void _showHelpDialog() {
    final localizations = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.help_outline, color: Theme.of(context).colorScheme.primary),
            SizedBox(width: 12),
            Text(localizations.translate('messaging_template_management.help_dialog_title')),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHelpSection(
                localizations.translate('messaging_template_management.help_sms_title'),
                localizations.translate('messaging_template_management.help_sms_content'),
              ),
              SizedBox(height: 16),
              _buildHelpSection(
                localizations.translate('messaging_template_management.help_whatsapp_title'),
                localizations.translate('messaging_template_management.help_whatsapp_content'),
              ),
              SizedBox(height: 16),
              _buildHelpSection(
                localizations.translate('messaging_template_management.help_variables_title'),
                localizations.translate('messaging_template_management.help_variables_content'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(localizations.translate('messaging_template_management.help_understood')),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpSection(String title, String content) {
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 6),
        Text(
          content,
          style: TextStyle(
            fontSize: 12,
            color: colorScheme.onSurfaceVariant,
            height: 1.4,
          ),
        ),
      ],
    );
  }
}

extension _ListExtension<T> on List<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    for (var element in this) {
      if (test(element)) return element;
    }
    return null;
  }
}
