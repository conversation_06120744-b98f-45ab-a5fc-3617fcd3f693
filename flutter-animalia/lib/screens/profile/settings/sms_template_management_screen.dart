import 'package:flutter/material.dart';

import '../../../l10n/app_localizations.dart';
import '../../../models/sms_template.dart';
import '../../../services/sms_template_service.dart';
import '../../../widgets/permission_guard.dart';
import '../../../widgets/sms_template_editor.dart';

/// Screen for managing SMS templates
class SmsTemplateManagementScreen extends StatefulWidget {
  const SmsTemplateManagementScreen({Key? key}) : super(key: key);

  @override
  State<SmsTemplateManagementScreen> createState() => _SmsTemplateManagementScreenState();
}

class _SmsTemplateManagementScreenState extends State<SmsTemplateManagementScreen> {
  List<SmsTemplate> _templates = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadTemplates();
  }

  Future<void> _loadTemplates() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await SmsTemplateService.getSmsTemplates();
      
      if (response.success && response.data != null) {
        setState(() {
          _templates = response.data!;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load templates';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading templates: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _resetAllTemplates() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('sms_templates.reset_confirmation_title')),
        content: Text(context.tr('sms_templates.reset_confirmation_message')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.tr('sms_templates.cancel')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(context.tr('sms_templates.reset')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final response = await SmsTemplateService.resetSmsTemplates();
        
        if (response.success && response.data != null) {
          setState(() {
            _templates = response.data!;
          });
          
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(context.tr('sms_templates.reset_success')),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(response.error ?? context.tr('sms_templates.reset_error')),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${context.tr('common.error')}: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _editTemplate(SmsTemplateType templateType) {
    final template = _templates.firstWhere(
      (t) => t.templateType == templateType,
      orElse: () => SmsTemplate(
        id: '',
        salonId: '',
        templateType: templateType,
        templateContent: templateType.getDefaultContent(),
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SmsTemplateEditor(
          template: template,
          templateType: templateType,
          onSaved: (updatedTemplate) {
            setState(() {
              final index = _templates.indexWhere((t) => t.templateType == templateType);
              if (index >= 0) {
                _templates[index] = updatedTemplate;
              } else {
                _templates.add(updatedTemplate);
              }
            });
            Navigator.of(context).pop();
          },
          onCancel: () => Navigator.of(context).pop(),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('sms_templates.title'),
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _resetAllTemplates,
            tooltip: context.tr('sms_templates.reset_all_templates'),
          ),
          IconButton(
            icon: Icon(Icons.help_outline),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text(context.tr('sms_templates.help')),
                  content: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(context.tr('sms_templates.help_description')),
                        SizedBox(height: 16),
                        Text(
                          context.tr('sms_templates.available_variables'),
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 8),
                        ...TemplateVariableInfo.getAllVariables().map((variable) => 
                          Padding(
                            padding: EdgeInsets.only(bottom: 4),
                            child: Text(
                              '• {${variable.name}} - ${variable.description}',
                              style: TextStyle(fontSize: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(context.tr('sms_templates.close')),
                    ),
                  ],
                ),
              );
            },
            tooltip: context.tr('common.help'),
          ),
        ],
      ),
      body: PermissionGuard(
        requireManagementAccess: true,
        fallback: _buildNoPermissionView(),
        child: _isLoading
            ? Center(child: CircularProgressIndicator())
            : _error != null
                ? _buildErrorView()
                : RefreshIndicator(
                    onRefresh: _loadTemplates,
                    child: _buildTemplatesList(),
                  ),
      ),
    );
  }

  Widget _buildNoPermissionView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: 16),
            Text(
              context.tr('sms_templates.restricted_access'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              context.tr('sms_templates.no_permission_message'),
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            SizedBox(height: 16),
            Text(
              context.tr('sms_templates.error_loading'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              _error ?? context.tr('sms_templates.unknown_error'),
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadTemplates,
              child: Text(context.tr('sms_templates.try_again')),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplatesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: SmsTemplateType.values.length,
      itemBuilder: (context, index) {
        final templateType = SmsTemplateType.values[index];
        final template = _templates.firstWhere(
          (t) => t.templateType == templateType,
          orElse: () => SmsTemplate(
            id: '',
            salonId: '',
            templateType: templateType,
            templateContent: templateType.getDefaultContent(),
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        return Card(
          elevation: 2,
          margin: EdgeInsets.only(bottom: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: ListTile(
            contentPadding: EdgeInsets.all(16),
            title: Text(
              context.tr(templateType.translationKey),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 8),
                Text(
                  template.templateContent.length > 100
                      ? '${template.templateContent.substring(0, 100)}...'
                      : template.templateContent,
                  style: TextStyle(fontSize: 14),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      template.isActive ? Icons.check_circle : Icons.cancel,
                      size: 16,
                      color: template.isActive ? Colors.green : Colors.grey,
                    ),
                    SizedBox(width: 4),
                    Text(
                      template.isActive ? context.tr('sms_templates.active') : context.tr('sms_templates.inactive'),
                      style: TextStyle(
                        fontSize: 12,
                        color: template.isActive ? Colors.green : Colors.grey,
                      ),
                    ),
                    SizedBox(width: 16),
                    _buildSmsInfo(template),
                  ],
                ),
              ],
            ),
            trailing: Icon(Icons.edit),
            onTap: () => _editTemplate(templateType),
          ),
        );
      },
    );
  }

  Widget _buildSmsInfo(SmsTemplate template) {
    final smsInfo = SmsTemplateService.getSmsCharacterInfo(template.templateContent);

    Color getSmsColor() {
      switch (smsInfo.costLevel) {
        case SmsCostLevel.low:
          return Colors.green;
        case SmsCostLevel.medium:
          return Colors.orange;
        case SmsCostLevel.high:
          return Colors.red;
      }
    }

    return Row(
      children: [
        Icon(
          smsInfo.smsCount == 1 ? Icons.sms : Icons.sms_outlined,
          size: 16,
          color: getSmsColor(),
        ),
        SizedBox(width: 4),
        Text(
          '${smsInfo.totalCharacters} ${context.tr('sms_templates.characters')}',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
        SizedBox(width: 8),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: getSmsColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: getSmsColor().withOpacity(0.3)),
          ),
          child: Text(
            '${smsInfo.smsCount} ${context.tr('sms_templates.sms_count')}',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: getSmsColor(),
            ),
          ),
        ),
      ],
    );
  }
}
