import 'dart:async';

import 'package:animaliaproject/utils/snack_bar_utils.dart';
import 'package:animaliaproject/services/api_service.dart';
import 'package:animaliaproject/services/auth/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../../models/sms_settings.dart';
import '../../../models/sms_reminder_timing.dart';
import '../../../models/sms_followup_timing.dart';
import '../../../models/salon_subscription.dart';
import '../../../providers/subscription_provider.dart';
import '../../../providers/appointment_settings_provider.dart';
import '../../../services/sms_quota_service.dart';
import '../../../services/sms_settings_service.dart';
import '../../../services/sms_reminder_timing_service.dart';
import '../../../services/sms_followup_timing_service.dart';
import '../../../services/ui_notification_service.dart';
import '../../../services/stripe_billing_service.dart';
import '../../../services/feature_toggle_service.dart';
import '../../../widgets/referral/referral_progress_widget.dart';
import '../../../widgets/permission_guard.dart';
import '../../../widgets/common/sms_usage_widget.dart';
import '../../../widgets/sms_logs_widget.dart';
import '../../../widgets/subscription/subscription_guard.dart';
import '../../../l10n/app_localizations.dart';
import 'messaging_template_management_screen.dart';

class SmsRemindersScreen extends StatefulWidget {
  const SmsRemindersScreen({Key? key}) : super(key: key);

  @override
  State<SmsRemindersScreen> createState() => _SmsRemindersScreenState();
}

class _SmsRemindersScreenState extends State<SmsRemindersScreen>
    with WidgetsBindingObserver {
  bool _smsEnabled = true;
  bool _appointmentConfirmations = true;
  bool _rescheduleNotifications = true;
  bool _cancellationNotifications = true;
  bool _followUpMessages = false;
  bool _completionMessages = false;
  MessagingChannel _messagingChannel = MessagingChannel.sms;
  int _remainingSms = 0;

  // Follow-up timing state
  int _followUpHours = 1; // Default 1 hour after completion
  List<SmsFollowUpTiming> _followUpTimings = [];
  bool _isLoadingFollowUpTimings = false;

  // Google review link state
  final TextEditingController _googleReviewLinkController = TextEditingController();
  bool _isSavingGoogleLink = false;

  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  // Referral system state
  String? _generatedReferralCode; // Deprecated - kept for backward compatibility
  String? _salonReferralCode; // The salon's unique referral code
  final TextEditingController _referralCodeController = TextEditingController();
  bool _isClaimingCode = false;
  bool _isValidatingCode = false;
  String? _validationMessage;
  bool? _isCodeValid;
  Timer? _validationTimer;

  // Preview section state

  // Reminder timing state
  List<SmsReminderTiming> _reminderTimings = [];
  bool _isLoadingTimings = false;

  // SMS billing subscription state
  bool _hasActiveSubscription = false;
  bool _isLoadingSubscription = false;
  Map<String, dynamic>? _subscriptionStatus;
  Map<String, dynamic>? _usageStats;

  // Scroll controller for navigation
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Listen to SMS count changes for real-time updates
    SmsQuotaService.smsCountNotifier.addListener(_onSmsCountChanged);

    // Load data after the widget is built to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSmsSettings();
      _loadReminderTimings();
      _loadFollowUpTimings(); // Load follow-up timings
      _loadRemainingSmsFromBackend(); // Prioritize backend data on init
      _loadAppointmentSettings(); // Load appointment settings
      _loadSubscriptionStatus(); // Load SMS billing subscription status

      // Listen to subscription changes to refresh SMS quota
      final subscriptionProvider = context.read<SubscriptionProvider>();
      subscriptionProvider.addListener(_onSubscriptionChanged);
    });
  }

  Future<void> _loadAppointmentSettings() async {
    try {
      await context.read<AppointmentSettingsProvider>().loadSettings();
    } catch (e) {
      // Handle error silently or show a snackbar if needed
      debugPrint('Error loading appointment settings: $e');
    }
  }

  /// Load SMS billing subscription status
  Future<void> _loadSubscriptionStatus() async {
    setState(() {
      _isLoadingSubscription = true;
    });

    try {
      // Load subscription status and usage stats in parallel
      final results = await Future.wait([
        StripeBillingService.getSubscriptionStatus(),
        StripeBillingService.getSmsUsageStats(),
      ]);

      final subscriptionResponse = results[0];
      final usageResponse = results[1];

      setState(() {
        if (subscriptionResponse.success && subscriptionResponse.data != null) {
          _subscriptionStatus = subscriptionResponse.data!;
          _hasActiveSubscription =
              _subscriptionStatus!['isActive'] as bool? ?? false;
        } else {
          _subscriptionStatus = null;
          _hasActiveSubscription = false;
        }

        if (usageResponse.success && usageResponse.data != null) {
          _usageStats = usageResponse.data!;
        } else {
          _usageStats = null;
        }

        _isLoadingSubscription = false;
      });
    } catch (e) {
      setState(() {
        _subscriptionStatus = null;
        _usageStats = null;
        _hasActiveSubscription = false;
        _isLoadingSubscription = false;
      });
      debugPrint('Error loading subscription status: $e');
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    SmsQuotaService.smsCountNotifier.removeListener(_onSmsCountChanged);

    // Remove subscription listener
    try {
      final subscriptionProvider = context.read<SubscriptionProvider>();
      subscriptionProvider.removeListener(_onSubscriptionChanged);
    } catch (e) {
      // Context might be disposed
    }

    // Dispose scroll controller
    _scrollController.dispose();

    // Dispose referral code controller
    _referralCodeController.dispose();

    // Cancel validation timer
    _validationTimer?.cancel();

    super.dispose();
  }

  /// Called when SMS count changes
  void _onSmsCountChanged() {
    if (mounted) {
      setState(() {
        _remainingSms = SmsQuotaService.smsCountNotifier.value;
      });
    }
  }

  /// Called when subscription changes
  void _onSubscriptionChanged() {
    if (mounted) {
      // Refresh SMS count when subscription changes
      _loadRemainingSmsFromBackend();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Refresh SMS counter when app resumes from background
    if (state == AppLifecycleState.resumed) {
      _loadRemainingSmsFromBackend();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh SMS counter when screen becomes visible again
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadRemainingSmsFromBackend();
      }
    });
  }

  /// Load reminder timings from backend
  Future<void> _loadReminderTimings() async {
    setState(() {
      _isLoadingTimings = true;
    });

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        throw Exception('No current salon found');
      }

      final response =
      await SmsReminderTimingService.getTimingsBySalon(salonId);

      if (response.success && response.data != null) {
        setState(() {
          _reminderTimings = response.data!;
          _isLoadingTimings = false;
        });
      } else {
        setState(() {
          _reminderTimings = [];
          _isLoadingTimings = false;
        });
        debugPrint('Failed to load reminder timings: ${response.error}');
      }
    } catch (e) {
      setState(() {
        _reminderTimings = [];
        _isLoadingTimings = false;
      });
      debugPrint('Error loading reminder timings: $e');
    }
  }

  /// Load follow-up timings from backend
  Future<void> _loadFollowUpTimings() async {
    setState(() {
      _isLoadingFollowUpTimings = true;
    });

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        throw Exception('No current salon found');
      }

      final response = await SmsFollowUpTimingService.getTimingsBySalon(salonId);

      if (response.success && response.data != null) {
        setState(() {
          _followUpTimings = response.data!;
          // Set the current follow-up hours from the first enabled timing
          if (_followUpTimings.isNotEmpty) {
            final enabledTiming = _followUpTimings.firstWhere(
                  (timing) => timing.isEnabled,
              orElse: () => _followUpTimings.first,
            );
            _followUpHours = enabledTiming.hoursAfter;
          }
          _isLoadingFollowUpTimings = false;
        });
      } else {
        setState(() {
          _followUpTimings = [];
          _isLoadingFollowUpTimings = false;
        });
        debugPrint('Failed to load follow-up timings: ${response.error}');
      }
    } catch (e) {
      setState(() {
        _followUpTimings = [];
        _isLoadingFollowUpTimings = false;
      });
      debugPrint('Error loading follow-up timings: $e');
    }
  }

  /// Load SMS settings from backend
  Future<void> _loadSmsSettings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await SmsSettingsService.getSmsSettings();

      if (response.success && response.data != null) {
        setState(() {
          _smsEnabled = response.data!.enabled;
          _appointmentConfirmations = response.data!.appointmentConfirmations;
          _rescheduleNotifications = response.data!.rescheduleNotifications;
          _cancellationNotifications = response.data!.cancellationNotifications;
          _followUpMessages = response.data!.followUpMessages;
          _completionMessages = response.data!.completionMessages;
          _messagingChannel = response.data!.messagingChannel;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load SMS settings';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading SMS settings: $e';
        _isLoading = false;
      });
    }
  }

  /// Load remaining SMS count from local storage
  Future<void> _loadRemainingSms() async {
    final count = await SmsQuotaService.getRemainingSms();
    if (mounted) {
      setState(() {
        _remainingSms = count;
      });
    }
  }

  /// Load remaining SMS count from backend (preferred)
  Future<void> _loadRemainingSmsFromBackend() async {
    try {
      final success = await SmsQuotaService.refreshFromBackend();
      if (success) {
        final count = await SmsQuotaService.getRemainingSms();
        if (mounted) {
          setState(() {
            _remainingSms = count;
          });
        }
      } else {
        // Fallback to local storage if backend fails
        await _loadRemainingSms();
      }
    } catch (e) {
      debugPrint('Error loading SMS count from backend: $e');
      // Fallback to local storage
      await _loadRemainingSms();
    }
  }

  /// Handle pull-to-refresh
  Future<void> _handleRefresh() async {
    try {
      // Refresh SMS settings, timings, quota, and subscription status from backend
      await Future.wait<void>([
        _loadSmsSettings(),
        _loadReminderTimings(),
        _loadFollowUpTimings(),
        _loadRemainingSmsFromBackend(),
        _loadSubscriptionStatus(),
      ]);
    } catch (e) {
      debugPrint('Error during refresh: $e');
      _showErrorSnackBar(context.tr('sms_reminders_refresh_data_error'));
    }
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          loc.t('sms_reminders_screen_title'),
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        actions: [
          // SMS Usage Indicator
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: SmsUsageIndicator(
              onTap: () {
                // Scroll to SMS usage section
                _scrollToSmsUsage();
              },
            ),
          ),
        ],
      ),
      body: PermissionGuard(
        requireManagementAccess: true,
        fallback: _buildNoPermissionView(),
        child: _isLoading
            ? Center(
            child: CircularProgressIndicator(
                color: Theme.of(context).colorScheme.onSurface))
            : _error != null
            ? _buildErrorView()
            : Column(
          children: [
            // Scrollable content with pull-to-refresh
            Expanded(
              child: RefreshIndicator(
                onRefresh: _handleRefresh,
                child: SingleChildScrollView(
                  controller: _scrollController,
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // SMS Settings Card
                      _buildSmsSettingsCard(),
                      SizedBox(height: 24),

                      // Reminder Timing Configuration
                      _buildReminderTimingCard(),
                      SizedBox(height: 24),

                      // Appointment Settings Section
                      appointmentSettingsSection(),
                      SizedBox(height: 24),

                      // SMS Usage and Free Trial Limits
                      _buildSmsUsageCard(),
                      SizedBox(height: 24),

                      // SMS Billing Subscription
                      _buildSmsSubscriptionCard(),
                      SizedBox(height: 24),

                      // SMS Logs Section
                      _buildSectionHeader(context.tr('sms_reminders_sms_history_title')),
                      const SizedBox(height: 16),
                      const SmsLogsWidget(),
                      SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Consumer<AppointmentSettingsProvider> appointmentSettingsSection() {
    return Consumer<AppointmentSettingsProvider>(
      builder: (context, appointmentProvider, child) {
        final loc = AppLocalizations.of(context);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader(loc.t('sms_reminders_appointment_settings')),
            const SizedBox(height: 16),
            _buildAutoFinalizationCard(appointmentProvider),
            const SizedBox(height: 16),
            _buildNotificationCard(appointmentProvider),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme
            .of(context)
            .colorScheme
            .onSurface,
      ),
    );
  }

  Widget _buildSmsSettingsCard() {
    final loc = AppLocalizations.of(context);

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(
                  Icons.settings_outlined,
                  size: 22,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    loc.t('sms_reminders_settings_title'),
                    style: TextStyle(
                      fontSize: 17,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                      letterSpacing: -0.3,
                    ),
                  ),
                ),
                OutlinedButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => MessagingTemplateManagementScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.edit_outlined, size: 16),
                  label: Text(
                    loc.t('sms_reminders_edit_templates_button'),
                    style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.primary,
                    side: BorderSide(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
                      width: 1.5,
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Divider(
            height: 1,
            thickness: 1,
            color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // SMS Counter
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        Theme.of(context).colorScheme.primary.withOpacity(0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.sms,
                          color: Theme.of(context).colorScheme.onPrimary,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              loc.t('sms_reminders_remaining', params: {'count': _remainingSms.toString()}),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'SMS-uri disponibile',
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                      TextButton(
                        onPressed: _scrollToSmsUsage,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              loc.t('sms_reminders_see_details'),
                              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600),
                            ),
                            const SizedBox(width: 4),
                            const Icon(Icons.arrow_forward_ios, size: 12),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Messaging Channel Selector
                _buildMessagingChannelSelector(),
                const SizedBox(height: 20),

                // SMS Switches
                _buildModernSwitchTile(
                  title: loc.t('sms_reminders_auto_sms_title'),
                  subtitle: loc.t('sms_reminders_auto_sms_description'),
                  icon: Icons.power_settings_new,
                  value: _smsEnabled,
                  onChanged: _isSaving
                      ? null
                      : (value) {
                          _updateSetting('enabled', value);
                          if (!value) {
                            _updateSetting('appointmentConfirmations', false);
                            _updateSetting('completionMessages', false);
                            _updateSetting('followUpMessages', false);
                          }
                        },
                ),
                const SizedBox(height: 12),
                _buildModernSwitchTile(
                  title: loc.t('sms_reminders_appointment_confirmations_title'),
                  subtitle: loc.t('sms_reminders_appointment_confirmations_description'),
                  icon: Icons.event_available_outlined,
                  value: _smsEnabled ? _appointmentConfirmations : false,
                  onChanged: _isSaving
                      ? null
                      : (value) {
                          if (!_smsEnabled && value) {
                            _updateSetting('enabled', true);
                          }
                          _updateSetting('appointmentConfirmations', value);
                        },
                ),
                const SizedBox(height: 12),
                _buildModernSwitchTile(
                  title: loc.t('sms_reminders_reschedule_notifications_title'),
                  subtitle: loc.t('sms_reminders_reschedule_notifications_description'),
                  icon: Icons.event_repeat_outlined,
                  value: _smsEnabled ? _rescheduleNotifications : false,
                  onChanged: _isSaving
                      ? null
                      : (value) {
                          if (!_smsEnabled && value) {
                            _updateSetting('enabled', true);
                          }
                          _updateSetting('rescheduleNotifications', value);
                        },
                ),
                const SizedBox(height: 12),
                _buildModernSwitchTile(
                  title: loc.t('sms_reminders_cancellation_notifications_title'),
                  subtitle: loc.t('sms_reminders_cancellation_notifications_description'),
                  icon: Icons.event_busy_outlined,
                  value: _smsEnabled ? _cancellationNotifications : false,
                  onChanged: _isSaving
                      ? null
                      : (value) {
                          if (!_smsEnabled && value) {
                            _updateSetting('enabled', true);
                          }
                          _updateSetting('cancellationNotifications', value);
                        },
                ),
                const SizedBox(height: 12),
                _buildModernSwitchTile(
                  title: loc.t('sms_reminders_followup_messages'),
                  subtitle: loc.t('sms_reminders_followup_messages_subtitle'),
                  icon: Icons.follow_the_signs_outlined,
                  value: _smsEnabled ? _followUpMessages : false,
                  onChanged: _isSaving
                      ? null
                      : (value) {
                          if (!_smsEnabled && value) {
                            _updateSetting('enabled', true);
                          }
                          _updateSetting('followUpMessages', value);
                        },
                ),
                if (_smsEnabled && _followUpMessages) ...[
                  const SizedBox(height: 16),
                  _buildFollowUpTimingDropdown(),
                  const SizedBox(height: 16),
                  _buildGoogleReviewLinkField(),
                ],
                const SizedBox(height: 12),
                _buildModernSwitchTile(
                  title: loc.t('sms_reminders_completion_messages'),
                  subtitle: loc.t('sms_reminders_completion_messages_subtitle'),
                  icon: Icons.check_circle_outline,
                  value: _smsEnabled ? _completionMessages : false,
                  onChanged: _isSaving
                      ? null
                      : (value) {
                          if (!_smsEnabled && value) {
                            _updateSetting('enabled', true);
                          }
                          _updateSetting('completionMessages', value);
                        },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernSwitchTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged ?? (_) {},
            inactiveThumbColor: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ],
      ),
    );
  }

  /// Build messaging channel selector widget
  Widget _buildMessagingChannelSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
            Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.swap_horiz,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr('sms_reminders_messaging_channel_title'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      context.tr('sms_reminders_messaging_channel_subtitle'),
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildChannelOption(
                  channel: MessagingChannel.sms,
                  icon: Icons.sms,
                  label: 'SMS',
                  // TODO TRANSLATE Doar pentru numere românești
                  description: context.tr('sms_reminders_channel_sms_description'),
                  isSelected: _messagingChannel == MessagingChannel.sms,
                  onTap: () => _updateMessagingChannel(MessagingChannel.sms),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildChannelOption(
                  channel: MessagingChannel.whatsapp,
                  icon: Icons.chat_bubble,
                  label: 'WhatsApp',
                  description: context.tr('sms_reminders_channel_whatsapp_description'),
                  isSelected: _messagingChannel == MessagingChannel.whatsapp,
                  onTap: () => _updateMessagingChannel(MessagingChannel.whatsapp),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build individual channel option
  Widget _buildChannelOption({
    required MessagingChannel channel,
    required IconData icon,
    required String label,
    required String description,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    // Check if this is WhatsApp channel and fetch feature flag status
    if (channel == MessagingChannel.whatsapp) {
      return FutureBuilder<bool>(
        future: FeatureToggleService.isWhatsAppEnabled(),
        builder: (context, snapshot) {
          final isWhatsAppEnabled = snapshot.data ?? false;

          return InkWell(
            onTap: isWhatsAppEnabled && !_isSaving ? onTap : null,
            borderRadius: BorderRadius.circular(10),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.15)
                    : (isWhatsAppEnabled
                        ? Theme.of(context).colorScheme.surface
                        : Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5)),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : (isWhatsAppEnabled
                          ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)
                          : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)),
                  width: isSelected ? 2 : 1,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : [],
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)
                          : (isWhatsAppEnabled
                              ? Theme.of(context).colorScheme.surfaceContainerHighest
                              : Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5)),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      FontAwesomeIcons.whatsapp,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : (isWhatsAppEnabled
                              ? const Color(0xFF25D366)
                              : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5)),
                      size: 24,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : (isWhatsAppEnabled
                              ? Theme.of(context).colorScheme.onSurface
                              : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5)),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    isWhatsAppEnabled ? description : 'Disponibil în curând',
                    style: TextStyle(
                      fontSize: 11,
                      color: isWhatsAppEnabled
                          ? Theme.of(context).colorScheme.onSurfaceVariant
                          : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                      fontStyle: isWhatsAppEnabled ? FontStyle.normal : FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (isSelected) ...[
                    const SizedBox(height: 8),
                    Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                      size: 18,
                    ),
                  ],
                  if (!isWhatsAppEnabled) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.orange.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.schedule,
                            size: 12,
                            color: Colors.orange.shade700,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'În curând',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ));
        },
      );
    }

    // For other channels (SMS), use the original implementation
    return InkWell(
      onTap: _isSaving ? null : onTap,
      borderRadius: BorderRadius.circular(10),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.15)
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : [],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)
                    : Theme.of(context).colorScheme.surfaceContainerHighest,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                fontSize: 11,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            if (isSelected) ...[
              const SizedBox(height: 8),
              Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.primary,
                size: 18,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Update messaging channel preference
  Future<void> _updateMessagingChannel(MessagingChannel channel) async {
    if (_messagingChannel == channel) return;

    setState(() {
      _isSaving = true;
      _messagingChannel = channel;
    });

    try {
      final request = UpdateSmsSettingsRequest(
        enabled: _smsEnabled,
        appointmentConfirmations: _appointmentConfirmations,
        completionMessages: _completionMessages,
        followUpMessages: _followUpMessages,
        messagingChannel: channel,
        rescheduleNotifications: _rescheduleNotifications,
        cancellationNotifications: _cancellationNotifications
      );

      final response = await SmsSettingsService.updateSmsSettings(request);

      if (response.success && response.data != null) {
        _showSuccessSnackBar(
          'Canal actualizat la ${channel.displayName}',
        );
      } else {
        // Revert on error
        setState(() {
          _messagingChannel = response.data?.messagingChannel ?? MessagingChannel.sms;
        });
        _showErrorSnackBar(
          response.error ?? 'Eroare la actualizarea canalului',
        );
      }
    } catch (e) {
      // Revert on error
      await _loadSmsSettings();
      _showErrorSnackBar('Eroare la actualizarea canalului: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Widget _buildFollowUpTimingDropdown() {
    final loc = AppLocalizations.of(context);

    final followUpOptions = [
      {'value': 0, 'label': loc.t('sms_reminders_timing_immediate')},
      {'value': 1, 'label': loc.t('sms_reminders_timing_1_hour')},
      {'value': 2, 'label': loc.t('sms_reminders_timing_2_hours')},
      {'value': 6, 'label': loc.t('sms_reminders_timing_6_hours')},
      {'value': 12, 'label': loc.t('sms_reminders_timing_12_hours')},
      {'value': 24, 'label': loc.t('sms_reminders_timing_24_hours')},
      {'value': 48, 'label': loc.t('sms_reminders_timing_48_hours')},
      {'value': 72, 'label': loc.t('sms_reminders_timing_72_hours')},
    ];

    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.02),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and label
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.schedule,
                    color: Theme.of(context).colorScheme.primary,
                    size: 18,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        loc.t('sms_reminders_followup_timing_title'),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        loc.t('sms_reminders_followup_timing_when'),
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Dropdown field
            DropdownButtonFormField<int>(
              value: _followUpHours,
              decoration: InputDecoration(
                labelText: loc.t('sms_reminders_send_after'),
                prefixIcon: Icon(
                  Icons.access_time,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
              isExpanded: true,
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              items: followUpOptions.map((option) {
                return DropdownMenuItem<int>(
                  value: option['value'] as int,
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          option['label'] as String,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: _isSaving ? null : (value) {
                if (value != null) {
                  _updateFollowUpTiming(value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoogleReviewLinkField() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Link Google Review',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Adaugă link-ul pentru recenzia Google a salonului tău.',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(height: 16),
            TextField(
              controller: _googleReviewLinkController,
              decoration: InputDecoration(
                labelText: 'Link Google Review',
                prefixIcon: Icon(
                  Icons.link,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              onChanged: (value) {
                // Optionally, handle changes to the link
              },
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isSavingGoogleLink ? null : _saveGoogleReviewLink,
              child: _isSavingGoogleLink
                  ? CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              )
                  : Text('Salvează link-ul'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveGoogleReviewLink() async {
    final link = _googleReviewLinkController.text.trim();
    if (link.isEmpty) {
      _showErrorSnackBar('Te rog introdu un link valid');
      return;
    }

    setState(() {
      _isSavingGoogleLink = true;
    });

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        throw Exception('Nu s-a putut identifica salonul');
      }

      // Update the salon's Google review link
      final response = await ApiService.updateSalonReviewLink(salonId, link);

      if (response.success) {
        _showSuccessSnackBar('Link-ul Google Review a fost salvat cu succes');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la salvarea link-ului');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la salvarea link-ului: $e');
    } finally {
      setState(() {
        _isSavingGoogleLink = false;
      });
    }
  }

  Widget _buildSmsUsageCard() {
    return SmsUsageWidget(
      showDetails: true,
      showUpgradeButton: true,
      onUpgradePressed: () {
        _navigateToSubscriptionPlans();
      },
    );
  }

  Widget _buildReminderTimingCard() {
    final loc = AppLocalizations.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  loc.t('sms_reminders_reminder_section_title'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme
                        .of(context)
                        .colorScheme
                        .onSurface,
                  ),
                ),
                TextButton.icon(
                  onPressed: _addNewReminder,
                  icon: Icon(Icons.add, size: 20),
                  label: Text(loc.t('sms_reminders_add_button')),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme
                        .of(context)
                        .colorScheme
                        .primary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              loc.t('sms_reminders_reminder_section_subtitle'),
              style: TextStyle(
                fontSize: 14,
                color: Theme
                    .of(context)
                    .colorScheme
                    .onSurfaceVariant,
              ),
            ),
            SizedBox(height: 16),
            if (_isLoadingTimings)
              Center(child: CircularProgressIndicator())
            else
              _buildReminderRows(),
            SizedBox(height: 8),
          ],
        ),
      ),
    );
  }


  Future<void> _saveAllSettings() async {
    // Remove this method as it's no longer needed
  }

  Widget _buildReminderRows() {
    return Column(
      children: [
        // Existing reminder rows
        ..._reminderTimings
            .asMap()
            .entries
            .map((entry) {
          final index = entry.key;
          final timing = entry.value;
          return _buildReminderRow(timing, index);
        }).toList(),

        // Empty state if no reminders
        if (_reminderTimings.isEmpty) _buildEmptyState(),
      ],
    );
  }

  Widget _buildReminderRow(SmsReminderTiming timing, int index) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: timing.isEnabled
            ? Theme
            .of(context)
            .colorScheme
            .surface
            : Theme
            .of(context)
            .colorScheme
            .surface
            .withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: timing.isEnabled
              ? Theme
              .of(context)
              .colorScheme
              .primary
              .withValues(alpha: 0.2)
              : Theme
              .of(context)
              .colorScheme
              .outline
              .withValues(alpha: 0.2),
          width: timing.isEnabled ? 1.5 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color:
            Colors.black.withValues(alpha: timing.isEnabled ? 0.06 : 0.02),
            blurRadius: timing.isEnabled ? 12 : 4,
            offset: Offset(0, timing.isEnabled ? 3 : 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // Hours dropdown
          Expanded(
            flex: 3,
            child: _buildHoursDropdown(timing, index),
          ),
          SizedBox(width: 16),

          // Toggle switch with animation
          AnimatedScale(
            scale: timing.isEnabled ? 1.0 : 0.95,
            duration: Duration(milliseconds: 200),
            child: Switch.adaptive(
              value: timing.isEnabled,
              onChanged: (value) => _toggleReminder(index, value),
              activeColor: Theme
                  .of(context)
                  .colorScheme
                  .primary,
              inactiveThumbColor: Theme
                  .of(context)
                  .colorScheme
                  .outline,
            ),
          ),
          SizedBox(width: 8),

          // Delete button with hover effect
          AnimatedOpacity(
            opacity: timing.isEnabled ? 1.0 : 0.5,
            duration: Duration(milliseconds: 200),
            child: IconButton(
              onPressed: () => _deleteReminder(index),
              icon: Icon(
                Icons.delete_outline,
                color:
                Theme
                    .of(context)
                    .colorScheme
                    .error
                    .withValues(alpha: 0.7),
                size: 20,
              ),
              tooltip: 'Șterge reminder',
              constraints: BoxConstraints(minWidth: 40, minHeight: 40),
              style: IconButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Theme
                    .of(context)
                    .colorScheme
                    .error,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHoursDropdown(SmsReminderTiming timing, int index) {
    final loc = AppLocalizations.of(context);

    final hourOptions = [
      {'value': 1, 'label': loc.t('sms_reminders_option_1_hour_before')},
      {'value': 2, 'label': loc.t('sms_reminders_option_2_hours_before')},
      {'value': 6, 'label': loc.t('sms_reminders_option_6_hours_before')},
      {'value': 12, 'label': loc.t('sms_reminders_option_12_hours_before')},
      {'value': 24, 'label': loc.t('sms_reminders_option_1_day_before')},
      {'value': 48, 'label': loc.t('sms_reminders_option_2_days_before')},
      {'value': 72, 'label': loc.t('sms_reminders_option_3_days_before')},
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: Theme
            .of(context)
            .colorScheme
            .surfaceContainerHighest
            .withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Theme
              .of(context)
              .colorScheme
              .outline
              .withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: timing.hoursBefore,
          isExpanded: true,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: Theme
                .of(context)
                .colorScheme
                .onSurfaceVariant,
            size: 20,
          ),
          style: TextStyle(
            color: Theme
                .of(context)
                .colorScheme
                .onSurface,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          items: hourOptions.map((option) {
            return DropdownMenuItem<int>(
              value: option['value'] as int,
              child: Text(option['label'] as String),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              _updateReminderHours(index, value);
            }
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final loc = AppLocalizations.of(context);

    return Container(
      padding: EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.schedule_outlined,
            size: 48,
            color:
            Theme
                .of(context)
                .colorScheme
                .onSurfaceVariant
                .withOpacity(0.6),
          ),
          SizedBox(height: 12),
          Text(
            loc.t('sms_reminders_empty_state'),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Theme
                  .of(context)
                  .colorScheme
                  .onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4),
          Text(
            loc.t('sms_reminders_empty_state_subtitle'),
            style: TextStyle(
              fontSize: 14,
              color: Theme
                  .of(context)
                  .colorScheme
                  .onSurfaceVariant
                  .withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Add a new reminder with default settings
  void _addNewReminder() async {
    final loc = AppLocalizations.of(context);

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        _showErrorSnackBar(loc.t('sms_reminders_salon_not_found'));
        return;
      }

      // Find the next available hour option that's not already used
      final usedHours = _reminderTimings.map((t) => t.hoursBefore).toSet();
      final availableHours = [1, 2, 6, 12, 24, 48, 72];
      final nextHour = availableHours.firstWhere(
            (hour) => !usedHours.contains(hour),
        orElse: () => 24, // Default to 24 hours if all are used
      );

      // Show loading state
      setState(() => _isSaving = true);

      final response = await SmsReminderTimingService.createTiming(
        salonId: salonId,
        hoursBefore: nextHour,
        isEnabled: true,
      );

      if (response.success && response.data != null) {
        setState(() {
          _reminderTimings.add(response.data!);
        });

        _showSuccessSnackBar(loc.t('sms_reminders_reminder_added'));
      } else {
        _showErrorSnackBar(
            response.error ?? loc.t('sms_reminders_reminder_error'));
      }
    } catch (e) {
      _showErrorSnackBar('${loc.t('sms_reminders_reminder_error')}: $e');
    } finally {
      setState(() => _isSaving = false);
    }
  }

  /// Toggle reminder on/off
  void _toggleReminder(int index, bool isEnabled) async {
    final loc = AppLocalizations.of(context);
    final timing = _reminderTimings[index];

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        _showErrorSnackBar(loc.t('sms_reminders_salon_not_found'));
        return;
      }

      // Optimistic update
      setState(() {
        _reminderTimings[index] = _reminderTimings[index].copyWith(
          isEnabled: isEnabled,
          updatedAt: DateTime.now(),
        );
      });

      final response = await SmsReminderTimingService.updateTiming(
        salonId: salonId,
        timingId: timing.id,
        hoursBefore: timing.hoursBefore,
        isEnabled: isEnabled,
      );

      if (response.success && response.data != null) {
        setState(() {
          _reminderTimings[index] = response.data!;
          _showSuccessSnackBar(loc.t('sms_reminders_reminder_updated'));
        });
      } else {
        // Revert on error
        setState(() {
          _reminderTimings[index] = timing;
        });
        _showErrorSnackBar(
            response.error ?? loc.t('sms_reminders_reminder_error'));
      }
    } catch (e) {
      // Revert on error
      setState(() {
        _reminderTimings[index] = timing;
      });
      _showErrorSnackBar('${loc.t('sms_reminders_reminder_error')}: $e');
    }
  }

  /// Update reminder hours
  void _updateReminderHours(int index, int hours) async {
    final loc = AppLocalizations.of(context);
    final timing = _reminderTimings[index];

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        _showErrorSnackBar(loc.t('sms_reminders_salon_not_found'));
        return;
      }

      // Optimistic update
      setState(() {
        _reminderTimings[index] = _reminderTimings[index].copyWith(
          hoursBefore: hours,
          description: _getDescriptionForHours(hours),
          updatedAt: DateTime.now(),
        );
      });

      final response = await SmsReminderTimingService.updateTiming(
        salonId: salonId,
        timingId: timing.id,
        hoursBefore: hours,
        isEnabled: timing.isEnabled,
      );

      if (response.success && response.data != null) {
        setState(() {
          _reminderTimings[index] = response.data!;
        });
      } else {
        // Revert on error
        setState(() {
          _reminderTimings[index] = timing;
        });
        _showErrorSnackBar(
            response.error ?? loc.t('sms_reminders_reminder_error'));
      }
    } catch (e) {
      // Revert on error
      setState(() {
        _reminderTimings[index] = timing;
      });
      _showErrorSnackBar('${loc.t('sms_reminders_reminder_error')}: $e');
    }
  }

  /// Delete a reminder
  void _deleteReminder(int index) async {
    final loc = AppLocalizations.of(context);
    final timing = _reminderTimings[index];

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        _showErrorSnackBar(loc.t('sms_reminders_salon_not_found'));
        return;
      }

      // Optimistic removal
      setState(() {
        _reminderTimings.removeAt(index);
      });

      final response = await SmsReminderTimingService.deleteTiming(
        salonId: salonId,
        timingId: timing.id,
      );

      if (response.success) {
        // Show undo option
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(loc.t('sms_reminders_reminder_deleted')),
            duration: Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: loc.t('sms_reminders_reminder_undo'),
              onPressed: () async {
                // Recreate the timing
                final recreateResponse =
                await SmsReminderTimingService.createTiming(
                  salonId: salonId,
                  hoursBefore: timing.hoursBefore,
                  isEnabled: timing.isEnabled,
                );

                if (recreateResponse.success && recreateResponse.data != null) {
                  setState(() {
                    _reminderTimings.insert(index, recreateResponse.data!);
                  });
                } else {
                  _showErrorSnackBar(loc.t('sms_reminders_reminder_restore_error'));
                }
              },
            ),
          ),
        );
      } else {
        // Revert on error
        setState(() {
          _reminderTimings.insert(index, timing);
        });
        _showErrorSnackBar(
            response.error ?? loc.t('sms_reminders_reminder_error'));
      }
    } catch (e) {
      // Revert on error
      setState(() {
        _reminderTimings.insert(index, timing);
      });
      _showErrorSnackBar('${loc.t('sms_reminders_reminder_error')}: $e');
    }
  }

  /// Get description for hours
  String _getDescriptionForHours(int hours) {
    switch (hours) {
      case 1:
        return 'Cu o oră înainte';
      case 2:
        return 'Cu 2 ore înainte';
      case 6:
        return 'Cu 6 ore înainte';
      case 12:
        return 'Cu 12 ore înainte';
      case 24:
        return 'Cu o zi înainte';
      case 48:
        return 'Cu 2 zile înainte';
      case 72:
        return 'Cu 3 zile înainte';
      default:
        return 'Cu $hours ore înainte';
    }
  }

  /// Update follow-up timing
  Future<void> _updateFollowUpTiming(int hours) async {
    setState(() {
      _isSaving = true;
      _followUpHours = hours; // Update local state immediately
    });

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        throw Exception('No current salon found');
      }

      // Check if there are existing follow-up timings
      if (_followUpTimings.isNotEmpty) {
        // Update the first enabled timing or create a new one
        final existingTiming = _followUpTimings.firstWhere(
              (timing) => timing.isEnabled,
          orElse: () => _followUpTimings.first,
        );

        final updateResponse = await SmsFollowUpTimingService.updateTiming(
          existingTiming.id,
          UpdateSmsFollowUpTimingRequest(
            hoursAfter: hours,
            isEnabled: true,
          ),
        );

        if (updateResponse.success && updateResponse.data != null) {
          // Update local state with the response
          setState(() {
            final index = _followUpTimings.indexWhere((t) => t.id == existingTiming.id);
            if (index != -1) {
              _followUpTimings[index] = updateResponse.data!;
            }
          });
          _showSuccessSnackBar('Timing-ul pentru follow-up a fost actualizat la ${_getFollowUpTimingLabel(hours)}');
        } else {
          throw Exception(updateResponse.message ?? 'Eroare la actualizarea timing-ului');
        }
      } else {
        // Create new timing if none exists
        final createResponse = await SmsFollowUpTimingService.createTiming(
          CreateSmsFollowUpTimingRequest(
            salonId: salonId,
            hoursAfter: hours,
            isEnabled: true,
          ),
        );

        if (createResponse.success && createResponse.data != null) {
          // Add to local state
          setState(() {
            _followUpTimings.add(createResponse.data!);
          });
          _showSuccessSnackBar('Timing-ul pentru follow-up a fost creat la ${_getFollowUpTimingLabel(hours)}');
        } else {
          throw Exception(createResponse.message ?? 'Eroare la crearea timing-ului');
        }
      }
    } catch (e) {
      // Revert local state on error
      setState(() {
        // Restore previous value if available
        if (_followUpTimings.isNotEmpty) {
          final enabledTiming = _followUpTimings.firstWhere(
                (timing) => timing.isEnabled,
            orElse: () => _followUpTimings.first,
          );
          _followUpHours = enabledTiming.hoursAfter;
        } else {
          _followUpHours = 1; // Default fallback
        }
      });
      _showErrorSnackBar('Eroare la actualizarea timing-ului: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Get label for follow-up timing
  String _getFollowUpTimingLabel(int hours) {
    switch (hours) {
      case 0:
        return 'imediat după finalizare';
      case 1:
        return '1 oră după finalizare';
      case 2:
        return '2 ore după finalizare';
      case 6:
        return '6 ore după finalizare';
      case 12:
        return '12 ore după finalizare';
      case 24:
        return '24 ore după finalizare';
      case 48:
        return '48 ore după finalizare';
      case 72:
        return '72 ore după finalizare';
      default:
        return '$hours ore după finalizare';
    }
  }

  /// Update a specific SMS setting
  Future<void> _updateSetting(String settingKey, dynamic value) async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Update local state first for immediate UI feedback
      setState(() {
        switch (settingKey) {
          case 'enabled':
            _smsEnabled = value as bool;
            break;
          case 'appointmentConfirmations':
            _appointmentConfirmations = value as bool;
            break;
          case 'rescheduleNotifications':
            _rescheduleNotifications = value as bool;
            break;
          case 'cancellationNotifications':
            _cancellationNotifications = value as bool;
            break;
          case 'completionMessages':
            _completionMessages = value as bool;
            break;
          case 'followUpMessages':
            _followUpMessages = value as bool;
            break;
        }
      });

      // Create update request with current values
      final request = UpdateSmsSettingsRequest(
        enabled: _smsEnabled,
        appointmentConfirmations: _appointmentConfirmations,
        rescheduleNotifications: _rescheduleNotifications,
        cancellationNotifications: _cancellationNotifications,
        completionMessages: _completionMessages,
        followUpMessages: _followUpMessages,
        messagingChannel: _messagingChannel,
      );

      // Send to backend
      final response = await SmsSettingsService.updateSmsSettings(request);

      if (response.success && response.data != null) {
        setState(() {});
        _showSuccessSnackBar('Setările SMS au fost actualizate cu succes!');
      } else {
        _showErrorSnackBar(
            response.error ?? 'Eroare la actualizarea setărilor SMS');
        // Revert local state
        await _loadSmsSettings();
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la actualizarea setărilor: $e');
      // Revert local state
      await _loadSmsSettings();
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Scroll to SMS usage section
  void _scrollToSmsUsage() {
    // Scroll to approximately where the SMS usage card is
    // This is roughly after the SMS settings card (around 300px)
    _scrollController.animateTo(
      1000.0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  /// Navigate to subscription plans screen
  void _navigateToSubscriptionPlans() {
    try {
      // Try to navigate to subscription screen
      Navigator.of(context).pushNamed('/subscription');
    } catch (e) {
      // Fallback: Show info dialog about upgrading
      showDialog(
        context: context,
        builder: (context) =>
            AlertDialog(
              title: Text('Upgrade Required'),
              content: Text(
                'You have reached your free trial SMS limit. Please upgrade to a paid plan to continue sending SMS messages.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('OK'),
                ),
              ],
            ),
      );
    }
  }

  /// Show success message
  void _showSuccessSnackBar(String message) {
    UINotificationService.showSuccess(
      context: context,
      title: 'Succes',
      message: message,
    );
  }

  /// Show error message
  void _showErrorSnackBar(String message) {
    showTopSnackBar(
      context,
      SnackBar(
        content: Text(message),
        backgroundColor: Theme
            .of(context)
            .colorScheme
            .error,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Build error view
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme
                  .of(context)
                  .colorScheme
                  .error,
            ),
            SizedBox(height: 16),
            Text(
              'Eroare la încărcarea setărilor SMS',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              _error ?? 'Eroare necunoscută',
              style: TextStyle(
                fontSize: 14,
                color: Theme
                    .of(context)
                    .colorScheme
                    .onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadSmsSettings,
              style: ElevatedButton.styleFrom(),
              child: Text('Încearcă din nou'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build no permission view
  Widget _buildNoPermissionView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 64,
              color: Theme
                  .of(context)
                  .colorScheme
                  .onSurface,
            ),
            SizedBox(height: 16),
            Text(
              'Acces restricționat',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme
                    .of(context)
                    .colorScheme
                    .onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'Doar groomer-ii șefi pot configura setările SMS pentru salon.',
              style: TextStyle(
                fontSize: 14,
                color: Theme
                    .of(context)
                    .colorScheme
                    .onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(),
              child: Text('Înapoi'),
            ),
          ],
        ),
      ),
    );
  }

  // Appointment Settings Card Builders
  Widget _buildAutoFinalizationCard(AppointmentSettingsProvider provider) {
    final loc = AppLocalizations.of(context);

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        loc.t('sms_reminders_auto_finalize'),
                        style:
                        Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        loc.t('sms_reminders_auto_finalize_subtitle'),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: provider.autoFinalizeEnabled,
                  onChanged: provider.isLoading
                      ? null
                      : (value) async {
                    await provider.setAutoFinalize(value);
                    _showSuccessSnackBar(
                        loc.t('sms_reminders_settings_auto_finalize_success'));
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationCard(AppointmentSettingsProvider provider) {
    final loc = AppLocalizations.of(context);

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.notifications_active,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        loc.t('sms_reminders_overdue_notifications'),
                        style:
                        Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        loc.t('sms_reminders_overdue_notifications_subtitle'),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: provider.overdueNotificationsEnabled,
                  onChanged: (provider.autoFinalizeEnabled ||
                      provider.isLoading)
                      ? null
                      : (value) async {
                    await provider.setOverdueNotifications(value);
                    _showSuccessSnackBar(
                        loc.t('sms_reminders_settings_notification_success'));
                  },
                ),
              ],
            ),
            if (provider.autoFinalizeEnabled)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  loc.t('sms_reminders_overdue_disabled_reason'),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build SMS billing subscription card with subscription guard
  Widget _buildSmsSubscriptionCard() {
    return FutureBuilder<String?>(
      future: AuthService.getCurrentSalonId(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        final salonId = snapshot.data;
        if (salonId == null) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Text('Nu s-a putut identifica salonul'),
            ),
          );
        }

        return SubscriptionGuard(
          feature: 'sms_subscription',
          salonId: salonId,
          requiredTier: SubscriptionTier.freelancer,
          upgradeMessage:
          context.tr('sms_reminders_subscription_upgrade_message'),
          child: _buildSmsSubscriptionContent(),
        );
      },
    );
  }

  /// Build the actual SMS subscription card content
  Widget _buildSmsSubscriptionContent() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.credit_card,
                  color: Theme
                      .of(context)
                      .colorScheme
                      .primary,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('sms_reminders_subscription_title'),
                  style: Theme
                      .of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoadingSubscription)
              const Center(child: CircularProgressIndicator())
            else
              if (_hasActiveSubscription)
                _buildActiveSubscriptionInfo()
              else
                _buildSubscriptionSetupSection(),
          ],
        ),
      ),
    );
  }

  /// Build active subscription information
  Widget _buildActiveSubscriptionInfo() {
    final status = _subscriptionStatus;
    final usage = _usageStats;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  context.tr('sms_reminders_subscription_active'),
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Subscription details
        if (status != null) ...[
          _buildInfoRow(context.tr('sms_subscription_status'), status['status'] ?? 'Active'),
          const SizedBox(height: 8),
          if (status['nextBillingDate'] != null)
            _buildInfoRow(context.tr('sms_subscription_next_billing'), status['nextBillingDate']),
          const SizedBox(height: 8),
        ],

        // Usage statistics
        if (usage != null) ...[
          const Divider(),
          const SizedBox(height: 8),
          Text(
            context.tr('sms_reminders_subscription_usage'),
            style: Theme
                .of(context)
                .textTheme
                .titleSmall
                ?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
              context.tr('sms_reminders_subscription_current_month'), '${usage['currentMonthSms'] ?? 0}'),
          const SizedBox(height: 4),
          _buildInfoRow(
              context.tr('sms_reminders_subscription_estimated_cost'), '${usage['estimatedCost'] ?? '0.00'} €'),
          const SizedBox(height: 12),
        ],

        Row(
          children: [
            ElevatedButton.icon(
              onPressed: _showUsageDetails,
              icon: const Icon(Icons.analytics),
              label: Text(context.tr('sms_reminders_subscription_details_usage')),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme
                    .of(context)
                    .colorScheme
                    .secondary,
              ),
            ),
            const SizedBox(width: 12),
            TextButton.icon(
              onPressed: _showCancelSubscriptionDialog,
              icon: const Icon(Icons.cancel, color: Colors.red),
              label:
              Text(context.tr('sms_reminders_subscription_cancel'), style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      ],
    );
  }

  /// Build subscription setup section
  Widget _buildSubscriptionSetupSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.info, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  context.tr('sms_reminders_subscription_setup'),
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Text(
          context.tr('sms_reminders_subscription_pay_only'),
          style: Theme
              .of(context)
              .textTheme
              .titleMedium
              ?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '• ${context.tr('sms_reminders_subscription_price_per_sms')}\n'
              '• ${context.tr('sms_reminders_subscription_no_monthly_fees')}\n'
              '• ${context.tr('sms_reminders_subscription_cancel_anytime')}\n'
              '• ${context.tr('sms_reminders_subscription_auto_billing')}',
          style: Theme
              .of(context)
              .textTheme
              .bodyMedium,
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(Icons.open_in_new, color: Colors.orange, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  context.tr('sms_reminders_subscription_stripe_redirect'),
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: _startSubscription,
              icon: const Icon(Icons.credit_card),
              label: Text(context.tr('sms_reminders_subscription_subscribe_now')),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme
                    .of(context)
                    .colorScheme
                    .primary,
                padding:
                const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
            const SizedBox(width: 8),
            TextButton.icon(
              onPressed: _showSubscriptionInfo,
              icon: const Icon(Icons.help_outline),
              label: Text(context.tr('sms_reminders_subscription_more_details')),
              style: ElevatedButton.styleFrom(
                textStyle: TextStyle(
                    color: Theme
                        .of(context)
                        .colorScheme
                        .onSurfaceVariant,
                    fontSize: 10
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build info row for billing details
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: Theme
                .of(context)
                .textTheme
                .bodySmall
                ?.copyWith(
              fontWeight: FontWeight.w500,
              color:
              Theme
                  .of(context)
                  .colorScheme
                  .onSurface
                  .withOpacity(0.7),
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme
                .of(context)
                .textTheme
                .bodySmall
                ?.copyWith(
              fontFamily: 'monospace',
            ),
          ),
        ),
      ],
    );
  }

  /// Start SMS subscription process
  void _startSubscription() async {
    // Show confirmation dialog first
    final confirmed = await _showSubscriptionConfirmationDialog();
    if (!confirmed) return;

    setState(() {
      _isLoadingSubscription = true;
    });

    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        _showErrorSnackBar(context.tr('sms_subscription_salon_not_found'));
        return;
      }

      // Create Stripe checkout session
      final response = await StripeBillingService.createSmsCheckoutSession(
        salonId: salonId,
        successUrl: 'https://www.app.animalia-programari.ro', // todo success
        cancelUrl: 'https://www.app.animalia-programari.ro', // todo cancel
      );

      if (response.success && response.data != null) {
        final checkoutUrl = response.data!;

        // Launch Stripe checkout in external browser
        final launched = await StripeBillingService.launchCheckout(checkoutUrl);

        if (!launched) {
          _showErrorSnackBar(
              context.tr('sms_subscription_payment_error'));
        } else {
          // Show success message and reload status after a delay
          _showSuccessSnackBar(
              context.tr('sms_reminders_subscription_redirect_success'));

          // Reload subscription status after user returns (simulate)
          Future.delayed(const Duration(seconds: 10), () {
            _loadSubscriptionStatus();
          });
        }
      } else {
        _showErrorSnackBar(
            response.error ?? context.tr('sms_subscription_session_error'));
      }
    } catch (e) {
      _showErrorSnackBar('${context.tr('sms_subscription_initiate_error', params: {'error': e.toString()})}');
    } finally {
      setState(() {
        _isLoadingSubscription = false;
      });
    }
  }

  /// Show subscription confirmation dialog
  Future<bool> _showSubscriptionConfirmationDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) =>
          AlertDialog(
            title: Row(
              children: [
                Icon(Icons.open_in_new, color: Colors.orange),
                const SizedBox(width: 8),
                Text(context.tr('sms_subscription_confirm_title')),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('sms_reminders_subscription_stripe_redirect'),
                  style: Theme
                      .of(context)
                      .textTheme
                      .bodyMedium,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                    Theme
                        .of(context)
                        .colorScheme
                        .primary
                        .withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('sms_subscription_details_title'),
                        style: Theme
                            .of(context)
                            .textTheme
                            .titleSmall
                            ?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '• Cost: 0.05€/SMS\n'
                            '• Fără taxe de abonament\n'
                            '• Fără taxe ascunse\n'
                            '• Facturare lunară automată',
                        style: Theme
                            .of(context)
                            .textTheme
                            .bodySmall,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.warning, color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          context.tr('sms_reminders_subscription_stripe_warning'),
                          style: TextStyle(
                            color: Colors.orange.shade700,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(context.tr('common.cancel')),
              ),
              ElevatedButton.icon(
                onPressed: () => Navigator.of(context).pop(true),
                icon: const Icon(Icons.credit_card, size: 16),
                label: Text(context.tr('sms_subscription_continue_payment')),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme
                      .of(context)
                      .colorScheme
                      .primary,
                ),
              ),
            ],
          ),
    ) ??
        false;
  }

  /// Show subscription information dialog
  void _showSubscriptionInfo() {
    showDialog(
      context: context,
      builder: (context) =>
          AlertDialog(
            title: Text(context.tr('sms_subscription_info_title')),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('sms_subscription_how_it_works'),
                    style: Theme
                        .of(context)
                        .textTheme
                        .titleMedium
                        ?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildInfoStep('1', context.tr('sms_subscription_step_1_title'),
                      context.tr('sms_subscription_step_1_desc')),
                  const SizedBox(height: 12),
                  _buildInfoStep('2', context.tr('sms_subscription_step_2_title'),
                      context.tr('sms_subscription_step_2_desc')),
                  const SizedBox(height: 12),
                  _buildInfoStep('3', context.tr('sms_subscription_step_3_title'),
                      context.tr('sms_subscription_step_3_desc')),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme
                          .of(context)
                          .colorScheme
                          .primary
                          .withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.tr('sms_subscription_pricing_title'),
                          style: Theme
                              .of(context)
                              .textTheme
                              .titleSmall
                              ?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• ${context.tr('sms_subscription_pricing_romania')}\n'
                              '• ${context.tr('sms_subscription_no_subscription_fees')}\n'
                              '• ${context.tr('sms_subscription_no_hidden_fees')}\n'
                              '• ${context.tr('sms_subscription_monthly_billing')}',
                          style: Theme
                              .of(context)
                              .textTheme
                              .bodySmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Închide'),
              ),
            ],
          ),
    );
  }

  /// Show usage details dialog
  void _showUsageDetails() {
    final usage = _usageStats;

    showDialog(
      context: context,
      builder: (context) =>
          AlertDialog(
            title: Text(context.tr('sms_subscription_usage_details_title')),
            content: SingleChildScrollView(
              child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (usage != null) ...[
                      _buildUsageRow(context.tr('sms_subscription_current_month_sms'),
                          '${usage['currentMonthSms'] ?? 0}'),
                      _buildUsageRow(context.tr('sms_subscription_last_month_sms'),
                          '${usage['lastMonthSms'] ?? 0}'),
                      _buildUsageRow(
                          context.tr('sms_subscription_total_sms'), '${usage['totalSms'] ?? 0}'),
                      const Divider(),
                      _buildUsageRow(context.tr('sms_subscription_current_month_cost'),
                          '${usage['currentMonthCost'] ?? '0.00'} €'),
                      _buildUsageRow(context.tr('sms_subscription_last_month_cost'),
                          '${usage['lastMonthCost'] ?? '0.00'} €'),
                      _buildUsageRow(
                          context.tr('sms_subscription_total_cost'), '${usage['totalCost'] ?? '0.00'} €'),
                    ] else
                      ...[
                        Text(
                          context.tr('sms_subscription_no_usage_data'),
                          style: Theme
                              .of(context)
                              .textTheme
                              .bodyMedium,
                        ),
                      ],
                  ]),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Închide'),
              ),
            ],
          ),
    );
  }

  /// Show cancel subscription confirmation dialog
  void _showCancelSubscriptionDialog() {
    showDialog(
      context: context,
      builder: (context) =>
          AlertDialog(
            title: Row(
              children: [
                Icon(Icons.warning, color: Colors.red),
                const SizedBox(width: 8),
                Text(context.tr('sms_subscription_cancel_title')),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('sms_subscription_cancel_confirm'),
                  style: Theme
                      .of(context)
                      .textTheme
                      .bodyMedium,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('sms_subscription_cancel_consequences'),
                        style: Theme
                            .of(context)
                            .textTheme
                            .titleSmall
                            ?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade700,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '• ${context.tr('sms_subscription_cancel_no_sms')}\n'
                            '• ${context.tr('sms_subscription_cancel_immediate')}\n'
                            '• ${context.tr('sms_subscription_cancel_billing')}',
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(context.tr('sms_subscription_cancel_keep')),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _cancelSubscription();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                ),
                child: Text(context.tr('sms_subscription_cancel_confirm_button'),
                    style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
    );
  }

  /// Cancel subscription
  void _cancelSubscription() async {
    setState(() {
      _isLoadingSubscription = true;
    });

    try {
      final response = await StripeBillingService.cancelSubscription();

      if (response.success) {
        _showSuccessSnackBar(context.tr('sms_subscription_cancelled_success'));
        await _loadSubscriptionStatus(); // Reload status
      } else {
        _showErrorSnackBar(
            response.error ?? context.tr('sms_subscription_cancel_error'));
      }
    } catch (e) {
      _showErrorSnackBar('${context.tr('sms_subscription_cancel_error')}: $e');
    } finally {
      setState(() {
        _isLoadingSubscription = false;
      });
    }
  }

  /// Build usage row for details dialog
  Widget _buildUsageRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme
                .of(context)
                .textTheme
                .bodyMedium,
          ),
          Text(
            value,
            style: Theme
                .of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Build info step for subscription info dialog
  Widget _buildInfoStep(String number, String title, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: Theme
                .of(context)
                .colorScheme
                .primary,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              number,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme
                    .of(context)
                    .textTheme
                    .titleSmall
                    ?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme
                    .of(context)
                    .textTheme
                    .bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
