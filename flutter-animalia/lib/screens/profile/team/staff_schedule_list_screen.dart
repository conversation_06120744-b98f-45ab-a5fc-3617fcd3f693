import 'package:flutter/material.dart';

import '../../../services/staff_service.dart';
import '../../../widgets/cards/staff_profile_card.dart';
import '../../../l10n/app_localizations.dart';

class StaffScheduleListScreen extends StatefulWidget {
  const StaffScheduleListScreen({Key? key}) : super(key: key);

  @override
  State<StaffScheduleListScreen> createState() => _StaffScheduleListScreenState();
}

class _StaffScheduleListScreenState extends State<StaffScheduleListScreen> {
  late Future<StaffListResponse?> _staffFuture;

  @override
  void initState() {
    super.initState();
    _staffFuture = _loadStaff();
  }

  Future<StaffListResponse?> _loadStaff() async {
    final response = await StaffService.getCurrentSalonStaff(activeOnly: true);
    if (response.success && response.data != null) {
      return response.data;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('staff_schedule_screen.title')),
        elevation: 0,
      ),
      body: FutureBuilder<StaffListResponse?>(
        future: _staffFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(
              child: Text(
                localizations.translate('staff_schedule_screen.error')
                    .replaceAll('{error}', snapshot.error.toString()),
              ),
            );
          }

          final staff = snapshot.data?.activeStaff ?? [];
          if (staff.isEmpty) {
            return Center(
              child: Text(localizations.translate('staff_schedule_screen.no_active_members')),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.only(top: 8, bottom: 8),
            itemCount: staff.length,
            itemBuilder: (context, index) {
              final member = staff[index];
              return StaffProfileCard(staff: member);
            },
          );
        },
      ),
    );
  }
}
