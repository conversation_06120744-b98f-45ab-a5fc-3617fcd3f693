import 'package:animaliaproject/screens/profile/team/staff_detail_screen.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../l10n/app_localizations.dart';
import '../../../models/user_role.dart';

import '../../../config/theme/app_theme.dart';
import '../../../models/salon_invitation.dart';
import '../../../models/salon_subscription.dart';
import '../../../models/staff_working_hours_settings.dart';
import '../../../models/working_hours_settings.dart';
import '../../../providers/calendar_provider.dart';
import '../../../providers/subscription_provider.dart';
import '../../../services/auth/auth_service.dart';
import '../../../services/invitation_service.dart';
import '../../../services/staff_service.dart';
import '../../../services/staff_working_hours_service.dart';
import '../../../services/subscription_limit_service.dart';
import '../../../services/ui_notification_service.dart';
import '../../../widgets/forms/add_staff_dialog.dart';
import '../../../widgets/subscription/limit_guard.dart';
import '../../../services/subscription/revenue_cat_paywall_service.dart';
import '../../../widgets/subscription/upgrade_prompt_card.dart';
import '../../../widgets/subscription/usage_dashboard.dart';
import '../../pending_invitation_detail_screen.dart';

class TeamManagementScreen extends StatefulWidget {
  const TeamManagementScreen({Key? key}) : super(key: key);

  @override
  State<TeamManagementScreen> createState() => _TeamManagementScreenState();
}

class _TeamManagementScreenState extends State<TeamManagementScreen> {
  CalendarProvider? _calendarProvider;
  ScrollController? _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Store reference to calendar provider safely
    _calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
  }

  @override
  void dispose() {
    // Dispose scroll controller to prevent animation errors
    _scrollController?.dispose();

    // Defer calendar staff refresh to after dispose completes to avoid framework lock error
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshCalendarStaffDataSafely();
    });

    super.dispose();
  }

  Future<StaffListResponse?> _loadStaff() async {
    try {
      final staffResponse = await StaffService.getCurrentSalonStaff(activeOnly: false);

      if (staffResponse.success && staffResponse.data != null) {
        final activeStaff = staffResponse.data!.activeStaff;

        // Also load pending invitations via InvitationService
        DebugLogger.logVerbose('🔄 Loading pending invitations...');
        final inviteResponse = await InvitationService.getSentInvitations();
        List<PendingStaffInvitation> pendingInvitations = [];

        if (inviteResponse.success && inviteResponse.data != null) {
          DebugLogger.logVerbose('   - Raw invitations: ${inviteResponse.data!.length}');
          pendingInvitations = inviteResponse.data!
              .map(_convertSalonInvitation)
              .toList();
          DebugLogger.logVerbose('   - Converted invitations: ${pendingInvitations.length}');
          for (final inv in pendingInvitations) {
            DebugLogger.logVerbose('     * ${inv.phoneNumber} - ${inv.status}');
          }
        } else {
          DebugLogger.logVerbose('   - Invitation error: ${inviteResponse.error}');
        }

        DebugLogger.logVerbose('- Active Staff: ${activeStaff.length}, Pending Invitations: ${pendingInvitations.length}') ;

        return StaffListResponse(
          activeStaff: activeStaff,
          pendingStaff: pendingInvitations,
          totalActiveCount: activeStaff.length,
          totalPendingCount: pendingInvitations.length,
          activeCount: activeStaff.where((s) => s.isActive).length,
          inactiveCount: activeStaff.where((s) => !s.isActive).length,
        );
      } else {
        DebugLogger.logVerbose('   - Error: ${staffResponse.error}');
      }
      return null;
    } catch (e) {
      DebugLogger.logVerbose('❌ TeamManagementScreen: Error loading staff: $e');
      return null;
    }
  }

  Future<void> _refreshStaff() async {
    if (mounted) {
      setState(() {
        // This will trigger a rebuild and reload the data
      });
    }
  }

  /// Refresh both team and calendar staff data
  void _refreshAllStaffData() {
    if (mounted) {
      _refreshStaff();
      _refreshCalendarStaffData();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Safety check to prevent build on disposed widget
    if (!mounted) {
      return const SizedBox.shrink();
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('team_management.title'),
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(context.tr('team_management.help_tooltip'))),
              );
            },
            tooltip: context.tr('team_management.help_tooltip_short'),
          ),
        ],
      ),
      body: FutureBuilder<StaffListResponse?>(
        future: _loadStaff(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('team_management.error_loading_team_title'),
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.tr('team_management.error_loading_team_message', params: {'error': '${snapshot.error}'}),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _refreshStaff(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text(context.tr('team_management.try_again_button')),
                  ),
                ],
              ),
            );
          }

          final staffList = snapshot.data;

          if (staffList == null ||
              (staffList.activeStaff.isEmpty && staffList.pendingStaff.isEmpty)) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.group_off,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('team_management.no_team_members_title'),
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.tr('team_management.no_team_members_message'),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade500,
                    ),
                  ),
                  const SizedBox(height: 16),
                  FutureBuilder<String?>(
                    future: AuthService.getCurrentSalonId(),
                    builder: (context, snapshot) {
                      final salonId = snapshot.data ?? '';
                      return LimitGuard(
                        limitType: 'staff',
                        salonId: salonId,
                        showCompactPrompt: true,
                        child: ElevatedButton(
                          onPressed: () => _showAddStaffDialog(),
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Icon(
                            Icons.add,
                            color: AppColors.lightGrayVariant
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            );
          }

          final activeStaff = staffList.activeStaff;
          final pendingInvitations = staffList.pendingStaff;

          return Column(
            children: [
              // Usage Dashboard
              FutureBuilder<String?>(
                future: AuthService.getCurrentSalonId(),
                builder: (context, snapshot) {
                  final salonId = snapshot.data ?? '';
                  if (salonId.isNotEmpty) {
                    return UsageDashboard(
                      salonId: salonId,
                      compact: true,
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // Header with add button
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        context.tr('team_management.members_count', params: {'count': activeStaff.length.toString()}),
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),

                    Consumer<SubscriptionProvider>(
                      builder: (context, subscriptionProvider, child) {
                        return ElevatedButton(
                          onPressed: () => _handleAddStaffWithUpgradeCheck(subscriptionProvider),
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Icon(Icons.add),
                        );
                      },
                    ),
                  ],
                ),
              ),
              // Team members list
              Expanded(
                child: RefreshIndicator(
                  onRefresh: _refreshStaff,
                  color: Theme.of(context).primaryColor,
                  child: ListView(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    children: [
                      for (final staff in activeStaff) _buildStaffCard(staff),
                      if (pendingInvitations.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        Text(
                          context.tr('team_management.pending_invitations_title'),
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        for (final invitation in pendingInvitations)
                          _buildPendingInvitationCard(invitation),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStaffCard(StaffResponse staff) {
    return GestureDetector(
      onTap: () => _navigateToStaffDetail(staff),
      child: Card(
        key: ValueKey(staff.id),
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Staff member avatar
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: staff.isActive ? Theme.of(context).colorScheme.primary : Colors.grey,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey.shade300, width: 2),
                    ),
                    child: Center(
                      child: Text(
                        staff.name.split(' ').map((n) => n.isNotEmpty ? n[0] : '').join(''),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Builder(
                                builder: (context) {
                                  // Add comprehensive logging for nickname display debugging
                                  return Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        staff.displayName,
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: staff.isActive ? Theme.of(context).colorScheme.onSurface : Colors.grey,
                                        ),
                                      ),
                                      if (staff.nickname != null && staff.nickname!.isNotEmpty && staff.nickname != staff.name) ...[
                                        const SizedBox(height: 2),
                                        Text(
                                          '(${staff.name})',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey.shade600,
                                            fontStyle: FontStyle.italic,
                                          ),
                                        ),
                                      ],
                                    ],
                                  );
                                },
                              ),
                            ),
                            // Role badge
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: staff.groomerRole.hasManagementAccess
                                    ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                                    : Colors.blue.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: staff.groomerRole.hasManagementAccess
                                      ? Theme.of(context).primaryColor.withValues(alpha: 0.3)
                                      : Colors.blue.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                staff.groomerRole.localizedDisplayName(context),
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: staff.groomerRole.hasManagementAccess
                                      ? Theme.of(context).primaryColor
                                      : Colors.blue,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        if (staff.phone != null && staff.phone!.isNotEmpty) ...[
                          Row(
                            children: [
                              Icon(Icons.phone, size: 16, color: Colors.grey.shade600),
                              const SizedBox(width: 4),
                              Text(
                                staff.formattedPhone ?? staff.phone!,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ],
                        // Status indicator
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: staff.isActive ? Colors.green : Colors.red,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              staff.isActive ? context.tr('team_management.staff_card_active_status') : context.tr('team_management.staff_card_inactive_status'),
                              style: TextStyle(
                                fontSize: 12,
                                color: staff.isActive ? Colors.green : Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(), // Add spacer to push "Detalii" to the right
                            // Visual indicator for tap action
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                   Text(
                                    context.tr('team_management.staff_card_details_button'),
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    size: 12,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (staff.specialties.isNotEmpty) ...[
                const SizedBox(height: 12),
                 Text(
                  context.tr('team_management.staff_card_specialties_title'),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: staff.specialties.map((specialty) =>
                    _buildSpecialtyChip(specialty)
                  ).toList(),
                ),
              ],
              if (staff.notes != null && staff.notes!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.note, size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          staff.notes!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade700,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSpecialtyChip(String specialty) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Theme.of(context).primaryColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        specialty,
        style:  TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  void _navigateToStaffDetail(StaffResponse staff) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StaffDetailScreen(
          staff: staff,
          initialTabIndex: 0,
        ),
      ),
    ).then((_) {
      // Refresh the list when returning from detail screen
      if (mounted) {
        _refreshStaff();
      }
    });
  }

  void _navigateToPendingInvitationDetail(PendingStaffInvitation invitation) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PendingInvitationDetailScreen(invitation: invitation),
      ),
    ).then((_) {
      // Refresh the list when returning from detail screen
      if (mounted) {
        _refreshStaff();
      }
    });
  }

  Widget _buildPendingInvitationCard(PendingStaffInvitation invitation) {
    return GestureDetector(
      onTap: () => _navigateToPendingInvitationDetail(invitation),
      child: Card(
        key: ValueKey(invitation.invitationId),
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Pending invitation avatar
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.7),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade300, width: 2),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.schedule,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Builder(
                              builder: (context) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      invitation.displayName,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.orange,
                                      ),
                                    ),
                                    if (invitation.nickname != null && invitation.nickname!.isNotEmpty) ...[
                                      const SizedBox(height: 2),
                                      Text(
                                        '(${invitation.formattedPhoneNumber})',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey.shade600,
                                          fontStyle: FontStyle.italic,
                                        ),
                                      ),
                                    ],
                                  ],
                                );
                              },
                            ),
                          ),
                          // Status badge
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.orange.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.orange.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Text(
                              invitation.status,
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      // Role badge
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: invitation.groomerRole.hasManagementAccess
                              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                              : Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: invitation.groomerRole.hasManagementAccess
                                ? Theme.of(context).primaryColor.withValues(alpha: 0.3)
                                : Colors.blue.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          invitation.groomerRole.localizedDisplayName(context),
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: invitation.groomerRole.hasManagementAccess
                                ? Theme.of(context).primaryColor
                                : Colors.blue,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      // Expiry info
                      Row(
                        children: [
                          Icon(
                            invitation.isValid ? Icons.access_time : Icons.error_outline,
                            size: 14,
                            color: invitation.isValid ? Colors.grey.shade600 : Colors.red,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            invitation.isValid
                                ? context.tr('team_management.invitation_card_expires', params: {'date': _formatDate(invitation.expiresAt)})
                                : context.tr('team_management.invitation_card_expired'),
                            style: TextStyle(
                              fontSize: 12,
                              color: invitation.isValid ? Colors.grey.shade600 : Colors.red,
                              fontWeight: invitation.isValid ? FontWeight.normal : FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'resend') {
                      _resendInvitation(invitation);
                    } else if (value == 'cancel') {
                      _showCancelInvitationConfirmation(invitation);
                    }
                  },
                  itemBuilder: (context) => [
                    if (invitation.isValid) ...[
                      PopupMenuItem(
                        value: 'resend',
                        child: Row(
                          children: [
                            Icon(Icons.send, color: Colors.blue),
                            SizedBox(width: 8),
                            Text(context.tr('team_management.invitation_menu_resend')),
                          ],
                        ),
                      ),
                    ],
                    PopupMenuItem(
                      value: 'cancel',
                      child: Row(
                        children: [
                          Icon(Icons.cancel, color: Colors.red),
                          SizedBox(width: 8),
                          Text(context.tr('team_management.invitation_menu_cancel')),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Invitation details
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.person, size: 16, color: Colors.orange),
                      const SizedBox(width: 8),
                      Text(
                        context.tr('team_management.invitation_card_invited_by', params: {'name': invitation.invitedBy}),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.calendar_today, size: 16, color: Colors.orange),
                      const SizedBox(width: 8),
                      Text(
                        context.tr('team_management.invitation_card_invited_at', params: {'date': _formatDate(invitation.invitedAt)}),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  /// Handle add staff with intelligent upgrade check
  Future<void> _handleAddStaffWithUpgradeCheck(SubscriptionProvider subscriptionProvider) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) return;

      // Get current subscription and limits
      final currentTier = subscriptionProvider.currentTier;
      final limits = await SubscriptionLimitService.getLimits(salonId);
      final currentStaffCount = limits.currentStaffCount;
      final maxStaff = limits.maxStaff;

      // Check if user can add more staff
      if (maxStaff == -1 || currentStaffCount < maxStaff) {
        // User can add staff, show dialog directly
        _showAddStaffDialog();
        return;
      }

      // User has reached limit, show intelligent upgrade prompt
      if (mounted) {
        _showIntelligentUpgradePrompt(currentTier, salonId);
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error checking staff limits: $e');
      // Fallback to showing dialog
      _showAddStaffDialog();
    }
  }

  /// Show intelligent upgrade prompt based on current tier
  void _showIntelligentUpgradePrompt(SubscriptionTier? currentTier, String salonId) {
    String title;
    String message;
    SubscriptionTier targetTier;

    switch (currentTier) {
      case SubscriptionTier.free:
        title = context.tr('team_management.upgrade_for_team_title');
        message = context.tr('team_management.upgrade_for_team_message');
        targetTier = SubscriptionTier.freelancer;
        break;
      case SubscriptionTier.freelancer:
        title = context.tr('team_management.extend_team_title');
        message = context.tr('team_management.extend_team_message');
        targetTier = SubscriptionTier.enterprise;
        break;
      case SubscriptionTier.team:
        title = context.tr('team_management.unlimited_team_title');
        message = context.tr('team_management.unlimited_team_message');
        targetTier = SubscriptionTier.enterprise;
        break;
      case SubscriptionTier.enterprise:
        // This shouldn't happen for Gold tier, but handle gracefully
        title = context.tr('team_management.limit_reached_title');
        message = context.tr('team_management.limit_reached_message');
        targetTier = SubscriptionTier.enterprise;
        break;
      case null:
        // No subscription
        title = context.tr('team_management.limit_reached_title');
        message = context.tr('team_management.limit_reached_message');
        targetTier = SubscriptionTier.team;
        break;
    }

    if (mounted) {
      // Show new RevenueCat paywall with tabs
      RevenueCatPaywallService.showPaywall(
        context: context,
        defaultTier: targetTier,
        salonId: salonId,
      );
    }
  }

  void _showAddStaffDialog() {
    showDialog(
      context: context,
      builder: (context) => AddStaffDialog(
        onSuccess: () {
          // Track staff addition for subscription limits
          _trackStaffAdded();

          // Force refresh with delay to ensure backend has processed the invitation
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              _refreshAllStaffData();
              // Also initialize working hours for new staff and refresh calendar working hours cache
              _initializeNewStaffWorkingHours();
              // Refresh calendar staff data to ensure new staff appears in calendar
              _refreshCalendarStaffData();
            }
          });
        },
      ),
    );
  }



  /// Track staff addition for subscription limits
  Future<void> _trackStaffAdded() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId != null) {
        await SubscriptionLimitService.trackStaffAdded(salonId);
      }
    } catch (e) {
      DebugLogger.logVerbose('⚠️ Failed to track staff addition: $e');
    }
  }






  Future<void> _resendInvitation(PendingStaffInvitation invitation) async {
    try {
      final response = await StaffService.resendInvitationInCurrentSalon(invitation.invitationId);

      if (response.success) {
        UINotificationService.showSuccess(
          context: context,
          title: 'Invitație retrimisă',
          message: 'Invitația pentru ${invitation.phoneNumber} a fost retrimisă cu succes',
        );
        if (mounted) {
          _refreshStaff();
        }
      } else {
        UINotificationService.showError(
          context: context,
          title: context.tr('team_management.invitation_resent_error_title'),
          message: response.error ?? context.tr('team_management.invitation_resent_error_message'),
        );
      }
    } catch (e) {
      UINotificationService.showError(
        context: context,
        title: context.tr('team_management.unexpected_error_title'),
        message: context.tr('team_management.unexpected_error_message'),
      );
    }
  }

  void _showCancelInvitationConfirmation(PendingStaffInvitation invitation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('team_management.invitation_cancel_title')),
        content: Text(context.tr('team_management.invitation_cancel_message', params: {'phone': invitation.phoneNumber})),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('team_management.invitation_cancel_no_button')),
          ),
          ElevatedButton(
            onPressed: () => _cancelInvitation(invitation),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(context.tr('team_management.invitation_cancel_yes_button')),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelInvitation(PendingStaffInvitation invitation) async {
    Navigator.pop(context); // Close dialog

    try {
      final response = await StaffService.cancelInvitationInCurrentSalon(invitation.invitationId);

      if (response.success) {
        UINotificationService.showSuccess(
          context: context,
          title: 'Invitație anulată',
          message: 'Invitația pentru ${invitation.phoneNumber} a fost anulată cu succes',
        );
        if (mounted) {
          _refreshStaff();
        }
      } else {
        UINotificationService.showError(
          context: context,
          title: context.tr('team_management.invitation_cancelled_error_title'),
          message: response.error ?? context.tr('team_management.invitation_cancelled_error_message'),
        );
      }
    } catch (e) {
      UINotificationService.showError(
        context: context,
        title: context.tr('team_management.unexpected_error_title'),
        message: context.tr('team_management.unexpected_error_message'),
      );
    }
  }

  String _formatDate(DateTime date) {
    final months = context.tr('team_management.date_format_months') as List<String>;
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }



  PendingStaffInvitation _convertSalonInvitation(SalonInvitation invitation) {
    return PendingStaffInvitation(
      invitationId: invitation.id,
      phoneNumber: invitation.invitedUserPhone,
      nickname: invitation.proposedNickname,
      groomerRole: invitation.proposedRole,
      clientDataPermission: invitation.proposedClientDataPermission,
      status: invitation.status.value,
      message: invitation.message,
      invitedBy: invitation.invitedByName,
      invitedAt: invitation.createdAt,
      expiresAt: invitation.expiresAt,
      isExpired: invitation.isExpired,
    );
  }

  /// Refresh calendar staff data when staff information is updated
  void _refreshCalendarStaffData() {
    try {
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      DebugLogger.logVerbose('🔄 TeamManagementScreen: Refreshing calendar staff data');
      calendarProvider.refreshStaffData();
    } catch (e) {
      DebugLogger.logVerbose('❌ TeamManagementScreen: Error refreshing calendar staff data: $e');
    }
  }

  /// Safely refresh calendar staff data (for use in dispose)
  void _refreshCalendarStaffDataSafely() {
    try {
      if (_calendarProvider != null) {
        DebugLogger.logVerbose('🔄 TeamManagementScreen: Safely refreshing calendar staff data');
        _calendarProvider!.refreshStaffData();
      } else {
        DebugLogger.logVerbose('⚠️ TeamManagementScreen: Calendar provider not available for refresh');
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ TeamManagementScreen: Error safely refreshing calendar staff data: $e');
    }
  }

  /// Initialize working hours for newly added staff and refresh calendar cache
  Future<void> _initializeNewStaffWorkingHours() async {
    try {
      DebugLogger.logVerbose('🔧 TeamManagementScreen: Initializing working hours for new staff');

      // Get the latest staff list to find newly added staff
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      await calendarProvider.loadStaff();

      // Get all staff members
      final allStaff = calendarProvider.availableStaff;
      DebugLogger.logVerbose('🔧 Found ${allStaff.length} staff members, checking for new staff needing working hours initialization');

      // Check which staff members need working hours initialization
      final staffNeedingInitialization = <String>[];

      for (final staffMember in allStaff) {
        try {
          // Check if staff already has working hours by trying to get them
          final existingHours = await StaffWorkingHoursService.getStaffWorkingHours(staffMember.id);
          if (!existingHours.success) {
            // Staff doesn't have working hours yet, needs initialization
            staffNeedingInitialization.add(staffMember.id);
            DebugLogger.logVerbose('🔧 Staff ${staffMember.displayName} needs working hours initialization');
          } else {
            DebugLogger.logVerbose('ℹ️ Staff ${staffMember.displayName} already has working hours');
          }
        } catch (e) {
          // If there's an error getting working hours, assume they need initialization
          staffNeedingInitialization.add(staffMember.id);
          DebugLogger.logVerbose('🔧 Staff ${staffMember.displayName} needs working hours initialization (error checking: $e)');
        }
      }

      DebugLogger.logVerbose('🔧 Found ${staffNeedingInitialization.length} staff members needing working hours initialization');

      // Initialize working hours for staff that need it and set to non-stop
      for (final staffId in staffNeedingInitialization) {
        final staffMember = allStaff.firstWhere((staff) => staff.id == staffId);
        try {
          DebugLogger.logVerbose('🔧 Creating working hours for new staff: ${staffMember.displayName} (${staffMember.id})');

          // Create working hours from business hours
          final response = await StaffWorkingHoursService.createStaffWorkingHoursFromBusiness(staffMember.id);

          if (response.success) {
            DebugLogger.logVerbose('✅ Working hours created for new staff: ${staffMember.displayName}');

            // Update staff schedule to non-stop (24/7) for new staff
            await _updateStaffToNonStopSchedule(staffMember.id, staffMember.displayName);
          } else {
            DebugLogger.logVerbose('⚠️ Failed to create working hours for new staff ${staffMember.displayName}: ${response.error}');
          }
        } catch (e) {
          DebugLogger.logVerbose('❌ Error initializing working hours for new staff ${staffMember.displayName}: $e');
        }
      }

      // Force refresh calendar provider's staff working hours cache
      DebugLogger.logVerbose('🔄 Forcing calendar provider to refresh staff working hours cache after new staff addition');
      await calendarProvider.refreshStaffWorkingHours(reason: 'After new staff addition');
      DebugLogger.logVerbose('✅ Calendar provider staff working hours cache refreshed');

    } catch (e) {
      DebugLogger.logVerbose('❌ Error during new staff working hours initialization: $e');
    }
  }

  /// Update staff schedule to non-stop (24/7) for new staff
  Future<void> _updateStaffToNonStopSchedule(String staffId, String staffDisplayName) async {
    try {
      DebugLogger.logVerbose('🔧 Updating staff schedule to non-stop for: $staffDisplayName ($staffId)');

      // Create non-stop schedule (24/7)
      final nonStopSchedule = {
        'monday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'tuesday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'wednesday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'thursday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'friday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'saturday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
      };

      // Create update request with non-stop schedule
      final request = UpdateStaffWorkingHoursRequest(
        weeklySchedule: nonStopSchedule,
        holidays: const [], // Empty holidays for new staff
        customClosures: const [], // Empty custom closures for new staff
      );

      DebugLogger.logVerbose('🔧 Sending non-stop schedule update for staff: $staffDisplayName');
      final response = await StaffWorkingHoursService.updateStaffWorkingHours(staffId, request);

      if (response.success) {
        DebugLogger.logVerbose('✅ Staff schedule updated to non-stop for: $staffDisplayName');
      } else {
        DebugLogger.logVerbose('⚠️ Failed to update staff schedule to non-stop for $staffDisplayName: ${response.error}');
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error updating staff schedule to non-stop for $staffDisplayName: $e');
    }
  }
}

