import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../models/salon.dart';
import '../../models/staff_working_hours_settings.dart';
import '../../models/working_hours_settings.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/role_provider.dart';
import '../../services/auth/auth_service.dart';
import '../../services/salon_service.dart';
import '../../services/staff_working_hours_service.dart';
import '../../services/ui_notification_service.dart';
import '../../utils/debug_logger.dart';
import '../../widgets/address_selection/location_selection_button.dart';
import '../../widgets/common/standard_form_field.dart';
import '../../widgets/subscription/subscription_guard.dart';
import '../../l10n/app_localizations.dart';

/// Screen for creating a new salon
class CreateSalonScreen extends StatefulWidget {
  const CreateSalonScreen({super.key});

  @override
  State<CreateSalonScreen> createState() => _CreateSalonScreenState();
}

class _CreateSalonScreenState extends State<CreateSalonScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _phoneController;
  late final TextEditingController _emailController;
  late final TextEditingController _ownerNameController;

  bool _isLoading = false;
  List<String> _validationErrors = [];

  // Location selection
  LatLng? _selectedLocation;
  String? _selectedAddress;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController();
    _descriptionController = TextEditingController();
    _phoneController = TextEditingController();
    _emailController = TextEditingController();
    _ownerNameController = TextEditingController();

    // Add a listener to the phone controller to filter out +40 followed by all zeros
    _phoneController.addListener(_filterPhoneNumber);

    // Add listeners to text controllers to update form validity
    _nameController.addListener(_onFormChanged);
    _ownerNameController.addListener(_onFormChanged);
    _phoneController.addListener(_onFormChanged);
  }

  void _onFormChanged() {
    setState(() {
      // This will trigger a rebuild to update button state
    });
  }
  
  @override
  void dispose() {
    _nameController.removeListener(_onFormChanged);
    _nameController.dispose();
    _descriptionController.dispose();
    _phoneController.removeListener(_filterPhoneNumber);
    _phoneController.removeListener(_onFormChanged);
    _phoneController.dispose();
    _emailController.dispose();
    _ownerNameController.removeListener(_onFormChanged);
    _ownerNameController.dispose();
    super.dispose();
  }
  
  // Filter out +40 followed by all zeros
  void _filterPhoneNumber() {
    final text = _phoneController.text;
    if (text.startsWith('+40')) {
      // Extract digits after +40
      final digitsAfterCode = text.substring(3).replaceAll(RegExp(r'\D'), '');
      
      // Check if all digits are zeros and there's at least one digit
      if (digitsAfterCode.isNotEmpty && digitsAfterCode.replaceAll('0', '').isEmpty) {
        // Remove the listener temporarily to avoid infinite loop
        _phoneController.removeListener(_filterPhoneNumber);
        _phoneController.clear();
        _phoneController.addListener(_filterPhoneNumber);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(
          context.tr('create_salon.title'),
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.white
          ),
        ),
        elevation: 0,
      ),
      body: FutureBuilder<String?>(
        future: AuthService.getCurrentSalonId(),
        builder: (context, snapshot) {
          final currentSalonId = snapshot.data ?? '';

          // If user already has a salon, check subscription for multi-salon creation
          if (currentSalonId.isNotEmpty) {
            return SubscriptionGuard(
              feature: 'salon_creation',
              salonId: currentSalonId,
              fallback: _buildUpgradePrompt(),
              child: _buildCreateSalonForm(),
            );
          }

          // First salon creation is always allowed
          return _buildCreateSalonForm();
        },
      ),
    );
  }

  Widget _buildCreateSalonForm() {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    _buildHeader(),
                    SizedBox(height: 32),

                    // Validation errors
                    if (_validationErrors.isNotEmpty) ...[
                      _buildErrorCard(),
                      SizedBox(height: 16),
                    ],

                    // Form fields
                    _buildFormFields(),

                    // Add some bottom padding to ensure content is not hidden behind button
                    SizedBox(height: 100),
                  ],
                ),
              ),
            ),
          ),

          // Keyboard-aware bottom section with create button
          _buildKeyboardAwareBottomSection(),
        ],
      );
  }

  Widget _buildUpgradePrompt() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.business,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 24),
          Text(
            context.tr('create_salon.multiple_salons_title'),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            context.tr('create_salon.multiple_salons_description'),
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () async {
              final salonId = await AuthService.getCurrentSalonId();
              if (salonId != null && mounted) {
                Navigator.of(context).pushNamed(
                  '/subscription-purchase',
                  arguments: {'salonId': salonId},
                );
              }
            },
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: const Text(
              context.tr('create_salon.upgrade_enterprise'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                Icons.add_business,
                color: Theme.of(context).colorScheme.onSurface,
                size: 32,
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr('create_salon.new_salon_title'),
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      context.tr('create_salon.new_salon_subtitle'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).colorScheme.error.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Theme.of(context).colorScheme.error,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                context.tr('create_salon.validation_errors'),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ..._validationErrors.map(
            (error) => Padding(
              padding: const EdgeInsets.only(left: 28, bottom: 4),
              child: Text(
                '• $error',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Salon name
        _buildTextField(
          controller: _nameController,
          label: context.tr('create_salon.salon_name_label'),
          hint: context.tr('create_salon.salon_name_hint'),
          icon: Icons.business,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return context.tr('create_salon.salon_name_required');
            }
            if (value.trim().length < 3) {
              return context.tr('create_salon.salon_name_min_length');
            }
            return null;
          },
        ),
        SizedBox(height: 16),

        // Owner name
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTextField(
              controller: _ownerNameController,
              label: context.tr('create_salon.owner_name_label'),
              hint: context.tr('create_salon.owner_name_hint'),
              icon: Icons.person,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return context.tr('create_salon.owner_name_required');
                }
                if (value.trim().length < 2) {
                  return context.tr('create_salon.owner_name_min_length');
                }
                return null;
              },
            ),
            SizedBox(height: 4),
            Padding(
              padding: EdgeInsets.only(left: 12),
              child: Text(
                context.tr('create_salon.owner_name_helper'),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 16),

        // Description
        _buildTextField(
          controller: _descriptionController,
          label: context.tr('create_salon.description_label'),
          hint: context.tr('create_salon.description_hint'),
          icon: Icons.description,
          maxLines: 3,
        ),
        SizedBox(height: 16),

        // Location selection
        LocationSelectionButton(
          selectedAddress: _selectedAddress,
          selectedLocation: _selectedLocation,
          label: context.tr('create_salon.location_label'),
          hint: context.tr('create_salon.location_hint'),
          isRequired: true,
          showReminderDisclaimer: true,
          onLocationSelected: (location, address) {
            setState(() {
              _selectedLocation = location;
              _selectedAddress = address;
            });
          },
          validator: (value) {
            if (_selectedLocation == null || _selectedAddress == null || _selectedAddress!.trim().isEmpty) {
              return context.tr('create_salon.location_required');
            }
            return null;
          },
        ),
        SizedBox(height: 16),

        // Phone
        StandardFormField(
          controller: _phoneController,
          labelText: context.tr('create_salon.phone_label'),
          hintText: context.tr('create_salon.phone_hint'),
          prefixIcon: Icons.phone,
          keyboardType: TextInputType.phone,
          type: StandardFormFieldType.phone,
          isRequired: false,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9+\s]')),
            TextInputFormatter.withFunction((oldValue, newValue) {
              // If the new value is +40 followed by all zeros, reject it
              if (newValue.text.startsWith('+40')) {
                final digitsOnly = newValue.text.substring(3).replaceAll(RegExp(r'\D'), '');
                if (digitsOnly.isNotEmpty && digitsOnly.replaceAll('0', '').isEmpty) {
                  return oldValue;
                }
              }
              return newValue;
            }),
          ],
        ),
        SizedBox(height: 16),

        // Email
        _buildTextField(
          controller: _emailController,
          label: context.tr('create_salon.email_label'),
          hint: context.tr('create_salon.email_hint'),
          icon: Icons.email,
          keyboardType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    return StandardFormField(
      controller: controller,
      labelText: label,
      hintText: hint,
      prefixIcon: icon,
      validator: validator,
      keyboardType: keyboardType,
      maxLines: maxLines,
    );
  }

  Widget _buildKeyboardAwareBottomSection() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isFormValid = _isFormValid();

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        top: 24,
        bottom: keyboardHeight > 0 ? keyboardHeight + 24 : 24,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: (_isLoading || !isFormValid) ? null : _createSalon,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              backgroundColor: isFormValid
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.12),
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.onPrimary),
                    ),
                  )
                : Text(
                    context.tr('create_salon.create_button'),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isFormValid
                        ? Theme.of(context).colorScheme.onPrimary
                        : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.38),
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  bool _isFormValid() {
    // Check if all required fields are filled
    final nameValid = _nameController.text.trim().isNotEmpty && _nameController.text.trim().length >= 3;
    final ownerNameValid = _ownerNameController.text.trim().isNotEmpty && _ownerNameController.text.trim().length >= 2;
    final phoneValid = _phoneController.text.trim().isNotEmpty;
    final locationValid = _selectedLocation != null && _selectedAddress != null && _selectedAddress!.trim().isNotEmpty;

    return nameValid && ownerNameValid && phoneValid && locationValid;
  }

  Future<void> _createSalon() async {
    // Clear previous errors
    setState(() => _validationErrors = []);

    // Validate salon name
    if (_nameController.text.trim().isEmpty) {
      UINotificationService.showWarning(
        context: context,
        title: context.tr('create_salon.name_missing_title'),
        message: context.tr('create_salon.name_missing_message'),
        actionLabel: context.tr('create_salon.name_missing_action'),
      );
      return;
    }

    // Validate owner name
    if (_ownerNameController.text.trim().isEmpty) {
      UINotificationService.showWarning(
        context: context,
        title: context.tr('create_salon.owner_name_missing_title'),
        message: context.tr('create_salon.owner_name_missing_message'),
        actionLabel: context.tr('create_salon.owner_name_missing_action'),
      );
      return;
    }

    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate location selection
    if (_selectedLocation == null || _selectedAddress == null || _selectedAddress!.trim().isEmpty) {
      UINotificationService.showWarning(
        context: context,
        title: context.tr('create_salon.location_missing_title'),
        message: context.tr('create_salon.location_missing_message'),
        actionLabel: context.tr('create_salon.location_missing_action'),
      );
      return;
    }

    // Process phone number - reject if it's +40 followed by all zeros
    String? phoneValue;
    if (_phoneController.text.trim().isNotEmpty) {
      final phone = _phoneController.text.trim();
      if (phone.startsWith('+40')) {
        final digitsAfterCode = phone.substring(3).replaceAll(RegExp(r'\D'), '');
        if (digitsAfterCode.isEmpty || digitsAfterCode.replaceAll('0', '').isNotEmpty) {
          phoneValue = phone; // Use valid phone number
        }
      } else {
        phoneValue = phone;
      }
    }

    // Create request
    final request = CreateSalonRequest(
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      address: _selectedAddress!,
      phone: phoneValue,
      email: _emailController.text.trim().isEmpty
          ? null
          : _emailController.text.trim(),
      ownerName: _ownerNameController.text.trim().isEmpty
          ? null
          : _ownerNameController.text.trim(),
    );

    // Additional validation
    final validationErrors = request.validate();
    if (validationErrors.isNotEmpty) {
      setState(() => _validationErrors = validationErrors);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final response = await SalonService.createSalon(request);

      if (response.success) {
        if (mounted) {
          // Refresh role provider and reload staff data for the new salon
          final roleProvider = context.read<RoleProvider>();
          final calendarProvider = context.read<CalendarProvider>();

          DebugLogger.logVerbose('🏢 Refreshing role provider after salon creation...');
          await roleProvider.refresh();

          // Add delay to ensure backend has processed the staff creation
          DebugLogger.logVerbose('🏢 Waiting for backend to process staff creation...');
          await Future.delayed(const Duration(seconds: 2));

          // Reload staff data for the new salon
          if (mounted) {
            DebugLogger.logVerbose('🏢 Reloading staff data for new salon...');
            await _retryStaffLoading(calendarProvider);
            await calendarProvider.loadServices();

            // Ensure all groomers are selected after salon creation
            calendarProvider.selectAllStaff();

            // Update staff schedules to non-stop for new salon
            await _updateStaffSchedulesToNonStop(calendarProvider);

            // Force refresh calendar provider's staff working hours cache
            DebugLogger.logVerbose('🔄 Forcing calendar provider to refresh staff working hours cache after non-stop schedule update');
            await calendarProvider.refreshStaffWorkingHours(reason: 'After salon creation non-stop schedule update');
            DebugLogger.logVerbose('✅ Calendar provider staff working hours cache refreshed');

            DebugLogger.logVerbose('🏢 Staff data reload completed');
          }

          // Navigate back and let the parent handle the success message
          if (mounted) {
            Navigator.of(context).pop(true);
          }
        }
      } else {
        if (mounted) {
          UINotificationService.showError(
            context: context,
            title: context.tr('create_salon.creation_error_title'),
            message: response.error ?? context.tr('create_salon.creation_error_message'),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('create_salon.unexpected_error_title'),
          message: context.tr('create_salon.unexpected_error_message'),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Retry staff loading with multiple attempts
  Future<void> _retryStaffLoading(CalendarProvider calendarProvider) async {
    const maxAttempts = 3;
    const delayBetweenAttempts = Duration(seconds: 1);

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      DebugLogger.logVerbose('🔄 Staff loading attempt $attempt/$maxAttempts');

      await calendarProvider.loadStaff();

      // Check if staff was loaded successfully
      if (calendarProvider.availableStaff.isNotEmpty) {
        DebugLogger.logVerbose('✅ Staff loaded successfully on attempt $attempt');
        return;
      }

      if (attempt < maxAttempts) {
        DebugLogger.logVerbose('⏳ No staff found, waiting before retry...');
        await Future.delayed(delayBetweenAttempts);
      }
    }

    DebugLogger.logVerbose('❌ Failed to load staff after $maxAttempts attempts');
  }

  /// Update staff schedules to non-stop (24/7) for new salon
  Future<void> _updateStaffSchedulesToNonStop(CalendarProvider calendarProvider) async {
    try {
      DebugLogger.logVerbose('🔧 Updating staff schedules to non-stop for new salon');

      // Create non-stop schedule (24/7)
      final nonStopSchedule = {
        'monday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'tuesday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'wednesday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'thursday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'friday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'saturday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
      };

      // Update schedule for each staff member
      for (final staffMember in calendarProvider.availableStaff) {
        try {
          DebugLogger.logVerbose('🔧 Updating schedule to non-stop for staff: ${staffMember.displayName} (${staffMember.id})');

          // Create update request with non-stop schedule
          final request = UpdateStaffWorkingHoursRequest(
            weeklySchedule: nonStopSchedule,
            holidays: const [], // Empty holidays for new salon
            customClosures: const [], // Empty custom closures for new salon
          );

          final response = await StaffWorkingHoursService.updateStaffWorkingHours(staffMember.id, request);

          if (response.success) {
            DebugLogger.logVerbose('✅ Staff schedule updated to non-stop for: ${staffMember.displayName}');
          } else {
            DebugLogger.logVerbose('⚠️ Failed to update staff schedule to non-stop for ${staffMember.displayName}: ${response.error}');
          }
        } catch (e) {
          DebugLogger.logVerbose('❌ Error updating staff schedule to non-stop for ${staffMember.displayName}: $e');
        }
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error updating staff schedules to non-stop: $e');
    }
  }
}
