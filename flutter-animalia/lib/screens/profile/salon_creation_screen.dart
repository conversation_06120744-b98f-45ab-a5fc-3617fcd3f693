import 'dart:convert';

import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:provider/provider.dart';

import '../../config/environment.dart';
import '../../l10n/app_localizations.dart';
import '../../config/theme/app_theme.dart';
import '../../models/salon.dart';
import '../../models/staff_working_hours_settings.dart';
import '../../models/working_hours_settings.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/role_provider.dart';
import '../../services/api_service.dart';
import '../../services/auth/auth_service.dart';
import '../../services/salon_service.dart';
import '../../services/staff_working_hours_service.dart';
import '../../services/ui_notification_service.dart';
import '../../utils/formatters/phone_number_utils.dart';
import '../../widgets/address_selection/location_selection_button.dart';
import '../../widgets/referral/referral_code_claim_section.dart';
import '../main_layout.dart';

class SalonCreationScreen extends StatefulWidget {
  final Salon? salon;
  const SalonCreationScreen({super.key, this.salon});

  bool get isEdit => salon != null;

  @override
  State<SalonCreationScreen> createState() => _SalonCreationScreenState();
}

class _SalonCreationScreenState extends State<SalonCreationScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _addressFocusNode = FocusNode();
  final _addressDetailsController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _ownerNameController = TextEditingController();

  // Store referral code from the claim section
  String? _referralCode;

  // Animation controllers
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  LatLng _selectedLocation = const LatLng(
      44.4268, 26.1025); // Default Bucharest (fallback)
  bool _isLoadingLocation = false;
  bool _isLoading = false;

  // Complete phone number with country code
  String _completePhoneNumber = '';

  // Initial country code extracted from phone number (for edit mode)
  String _initialCountryCode = 'RO'; // Default to Romania

  @override
  void initState() {
    super.initState();
    if (widget.isEdit && widget.salon != null) {
      final salon = widget.salon!;
      _nameController.text = salon.name;

      // Extract country code from phone number if editing
      if (salon.phone != null && salon.phone!.isNotEmpty) {
        _phoneController.text = PhoneNumberUtils.formatForDisplay(salon.phone!);
        _completePhoneNumber = salon.phone!;
        _initialCountryCode = _extractCountryCode(salon.phone!);
      }

      _emailController.text = salon.email ?? '';

      // Parse address to separate main address from details
      _parseAndPopulateAddress(salon.address);
      // Load coordinates for existing address
      _loadLocationFromAddress(salon.address);

      _descriptionController.text = salon.description ?? '';
    } else {
      // For new salon creation, try to get user's current location
      _initializeUserLocation();
    }
    _setupAnimations();
    _loadUserPhoneNumber();
    _startAnimations();
  }

  /// Extract country code from international phone number
  /// Returns ISO country code (e.g., 'RO', 'MD', 'US', 'GB')
  String _extractCountryCode(String phoneNumber) {
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Map of country dial codes to ISO codes
    final countryCodeMap = {
      '+1': 'US',      // USA/Canada
      '+7': 'RU',      // Russia
      '+33': 'FR',     // France
      '+34': 'ES',     // Spain
      '+39': 'IT',     // Italy
      '+40': 'RO',     // Romania
      '+41': 'CH',     // Switzerland
      '+43': 'AT',     // Austria
      '+44': 'GB',     // United Kingdom
      '+45': 'DK',     // Denmark
      '+46': 'SE',     // Sweden
      '+47': 'NO',     // Norway
      '+48': 'PL',     // Poland
      '+49': 'DE',     // Germany
      '+90': 'TR',     // Turkey
      '+91': 'IN',     // India
      '+212': 'MA',    // Morocco
      '+213': 'DZ',    // Algeria
      '+351': 'PT',    // Portugal
      '+352': 'LU',    // Luxembourg
      '+353': 'IE',    // Ireland
      '+354': 'IS',    // Iceland
      '+355': 'AL',    // Albania
      '+356': 'MT',    // Malta
      '+357': 'CY',    // Cyprus
      '+358': 'FI',    // Finland
      '+359': 'BG',    // Bulgaria
      '+370': 'LT',    // Lithuania
      '+371': 'LV',    // Latvia
      '+372': 'EE',    // Estonia
      '+373': 'MD',    // Moldova
      '+374': 'AM',    // Armenia
      '+375': 'BY',    // Belarus
      '+376': 'AD',    // Andorra
      '+377': 'MC',    // Monaco
      '+378': 'SM',    // San Marino
      '+380': 'UA',    // Ukraine
      '+381': 'RS',    // Serbia
      '+382': 'ME',    // Montenegro
      '+383': 'XK',    // Kosovo
      '+385': 'HR',    // Croatia
      '+386': 'SI',    // Slovenia
      '+387': 'BA',    // Bosnia and Herzegovina
      '+389': 'MK',    // North Macedonia
    };

    // Try to match country codes from longest to shortest
    final sortedCodes = countryCodeMap.keys.toList()
      ..sort((a, b) => b.length.compareTo(a.length));

    for (final code in sortedCodes) {
      if (cleanPhone.startsWith(code)) {
        return countryCodeMap[code]!;
      }
    }

    // Default to Romania if no match found
    return 'RO';
  }

  /// Parse the combined address and populate address and address details fields
  void _parseAndPopulateAddress(String fullAddress) {
    final parts = fullAddress.split(',');
    switch (parts.length) {
      case 1:
        _addressController.text = parts[0];
        return;
      case 2:
        _addressController.text = parts[0] + parts[1];
        return;
      case 3:
        _addressController.text = parts[0] + parts[1] + parts[2];
        return;
      case 4:
        _addressController.text = parts[0] + parts[1] + parts[2];
        _addressController.text += parts[3];
    }
  }

  void _setupAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.elasticOut,
    ));

    // Add listeners to ensure values stay within bounds
    _fadeAnimationController.addListener(() {
      if (_fadeAnimationController.value < 0.0) {
        _fadeAnimationController.value = 0.0;
      } else if (_fadeAnimationController.value > 1.0) {
        _fadeAnimationController.value = 1.0;
      }
    });

    _slideAnimationController.addListener(() {
      if (_slideAnimationController.value < 0.0) {
        _slideAnimationController.value = 0.0;
      } else if (_slideAnimationController.value > 1.0) {
        _slideAnimationController.value = 1.0;
      }
    });
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _fadeAnimationController.forward();
        _slideAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    
    // Dispose controllers
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _addressDetailsController.dispose();
    _descriptionController.dispose();
    _ownerNameController.dispose();
    
    _addressFocusNode.dispose();

    super.dispose();
  }

  /// Load and pre-fill user's phone number for salon
  Future<void> _loadUserPhoneNumber() async {
    try {
      if (_phoneController.text.isNotEmpty) return;
      final userPhone = await AuthService.getCurrentUserPhone();
      if (userPhone != null &&
          userPhone.isNotEmpty &&
          userPhone.replaceAll(' ', '') != '+4000000000' &&
          mounted) {
        setState(() {
          _phoneController.text =
              PhoneNumberUtils.formatForDisplay(userPhone);
          _completePhoneNumber = userPhone; // Update complete phone number
        });
      }
    } catch (e) {
      // If we can't get user phone, just leave the field empty
      DebugLogger.logVerbose('Could not load user phone number: $e');
    }
  }

  /// Initialize user's current location for new salon creation
  Future<void> _initializeUserLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        // Permission denied, keep Bucharest default
        DebugLogger.logVerbose('📍 Location permission denied, using Bucharest default');
        return;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      if (mounted) {
        setState(() {
          _selectedLocation = LatLng(position.latitude, position.longitude);
        });
        DebugLogger.logVerbose('📍 Initial location set to user position: ${position.latitude}, ${position.longitude}');
      }
    } catch (e) {
      // Error getting location, keep Bucharest default
      DebugLogger.logVerbose('📍 Error getting user location: $e, using Bucharest default');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
      }
    }
  }

  /// Geocode an address to obtain coordinates when editing a salon
  Future<void> _loadLocationFromAddress(String address) async {
    final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/geocode/json?address=${Uri.encodeComponent(address)}&key=${EnvironmentConfig.googleMapsApiKey}');
    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final results = data['results'] as List<dynamic>?;
        if (results != null && results.isNotEmpty) {
          final loc = results.first['geometry']['location'];
          final lat = loc['lat'] as num?;
          final lng = loc['lng'] as num?;
          if (lat != null && lng != null && mounted) {
            setState(() {
              _selectedLocation = LatLng(lat.toDouble(), lng.toDouble());
            });
          }
        }
      }
    } catch (e) {
      DebugLogger.logVerbose('📍 Error geocoding address: $e');
    }
  }



  Future<void> _submitSalon() async {
    // Validate salon name
    if (_nameController.text.trim().isEmpty) {
      UINotificationService.showWarning(
        context: context,
        title: context.tr('create_salon.name_missing_title'),
        message: context.tr('create_salon.name_missing_message'),
        actionLabel: context.tr('create_salon.name_missing_action'),
      );
      return;
    }

    if (!_formKey.currentState!.validate()) return;

    // Validate location selection
    if (_selectedLocation == null || _addressController.text.trim().isEmpty) {
      UINotificationService.showWarning(
        context: context,
        title: context.tr('create_salon.location_missing_title'),
        message: context.tr('create_salon.location_missing_message'),
        actionLabel: context.tr('create_salon.location_missing_action'),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Combine address and address details
      String fullAddress = _addressController.text.trim();
      if (_addressDetailsController.text.trim().isNotEmpty) {
        fullAddress += ', ${_addressDetailsController.text.trim()}';
      }

      final request = CreateSalonRequest(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        address: fullAddress,
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        ownerName: !widget.isEdit && _ownerNameController.text.trim().isNotEmpty
            ? _ownerNameController.text.trim()
            : null,
      );

      final response = widget.isEdit
          ? await SalonService.updateSalon(widget.salon!.id, request)
          : await SalonService.createSalon(request);

      if (response.success && response.data != null) {
        final salonId = widget.isEdit
            ? (response.data as Salon).id
            : (response.data as CreateSalonResponse).salon.id;

        // Claim referral code if one was entered (only for new salon creation)
        if (!widget.isEdit) {
          if (_referralCode != null && _referralCode!.isNotEmpty) {
            try {
              DebugLogger.logVerbose('🎁 Attempting to claim referral code: $_referralCode for salon: $salonId');
              final claimResponse = await ApiService.post<Map<String, dynamic>>(
                '/api/referral/$salonId/claim',
                body: {'code': _referralCode},
              );

              if (claimResponse.success && claimResponse.data != null) {
                final success = claimResponse.data!['success'] as bool? ?? false;
                if (success) {
                  final smsCreditsAwarded = claimResponse.data!['smsCreditsAwarded'] as int? ?? 100;
                  DebugLogger.logVerbose('✅ Referral code claimed successfully! SMS credits awarded: $smsCreditsAwarded');

                  if (mounted) {
                    UINotificationService.showSuccess(
                      context: context,
                      title: 'Cod referal revendicat!',
                      message: 'Ai primit $smsCreditsAwarded credite SMS bonus!',
                      duration: const Duration(seconds: 4),
                    );
                  }
                } else {
                  DebugLogger.logVerbose('❌ Failed to claim referral code: ${claimResponse.data!['message']}');
                }
              } else {
                DebugLogger.logVerbose('❌ Referral code claim request failed: ${claimResponse.error}');
              }
            } catch (e) {
              DebugLogger.logVerbose('❌ Exception while claiming referral code: $e');
            }
          }
        }

        // Refresh role provider to update salon association
        if (mounted) {
          final roleProvider = context.read<RoleProvider>();
          final calendarProvider = context.read<CalendarProvider>();

          await roleProvider.refresh();

          // Add delay to ensure backend has processed the staff creation
          await Future.delayed(const Duration(seconds: 2));

          // Reload staff data for the new salon
          if (mounted) {
            // DebugLogger.logVerbose(message)('🏢 Reloading staff data for new salon...');
            await _retryStaffLoading(calendarProvider);
            await calendarProvider.loadServices();

            // Ensure all groomers are selected after salon creation
            calendarProvider.selectAllStaff();

            // Update staff schedules to non-stop for new salon
            await _updateStaffSchedulesToNonStop(calendarProvider);

            // Force refresh calendar provider's staff working hours cache
            DebugLogger.logVerbose('🔄 Forcing calendar provider to refresh staff working hours cache after non-stop schedule update');
            await calendarProvider.refreshStaffWorkingHours(reason: 'After salon creation non-stop schedule update');
            DebugLogger.logVerbose('✅ Calendar provider staff working hours cache refreshed');

            DebugLogger.logVerbose('🏢 Staff data reload and initialization completed');
          }

          // Navigate back and start tour only for first salon
          if (mounted) {
            // Check if this is the first salon creation
            if (!widget.isEdit) {
              // Get user's salon count to determine if this is the first salon
              final userSalonsResponse = await SalonService.getUserSalons();
              final isFirstSalon = userSalonsResponse.success && 
                                   (userSalonsResponse.data?.length ?? 0) <= 1;
              
              if (isFirstSalon) {
                DebugLogger.logVerbose('🎯 First salon created - triggering tour...');
                Navigator.of(context).pop(true);
                
                // Start tour after navigation completes
                WidgetsBinding.instance.addPostFrameCallback((_) async {
                  await Future.delayed(const Duration(milliseconds: 1000));
                  final mainLayoutState = MainLayout.globalKey.currentState;
                  if (mainLayoutState != null) {
                    mainLayoutState.startNewSalonTour();
                  }
                });
              } else {
                DebugLogger.logVerbose('🎯 Additional salon created - no tour needed');
                Navigator.of(context).pop(true);
              }
            } else {
              Navigator.of(context).pop(true); // Return success for edit
            }
          }
        }
      } else {
        DebugLogger.logVerbose('❌ Salon submit failed: ${response.error}');
        throw Exception(response.error ?? 'Failed to save salon');
      }
    } catch (e) {
      if (mounted) {
        DebugLogger.logVerbose('❌ Error saving salon: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.isEdit ? context.tr('create_salon.edit_title') : context.tr('create_salon.title'),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
      ),
      body: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Card with enhanced animation
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1200),
                        tween: Tween(begin: 0.0, end: 1.0),
                        curve: Curves.elasticOut,
                        builder: (context, value, child) {
                          return SizedBox(
                            width: double.infinity,
                            child: Transform.scale(
                              scale: 0.8 + (0.2 * value),
                              child: Opacity(
                                opacity: value.clamp(0.0, 1.0),
                                // child: _buildHeaderCard(),
                              ),
                            ),
                          );
                        },
                      ),
                      SizedBox(height: 24),

                      // Form Fields with staggered animation
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1000),
                        tween: Tween(begin: 0.0, end: 1.0),
                        curve: Curves.easeOutCubic,
                        builder: (context, value, child) {
                          return SizedBox(
                            width: double.infinity,
                            child: Transform.translate(
                              offset: Offset(0, 30 * (1 - value)),
                              child: Opacity(
                                opacity: value.clamp(0.0, 1.0),
                                child: _buildFormSection(),
                              ),
                            ),
                          );
                        },
                      ),
                      SizedBox(height: 32),

                      // Create Button with bounce animation
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1400),
                        tween: Tween(begin: 0.0, end: 1.0),
                        curve: Curves.elasticOut,
                        builder: (context, value, child) {
                          return SizedBox(
                            width: double.infinity,
                            child: Transform.scale(
                              scale: value.clamp(0.0, 1.0),
                              child: _buildCreateButton(),
                            ),
                          );
                        },
                      ),
                      SizedBox(height: 16),

                      // Info Card with fade animation
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1600),
                        tween: Tween(begin: 0.0, end: 1.0),
                        curve: Curves.easeOut,
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value.clamp(0.0, 1.0),
                            child: _buildInfoCard(),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }


  Widget _buildCreateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _submitSalon,
        style: ElevatedButton.styleFrom(

          
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: _isLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.onPrimary),
                ),
              )
            : Text(
                widget.isEdit ? context.tr('create_salon.save_button') : context.tr('create_salon.create_button'),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 1,
      color: Theme.of(context).colorScheme.surfaceVariant,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Theme.of(context).colorScheme.onSurface,
              size: 24,
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr('create_salon.important_info_title'),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    context.tr('create_salon.important_info_description'),
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('create_salon.salon_info_title'),
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 16),

        // Salon Name
        _buildTextField(
          controller: _nameController,
          label: context.tr('create_salon.salon_name_label'),
          hint: context.tr('create_salon.salon_name_hint'),
          icon: Icons.pets,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return context.tr('create_salon.salon_name_required');
            }
            if (value.trim().length < 3) {
              return context.tr('create_salon.salon_name_min_length');
            }
            return null;
          }, inputFormatters: [],
        ),
        SizedBox(height: 16),

        // Owner name (only show for new salon creation, not editing)
        if (!widget.isEdit) ...[
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTextField(
                controller: _ownerNameController,
                label: context.tr('create_salon.owner_name_label'),
                hint: context.tr('create_salon.owner_name_hint'),
                icon: Icons.person,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return context.tr('create_salon.owner_name_required');
                  }
                  if (value.trim().length < 2) {
                    return context.tr('create_salon.owner_name_min_length');
                  }
                  return null;
                }, inputFormatters: [],
              ),
              SizedBox(height: 4),
              Padding(
                padding: EdgeInsets.only(left: 12),
                child: Text(
                  context.tr('create_salon.owner_name_helper'),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
        ],

        // Phone - International phone field with country code dropdown
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0),
          child: IntlPhoneField(
            decoration: InputDecoration(
              labelText: context.tr('create_salon.phone_label'),
              hintText: context.tr('create_salon.phone_hint'),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              prefixIcon: const Icon(Icons.phone),
              contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            ),
            initialCountryCode: _initialCountryCode,
            dropdownIconPosition: IconPosition.trailing,
            flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8.0),
            dropdownTextStyle: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
            ),
            showDropdownIcon: true,
            disableLengthCheck: false,
            keyboardType: TextInputType.phone,
            onChanged: (phone) {
              setState(() {
                _completePhoneNumber = phone.completeNumber;
                _phoneController.text = phone.completeNumber;
              });
            },
            validator: (value) {
              if (value == null || value.number.isEmpty) {
                // TODO TRANSLATE
                return 'Numărul de telefon este obligatoriu';
              }
              // Basic validation - accept numbers with at least 6 digits
              if (value.number.length < 6) {
                return 'Numărul de telefon nu este valid';
              }
              return null;
            },
            initialValue: _phoneController.text.isNotEmpty
                ? _phoneController.text.replaceAll(RegExp(r'[^\d]'), '')
                : null,
          ),
        ),
        // Phone number explanation
        Padding(
          padding: const EdgeInsets.only(left: 12, top: 4),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              SizedBox(width: 4),
              Expanded(
                child: Text(
                  context.tr('create_salon.phone_helper_text'),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16),

        // Email todo add after v2
        // _buildTextField(
        //   controller: _emailController,
        //   label: 'Email',
        //   hint: '<EMAIL>',
        //   icon: Icons.email,
        //   keyboardType: TextInputType.emailAddress,
        //   validator: (value) {
        //     if (value != null && value.trim().isNotEmpty) {
        //       if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
        //         return 'Adresa de email nu este validă';
        //       }
        //     }
        //     return null;
        //   },
        // ),
        // SizedBox(height: 16),
        //
        // Simplified location selection
        LocationSelectionButton(
          selectedAddress: _addressController.text.isNotEmpty ? _addressController.text : null,
          selectedLocation: _selectedLocation,
          label: context.tr('create_salon.location_label'),
          hint: _isLoadingLocation ? context.tr('create_salon.location_selecting') : context.tr('create_salon.location_hint'),
          isRequired: true,
          showReminderDisclaimer: true,
          onLocationSelected: (location, address) {
            setState(() {
              _selectedLocation = location;
              if (address != null && address.isNotEmpty) {
                _addressController.text = address;
              }
            });
          },
        ),
        SizedBox(height: 16),

        // Address Details
        _buildTextField(
          controller: _addressDetailsController,
          label: context.tr('create_salon.address_details_label'),
          hint: context.tr('create_salon.address_details_hint'),
          icon: Icons.info_outline,
          maxLines: 2,
          validator: null, inputFormatters: [],
        ),
        // Address details explanation
        Padding(
          padding: const EdgeInsets.only(left: 12, top: 4),
          child: Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              SizedBox(width: 4),
              Expanded(
                child: Text(
                  context.tr('create_salon.address_details_helper'),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16),

        // Referral Code Claim Section (only for new salon creation)
        if (!widget.isEdit) ...[
          ReferralCodeClaimSection(
            enabled: !_isLoading,
            isCreatingSalon: true,
            onReferralCodeChanged: (code) {
              _referralCode = code;
            },
          ),
          SizedBox(height: 16),
        ],

        // Description
        // _buildTextField(
        //   controller: _descriptionController,
        //   label: 'Descriere (opțional)',
        //   hint: 'Scurtă descriere a salonului...',
        //   icon: Icons.description,
        //   maxLines: 3, inputFormatters: [],
        // ),
      ],
    );
  }



  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
    required List<TextInputFormatter> inputFormatters,
  }) {
    return StandardFormField(
      controller: controller,
      labelText: label,
      hintText: hint,
      prefixIcon: icon,
      keyboardType: keyboardType,
      maxLines: maxLines,
      validator: validator,
      inputFormatters: inputFormatters,
    );
  }


  
  /// Retry staff loading with multiple attempts
  Future<void> _retryStaffLoading(CalendarProvider calendarProvider) async {
    const maxAttempts = 3;
    const delayBetweenAttempts = Duration(seconds: 1);

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      DebugLogger.logVerbose('🔄 Staff loading attempt $attempt/$maxAttempts');

      await calendarProvider.loadStaff();

      // Check if staff was loaded successfully
      if (calendarProvider.availableStaff.isNotEmpty) {
        DebugLogger.logVerbose('✅ Staff loaded successfully on attempt $attempt');
        return;
      }

      if (attempt < maxAttempts) {
        DebugLogger.logVerbose('⏳ No staff found, waiting before retry...');
        await Future.delayed(delayBetweenAttempts);
      }
    }

    DebugLogger.logVerbose('❌ Failed to load staff after $maxAttempts attempts');
  }

  /// Update staff schedules to non-stop (24/7) for new salon
  Future<void> _updateStaffSchedulesToNonStop(CalendarProvider calendarProvider) async {
    try {
      DebugLogger.logVerbose('🔧 Updating staff schedules to non-stop for new salon');

      // Create non-stop schedule (24/7)
      final nonStopSchedule = {
        'monday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'tuesday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'wednesday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'thursday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'friday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'saturday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
      };

      // Update schedule for each staff member
      for (final staffMember in calendarProvider.availableStaff) {
        try {
          DebugLogger.logVerbose('🔧 Updating schedule to non-stop for staff: ${staffMember.displayName} (${staffMember.id})');

          // Create update request with non-stop schedule
          final request = UpdateStaffWorkingHoursRequest(
            weeklySchedule: nonStopSchedule,
            holidays: const [], // Empty holidays for new salon
            customClosures: const [], // Empty custom closures for new salon
          );

          final response = await StaffWorkingHoursService.updateStaffWorkingHours(staffMember.id, request);

          if (response.success) {
            DebugLogger.logVerbose('✅ Staff schedule updated to non-stop for: ${staffMember.displayName}');
          } else {
            DebugLogger.logVerbose('⚠️ Failed to update staff schedule to non-stop for ${staffMember.displayName}: ${response.error}');
          }
        } catch (e) {
          DebugLogger.logVerbose('❌ Error updating staff schedule to non-stop for ${staffMember.displayName}: $e');
        }
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ Error updating staff schedules to non-stop: $e');
    }
  }
}
