import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../config/theme/app_theme.dart';
import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../services/analytics_insights_service.dart';
import '../../services/appointment/appointment_service.dart';
import '../../services/auth/auth_service.dart';
import '../../services/feature_toggle_service.dart';
import '../../services/screen_time_service.dart';
import '../../services/tour_keys.dart';
import '../../services/ui_notification_service.dart';
import '../../utils/genie_animation.dart';
import '../../widgets/analytics/comprehensive_analytics_mixin.dart';
import '../../widgets/calendar_views/day_view.dart';
import '../../widgets/calendar_views/google_calendar_view.dart';
import '../../widgets/calendar_views/week_view.dart';
import '../../widgets/dialogs/appointment_details_dialog.dart';
import '../../widgets/dialogs/block_time_dialog.dart';
import '../../widgets/drawers/calendar_settings_drawer.dart';
import '../calendar/calendar_list_screen.dart';
import 'new_appointment_screen.dart';
import '../../l10n/app_localizations.dart';

class CalendarScreen extends StatefulWidget {
  final DateTime? initialDate;
  final String? focusedAppointmentId;

  /// Global key to access state from outside the widget tree
  static final GlobalKey<_CalendarScreenState> globalKey =
      GlobalKey<_CalendarScreenState>();

  const CalendarScreen({super.key, this.initialDate, this.focusedAppointmentId});

  /// Open an appointment on the currently active calendar screen
  static Future<void> openAppointment(String appointmentId) async {
    await globalKey.currentState?._openAppointmentById(appointmentId);
  }

  @override
  State<CalendarScreen> createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen>
    with ComprehensiveAnalyticsMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // Analytics mixin implementation
  @override
  String get screenName => 'calendar_screen';

  @override
  String get screenCategory => 'appointments';

  @override
  String? get entryPoint => widget.focusedAppointmentId != null ? 'deep_link' : 'tab_navigation';

  // Make fields final and use setState to update them
  CalendarViewMode _currentViewMode = CalendarViewMode.week;
  bool _monthlyViewEnabled = false;

  // Analytics tracking
  DateTime? _appointmentCreationStartTime;
  late DateTime _selectedDate;

  @override
  void initState() {
    super.initState();

    _selectedDate = widget.initialDate ?? DateTime.now();

    // Add screen time tracking only - tour will be triggered when page becomes visible
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScreenTimeService.startScreenTime(screenName);
    });

    // Track screen initialization
    AnalyticsInsightsService.trackFeatureDiscovery(
      featureName: 'calendar',
      discoveryMethod: 'navigation',
      discoveryData: {'view_mode': _currentViewMode.toString()},
    );

    _loadFeatureToggles();

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final calendarProvider =
          Provider.of<CalendarProvider>(context, listen: false);
      _initializeCalendar(calendarProvider);
      if (widget.focusedAppointmentId != null) {
        await _openAppointmentById(widget.focusedAppointmentId!);
      }
    });
  }

  @override
  void dispose() {
    ScreenTimeService.endScreenTime(screenName);
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Refresh calendar data when screen becomes active (e.g., after salon switch)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final calendarProvider =
          Provider.of<CalendarProvider>(context, listen: false);
      DebugLogger.logVerbose(
          '📅 CalendarScreen: Dependencies changed, refreshing calendar data...');
      _loadCalendarData(calendarProvider);
    });
  }


  /// Initialize calendar with preferences and data
  Future<void> _initializeCalendar(CalendarProvider calendarProvider) async {

    // Initialize preferences first (this will start Google Calendar auto-refresh if enabled)
    await calendarProvider.initializePreferences();

    // Load calendar data
    await _loadCalendarData(calendarProvider);

  }

  /// Load calendar data with retry mechanism for new salons
  Future<void> _loadCalendarData(CalendarProvider calendarProvider) async {

    // Check if user has salon association before loading data
    final currentSalonId = await AuthService.getCurrentSalonId();
    if (currentSalonId == null) {
      return;
    }

    // Load staff with retry mechanism (important for new salons)
    await _loadStaffWithRetry(calendarProvider);

    // Load other data
    await calendarProvider.loadServices();
    await calendarProvider.loadWorkingHours();

    // Load staff working hours for selected staff (optimized approach)
    if (calendarProvider.selectedStaff.isNotEmpty) {
      // Force refresh staff working hours to ensure we have the latest data
      // This is especially important after salon creation when schedules are updated to non-stop
      await calendarProvider.refreshStaffWorkingHours(reason: 'Initial calendar load - force refresh');
    }

    // Load appointments and block times for today
    final today = DateTime.now();
    await calendarProvider.fetchAppointmentsForDate(today, forceRefresh: true);
    await calendarProvider.fetchBlockedTimesForDate(today, forceRefresh: true);

    // Fetch Google Calendar events for the current week if sync is enabled
    if (calendarProvider.googleCalendarSyncEnabled) {
      final weekStart = today.subtract(Duration(days: today.weekday - 1));
      await calendarProvider.fetchGoogleCalendarEventsForWeek(weekStart, forceRefresh: true);
    }

  }

  /// Load staff with retry mechanism for new salon owners
  Future<void> _loadStaffWithRetry(CalendarProvider calendarProvider) async {
    const maxAttempts = 2;
    const delayBetweenAttempts = Duration(milliseconds: 500);

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {

      await calendarProvider.loadStaff();

      // Check if staff was loaded successfully
      if (calendarProvider.availableStaff.isNotEmpty) {
        return;
      }

      if (attempt < maxAttempts) {
        await Future.delayed(delayBetweenAttempts);
      }
    }

    DebugLogger.logVerbose(
        '📅 Staff loading completed (${calendarProvider.availableStaff.length} staff members)');
  }

  Future<void> _loadFeatureToggles() async {
    final monthlyViewEnabled =
        await FeatureToggleService.isMonthlyViewEnabled();
    if (mounted) {
      setState(() {
        _monthlyViewEnabled = monthlyViewEnabled;
      });
    }
  }




  /// Refresh staff data, working hours, appointments and blocked times
  void _refreshStaffData() async {
    DebugLogger.logVerbose('🔄 CalendarScreen: Manual refresh requested - staff data, working hours, appointments and blocked times');

    final calendarProvider =
        Provider.of<CalendarProvider>(context, listen: false);

    try {
      // Refresh staff data (names, roles, etc.)
      await calendarProvider.refreshStaffData();

      // Refresh staff working hours using new batch API
      await calendarProvider.refreshStaffWorkingHours(reason: 'Manual refresh button - using batch API');

      // Refresh appointments for current date
      await calendarProvider.fetchAppointmentsForDate(_selectedDate, forceRefresh: true);

      // Refresh blocked times for current date
      await calendarProvider.fetchBlockedTimesForDate(_selectedDate, forceRefresh: true);

      // Refresh Google Calendar events if sync is enabled
      if (calendarProvider.googleCalendarSyncEnabled) {
        if (_currentViewMode == CalendarViewMode.week) {
          final weekStart = _getWeekStart(_selectedDate);
          await calendarProvider.fetchGoogleCalendarEventsForWeek(weekStart, forceRefresh: true);
        } else {
          await calendarProvider.fetchGoogleCalendarEventsForDate(_selectedDate, forceRefresh: true);
        }
      }

      // If we're in week view, refresh the entire week
      if (_currentViewMode == CalendarViewMode.week) {
        final weekStart = _getWeekStart(_selectedDate);
        final weekEnd = weekStart.add(const Duration(days: 6));
        await calendarProvider.fetchAppointmentsForDateRange(weekStart, weekEnd);

        // Refresh blocked times for the entire week
        for (int i = 0; i < 7; i++) {
          final day = weekStart.add(Duration(days: i));
          await calendarProvider.fetchBlockedTimesForDate(day, forceRefresh: true);
        }
      }

      if (mounted) {
        UINotificationService.showSuccess(
          context: context,
          title: context.tr('calendar.update_complete'),
          message: context.tr('calendar.update_complete_message'),
        );
      }
    } catch (e) {
      DebugLogger.logVerbose('❌ CalendarScreen: Error during refresh: $e');
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: context.tr('calendar.update_error'),
          message: context.tr('calendar.update_error_message', params: {
            'error': e.toString(),
          }),
        );
      }
    }
  }


  void _showBlockTimeScreen() {
    final selectedDate = DateTime.now();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BlockTimeDialog(
          selectedDate: selectedDate,
        );
      },
    );
  }

  void _openAppointmentDetails(Appointment appointment) {
    GenieAnimation.showGenieDialog(
      context: context,
      dialog: AppointmentDetailsDialog(appointment: appointment),
    ).then((_) {
      // Auto-scroll to appointment time when dialog is closed
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_currentViewMode == CalendarViewMode.day) {
          DayView.globalKey.currentState?.scrollToTime(appointment.startTime);
        } else if (_currentViewMode == CalendarViewMode.week) {
          WeekView.globalKey.currentState?.scrollToDateTime(appointment.startTime);
        }
      });
    });
  }

  Future<void> _openAppointmentById(String id) async {
    final response = await AppointmentService.getAppointment(id);
    if (!mounted) return;
    if (response.success && response.data != null) {
      final appt = response.data!;
      setState(() {
        _selectedDate = DateTime(
          appt.startTime.year,
          appt.startTime.month,
          appt.startTime.day,
        );
      });
      final provider = Provider.of<CalendarProvider>(context, listen: false);
      await provider.fetchAppointmentsForDate(_selectedDate, forceRefresh: true);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _openAppointmentDetails(appt);
      });
    }
  }

  void _showNewAppointmentScreen(
      [DateTime? selectedDate, String? groomerId]) async {
    final date = selectedDate ?? DateTime.now();

    // Track appointment creation start time
    _appointmentCreationStartTime = DateTime.now();

    // Track button click for appointment creation
    trackButtonClick('create_appointment', context: {
      'selected_date': date.toIso8601String(),
      'preselected_groomer': groomerId != null,
      'view_mode': _currentViewMode.name,
      'trigger_source': selectedDate != null ? 'time_slot_tap' : 'floating_action_button',
    });

    // Track navigation to appointment creation
    trackNavigationTo('new_appointment_screen', method: 'appointment_creation_flow');

    // Track appointment creation flow start (existing analytics)
    AnalyticsInsightsService.trackAppointmentCreationFlow(
      'started',
      stepData: {
        'selected_date': date.toIso8601String(),
        'preselected_groomer': groomerId,
        'initiated_from': 'calendar_screen',
      },
    );

    final newAppointment = await Navigator.of(context).push<Appointment?>(
      MaterialPageRoute(
        builder: (context) => NewAppointmentScreen(
          selectedDate: DateTime(date.year, date.month, date.day), // Date only
          selectedDateTime: selectedDate, // Full datetime with time component
          preselectedStaffId: groomerId,
        ),
      ),
    );

    if (mounted && newAppointment != null) {
      // Track appointment creation result
      DebugLogger.logVerbose('✅ Appointment added successfully');

      // Track successful appointment creation with comprehensive analytics
      trackAppointmentCreationSuccess(
        appointmentId: newAppointment.id,
        clientType: newAppointment.clientId.isNotEmpty ? 'existing' : 'new',
        serviceIds: newAppointment.services,
        creationTimeMs: DateTime.now().difference(_appointmentCreationStartTime ?? DateTime.now()).inMilliseconds,
        totalPrice: newAppointment.totalPrice,
        context: {
          'created_from': 'calendar_screen',
          'view_mode': _currentViewMode.name,
          'preselected_groomer': groomerId != null,
        },
      );

      // Track existing analytics
      AnalyticsInsightsService.trackAppointmentCreationFlow(
        'completed',
        stepData: {'success': true, 'completion_source': 'calendar_screen'},
      );

      // Feature 1: Update selected date and refresh calendar immediately
      setState(() {
        _selectedDate = DateTime(
          newAppointment.startTime.year,
          newAppointment.startTime.month,
          newAppointment.startTime.day,
        );
      });

      final calendarProvider =
          Provider.of<CalendarProvider>(context, listen: false);

      // Force refresh to ensure new appointment appears immediately
      await calendarProvider.fetchAppointmentsForDate(_selectedDate, forceRefresh: true);

      // Problem 2 Solution: Navigate to appointment date/time and highlight it
      // Scroll the calendar view to the appointment time
      if (_currentViewMode == CalendarViewMode.day) {
        DayView.globalKey.currentState?.scrollToTime(newAppointment.startTime);
      } else if (_currentViewMode == CalendarViewMode.week) {
        WeekView.globalKey.currentState?.scrollToDateTime(newAppointment.startTime);
      }

      // Highlight the newly created appointment for visual feedback
      calendarProvider.highlightAppointment(
        newAppointment.id,
        duration: const Duration(seconds: 3),
      );

      // Show success notification
      UINotificationService.showSuccess(
        context: context,
        title: context.tr('calendar.appointment_added'),
        message: context.tr('calendar.appointment_added_message'),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      endDrawer: CalendarSettingsDrawer(
        currentViewMode: _currentViewMode,
        monthlyViewEnabled: _monthlyViewEnabled,
        onViewModeChanged: (mode) {
          setState(() {
            _currentViewMode = mode;
          });
        },
      ),
      // Professional AppBar with elegant brown theme styling
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        foregroundColor: Theme.of(context).brightness == Brightness.dark
            ? AppColors.darkText
            : AppColors.lightText,
        title: Row(
          children: [
            Flexible(
              child: Text(
                _getMonthYearText(),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.darkTextSecondary
                      : AppColors.secondaryText,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.list),
            onPressed: () => _switchToListView(),
            tooltip: context.tr('calendar.list_view'),
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () {
              final tourKeys = CalendarTourKeys.getCalendarTourKeys();
              ShowCaseWidget.of(context).startShowCase(tourKeys);
            },
            tooltip: context.tr('common.help'),
          ),
          Showcase(
            key: CalendarTourKeys.settingsButtonKey,
            title: context.tr('calendar.settings_tour_title'),
            description: context.tr('calendar.settings_tour_description'),
            child: IconButton(
              icon: Icon(
                Icons.settings,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              onPressed: () => _scaffoldKey.currentState?.openEndDrawer(),
              tooltip: context.tr('calendar.calendar_settings'),
            ),
          ),
        ],
      ),
      body: SafeArea(
        top: false, // AppBar handles status bar spacing
        child: SizedBox.expand(
          child: Stack(
            children: [
              Showcase(
                key: CalendarTourKeys.calendarViewKey,
                title: context.tr('calendar.calendar_tour_title'),
                description: context.tr('calendar.calendar_tour_description'),
                child: GoogleCalendarView(
                  currentViewMode: _currentViewMode,
                  selectedDate: _selectedDate,
                  onTimeSlotTap: (dateTime, staffId) => _showNewAppointmentScreen(dateTime, staffId),
                onViewModeChange: (mode) {
                  setState(() {
                    _currentViewMode = mode;
                  });
                },
                onDateChanged: (date) {
                  setState(() {
                    _selectedDate = date;
                  });
                },
                onRefreshPressed: _refreshStaffData,
                onSettingsPressed: () => _scaffoldKey.currentState?.openEndDrawer(),
                ),
              ),
              // Single showcase for tap-to-create functionality
              Positioned(
                top: 100,
                left: 100,
                child: Showcase(
                  key: CalendarTourKeys.tapToCreateKey,
                  title: context.tr('calendar.create_appointment_tour_title'),
                  description: context.tr('calendar.create_appointment_tour_description'),
                  child: Container(
                    width: 1,
                    height: 1,
                    color: Colors.transparent,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: Showcase(
        key: CalendarTourKeys.addAppointmentKey,
        title: context.tr('calendar.quick_actions_tour_title'),
        description: context.tr('calendar.quick_actions_tour_description'),
        child: SpeedDial(
          animatedIcon: AnimatedIcons.menu_close,
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          activeBackgroundColor: Theme.of(context).colorScheme.secondary,
          spacing: 3,
          childPadding: const EdgeInsets.all(5),
          spaceBetweenChildren: 4,
          tooltip: context.tr('calendar.actions_tooltip'),
        children: [
          SpeedDialChild(
            child: const Icon(Icons.block),
            backgroundColor: Theme.of(context).colorScheme.surface,
            foregroundColor: Theme.of(context).colorScheme.primary,
            label: context.tr('calendar.block_time'),
            onTap: _showBlockTimeScreen,
          ),
          SpeedDialChild(
            child: const Icon(Icons.add),
            backgroundColor: Theme.of(context).colorScheme.surface,
            foregroundColor: Theme.of(context).colorScheme.primary,
            label: context.tr('calendar.add_appointment'),
            onTap: () => _showNewAppointmentScreen(),
          ),
        ],
        ),
      ),
      );
  }

  int _getAppointmentCountForCurrentView(CalendarProvider provider) {
    switch (_currentViewMode) {
      case CalendarViewMode.day:
        return provider.getFilteredAppointmentsForDate(_selectedDate).length;
      case CalendarViewMode.week:
        final weekStart = _getWeekStart(_selectedDate);
        int totalCount = 0;
        for (int i = 0; i < 7; i++) {
          final day = weekStart.add(Duration(days: i));
          totalCount += provider.getFilteredAppointmentsForDate(day).length;
        }
        return totalCount;
      case CalendarViewMode.month:
        final firstDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month, 1);
        final lastDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);
        int totalCount = 0;
        DateTime currentDay = firstDayOfMonth;
        while (currentDay.isBefore(lastDayOfMonth) || currentDay.isAtSameMomentAs(lastDayOfMonth)) {
          totalCount += provider.getFilteredAppointmentsForDate(currentDay).length;
          currentDay = currentDay.add(const Duration(days: 1));
        }
        return totalCount;
    }
  }

  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  String _getMonthYearText() {
    final locale = Localizations.localeOf(context).languageCode;
    switch (_currentViewMode) {
      case CalendarViewMode.day:
        return DateFormat('MMMM yyyy', locale).format(_selectedDate);
      case CalendarViewMode.week:
        final weekStart = _getWeekStart(_selectedDate);
        return DateFormat('MMMM yyyy', locale).format(weekStart);
      case CalendarViewMode.month:
        return DateFormat('MMMM yyyy', locale).format(_selectedDate);
    }
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  void _switchToListView() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CalendarListScreen(),
      ),
    );
  }

}
