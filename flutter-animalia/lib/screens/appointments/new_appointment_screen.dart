import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/appointment_alternative.dart';
import '../../providers/calendar_provider.dart';
import '../../services/appointment/calendar_service.dart';
import '../../services/error_message_service.dart';
import '../../services/ui_notification_service.dart';
import '../../utils/debug_logger.dart';
import '../../widgets/common/responsive_layout_wrapper.dart';
import '../../widgets/dialogs/conflict_resolution_dialog.dart';
import '../../widgets/new_appointment/appointment_footer.dart';
import '../../widgets/new_appointment/appointment_form_constants.dart';
import '../../widgets/new_appointment/appointment_form_data.dart';
import '../../widgets/new_appointment/appointment_form_logic.dart';
import '../../widgets/new_appointment/client_selection_widget.dart';
import '../../widgets/new_appointment/coworker_selection_widget.dart';
import '../../widgets/new_appointment/date_time_selection_widget.dart';
import '../../widgets/new_appointment/multiple_services_widget.dart';
import '../../widgets/new_appointment/notes_repetition_widget.dart';
import '../../widgets/new_appointment/pet_selection_widget.dart';
import '../../widgets/analytics/comprehensive_analytics_mixin.dart';

class NewAppointmentScreen extends StatefulWidget {
  final DateTime selectedDate;
  final String? preselectedStaffId;
  final DateTime? selectedDateTime; // New parameter for specific time selection

  const NewAppointmentScreen({
    super.key,
    required this.selectedDate,
    this.preselectedStaffId,
    this.selectedDateTime, // Optional specific time from calendar
  });

  @override
  State<NewAppointmentScreen> createState() => _NewAppointmentScreenState();
}

class _NewAppointmentScreenState extends State<NewAppointmentScreen>
    with ComprehensiveAnalyticsMixin {
  // Analytics mixin implementation
  @override
  String get screenName => 'new_appointment_screen';

  @override
  String get screenCategory => 'appointments';

  @override
  String? get entryPoint => widget.preselectedStaffId != null ? 'staff_preselected' : 'calendar_selection';

  late AppointmentFormData _formData;
  late AppointmentFormLogic _formLogic;
  bool _isLoading = false;
  Future<AppointmentCreationResult>? _addAppointmentFuture;
  bool _conflictDialogShown = false;

  // Analytics tracking
  DateTime? _appointmentCreationStartTime;

  @override
  void initState() {
    super.initState();
    _appointmentCreationStartTime = DateTime.now();
    _initializeFormData();
    _initializeFormLogic();
    _loadInitialData();

    // Track appointment creation start
    trackAppointmentCreationStart(
      clientType: 'new', // Default to new since we don't have preselected client
      serviceIds: [], // Will be updated as services are selected
      context: {
        'preselected_date': widget.selectedDate.toIso8601String(),
        'preselected_staff': widget.preselectedStaffId,
        'selected_date_time': widget.selectedDateTime?.toIso8601String(),
      },
    );
  }

  void _initializeFormData() {
    final initialStartTime = _calculateInitialStartTime();
    // Calculate initial end time based on default service duration
    final defaultServiceDuration = 0;
    final initialEndTime = initialStartTime.add(Duration(minutes: defaultServiceDuration));

    _formData = AppointmentFormData(
      appointmentDate: widget.selectedDate,
      startTime: initialStartTime,
      endTime: initialEndTime,
    );
  }

  void _initializeFormLogic() {
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
    _formLogic = AppointmentFormLogic(calendarProvider);
  }

  DateTime _calculateInitialStartTime() {
    // Use selectedDateTime if provided (from calendar time slot click), otherwise use current time
    final baseDateTime = widget.selectedDateTime ?? DateTime.now();

    final selectedDateTime = DateTime(
      widget.selectedDate.year,
      widget.selectedDate.month,
      widget.selectedDate.day,
      baseDateTime.hour,
      baseDateTime.minute,
    );

    // Round up to the nearest 15-minute interval
    final minutes = selectedDateTime.minute;
    final roundedMinutes = ((minutes / 15).ceil() * 15) % 60;
    final additionalHour = ((minutes / 15).ceil() * 15) ~/ 60;

    var startTime = DateTime(
      selectedDateTime.year,
      selectedDateTime.month,
      selectedDateTime.day,
      selectedDateTime.hour + additionalHour,
      roundedMinutes,
    );

    if (widget.selectedDateTime == null) {
      if (startTime.hour < AppointmentFormConstants.businessStartHour) {
        startTime = DateTime(startTime.year, startTime.month, startTime.day,
            AppointmentFormConstants.businessStartHour, 0);
      } else if (startTime.hour >= AppointmentFormConstants.businessEndHour) {
        if (widget.selectedDate.isAfter(DateTime.now())) {
          startTime = DateTime(widget.selectedDate.year, widget.selectedDate.month,
              widget.selectedDate.day, AppointmentFormConstants.businessStartHour, 0);
        } else {
          startTime = DateTime(startTime.year, startTime.month, startTime.day + 1,
              AppointmentFormConstants.businessStartHour, 0);
        }
      }
    }

    return startTime;
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      setState(() => _isLoading = true);
      try {
        await _formLogic.loadInitialData(_formData, preselectedStaffId: widget.preselectedStaffId);
        // Sync end time with default service duration after services are loaded
        await _formLogic.updateEndTimeBasedOnServices(_formData);
      } finally {
        if (mounted) setState(() => _isLoading = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_addAppointmentFuture != null) {
      return _buildAppointmentCreationScreen();
    }

    return _buildFormScreen();
  }

  Widget _buildAppointmentCreationScreen() {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('new_appointment.appointment_title')),
        automaticallyImplyLeading: false,
      ),
      body: ResponsiveLayoutWrapper(
        maxWidth: 600.0, // Optimal width for forms
        child: Container(
          color: Theme.of(context).colorScheme.surface,
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: FutureBuilder<AppointmentCreationResult>(
                future: _addAppointmentFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return _buildLoadingContent();
                  } else if (snapshot.hasError) {
                    return _buildErrorContent(snapshot.error.toString());
                  } else if (snapshot.hasData) {
                    final result = snapshot.data!;
                    if (result.success) {
                      return _buildResultContent(result);
                    }
                    return _buildResultContent(result);
                  } else {
                    return _buildErrorContent(context.tr('new_appointment.could_not_create_appointment'));
                  }
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(color: Theme.of(context).colorScheme.onSurface),
        const SizedBox(height: 20),
        Text(
          context.tr('new_appointment.creating_appointment'),
          style: TextStyle(
            fontSize: 18,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorContent(String error) {
    // Track appointment creation failure
    trackAppointmentCreationFailure(
      errorType: 'creation_error',
      errorMessage: error,
      attemptTimeMs: DateTime.now().difference(_appointmentCreationStartTime ?? DateTime.now()).inMilliseconds,
      context: {
        'services_count': _formData.services.length,
        'has_client': _formData.clientId.isNotEmpty,
        'error_source': 'appointment_service',
      },
    );

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.error, color: AppTheme.getStatusColor(context, 'error'), size: 64),
        const SizedBox(height: 16),
        Text(
          context.tr('new_appointment.error_prefix', params: {'error': error}),
          style: TextStyle(
            fontSize: 16,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(false),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
          ),
          child: Text(context.tr('new_appointment.close')),
        ),
      ],
    );
  }

  Widget _buildResultContent(AppointmentCreationResult result) {
    if (result.success) {
      // Track successful appointment creation
      trackAppointmentCreationSuccess(
        appointmentId: result.appointment?.id ?? 'unknown',
        clientType: _formData.clientId.isNotEmpty ? 'existing' : 'new',
        serviceIds: _formData.services,
        creationTimeMs: DateTime.now().difference(_appointmentCreationStartTime ?? DateTime.now()).inMilliseconds,
        totalPrice: 0.0, // TODO: Calculate from services
        context: {
          'has_notes': _formData.notes.isNotEmpty,
          'is_paid': _formData.isPaid,
          'services_count': _formData.services.length,
        },
      );

      // Feature 1: Remove confirmation screen - automatically navigate back
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).pop(result.appointment);
        }
      });

      // Show a brief loading indicator while navigating back
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.check_circle, color: AppTheme.getStatusColor(context, 'success'), size: 64),
          const SizedBox(height: 16),
          Text(
            context.tr('new_appointment.appointment_created_success'),
            style: TextStyle(
              fontSize: 18,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          const CircularProgressIndicator(),
        ],
      );
    } else {
      // Show specific error message for business logic errors
      final errorMessage = result.userFriendlyError;
      final suggestion = ErrorMessageService.getSuggestedAction(result.errorCode);

      DebugLogger.logVerbose('🔍 Checking conflict dialog conditions:');
      DebugLogger.logVerbose('  - isSchedulingConflict: ${result.isSchedulingConflict}');
      DebugLogger.logVerbose('  - conflictDialogShown: $_conflictDialogShown');
      DebugLogger.logVerbose('  - alternatives count: ${result.alternatives.length}');
      DebugLogger.logVerbose('  - errorCode: ${result.errorCode}');

      if (result.isSchedulingConflict && !_conflictDialogShown) {
        DebugLogger.logVerbose('✅ Showing conflict resolution dialog');
        _conflictDialogShown = true;
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          final alt = await showDialog<AppointmentAlternative>(
            context: context,
            builder: (ctx) => ConflictResolutionDialog(
              alternatives: result.alternatives,
              onTryDifferentTime: () {
                Navigator.of(ctx).pop();
                setState(() {
                  _addAppointmentFuture = null;
                  _conflictDialogShown = false;
                });
              },
              onContactSalon: () {
                Navigator.of(ctx).pop();
              },
              onAddToWaitingList: () {
                // Store context reference before closing dialog
                final parentContext = context;
                Navigator.of(ctx).pop();

                // Use UI notification for feedback
                UINotificationService.showSuccess(
                  context: parentContext,
                  title: context.tr('new_appointment.waitlist_added_title'),
                  message: context.tr('new_appointment.waitlist_added_message'),
                );
                setState(() {
                  _addAppointmentFuture = null;
                  _conflictDialogShown = false;
                });
              },
              onSelectAlternative: (alternative) {
                Navigator.of(ctx).pop(alternative);
              },
            ),
          );
          if (alt != null) {
            DebugLogger.logVerbose('🔄 Alternative selected: ${alt.startTime} - ${alt.endTime} (Staff: ${alt.staffName})');
            setState(() {
              _formData.startTime = alt.startTime;
              _formData.endTime = alt.endTime;
              _formData.assignedStaffId = alt.staffId;
              // CRITICAL FIX: Update appointmentDate to match the alternative date
              _formData.appointmentDate = DateTime(
                alt.startTime.year,
                alt.startTime.month,
                alt.startTime.day,
              );
              DebugLogger.logVerbose('✅ Form data updated - Date: ${_formData.appointmentDate}, Time: ${_formData.startTime} - ${_formData.endTime}');
              _addAppointmentFuture = _formLogic.createAppointment(_formData);
              _conflictDialogShown = false;
            });
          }
        });
      }

      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            result.isSchedulingConflict ? Icons.schedule : Icons.error,
            color: result.isSchedulingConflict
                ? AppTheme.getStatusColor(context, 'warning')
                : AppTheme.getStatusColor(context, 'error'),
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            errorMessage,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
          if (suggestion != null) ...[
            const SizedBox(height: 12),
            Text(
              suggestion,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              // Go back to form to try again
              setState(() {
                _addAppointmentFuture = null;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
            child: Text(context.tr('new_appointment.ok')),
          ),
        ],
      );
    }
  }

  Widget _buildFormScreen() {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('new_appointment.title')),
        elevation: 0,
      ),
      body: ResponsiveFormWrapper(
        maxWidth: 800.0, // Optimal width for appointment forms
        child: Column(
          children: [
            _buildProgressIndicator(),
            Expanded(
              child: Container(
                color: Theme.of(context).colorScheme.surface,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildClientPetSection(),
                      _buildSectionDivider(),
                      _buildServicesSection(),
                      _buildSectionDivider(),
                      _buildTimeStaffSection(),
                      _buildSectionDivider(),
                      _buildOptionalSection(),
                      const SizedBox(height: 80),
                    ],
                  ),
                ),
              ),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  // Progress indicator showing form completion
  Widget _buildProgressIndicator() {
    int completedSections = 0;
    int totalSections = 4; // Client+Pet, Services, Time+Staff, Optional

    // Check completion status
    if (_isClientDataComplete()) completedSections++;
    if (_formData.services.isNotEmpty) completedSections++;
    if (_formData.assignedStaffId.isNotEmpty) completedSections++;
    // Optional section is always considered "complete"
    completedSections++;

    final progress = completedSections / totalSections;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.tr('new_appointment.form_progress'),
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                '$completedSections/$totalSections',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  // Subtle section divider instead of heavy cards
  Widget _buildSectionDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12),
      height: 1,
      color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
    );
  }

  // Combined client and pet section for better flow
  Widget _buildClientPetSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(context.tr('new_appointment.client_section'), Icons.person_add, isRequired: true),
        const SizedBox(height: 12),
        _buildClientContent(),
        // Show pet content only for existing clients when they have data
        // For new clients, pet fields are integrated into ClientSelectionWidget
        if (_formData.isExistingClient && (_formData.clientName.isNotEmpty || _formData.clientId.isNotEmpty)) ...[
          const SizedBox(height: 16),
          _buildPetContent(),
        ],
      ],
    );
  }

  // Combined time and staff section
  Widget _buildTimeStaffSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTimeContent(),
        const SizedBox(height: 16),
        _buildCoworkerContent(),
      ],
    );
  }

  // Collapsible optional section
  Widget _buildOptionalSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(context.tr('new_appointment.additional_options'), Icons.tune, isRequired: false),
        const SizedBox(height: 12),
        _buildNotesRepetitionContent(),
      ],
    );
  }

  // Consistent section headers with icons and required indicators
  Widget _buildSectionHeader(String title, IconData icon, {required bool isRequired}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        if (isRequired) ...[
          const SizedBox(width: 4),
          Text(
            '*',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  // Content methods without card wrappers for cleaner design
  Widget _buildClientContent() {
    return ClientSelectionWidget(
      formData: _formData,
      isLoadingClients: _isLoading,
      onClientTypeChanged: (isExisting) {
        setState(() {
          _formLogic.handleClientTypeChange(_formData, isExisting);
        });
      },
      onClientSelected: (client) async {
        DebugLogger.logVerbose('🔄 NewAppointmentScreen: Client selected: ${client.name} (${client.id})');

        // Update UI immediately to show client selection
        setState(() {
          _formLogic.handleClientTypeChange(_formData, true);
          _formData.updateClientData(client);
        });

        // Handle complete client selection (pets + service auto-population) asynchronously
        try {
          await _formLogic.handleClientSelection(_formData, client);
          DebugLogger.logVerbose('✅ NewAppointmentScreen: Client selection completed (pets + services), updating UI');
          setState(() {
            // Trigger UI rebuild with loaded pets and auto-populated services
          });
        } catch (e) {
          DebugLogger.logVerbose('❌ NewAppointmentScreen: Error in client selection: $e');
          setState(() {
            // Update UI even if client selection failed
          });
        }
      },
      onClientNameChanged: (name) {
        setState(() => _formData.clientName = name);
      },
      onClientPhoneChanged: (phone) {
        setState(() => _formData.clientPhone = phone);
      },
      // New pet-related callbacks for integrated form
      onPetNameChanged: (name) {
        setState(() => _formData.petName = name);
      },
      onPetBreedChanged: (breed) {
        setState(() {
          _formLogic.handlePetBreedChange(_formData, breed);
        });
      },
      onPetSpeciesChanged: (species) {
        setState(() {
          _formData.petSpecies = species;
        });
      },
    );
  }


  Widget _buildPetContent() {
    return PetSelectionWidget(
      formData: _formData,
      onPetSelected: (pet) {
        setState(() {
          _formLogic.handlePetSelection(_formData, pet);
        });
      },
      onPetNameChanged: (name) {
        setState(() => _formData.petName = name);
      },
      onPetBreedChanged: (breed) {
        setState(() {
          _formLogic.handlePetBreedChange(_formData, breed);
        });
      },
      onAddNewPet: () {
        setState(() {
          _formLogic.handleToggleNewPet(_formData);
        });
      },
      onPriceUpdateNeeded: () {
        // Force UI update to recalculate prices based on new breed/size
        setState(() {});
        DebugLogger.logVerbose('💰 Price update triggered by breed change - new size: ${_formData.petSize}');
      },
    );
  }

  Widget _buildPetSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: PetSelectionWidget(
          formData: _formData,
          onPetSelected: (pet) {
            setState(() {
              _formLogic.handlePetSelection(_formData, pet);
            });
          },
          onPetNameChanged: (name) {
            setState(() => _formData.petName = name);
          },
          onPetBreedChanged: (breed) {
            setState(() {
              _formLogic.handlePetBreedChange(_formData, breed);
            });
          },
          onAddNewPet: () {
            setState(() {
              _formLogic.handleToggleNewPet(_formData);
            });
          },
          onPriceUpdateNeeded: () {
            // Force UI update to recalculate prices based on new breed/size
            setState(() {});
            DebugLogger.logVerbose('💰 Price update triggered by breed change - new size: ${_formData.petSize}');
          },
        ),
      ),
    );
  }

  Widget _buildServicesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // _buildSectionHeader('Servicii', Icons.cut_outlined, isRequired: true),
        const SizedBox(height: 12),
        MultipleServicesWidget(
          formData: _formData,
          onAddService: (service) async {
            await _formLogic.handleAddService(_formData, service);
            setState(() {
              // Trigger UI update after async operation completes
            });
          },
          onRemoveService: (service) async {
            await _formLogic.handleRemoveService(_formData, service);
            setState(() {
              // Trigger UI update after async operation completes
            });
          },
        ),
      ],
    );
  }

  Widget _buildTimeContent() {
    return DateTimeSelectionWidget(
      formData: _formData,
      onDateChanged: (date) {
        setState(() {
          _formLogic.handleAppointmentDateChange(_formData, date);
        });
      },
      onStartTimeChanged: (startTime) async {
        await _formLogic.handleStartTimeChange(_formData, startTime);
        setState(() {
          // Trigger UI update after async operation completes
        });
      },
      onEndTimeChanged: (endTime) {
        setState(() => _formData.endTime = endTime);
      },
      onDurationChanged: (duration) {
        // Duration changes are handled by updating end time
        DebugLogger.logVerbose('Duration changed to: ${duration.inMinutes} minutes');
      },
    );
  }

  Widget _buildTimeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: DateTimeSelectionWidget(
          formData: _formData,
          onDateChanged: (date) {
            setState(() {
              _formLogic.handleAppointmentDateChange(_formData, date);
            });
          },
          onStartTimeChanged: (startTime) async {
            await _formLogic.handleStartTimeChange(_formData, startTime);
            setState(() {
              // Trigger UI update after async operation completes
            });
          },
          onEndTimeChanged: (endTime) {
            setState(() => _formData.endTime = endTime);
          },
          onDurationChanged: (duration) {
            // Duration changes are handled by updating end time
            DebugLogger.logVerbose('Duration changed to: ${duration.inMinutes} minutes');
          },
        ),
      ),
    );
  }

  Widget _buildCoworkerContent() {
    return CoworkerSelectionWidget(
      formData: _formData,
      onStaffChanged: (staffId) {
        setState(() {
          _formLogic.handleStaffChange(_formData, staffId);
        });
      },
    );
  }

  Widget _buildCoworkerSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: CoworkerSelectionWidget(
          formData: _formData,
          onStaffChanged: (staffId) {
            setState(() {
              _formLogic.handleStaffChange(_formData, staffId);
            });
          },
        ),
      ),
    );
  }

  Widget _buildNotesRepetitionContent() {
    return NotesRepetitionWidget(
      formData: _formData,
      onNotesVisibilityChanged: (showNotes) {
        setState(() {
          _formLogic.handleNotesVisibilityChange(_formData, showNotes);
        });
      },
      onRepetitionVisibilityChanged: (showRepetition) {
        setState(() {
          _formLogic.handleRepetitionVisibilityChange(_formData, showRepetition);
        });
      },
      onRepetitionFrequencyChanged: (frequency) {
        setState(() {
          _formLogic.handleRepetitionFrequencyChange(_formData, frequency);
        });
      },
      onNotesChanged: (notes) {
        setState(() => _formData.notes = notes);
      },
      onRecurrenceFrequencyChanged: (frequency) {
        setState(() {
          _formData.recurrenceFrequency = frequency;
        });
      },
      onRecurrencePeriodChanged: (period) {
        setState(() {
          _formData.recurrencePeriod = period;
        });
      },
      onTotalRepetitionsChanged: (repetitions) {
        setState(() {
          _formData.totalRepetitions = repetitions;
        });
      },
      onPaymentModelChanged: (paymentModel) {
        setState(() {
          _formData.paymentModel = paymentModel;
          // Reset discount when switching to per-appointment
          if (paymentModel == 'per_appointment') {
            _formData.discountPercentage = 0.0;
          }
        });
      },
      onDiscountPercentageChanged: (discountPercentage) {
        setState(() {
          _formData.discountPercentage = discountPercentage;
        });
      },
    );
  }



  Widget _buildNotesRepetitionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: NotesRepetitionWidget(
          formData: _formData,
          onNotesVisibilityChanged: (showNotes) {
            setState(() {
              _formLogic.handleNotesVisibilityChange(_formData, showNotes);
            });
          },
          onRepetitionVisibilityChanged: (showRepetition) {
            setState(() {
              _formLogic.handleRepetitionVisibilityChange(_formData, showRepetition);
            });
          },
          onRepetitionFrequencyChanged: (frequency) {
            setState(() {
              _formLogic.handleRepetitionFrequencyChange(_formData, frequency);
            });
          },
          onNotesChanged: (notes) {
            setState(() => _formData.notes = notes);
          },
          onRecurrenceFrequencyChanged: (frequency) {
            setState(() {
              _formData.recurrenceFrequency = frequency;
            });
          },
          onRecurrencePeriodChanged: (period) {
            setState(() {
              _formData.recurrencePeriod = period;
            });
          },
          onTotalRepetitionsChanged: (repetitions) {
            setState(() {
              _formData.totalRepetitions = repetitions;
            });
          },
          onPaymentModelChanged: (paymentModel) {
            setState(() {
              _formData.paymentModel = paymentModel;
              // Reset discount when switching to per-appointment
              if (paymentModel == 'per_appointment') {
                _formData.discountPercentage = 0.0;
              }
            });
          },
          onDiscountPercentageChanged: (discountPercentage) {
            setState(() {
              _formData.discountPercentage = discountPercentage;
            });
          },
        ),
      ),
    );
  }

  // Helper method to check if client data is complete
  bool _isClientDataComplete() {
    if (_formData.isExistingClient) {
      return _formData.clientId.isNotEmpty &&
             _formData.clientPhone.isNotEmpty &&
             _formData.petName.isNotEmpty;
    } else {
      return _formData.clientName.isNotEmpty &&
             _formData.clientPhone.isNotEmpty &&
             _formData.petName.isNotEmpty &&
             _formData.petBreed.isNotEmpty;
    }
  }

  Widget _buildFooter() {
    return AppointmentFooter(
      formData: _formData,
      onCreateAppointment: _handleCreateAppointment,
      onPaidStatusChanged: (isPaid) {
        setState(() => _formData.isPaid = isPaid);
      },
    );
  }

  void _handleCreateAppointment() async {
    final startTime = DateTime.now();

    // Track save button click
    trackButtonClick('save_appointment', context: {
      'has_client': _formData.clientId.isNotEmpty,
      'services_count': _formData.services.length,
      'has_notes': _formData.notes.isNotEmpty,
      'is_paid': _formData.isPaid,
    });

    final validationError = _formLogic.validateForm(_formData, context);
    if (validationError != null) {
      final isPastError = validationError.contains('în trecut');

      // Track validation failure
      trackAppointmentCreationFailure(
        errorType: isPastError ? 'past_date_error' : 'validation_error',
        errorMessage: validationError,
        attemptTimeMs: DateTime.now().difference(startTime).inMilliseconds,
        context: {
          'validation_field': isPastError ? 'date' : 'form_fields',
        },
      );

      UINotificationService.showWarning(
        context: context,
        title: isPastError ? context.tr('new_appointment.validation_invalid_date') : context.tr('new_appointment.validation_incomplete_form'),
        message: validationError,
      );
      return;
    }

    setState(() {
      _conflictDialogShown = false;
      _addAppointmentFuture = _formLogic.createAppointment(_formData);
    });
  }
}
