import 'package:flutter/material.dart';

import '../../widgets/calendar_views/appointments_list_view.dart';
import '../appointments/new_appointment_screen.dart';
import '../../l10n/app_localizations.dart';

class CalendarListScreen extends StatefulWidget {
  final AppointmentFilter? initialFilter;

  const CalendarListScreen({
    super.key,
    this.initialFilter,
  });

  @override
  State<CalendarListScreen> createState() => _CalendarListScreenState();
}

class _CalendarListScreenState extends State<CalendarListScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(
          context.tr('calendar.appointments_list'),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          // Switch to calendar view
          IconButton(
            icon: const Icon(Icons.calendar_month),
            onPressed: () => _switchToCalendarView(),
            tooltip: context.tr('calendar.calendar_view'),
          ),
          // Export options
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            tooltip: context.tr('common.options'),
            onSelected: _handleMenuSelection,
            itemBuilder: (BuildContext context) => [
              PopupMenuItem<String>(
                value: 'export_excel',
                child: Row(
                  children: [
                    const Icon(Icons.file_download, size: 20),
                    const SizedBox(width: 8),
                    Text(context.tr('calendar.export_excel')),
                  ],
                ),
              ),
              PopupMenuItem<String>(
                value: 'print',
                child: Row(
                  children: [
                    const Icon(Icons.print, size: 20),
                    const SizedBox(width: 8),
                    Text(context.tr('calendar.print')),
                  ],
                ),
              ),
              PopupMenuItem<String>(
                value: 'share',
                child: Row(
                  children: [
                    const Icon(Icons.share, size: 20),
                    const SizedBox(width: 8),
                    Text(context.tr('calendar.share')),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: AppointmentsListView(
        initialFilter: widget.initialFilter ?? AppointmentFilter.upcoming,
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _addNewAppointment(),
        icon: const Icon(Icons.add),
        label: Text(context.tr('calendar.new_appointment')),
        tooltip: context.tr('calendar.add_new_appointment'),
      ),
    );
  }

  void _switchToCalendarView() {
    Navigator.pop(context);
  }

  void _handleMenuSelection(String selection) {
    switch (selection) {
      case 'export_excel':
        _exportToExcel();
        break;
      case 'print':
        _printAppointments();
        break;
      case 'share':
        _shareAppointments();
        break;
    }
  }

  void _exportToExcel() {
    // Navigate to appointments report for Excel export
    Navigator.pushNamed(context, '/reports/appointments');
  }

  void _printAppointments() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('calendar.print_feature_coming')),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _shareAppointments() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('calendar.share_feature_coming')),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _addNewAppointment() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NewAppointmentScreen(
          selectedDate: DateTime.now(),
        ),
      ),
    );
  }
}
