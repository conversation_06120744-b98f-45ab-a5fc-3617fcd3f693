import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../config/theme/app_theme.dart';
import '../../l10n/app_localizations.dart';
import '../../models/report_data.dart';
import 'appointments_report_screen.dart';
import 'client_report_screen.dart';
import 'pet_report_screen.dart';
import 'revenue_report_screen.dart';
import 'service_report_screen.dart';
import 'staff_performance_screen.dart';

class BusinessReportsScreen extends StatefulWidget {
  const BusinessReportsScreen({super.key});

  @override
  State<BusinessReportsScreen> createState() => _BusinessReportsScreenState();
}

class _BusinessReportsScreenState extends State<BusinessReportsScreen> {
  bool _showSampleDataInfo = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('business_reports.title'),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showInfoDialog,
            tooltip: context.tr('business_reports.info_tooltip'),
          ),
        ],
      ),
      body: Column(
        children: [
          // Sample data info banner
          if (_showSampleDataInfo) _buildSampleDataBanner(),
          
          // Reports list
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _buildSectionHeader(context.tr('business_reports.available_analyses'), context),
                const SizedBox(height: 16),

                // Appointments report - FIRST PRIORITY
                _buildReportCard(
                  title: context.tr('business_reports.appointments_report_title'),
                  subtitle: context.tr('business_reports.appointments_report_subtitle'),
                  icon: Icons.event_note,
                  color: Colors.blue,
                  onTap: () => _navigateToAppointmentsReport(),
                ),

                const SizedBox(height: 12),

                // Pet reports
                _buildReportCard(
                  title: context.tr('business_reports.pets_by_size_breed_title'),
                  subtitle: context.tr('business_reports.pets_by_size_breed_subtitle'),
                  icon: FontAwesomeIcons.dog,
                  color: Colors.lightBlue,
                  onTap: () => _navigateToReport(ReportType.petSizeBreed),
                ),
                
                const SizedBox(height: 12),
                
                // Service reports
                _buildReportCard(
                  title: context.tr('business_reports.services_requested_title'),
                  subtitle: context.tr('business_reports.services_requested_subtitle'),
                  icon: Icons.design_services,
                  color: Colors.purple,
                  onTap: () => _navigateToReport(ReportType.serviceRequests),
                ),
                
                const SizedBox(height: 12),
                
                // Client reports
                _buildReportCard(
                  title: context.tr('business_reports.top_clients_title'),
                  subtitle: context.tr('business_reports.top_clients_subtitle'),
                  icon: Icons.people,
                  color: Colors.orange,
                  onTap: () => _navigateToReport(ReportType.topClients),
                ),
                
                const SizedBox(height: 12),
                
                // Staff performance
                _buildReportCard(
                  title: context.tr('business_reports.staff_performance_title'),
                  subtitle: context.tr('business_reports.staff_performance_subtitle'),
                  icon: Icons.person_search,
                  color: Colors.green,
                  onTap: () => _navigateToReport(ReportType.staffPerformance),
                ),
                
                const SizedBox(height: 12),
                
                // Revenue reports
                _buildReportCard(
                  title: context.tr('business_reports.revenue_title'),
                  subtitle: context.tr('business_reports.revenue_subtitle'),
                  icon: Icons.trending_up,
                  color: Colors.teal,
                  onTap: () => _navigateToReport(ReportType.revenue),
                ),
                
                const SizedBox(height: 24),
                
                // Quick actions section
                _buildSectionHeader(context.tr('business_reports.quick_actions'), context),
                const SizedBox(height: 16),
                
                _buildQuickActionCard(
                  title: context.tr('business_reports.sample_data_preview_title'),
                  subtitle: context.tr('business_reports.sample_data_preview_subtitle'),
                  icon: Icons.preview,
                  color: Theme.of(context).colorScheme.primary,
                  onTap: _showSampleDataPreview,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSampleDataBanner() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber[50],
        border: Border.all(color: Colors.amber[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.info, color: Colors.amber[700]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('business_reports.sample_data_banner_title'),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.amber[800],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  context.tr('business_reports.sample_data_banner_message'),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.amber[700],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 20),
            onPressed: () => setState(() => _showSampleDataInfo = false),
            color: Colors.amber[700],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, BuildContext context) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildReportCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              Icon(icon, color: color, size: 28),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: color.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToReport(ReportType reportType) {
    Widget screen;
    
    switch (reportType) {
      case ReportType.petSizeBreed:
        screen = const PetReportScreen();
        break;
      case ReportType.serviceRequests:
        screen = const ServiceReportScreen();
        break;
      case ReportType.topClients:
        screen = const ClientReportScreen();
        break;
      case ReportType.staffPerformance:
        screen = const StaffPerformanceScreen();
        break;
      case ReportType.revenue:
        screen = const RevenueReportScreen();
        break;
    }

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }

  void _showSampleDataPreview() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('business_reports.sample_data_dialog_title')),
        content: Text(context.tr('business_reports.sample_data_dialog_content')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('business_reports.sample_data_dialog_understood')),
          ),
        ],
      ),
    );
  }

  void _navigateToAppointmentsReport() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AppointmentsReportScreen(),
      ),
    );
  }

  void _showInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('business_reports.info_dialog_title')),
        content: Text(context.tr('business_reports.info_dialog_content')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('business_reports.info_dialog_close')),
          ),
        ],
      ),
    );
  }
}
