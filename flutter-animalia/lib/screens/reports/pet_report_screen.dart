import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:excel/excel.dart' as excel;
import 'dart:typed_data';

import '../../config/theme/app_theme.dart';
import '../../models/report_data.dart';
import '../../services/reports/reports_service.dart';
import '../../services/sample_data_service.dart';
import '../../services/file_download_service.dart';
import '../../widgets/charts/report_chart_widget.dart';
import '../../widgets/reports/date_range_selector.dart';
import '../../l10n/app_localizations.dart';

class PetReportScreen extends StatefulWidget {
  const PetReportScreen({super.key});

  @override
  State<PetReportScreen> createState() => _PetReportScreenState();
}

class _PetReportScreenState extends State<PetReportScreen> {
  DateRangePreset _selectedPreset = DateRangePreset.lastMonth;
  DateRange? _customRange;
  PetReportData? _reportData;
  bool _isLoading = false;
  bool _showingSampleData = false;

  @override
  void initState() {
    super.initState();
    _loadReportData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('pets_report.title'),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          if (_reportData != null)
            IconButton(
              icon: const Icon(Icons.file_download),
              onPressed: _exportToExcel,
              tooltip: context.tr('pets_report.export_excel_tooltip'),
            ),
          // More visible sample data toggle
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: TextButton.icon(
              onPressed: _toggleSampleData,
              icon: Icon(
                _showingSampleData ? Icons.science : Icons.analytics,
                color: Colors.white,
                size: 18,
              ),
              label: Text(
                _showingSampleData ? context.tr('pets_report.demo_button') : context.tr('pets_report.real_button'),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: TextButton.styleFrom(
                backgroundColor: _showingSampleData
                    ? Colors.orange.withValues(alpha: 0.8)
                    : Colors.green.withValues(alpha: 0.8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Sample data indicator
                  if (_showingSampleData) _buildSampleDataIndicator(),
                  
                  // Date range selector
                  DateRangeSelector(
                    selectedPreset: _selectedPreset,
                    customRange: _customRange,
                    onRangeChanged: _onDateRangeChanged,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  if (_reportData != null) ...[
                    // Summary cards
                    _buildSummaryCards(),
                    
                    const SizedBox(height: 24),
                    
                    // Size distribution chart
                    _buildSizeDistributionChart(),
                    
                    const SizedBox(height: 24),
                    
                    // Breed distribution chart
                    _buildBreedDistributionChart(),
                    
                    const SizedBox(height: 24),
                    
                    // Detailed breakdown
                    _buildDetailedBreakdown(),
                  ] else
                    _buildEmptyState(),
                ],
              ),
            ),
    );
  }

  Widget _buildSampleDataIndicator() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDark ? Colors.blue[900]!.withOpacity(0.3) : Colors.blue[50],
        border: Border.all(color: isDark ? Colors.blue[400]! : Colors.blue[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.info, color: Colors.blue[700], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              context.tr('pets_report.sample_data_indicator'),
              style: TextStyle(
                color: Colors.blue[800],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            context.tr('pets_report.total_pets'),
            _reportData!.totalPets.toString(),
            FontAwesomeIcons.dog,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context.tr('pets_report.size_types'),
            _reportData!.sizeBreakdown.length.toString(),
            Icons.straighten,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context.tr('pets_report.different_breeds'),
            _reportData!.breedBreakdown.length.toString(),
            Icons.pets,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? color.withOpacity(0.2) : color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: isDark ? color.withOpacity(0.5) : color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSizeDistributionChart() {
    final chartData = BarChartReportData(
      title: context.tr('pets_report.size_distribution_title'),
      startDate: _getCurrentRange().start,
      endDate: _getCurrentRange().end,
      items: _reportData!.sizeBreakdown,
      xAxisLabel: context.tr('pets_report.size_axis_label'),
      yAxisLabel: context.tr('pets_report.pets_count_axis_label'),
      primaryColor: Colors.blue,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.file_download, size: 20),
              onPressed: () => _exportChart('size'),
              tooltip: context.tr('pets_report.export_chart_tooltip'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ReportChartWidget(data: chartData, height: 250),
      ],
    );
  }

  Widget _buildBreedDistributionChart() {
    final chartData = BarChartReportData(
      title: context.tr('pets_report.breed_distribution_title'),
      startDate: _getCurrentRange().start,
      endDate: _getCurrentRange().end,
      items: _reportData!.breedBreakdown,
      xAxisLabel: context.tr('pets_report.breed_axis_label'),
      yAxisLabel: context.tr('pets_report.pets_count_axis_label'),
      primaryColor: Colors.green,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.file_download, size: 20),
              onPressed: () => _exportChart('breed'),
              tooltip: context.tr('pets_report.export_chart_tooltip'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ReportChartWidget(data: chartData, height: 300),
      ],
    );
  }

  Widget _buildDetailedBreakdown() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: Theme.of(context).brightness == Brightness.dark 
          ? Colors.grey[850] 
          : null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            
            // Size breakdown table
            _buildBreakdownTable(
              context.tr('pets_report.size_breakdown_title'),
              _reportData!.sizeBreakdown,
              _reportData!.totalPets,
            ),
            
            const SizedBox(height: 24),
            
            // Breed breakdown table
            _buildBreakdownTable(
              context.tr('pets_report.top_breeds_title'),
              _reportData!.breedBreakdown.take(5).toList(),
              _reportData!.totalPets,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBreakdownTable(String title, List<BarChartItem> items, int total) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Table(
          columnWidths: const {
            0: FlexColumnWidth(3),
            1: FlexColumnWidth(1),
            2: FlexColumnWidth(1),
          },
          children: [
            TableRow(
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.grey[800] 
                    : Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(context.tr('pets_report.category_column'), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                ),
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(context.tr('pets_report.count_column'), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                ),
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(context.tr('pets_report.percentage_column'), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                ),
              ],
            ),
            ...items.map((item) {
              final percentage = (item.value / total * 100).toStringAsFixed(1);
              return TableRow(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text(item.label),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text(item.value.toInt().toString()),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text('$percentage%'),
                  ),
                ],
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.dog,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            context.tr('pets_report.empty_state_title'),
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.tr('pets_report.empty_state_subtitle'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _onDateRangeChanged(DateRangePreset preset, DateRange range) {
    setState(() {
      _selectedPreset = preset;
      _customRange = preset == DateRangePreset.custom ? range : null;
    });
    _loadReportData();
  }

  void _loadReportData() async {
    setState(() => _isLoading = true);

    try {
      final range = _getCurrentRange();

      if (_showingSampleData) {
        // Simulate loading delay for sample data
        await Future.delayed(const Duration(milliseconds: 500));
        setState(() {
          _reportData = SampleDataService.generatePetReportData(
            startDate: range.start,
            endDate: range.end,
          );
          _isLoading = false;
        });
      } else {
        // Load real data from API
        final response = await ReportsService.getPetReport(
          startDate: range.start,
          endDate: range.end,
        );

        setState(() {
          if (response.success && response.data != null) {
            _reportData = response.data;
          } else {
            _reportData = null;
            // Show error message
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(context.tr('pets_report.error_loading_data').replaceAll('{error}', response.error ?? '')),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _reportData = null;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('pets_report.error_loading_data').replaceAll('{error}', e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleSampleData() {
    setState(() {
      _showingSampleData = !_showingSampleData;
    });
    _loadReportData();
  }

  DateRange _getCurrentRange() {
    return _customRange ?? _selectedPreset.getDateRange();
  }

  void _exportToExcel() async {
    if (_reportData == null) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('pets_report.generating_report'))),
      );

      // Create Excel file directly
      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile[context.tr('pets_report.excel_sheet_title')];

      // Add headers
      sheet.cell(excel.CellIndex.indexByString('A1')).value = excel.TextCellValue(context.tr('pets_report.excel_report_title'));
      sheet.cell(excel.CellIndex.indexByString('A2')).value = excel.TextCellValue(context.tr('pets_report.excel_total_pets').replaceAll('{count}', _reportData!.totalPets.toString()));

      // Add sample data
      sheet.cell(excel.CellIndex.indexByString('A4')).value = excel.TextCellValue(context.tr('pets_report.excel_size_header'));
      sheet.cell(excel.CellIndex.indexByString('B4')).value = excel.TextCellValue(context.tr('pets_report.excel_count_header'));

      int row = 5;
      for (final item in _reportData!.sizeBreakdown) {
        sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue(item.label);
        sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.IntCellValue(item.value.toInt());
        row++;
      }

      // Generate file and download
      final bytes = excelFile.save();
      if (bytes != null) {
        final fileName = context.tr('pets_report.excel_filename');

        await FileDownloadService.downloadFile(
          bytes: Uint8List.fromList(bytes),
          fileName: fileName,
          shareText: context.tr('pets_report.excel_share_text'),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(context.tr('pets_report.report_downloaded_success')), backgroundColor: Colors.green),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('pets_report.error_loading_data').replaceAll('{error}', e.toString())), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _exportChart(String chartType) {
    // TODO: Implement individual chart export
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('pets_report.export_chart_coming_soon').replaceAll('{type}', chartType)),
      ),
    );
  }
}
