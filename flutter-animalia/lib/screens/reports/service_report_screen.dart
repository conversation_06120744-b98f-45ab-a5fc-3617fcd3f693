import 'package:flutter/material.dart';
import 'package:excel/excel.dart' as excel;
import 'dart:typed_data';

import '../../models/report_data.dart';
import '../../services/reports/reports_service.dart';
import '../../services/sample_data_service.dart';
import '../../services/file_download_service.dart';
import '../../widgets/charts/report_chart_widget.dart';
import '../../widgets/reports/date_range_selector.dart';
import '../../l10n/app_localizations.dart';

class ServiceReportScreen extends StatefulWidget {
  const ServiceReportScreen({super.key});

  @override
  State<ServiceReportScreen> createState() => _ServiceReportScreenState();
}

class _ServiceReportScreenState extends State<ServiceReportScreen> {
  DateRangePreset _selectedPreset = DateRangePreset.lastMonth;
  DateRange? _customRange;
  ServiceReportData? _reportData;
  bool _isLoading = false;
  bool _showingSampleData = false;
  bool _showRevenue = false; // Toggle between requests and revenue

  @override
  void initState() {
    super.initState();
    _loadReportData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('services_report.title'),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          if (_reportData != null)
            IconButton(
              icon: const Icon(Icons.file_download),
              onPressed: _exportToExcel,
              tooltip: context.tr('services_report.export_excel_tooltip'),
            ),
          // More visible sample data toggle
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: TextButton.icon(
              onPressed: _toggleSampleData,
              icon: Icon(
                _showingSampleData ? Icons.science : Icons.analytics,
                color: Colors.white,
                size: 18,
              ),
              label: Text(
                _showingSampleData ? context.tr('services_report.demo_button') : context.tr('services_report.real_button'),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: TextButton.styleFrom(
                backgroundColor: _showingSampleData
                    ? Colors.orange.withValues(alpha: 0.8)
                    : Colors.green.withValues(alpha: 0.8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ?  Center(child: CircularProgressIndicator(color: Theme.of(context).primaryColor))
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Sample data indicator
                  if (_showingSampleData) _buildSampleDataIndicator(),
                  
                  // Date range selector
                  DateRangeSelector(
                    selectedPreset: _selectedPreset,
                    customRange: _customRange,
                    onRangeChanged: _onDateRangeChanged,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  if (_reportData != null) ...[
                    // Summary cards
                    _buildSummaryCards(),
                    
                    const SizedBox(height: 24),
                    
                    // Chart toggle
                    _buildChartToggle(),
                    
                    const SizedBox(height: 16),
                    
                    // Main chart
                    _buildMainChart(),
                    
                    const SizedBox(height: 24),
                    
                    // Service performance table
                    _buildServicePerformanceTable(),
                  ] else
                    _buildEmptyState(),
                ],
              ),
            ),
    );
  }

  Widget _buildSampleDataIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple[50],
        border: Border.all(color: Colors.purple[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.info, color: Colors.purple[700], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              context.tr('services_report.sample_data_indicator'),
              style: TextStyle(
                color: Colors.purple[800],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            context.tr('services_report.total_services'),
            _reportData!.totalServices.toString(),
            Icons.design_services,
            Colors.purple,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context.tr('services_report.total_revenue'),
            '${_reportData!.totalRevenue.toStringAsFixed(0)} RON',
            Icons.attach_money,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context.tr('services_report.popular_service'),
            _getMostPopularService(),
            Icons.star,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: _isLongText(value) ? 16 : 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildChartToggle() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.bar_chart,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              context.tr('services_report.chart_type_label'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const Spacer(),
            SegmentedButton<bool>(
              segments: [
                ButtonSegment<bool>(
                  value: false,
                  label: Text(context.tr('services_report.requests_button')),
                  icon: const Icon(Icons.format_list_numbered, size: 16),
                ),
                ButtonSegment<bool>(
                  value: true,
                  label: Text(context.tr('services_report.revenue_button')),
                  icon: const Icon(Icons.attach_money, size: 16),
                ),
              ],
              selected: {_showRevenue},
              onSelectionChanged: (Set<bool> selection) {
                setState(() {
                  _showRevenue = selection.first;
                });
              },
              style: SegmentedButton.styleFrom(
                selectedForegroundColor: Colors.white,
                selectedBackgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainChart() {
    final items = _showRevenue ? _reportData!.serviceRevenue : _reportData!.serviceRequests;
    final title = _showRevenue ? context.tr('services_report.revenue_chart_title') : context.tr('services_report.requests_chart_title');
    final yAxisLabel = _showRevenue ? context.tr('services_report.revenue_axis_label') : context.tr('services_report.requests_axis_label');
    final color = _showRevenue ? Colors.green : Colors.purple;

    final chartData = BarChartReportData(
      title: title,
      startDate: _getCurrentRange().start,
      endDate: _getCurrentRange().end,
      items: items,
      xAxisLabel: context.tr('services_report.service_axis_label'),
      yAxisLabel: yAxisLabel,
      primaryColor: color,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.file_download, size: 20),
              onPressed: () => _exportChart(_showRevenue ? 'revenue' : 'requests'),
              tooltip: context.tr('services_report.export_chart_tooltip'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ReportChartWidget(data: chartData, height: 350),
      ],
    );
  }

  Widget _buildServicePerformanceTable() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('services_report.performance_table_title'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Table(
              columnWidths: const {
                0: FlexColumnWidth(2.5),
                1: FlexColumnWidth(1.5),
                2: FlexColumnWidth(1.5),
                3: FlexColumnWidth(1.3),
              },
              children: [
                TableRow(
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark 
                        ? Colors.grey[800] 
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Text(context.tr('services_report.service_column'), style: const TextStyle(fontWeight: FontWeight.bold)),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Text(context.tr('services_report.requests_column'), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Text(context.tr('services_report.revenue_column'), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Text(context.tr('services_report.percentage_column'), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                    ),
                  ],
                ),
                ..._reportData!.serviceRequests.asMap().entries.map((entry) {
                  final index = entry.key;
                  final request = entry.value;
                  final revenue = _reportData!.serviceRevenue[index];
                  final percentage = (revenue.value / _reportData!.totalRevenue * 100).toStringAsFixed(1);
                  
                  return TableRow(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text(request.label),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text(request.value.toInt().toString()),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text('${revenue.value.toStringAsFixed(0)} RON'),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text('$percentage%'),
                      ),
                    ],
                  );
                }).toList(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.design_services,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            context.tr('services_report.empty_state_title'),
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.tr('services_report.empty_state_subtitle'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getMostPopularService() {
    if (_reportData == null || _reportData!.serviceRequests.isEmpty) {
      return 'N/A';
    }
    
    final mostPopular = _reportData!.serviceRequests.reduce(
      (a, b) => a.value > b.value ? a : b,
    );
    
    return mostPopular.label.length > 15 
        ? '${mostPopular.label.substring(0, 15)}...'
        : mostPopular.label;
  }

  bool _isLongText(String text) {
    return text.length > 12;
  }

  void _onDateRangeChanged(DateRangePreset preset, DateRange range) {
    setState(() {
      _selectedPreset = preset;
      _customRange = preset == DateRangePreset.custom ? range : null;
    });
    _loadReportData();
  }

  void _loadReportData() async {
    setState(() => _isLoading = true);

    try {
      final range = _getCurrentRange();

      if (_showingSampleData) {
        // Simulate loading delay for sample data
        await Future.delayed(const Duration(milliseconds: 500));
        setState(() {
          _reportData = SampleDataService.generateServiceReportData(
            startDate: range.start,
            endDate: range.end,
          );
          _isLoading = false;
        });
      } else {
        // Load real data from API
        final response = await ReportsService.getServiceReport(
          startDate: range.start,
          endDate: range.end,
        );

        setState(() {
          if (response.success && response.data != null) {
            _reportData = response.data;
          } else {
            _reportData = null;
            // Show error message
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(context.tr('services_report.error_loading_data').replaceAll('{error}', response.error ?? '')),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _reportData = null;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('services_report.error_loading_data').replaceAll('{error}', e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleSampleData() {
    setState(() {
      _showingSampleData = !_showingSampleData;
    });
    _loadReportData();
  }

  DateRange _getCurrentRange() {
    return _customRange ?? _selectedPreset.getDateRange();
  }

  void _exportToExcel() async {
    if (_reportData == null) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('services_report.generating_report'))),
      );

      // Create Excel file directly
      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile[context.tr('services_report.excel_sheet_title')];

      // Add headers
      sheet.cell(excel.CellIndex.indexByString('A1')).value = excel.TextCellValue(context.tr('services_report.excel_sheet_title'));
      sheet.cell(excel.CellIndex.indexByString('A2')).value = excel.TextCellValue(context.tr('services_report.excel_total_revenue').replaceAll('{amount}', _reportData!.totalRevenue.toStringAsFixed(2)));

      // Add sample data
      sheet.cell(excel.CellIndex.indexByString('A4')).value = excel.TextCellValue(context.tr('services_report.excel_service_header'));
      sheet.cell(excel.CellIndex.indexByString('B4')).value = excel.TextCellValue(context.tr('services_report.excel_requests_header'));
      sheet.cell(excel.CellIndex.indexByString('C4')).value = excel.TextCellValue(context.tr('services_report.excel_revenue_header'));

      int row = 5;
      for (int i = 0; i < _reportData!.serviceRequests.length; i++) {
        final request = _reportData!.serviceRequests[i];
        final revenue = _reportData!.serviceRevenue[i];
        sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue(request.label);
        sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.IntCellValue(request.value.toInt());
        sheet.cell(excel.CellIndex.indexByString('C$row')).value = excel.DoubleCellValue(revenue.value);
        row++;
      }

      // Generate file and download
      final bytes = excelFile.save();
      if (bytes != null) {
        final fileName = context.tr('services_report.excel_filename');

        await FileDownloadService.downloadFile(
          bytes: Uint8List.fromList(bytes),
          fileName: fileName,
          shareText: context.tr('services_report.excel_share_text'),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(context.tr('services_report.report_downloaded_success')), backgroundColor: Colors.green),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('services_report.error_loading_data').replaceAll('{error}', e.toString())), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _exportChart(String chartType) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('services_report.export_chart_coming_soon').replaceAll('{type}', chartType)),
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }
}
