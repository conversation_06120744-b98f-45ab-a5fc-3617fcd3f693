import 'package:flutter/material.dart';
import 'package:excel/excel.dart' as excel;
import 'dart:typed_data';

import '../../config/theme/app_theme.dart';
import '../../models/report_data.dart';
import '../../services/reports/reports_service.dart';
import '../../services/sample_data_service.dart';
import '../../services/file_download_service.dart';
import '../../widgets/charts/report_chart_widget.dart';
import '../../widgets/reports/date_range_selector.dart';
import '../../l10n/app_localizations.dart';

class RevenueReportScreen extends StatefulWidget {
  const RevenueReportScreen({super.key});

  @override
  State<RevenueReportScreen> createState() => _RevenueReportScreenState();
}

class _RevenueReportScreenState extends State<RevenueReportScreen> {
  DateRangePreset _selectedPreset = DateRangePreset.lastMonth;
  DateRange? _customRange;
  RevenueReportData? _reportData;
  bool _isLoading = false;
  bool _showingSampleData = false;

  @override
  void initState() {
    super.initState();
    _loadReportData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('revenue_report.title'),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          if (_reportData != null)
            IconButton(
              icon: const Icon(Icons.file_download),
              onPressed: _exportToExcel,
              tooltip: context.tr('revenue_report.export_excel'),
            ),
          // More visible sample data toggle
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: TextButton.icon(
              onPressed: _toggleSampleData,
              icon: Icon(
                _showingSampleData ? Icons.science : Icons.analytics,
                color: Colors.white,
                size: 18,
              ),
              label: Text(
                _showingSampleData ? context.tr('revenue_report.demo') : context.tr('revenue_report.real'),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: TextButton.styleFrom(
                backgroundColor: _showingSampleData
                    ? Colors.orange.withValues(alpha: 0.8)
                    : Colors.green.withValues(alpha: 0.8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Sample data indicator
                  if (_showingSampleData) _buildSampleDataIndicator(),
                  
                  // Date range selector
                  DateRangeSelector(
                    selectedPreset: _selectedPreset,
                    customRange: _customRange,
                    onRangeChanged: _onDateRangeChanged,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  if (_reportData != null) ...[
                    // Revenue overview
                    _buildRevenueOverview(),
                    
                    const SizedBox(height: 24),
                    
                    // Daily revenue chart
                    _buildDailyRevenueChart(),
                    
                    const SizedBox(height: 24),
                    
                    // Revenue by service chart
                    _buildRevenueByServiceChart(),
                    
                    const SizedBox(height: 24),
                    
                    // Revenue insights
                    _buildRevenueInsights(),
                  ] else
                    _buildEmptyState(),
                ],
              ),
            ),
    );
  }

  Widget _buildSampleDataIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.teal[50],
        border: Border.all(color: Colors.teal[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.info, color: Colors.teal[700], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              context.tr('revenue_report.sample_data_message'),
              style: TextStyle(
                color: Colors.teal[800],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRevenueOverview() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
             Text(
              context.tr('revenue_report.revenue_summary'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            
            // Revenue metrics grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildMetricCard(
                  context.tr('revenue_report.total_revenue'),
                  '${_reportData!.totalRevenue.toStringAsFixed(0)} RON',
                  Icons.attach_money,
                  Colors.green,
                ),
                _buildMetricCard(
                  context.tr('revenue_report.daily_average'),
                  '${_reportData!.averageDailyRevenue.toStringAsFixed(0)} RON',
                  Icons.today,
                  Colors.blue,
                ),
                _buildMetricCard(
                  context.tr('revenue_report.growth_rate'),
                  '${(_reportData!.growthRate * 100).toStringAsFixed(1)}%',
                  _reportData!.growthRate >= 0 ? Icons.trending_up : Icons.trending_down,
                  _reportData!.growthRate >= 0 ? Colors.green : Colors.red,
                ),
                _buildMetricCard(
                  context.tr('revenue_report.active_days'),
                  '${_reportData!.dailyRevenue.length}',
                  Icons.calendar_today,
                  Colors.orange,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Growth indicator
            _buildGrowthIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 11,
              color: color.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildGrowthIndicator() {
    final isPositiveGrowth = _reportData!.growthRate >= 0;
    final growthText = isPositiveGrowth ? context.tr('revenue_report.growth') : context.tr('revenue_report.decline');
    final growthPercentage = (_reportData!.growthRate.abs() * 100).toStringAsFixed(1);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isPositiveGrowth ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isPositiveGrowth ? Colors.green[300]! : Colors.red[300]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isPositiveGrowth ? Icons.trending_up : Icons.trending_down,
            color: isPositiveGrowth ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              context.tr('revenue_report.growth_comparison').replaceAll('{growth}', growthText).replaceAll('{percentage}', growthPercentage),
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: isPositiveGrowth ? Colors.green[800] : Colors.red[800],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDailyRevenueChart() {
    final chartData = LineChartReportData(
      title: context.tr('revenue_report.daily_revenue_evolution'),
      startDate: _getCurrentRange().start,
      endDate: _getCurrentRange().end,
      points: _reportData!.dailyRevenue,
      xAxisLabel: context.tr('revenue_report.date_label'),
      yAxisLabel: context.tr('revenue_report.revenue_label'),
      lineColor: Colors.teal,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.file_download, size: 20),
              onPressed: () => _exportChart('daily_revenue'),
              tooltip: context.tr('revenue_report.export_chart'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ReportChartWidget(data: chartData, height: 300),
      ],
    );
  }

  Widget _buildRevenueByServiceChart() {
    final chartData = BarChartReportData(
      title: context.tr('revenue_report.revenue_by_service'),
      startDate: _getCurrentRange().start,
      endDate: _getCurrentRange().end,
      items: _reportData!.revenueByService,
      xAxisLabel: context.tr('revenue_report.service_label'),
      yAxisLabel: context.tr('revenue_report.revenue_label'),
      primaryColor: Colors.green,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.file_download, size: 20),
              onPressed: () => _exportChart('revenue_by_service'),
              tooltip: context.tr('revenue_report.export_chart'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ReportChartWidget(data: chartData, height: 300),
      ],
    );
  }

  Widget _buildRevenueInsights() {
    // Check if data is available
    if (_reportData!.dailyRevenue.isEmpty || _reportData!.revenueByService.isEmpty) {
      return const SizedBox.shrink();
    }
    
    final bestDay = _reportData!.dailyRevenue.reduce(
      (a, b) => a.value > b.value ? a : b,
    );
    final worstDay = _reportData!.dailyRevenue.reduce(
      (a, b) => a.value < b.value ? a : b,
    );
    final topService = _reportData!.revenueByService.reduce(
      (a, b) => a.value > b.value ? a : b,
    );

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
             Text(
              context.tr('revenue_report.analysis_trends'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            
            // Best performing day
            _buildInsightItem(
              context.tr('revenue_report.best_day'),
              '${bestDay.value.toStringAsFixed(0)} RON',
              '${bestDay.date.day}/${bestDay.date.month}/${bestDay.date.year}',
              Icons.star,
              Colors.green,
            ),
            
            const SizedBox(height: 12),
            
            // Worst performing day
            _buildInsightItem(
              context.tr('revenue_report.worst_day'),
              '${worstDay.value.toStringAsFixed(0)} RON',
              '${worstDay.date.day}/${worstDay.date.month}/${worstDay.date.year}',
              Icons.trending_down,
              Colors.orange,
            ),
            
            const SizedBox(height: 12),
            
            // Top service
            _buildInsightItem(
              context.tr('revenue_report.top_service'),
              '${topService.value.toStringAsFixed(0)} RON',
              topService.label,
              Icons.emoji_events,
              Colors.blue,
            ),
            
            const SizedBox(height: 16),
            
            // Revenue breakdown table
            _buildRevenueBreakdownTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightItem(String title, String value, String subtitle, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: color.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRevenueBreakdownTable() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('revenue_report.revenue_distribution'),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Table(
          columnWidths: const {
            0: FlexColumnWidth(3),
            1: FlexColumnWidth(2),
            2: FlexColumnWidth(1.5),
          },
          children: [
            TableRow(
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.grey[800] 
                    : Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              children: [
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(context.tr('revenue_report.service_column'), style: const TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(context.tr('revenue_report.revenue_column'), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                ),
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(context.tr('revenue_report.percent_column'), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                ),
              ],
            ),
            ..._reportData!.revenueByService.map((service) {
              final percentage = (service.value / _reportData!.totalRevenue * 100).toStringAsFixed(1);
              return TableRow(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text(service.label),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text('${service.value.toStringAsFixed(0)} RON'),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text('$percentage%'),
                  ),
                ],
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.trending_up,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            context.tr('revenue_report.no_data_available'),
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.tr('revenue_report.no_data_suggestion'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _onDateRangeChanged(DateRangePreset preset, DateRange range) {
    setState(() {
      _selectedPreset = preset;
      _customRange = preset == DateRangePreset.custom ? range : null;
    });
    _loadReportData();
  }

  void _loadReportData() async {
    setState(() => _isLoading = true);

    try {
      final range = _getCurrentRange();

      if (_showingSampleData) {
        // Simulate loading delay for sample data
        await Future.delayed(const Duration(milliseconds: 500));
        setState(() {
          _reportData = SampleDataService.generateRevenueReportData(
            startDate: range.start,
            endDate: range.end,
          );
          _isLoading = false;
        });
      } else {
        // Load real data from API
        final response = await ReportsService.getRevenueReport(
          startDate: range.start,
          endDate: range.end,
        );

        setState(() {
          if (response.success && response.data != null) {
            _reportData = response.data;
          } else {
            _reportData = null;
            // Show error message
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(context.tr('revenue_report.loading_error').replaceAll('{error}', response.error ?? '')),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _reportData = null;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('revenue_report.loading_error').replaceAll('{error}', e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleSampleData() {
    setState(() {
      _showingSampleData = !_showingSampleData;
    });
    _loadReportData();
  }

  DateRange _getCurrentRange() {
    return _customRange ?? _selectedPreset.getDateRange();
  }

  void _exportToExcel() async {
    if (_reportData == null) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('revenue_report.generating_report'))),
      );

      // Create Excel file directly
      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile['Raport Venituri'];

      // Add headers
      sheet.cell(excel.CellIndex.indexByString('A1')).value = excel.TextCellValue(context.tr('revenue_report.title'));
      sheet.cell(excel.CellIndex.indexByString('A2')).value = excel.TextCellValue(context.tr('revenue_report.excel_total_revenue').replaceAll('{amount}', _reportData!.totalRevenue.toStringAsFixed(2)));

      // Add sample data
      sheet.cell(excel.CellIndex.indexByString('A4')).value = excel.TextCellValue(context.tr('revenue_report.service_column'));
      sheet.cell(excel.CellIndex.indexByString('B4')).value = excel.TextCellValue(context.tr('revenue_report.revenue_column'));

      int row = 5;
      for (final item in _reportData!.revenueByService) {
        sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue(item.label);
        sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.DoubleCellValue(item.value);
        row++;
      }

      // Generate file and download
      final bytes = excelFile.save();
      if (bytes != null) {
        final fileName = context.tr('revenue_report.excel_filename');

        await FileDownloadService.downloadFile(
          bytes: Uint8List.fromList(bytes),
          fileName: fileName,
          shareText: context.tr('revenue_report.excel_share_text'),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(context.tr('revenue_report.excel_success')), backgroundColor: Colors.green),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('revenue_report.excel_error').replaceAll('{error}', e.toString())), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _exportChart(String chartType) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('revenue_report.chart_export_coming_soon').replaceAll('{type}', chartType)),
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }
}
