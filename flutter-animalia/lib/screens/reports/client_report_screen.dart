import 'package:flutter/material.dart';
import 'package:excel/excel.dart' as excel;
import 'dart:typed_data';

import '../../config/theme/app_theme.dart';
import '../../models/report_data.dart';
import '../../services/reports/reports_service.dart';
import '../../services/sample_data_service.dart';
import '../../services/file_download_service.dart';
import '../../widgets/charts/report_chart_widget.dart';
import '../../widgets/reports/date_range_selector.dart';
import '../../l10n/app_localizations.dart';

class ClientReportScreen extends StatefulWidget {
  const ClientReportScreen({super.key});

  @override
  State<ClientReportScreen> createState() => _ClientReportScreenState();
}

class _ClientReportScreenState extends State<ClientReportScreen> {
  DateRangePreset _selectedPreset = DateRangePreset.lastMonth;
  DateRange? _customRange;
  ClientReportData? _reportData;
  bool _isLoading = false;
  bool _showingSampleData = false;

  @override
  void initState() {
    super.initState();
    _loadReportData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('clients_report.title'),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          if (_reportData != null)
            IconButton(
              icon: const Icon(Icons.file_download),
              onPressed: _exportToExcel,
              tooltip: context.tr('clients_report.export_excel_tooltip'),
            ),
          // More visible sample data toggle
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: TextButton.icon(
              onPressed: _toggleSampleData,
              icon: Icon(
                _showingSampleData ? Icons.science : Icons.analytics,
                color: Colors.white,
                size: 18,
              ),
              label: Text(
                _showingSampleData ? context.tr('clients_report.demo_button') : context.tr('clients_report.real_button'),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: TextButton.styleFrom(
                backgroundColor: _showingSampleData
                    ? Colors.orange.withValues(alpha: 0.8)
                    : Colors.green.withValues(alpha: 0.8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Sample data indicator
                  if (_showingSampleData) _buildSampleDataIndicator(),
                  
                  // Date range selector
                  DateRangeSelector(
                    selectedPreset: _selectedPreset,
                    customRange: _customRange,
                    onRangeChanged: _onDateRangeChanged,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  if (_reportData != null) ...[
                    // Summary cards
                    _buildSummaryCards(),
                    
                    const SizedBox(height: 24),
                    
                    // Top clients chart
                    _buildTopClientsChart(),
                    
                    const SizedBox(height: 24),
                    
                    // Client insights
                    _buildClientInsights(),
                    
                    const SizedBox(height: 24),
                    
                    // Detailed client list
                    _buildDetailedClientList(),
                  ] else
                    _buildEmptyState(),
                ],
              ),
            ),
    );
  }

  Widget _buildSampleDataIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        border: Border.all(color: Colors.orange[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.info, color: Colors.orange[700], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              context.tr('clients_report.sample_data_indicator'),
              style: TextStyle(
                color: Colors.orange[800],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            context.tr('clients_report.total_clients'),
            _reportData!.totalClients.toString(),
            Icons.people,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context.tr('clients_report.average_spending'),
            '${_reportData!.averageSpending.toStringAsFixed(0)} RON',
            Icons.attach_money,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context.tr('clients_report.new_clients'),
            _reportData!.newClients.toString(),
            Icons.person_add,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTopClientsChart() {
    final chartData = BarChartReportData(
      title: context.tr('clients_report.top_clients_chart_title'),
      startDate: _getCurrentRange().start,
      endDate: _getCurrentRange().end,
      items: _reportData!.topClients,
      xAxisLabel: context.tr('clients_report.client_axis_label'),
      yAxisLabel: context.tr('clients_report.spending_axis_label'),
      primaryColor: Colors.orange,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              context.tr('clients_report.top_clients_section_title'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.file_download, size: 20),
              onPressed: () => _exportChart('top_clients'),
              tooltip: context.tr('clients_report.export_chart_tooltip'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ReportChartWidget(data: chartData, height: 350),
      ],
    );
  }

  Widget _buildClientInsights() {
    final returningPercentage = (_reportData!.returningClients / _reportData!.totalClients * 100);
    final newPercentage = (_reportData!.newClients / _reportData!.totalClients * 100);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('clients_report.client_analysis_title'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildInsightCard(
                    context.tr('clients_report.returning_clients'),
                    '${_reportData!.returningClients}',
                    '${returningPercentage.toStringAsFixed(1)}%',
                    Icons.repeat,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildInsightCard(
                    context.tr('clients_report.new_clients'),
                    '${_reportData!.newClients}',
                    '${newPercentage.toStringAsFixed(1)}%',
                    Icons.person_add,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Client retention insights
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.grey[800] 
                    : Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).brightness == Brightness.dark 
                      ? Colors.grey[600]! 
                      : Colors.grey[300]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    returningPercentage > 70 ? Icons.trending_up : Icons.trending_down,
                    color: returningPercentage > 70 ? Colors.green : Colors.orange,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      returningPercentage > 70 
                          ? context.tr('clients_report.retention_excellent')
                          : context.tr('clients_report.retention_improvement'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).brightness == Brightness.dark 
                            ? Colors.grey[300] 
                            : Colors.grey[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightCard(String title, String value, String percentage, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            percentage,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedClientList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('clients_report.detailed_clients_title'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ...(_reportData!.topClients.asMap().entries.map((entry) {
              final index = entry.key;
              final client = entry.value;
              final isTop3 = index < 3;
              
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isTop3 ? Colors.amber[50] : Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isTop3 ? Colors.amber[300]! : Colors.grey[300]!,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: _getPositionColor(index),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style:  TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            client.label,
                            style:  TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.black,
                            ),
                          ),
                          Text(
                            context.tr('clients_report.spending_label').replaceAll('{amount}', client.value.toStringAsFixed(0)),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (isTop3)
                      Icon(
                        index == 0 ? Icons.emoji_events : Icons.star,
                        color: _getPositionColor(index),
                        size: 20,
                      ),
                  ],
                ),
              );
            }).toList()),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            context.tr('clients_report.empty_state_title'),
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.tr('clients_report.empty_state_subtitle'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getPositionColor(int index) {
    switch (index) {
      case 0:
        return Colors.amber[600]!; // Gold
      case 1:
        return Colors.grey[600]!; // Silver
      case 2:
        return Colors.brown[400]!; // Bronze
      default:
        return Colors.blue[400]!;
    }
  }

  void _onDateRangeChanged(DateRangePreset preset, DateRange range) {
    setState(() {
      _selectedPreset = preset;
      _customRange = preset == DateRangePreset.custom ? range : null;
    });
    _loadReportData();
  }

  void _loadReportData() async {
    setState(() => _isLoading = true);

    try {
      final range = _getCurrentRange();

      if (_showingSampleData) {
        // Simulate loading delay for sample data
        await Future.delayed(const Duration(milliseconds: 500));
        setState(() {
          _reportData = SampleDataService.generateClientReportData(
            startDate: range.start,
            endDate: range.end,
          );
          _isLoading = false;
        });
      } else {
        // Load real data from API
        final response = await ReportsService.getClientReport(
          startDate: range.start,
          endDate: range.end,
        );

        setState(() {
          if (response.success && response.data != null) {
            _reportData = response.data;
          } else {
            _reportData = null;
            // Show error message
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(context.tr('clients_report.error_loading_data').replaceAll('{error}', response.error ?? '')),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _reportData = null;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('clients_report.error_loading_data').replaceAll('{error}', e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleSampleData() {
    setState(() {
      _showingSampleData = !_showingSampleData;
    });
    _loadReportData();
  }

  DateRange _getCurrentRange() {
    return _customRange ?? _selectedPreset.getDateRange();
  }

  void _exportToExcel() async {
    if (_reportData == null) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('clients_report.generating_report'))),
      );

      // Create Excel file directly
      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile[context.tr('clients_report.excel_sheet_title')];

      // Add headers
      sheet.cell(excel.CellIndex.indexByString('A1')).value = excel.TextCellValue(context.tr('clients_report.excel_sheet_title'));
      sheet.cell(excel.CellIndex.indexByString('A2')).value = excel.TextCellValue(context.tr('clients_report.excel_total_clients').replaceAll('{count}', _reportData!.totalClients.toString()));

      // Add sample data
      sheet.cell(excel.CellIndex.indexByString('A4')).value = excel.TextCellValue(context.tr('clients_report.excel_top_clients_header'));
      sheet.cell(excel.CellIndex.indexByString('B4')).value = excel.TextCellValue(context.tr('clients_report.excel_spending_header'));

      int row = 5;
      for (final item in _reportData!.topClients) {
        sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue(item.label);
        sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.DoubleCellValue(item.value);
        row++;
      }

      // Generate file and download
      final bytes = excelFile.save();
      if (bytes != null) {
        final fileName = context.tr('clients_report.excel_filename');

        await FileDownloadService.downloadFile(
          bytes: Uint8List.fromList(bytes),
          fileName: fileName,
          shareText: context.tr('clients_report.excel_share_text'),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(context.tr('clients_report.report_downloaded_success')), backgroundColor: Colors.green),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('clients_report.error_loading_data').replaceAll('{error}', e.toString())), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _exportChart(String chartType) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('clients_report.export_chart_coming_soon').replaceAll('{type}', chartType)),
      ),
    );
  }
}
