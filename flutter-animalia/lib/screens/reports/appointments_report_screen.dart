import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:excel/excel.dart' as excel;
import 'dart:typed_data';

import '../../models/appointment.dart';
import '../../models/report_data.dart';
import '../../services/appointment/appointment_service.dart';
import '../../services/auth/auth_service.dart';
import '../../services/file_download_service.dart';
import '../../widgets/reports/date_range_selector.dart';
import '../../l10n/app_localizations.dart';

class AppointmentsReportScreen extends StatefulWidget {
  const AppointmentsReportScreen({super.key});

  @override
  State<AppointmentsReportScreen> createState() => _AppointmentsReportScreenState();
}

class _AppointmentsReportScreenState extends State<AppointmentsReportScreen> {
  DateRangePreset _selectedPreset = DateRangePreset.lastMonth;
  DateRange? _customRange;
  List<Appointment> _appointments = [];
  bool _isLoading = false;
  bool _showingSampleData = false;

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('appointments_report.title'),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          if (_appointments.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.file_download),
              onPressed: _exportToExcel,
              tooltip: context.tr('appointments_report.export_excel_tooltip'),
            ),
          // Sample data toggle
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: Material(
              color: _showingSampleData ? Colors.orange : Colors.transparent,
              borderRadius: BorderRadius.circular(20),
              child: IconButton(
                icon: Icon(
                  _showingSampleData ? Icons.science : Icons.science_outlined,
                  color: _showingSampleData ? Colors.white : null,
                ),
                onPressed: _toggleSampleData,
                tooltip: _showingSampleData 
                    ? context.tr('appointments_report.use_real_data_tooltip') 
                    : context.tr('appointments_report.use_sample_data_tooltip'),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Date Range Selector
          Container(
            padding: const EdgeInsets.all(16),
            child: DateRangeSelector(
              selectedPreset: _selectedPreset,
              customRange: _customRange,
              onRangeChanged: (preset, range) {
                setState(() {
                  _selectedPreset = preset;
                  _customRange = range;
                });
                _loadAppointments();
              },
            ),
          ),
          
          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _appointments.isEmpty
                    ? _buildEmptyState()
                    : _buildAppointmentsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            context.tr('appointments_report.empty_state_title'),
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.tr('appointments_report.empty_state_subtitle'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentsList() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Card
          _buildSummaryCard(),
          const SizedBox(height: 24),
          
          // Appointments Table
          _buildAppointmentsTable(),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    final totalAppointments = _appointments.length;
    final totalRevenue = _appointments.fold<double>(0, (sum, apt) => sum + apt.totalPrice);
    final completedAppointments = _appointments.where((apt) => apt.status.toLowerCase() == 'completed').length;
    final cancelledAppointments = _appointments.where((apt) => apt.status.toLowerCase() == 'cancelled').length;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('appointments_report.summary_title'),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(child: _buildSummaryItem(context.tr('appointments_report.summary_total'), totalAppointments.toString(), Icons.event)),
                Expanded(child: _buildSummaryItem(context.tr('appointments_report.summary_completed'), completedAppointments.toString(), Icons.check_circle)),
                Expanded(child: _buildSummaryItem(context.tr('appointments_report.summary_cancelled'), cancelledAppointments.toString(), Icons.cancel)),
                Expanded(child: _buildSummaryItem(context.tr('appointments_report.summary_revenue'), '${totalRevenue.toStringAsFixed(0)} RON', Icons.attach_money)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Theme.of(context).colorScheme.primary),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildAppointmentsTable() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('appointments_report.details_title').replaceAll('{count}', _appointments.length.toString()),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columnSpacing: 16,
                columns: [
                  DataColumn(label: Text(context.tr('appointments_report.table_date_time'), style: const TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text(context.tr('appointments_report.table_client'), style: const TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text(context.tr('appointments_report.table_pet'), style: const TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text(context.tr('appointments_report.table_service'), style: const TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text(context.tr('appointments_report.table_staff'), style: const TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text(context.tr('appointments_report.table_status'), style: const TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text(context.tr('appointments_report.table_price'), style: const TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text(context.tr('appointments_report.table_duration'), style: const TextStyle(fontWeight: FontWeight.bold))),
                ],
                rows: _appointments.map((appointment) => _buildAppointmentRow(appointment)).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  DataRow _buildAppointmentRow(Appointment appointment) {
    return DataRow(
      cells: [
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(DateFormat('dd/MM/yyyy').format(appointment.startTime)),
              Text(
                appointment.timeRange,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        DataCell(Text(appointment.clientName)),
        DataCell(Text(appointment.petName)),
        DataCell(Text(appointment.service)),
        DataCell(Text(appointment.assignedGroomer)),
        DataCell(_buildStatusChip(appointment.status)),
        DataCell(Text('${appointment.totalPrice.toStringAsFixed(0)} RON')),
        DataCell(Text('${appointment.totalDuration} min')),
      ],
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String displayStatus;
    
    switch (status.toLowerCase()) {
      case 'completed':
        color = Colors.green;
        displayStatus = context.tr('appointments_report.status_completed');
        break;
      case 'cancelled':
        color = Colors.red;
        displayStatus = context.tr('appointments_report.status_cancelled');
        break;
      case 'confirmed':
        color = Colors.blue;
        displayStatus = context.tr('appointments_report.status_confirmed');
        break;
      case 'scheduled':
        color = Colors.orange;
        displayStatus = context.tr('appointments_report.status_scheduled');
        break;
      default:
        color = Colors.grey;
        displayStatus = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        displayStatus,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Future<void> _loadAppointments() async {
    setState(() => _isLoading = true);

    try {
      final range = _getCurrentRange();
      
      if (_showingSampleData) {
        // Generate sample data
        await Future.delayed(const Duration(milliseconds: 500));
        setState(() {
          _appointments = _generateSampleAppointments(range.start, range.end);
          _isLoading = false;
        });
      } else {
        // Load real data from API
        final response = await AppointmentService.getAppointments(
          startDate: range.start,
          endDate: range.end,
        );

        setState(() {
          if (response.success && response.data != null) {
            _appointments = response.data!;
            // Sort by date/time descending (most recent first)
            _appointments.sort((a, b) => b.startTime.compareTo(a.startTime));
          } else {
            _appointments = [];
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(context.tr('appointments_report.error_loading_appointments').replaceAll('{error}', response.error ?? '')),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _appointments = [];
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('appointments_report.error_loading_appointments').replaceAll('{error}', e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleSampleData() {
    setState(() {
      _showingSampleData = !_showingSampleData;
    });
    _loadAppointments();
  }

  DateRange _getCurrentRange() {
    return _customRange ?? _selectedPreset.getDateRange();
  }

  void _exportToExcel() async {
    if (_appointments.isEmpty) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('appointments_report.generating_report'))),
      );

      // Create Excel file directly
      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile[context.tr('appointments_report.excel_sheet_title')];

      // Add headers
      final range = _getCurrentRange();
      sheet.cell(excel.CellIndex.indexByString('A1')).value = excel.TextCellValue(context.tr('appointments_report.excel_sheet_title'));
      sheet.cell(excel.CellIndex.indexByString('A2')).value = excel.TextCellValue(context.tr('appointments_report.excel_period_label')
          .replaceAll('{start}', DateFormat('dd/MM/yyyy').format(range.start))
          .replaceAll('{end}', DateFormat('dd/MM/yyyy').format(range.end)));
      sheet.cell(excel.CellIndex.indexByString('A3')).value = excel.TextCellValue(context.tr('appointments_report.excel_generated_label')
          .replaceAll('{date}', DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())));
      sheet.cell(excel.CellIndex.indexByString('A4')).value = excel.TextCellValue(context.tr('appointments_report.excel_total_label')
          .replaceAll('{count}', _appointments.length.toString()));

      // Add column headers
      final headers = [
        context.tr('appointments_report.excel_headers.date'),
        context.tr('appointments_report.excel_headers.start_time'),
        context.tr('appointments_report.excel_headers.end_time'),
        context.tr('appointments_report.excel_headers.client'),
        context.tr('appointments_report.excel_headers.phone'),
        context.tr('appointments_report.excel_headers.pet'),
        context.tr('appointments_report.excel_headers.species'),
        context.tr('appointments_report.excel_headers.service'),
        context.tr('appointments_report.excel_headers.staff'),
        context.tr('appointments_report.excel_headers.status'),
        context.tr('appointments_report.excel_headers.price_ron'),
        context.tr('appointments_report.excel_headers.duration_min'),
        context.tr('appointments_report.excel_headers.notes')
      ];
      for (int i = 0; i < headers.length; i++) {
        sheet.cell(excel.CellIndex.indexByString('${String.fromCharCode(65 + i)}6')).value = excel.TextCellValue(headers[i]);
      }

      // Add appointment data
      int row = 7;
      for (final appointment in _appointments) {
        sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue(DateFormat('dd/MM/yyyy').format(appointment.startTime));
        sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.TextCellValue(DateFormat('HH:mm').format(appointment.startTime));
        sheet.cell(excel.CellIndex.indexByString('C$row')).value = excel.TextCellValue(DateFormat('HH:mm').format(appointment.endTime));
        sheet.cell(excel.CellIndex.indexByString('D$row')).value = excel.TextCellValue(appointment.clientName);
        sheet.cell(excel.CellIndex.indexByString('E$row')).value = excel.TextCellValue(appointment.clientPhone);
        sheet.cell(excel.CellIndex.indexByString('F$row')).value = excel.TextCellValue(appointment.petName);
        sheet.cell(excel.CellIndex.indexByString('G$row')).value = excel.TextCellValue(appointment.petSpecies);
        sheet.cell(excel.CellIndex.indexByString('H$row')).value = excel.TextCellValue(appointment.service);
        sheet.cell(excel.CellIndex.indexByString('I$row')).value = excel.TextCellValue(appointment.assignedGroomer);
        sheet.cell(excel.CellIndex.indexByString('J$row')).value = excel.TextCellValue(_getStatusDisplayName(appointment.status));
        sheet.cell(excel.CellIndex.indexByString('K$row')).value = excel.DoubleCellValue(appointment.totalPrice);
        sheet.cell(excel.CellIndex.indexByString('L$row')).value = excel.IntCellValue(appointment.totalDuration);
        sheet.cell(excel.CellIndex.indexByString('M$row')).value = excel.TextCellValue(appointment.notes);
        row++;
      }

      // Generate file and download
      final bytes = excelFile.save();
      if (bytes != null) {
        final fileName = 'raport_programari_${DateFormat('ddMMyyyy').format(range.start)}_${DateFormat('ddMMyyyy').format(range.end)}.xlsx';

        await FileDownloadService.downloadFile(
          bytes: Uint8List.fromList(bytes),
          fileName: fileName,
          shareText: context.tr('appointments_report.excel_sheet_title'),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(context.tr('appointments_report.report_downloaded_success')), backgroundColor: Colors.green),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('appointments_report.error_generating_report').replaceAll('{error}', e.toString())), backgroundColor: Colors.red),
        );
      }
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return context.tr('appointments_report.status_completed');
      case 'cancelled':
        return context.tr('appointments_report.status_cancelled');
      case 'confirmed':
        return context.tr('appointments_report.status_confirmed');
      case 'scheduled':
        return context.tr('appointments_report.status_scheduled');
      case 'in_progress':
        return context.tr('appointments_report.status_in_progress');
      case 'no_show':
        return context.tr('appointments_report.status_no_show');
      default:
        return status;
    }
  }

  List<Appointment> _generateSampleAppointments(DateTime startDate, DateTime endDate) {
    final appointments = <Appointment>[];
    final random = DateTime.now().millisecondsSinceEpoch;

    final clients = ['Ana Popescu', 'Ion Marinescu', 'Maria Ionescu', 'Gheorghe Dumitrescu', 'Elena Stoica'];
    final pets = ['Rex', 'Bella', 'Max', 'Luna', 'Charlie', 'Mia', 'Rocky', 'Zoe'];
    final species = ['Câine', 'Pisică', 'Iepure'];
    final services = ['Tuns și spălat', 'Doar tuns', 'Doar spălat', 'Tăiat unghii', 'Curățat urechi', 'Tratament antiparazitar'];
    final staff = ['Maria Groomer', 'Ana Specialist', 'Ion Veterinar', 'Elena Expert'];
    final statuses = ['completed', 'confirmed', 'cancelled', 'scheduled'];

    DateTime currentDate = startDate;
    int appointmentId = 1;

    while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
      // Generate 2-5 appointments per day
      final appointmentsPerDay = (random + currentDate.day) % 4 + 2;

      for (int i = 0; i < appointmentsPerDay; i++) {
        final startHour = 9 + (i * 2) + ((random + i) % 2);
        final startTime = DateTime(currentDate.year, currentDate.month, currentDate.day, startHour, (random + i * 15) % 60);
        final duration = [60, 90, 120][(random + i) % 3];
        final endTime = startTime.add(Duration(minutes: duration));

        final clientIndex = (random + appointmentId) % clients.length;
        final petIndex = (random + appointmentId * 2) % pets.length;
        final serviceIndex = (random + appointmentId * 3) % services.length;
        final staffIndex = (random + appointmentId * 4) % staff.length;
        final statusIndex = (random + appointmentId * 5) % statuses.length;

        appointments.add(Appointment(
          id: 'apt_$appointmentId',
          clientId: 'client_${clientIndex + 1}',
          clientName: clients[clientIndex],
          clientPhone: '07${(random + clientIndex).toString().padLeft(8, '0').substring(0, 8)}',
          petId: 'pet_${petIndex + 1}',
          petName: pets[petIndex],
          petSpecies: species[(random + petIndex) % species.length],
          service: services[serviceIndex],
          startTime: startTime,
          endTime: endTime,
          status: statuses[statusIndex],
          isPaid: statusIndex == 0, // completed appointments are paid
          assignedGroomer: staff[staffIndex],
          groomerId: 'staff_${staffIndex + 1}',
          totalPrice: [80, 120, 150, 200][(random + serviceIndex) % 4].toDouble(),
          totalDuration: duration,
          notes: i % 3 == 0 ? 'Animal foarte prietenos' : '',
        ));

        appointmentId++;
      }

      currentDate = currentDate.add(const Duration(days: 1));
    }

    // Sort by date/time descending (most recent first)
    appointments.sort((a, b) => b.startTime.compareTo(a.startTime));

    return appointments;
  }
}
