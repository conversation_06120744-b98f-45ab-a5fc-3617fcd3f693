import 'package:flutter/material.dart';
import 'package:excel/excel.dart' as excel;
import 'dart:typed_data';

import '../../models/report_data.dart';
import '../../services/reports/reports_service.dart';
import '../../services/sample_data_service.dart';
import '../../services/staff_service.dart';
import '../../services/file_download_service.dart';
import '../../widgets/charts/report_chart_widget.dart';
import '../../widgets/reports/date_range_selector.dart';
import '../../l10n/app_localizations.dart';

class StaffPerformanceScreen extends StatefulWidget {
  const StaffPerformanceScreen({super.key});

  @override
  State<StaffPerformanceScreen> createState() => _StaffPerformanceScreenState();
}

class _StaffPerformanceScreenState extends State<StaffPerformanceScreen> {
  DateRangePreset _selectedPreset = DateRangePreset.lastMonth;
  DateRange? _customRange;
  StaffPerformanceReportData? _reportData;
  bool _isLoading = false;
  bool _showingSampleData = false;
  
  // Staff selection
  String? _selectedStaffId;
  String _selectedStaffName = '';
  List<StaffResponse> _staffList = [];
  bool _isLoadingStaff = false;

  @override
  void initState() {
    super.initState();
    _loadStaffList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.tr('staff_performance_report.title'),
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          if (_reportData != null)
            IconButton(
              icon: const Icon(Icons.file_download),
              onPressed: _exportToExcel,
              tooltip: context.tr('staff_performance_report.export_excel_tooltip'),
            ),
          // More visible sample data toggle
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: TextButton.icon(
              onPressed: _toggleSampleData,
              icon: Icon(
                _showingSampleData ? Icons.science : Icons.analytics,
                color: Colors.white,
                size: 18,
              ),
              label: Text(
                _showingSampleData ? context.tr('staff_performance_report.demo_button') : context.tr('staff_performance_report.real_button'),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: TextButton.styleFrom(
                backgroundColor: _showingSampleData
                    ? Colors.orange.withValues(alpha: 0.8)
                    : Colors.green.withValues(alpha: 0.8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Data mode indicator

                  // Staff selector
                  _buildStaffSelector(),
                  
                  const SizedBox(height: 16),
                  
                  // Date range selector
                  DateRangeSelector(
                    selectedPreset: _selectedPreset,
                    customRange: _customRange,
                    onRangeChanged: _onDateRangeChanged,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  if (_reportData != null) ...[
                    // Performance overview
                    _buildPerformanceOverview(),

                    // Service breakdown chart
                    _buildServiceBreakdownChart(),
                    // Revenue over time chart
                    _buildRevenueOverTimeChart(),
                    // Detailed metrics
                    _buildDetailedMetrics(),
                  ] else
                    _buildEmptyState(),
                ],
              ),
            ),
    );
  }


  Widget _buildStaffSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.person_search,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr('staff_performance_report.select_team_member'),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _isLoadingStaff
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                  )
                : _staffList.isEmpty
                    ? Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          context.tr('staff_performance_report.no_team_members'),
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    : DropdownButtonFormField<String>(
                        value: _selectedStaffId,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: _staffList.map((staff) {
                          return DropdownMenuItem<String>(
                            value: staff.id,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CircleAvatar(
                                  radius: 16,
                                  child: Text(
                                    staff.name.isNotEmpty ? staff.name[0].toUpperCase() : '?',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        staff.name,
                                        style: const TextStyle(fontWeight: FontWeight.w500),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            final selectedStaff = _staffList.firstWhere((staff) => staff.id == newValue);
                            setState(() {
                              _selectedStaffId = newValue;
                              _selectedStaffName = selectedStaff.name;
                            });
                            _loadReportData();
                          }
                        },
                      ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceOverview() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr('staff_performance_report.performance_title').replaceAll('{name}', _selectedStaffName),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Performance metrics grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildMetricCard(
                  context.tr('staff_performance_report.total_appointments'),
                  _reportData!.totalAppointments.toString(),
                  Icons.event,
                  Colors.blue,
                ),
                _buildMetricCard(
                  context.tr('staff_performance_report.total_revenue'),
                  '${_reportData!.totalRevenue.toStringAsFixed(0)} RON',
                  Icons.attach_money,
                  Colors.green,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Success rate indicator
            _buildSuccessRateIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 11,
              color: color.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessRateIndicator() {
    final successRate = _reportData!.completedAppointments / _reportData!.totalAppointments;
    final successPercentage = (successRate * 100).toStringAsFixed(1);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark 
            ? Colors.grey[800] 
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark 
              ? Colors.grey[600]! 
              : Colors.grey[300]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            successRate > 0.9 ? Icons.check_circle : Icons.warning,
            color: successRate > 0.9 ? Colors.green : Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.tr('staff_performance_report.success_rate').replaceAll('{percentage}', successPercentage),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  context.tr('staff_performance_report.completed_out_of_total')
                      .replaceAll('{completed}', _reportData!.completedAppointments.toString())
                      .replaceAll('{total}', _reportData!.totalAppointments.toString()),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).brightness == Brightness.dark 
                        ? Colors.grey[300] 
                        : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceBreakdownChart() {
    final chartData = BarChartReportData(
      title: context.tr('staff_performance_report.service_distribution_title'),
      startDate: _getCurrentRange().start,
      endDate: _getCurrentRange().end,
      items: _reportData!.serviceBreakdown,
      xAxisLabel: context.tr('staff_performance_report.service_axis_label'),
      yAxisLabel: context.tr('staff_performance_report.number_axis_label'),
      primaryColor: Colors.green,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children:[
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.file_download, size: 20),
              onPressed: () => _exportChart('services'),
              tooltip: context.tr('staff_performance_report.export_chart_tooltip'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ReportChartWidget(data: chartData, height: 250),
      ],
    );
  }

  Widget _buildRevenueOverTimeChart() {
    final chartData = LineChartReportData(
      title: context.tr('staff_performance_report.revenue_evolution_title'),
      startDate: _getCurrentRange().start,
      endDate: _getCurrentRange().end,
      points: _reportData!.revenueOverTime,
      xAxisLabel: context.tr('staff_performance_report.date_axis_label'),
      yAxisLabel: context.tr('staff_performance_report.revenue_axis_label'),
      lineColor: Colors.blue,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.file_download, size: 20),
              onPressed: () => _exportChart('revenue'),
              tooltip: context.tr('staff_performance_report.export_chart_tooltip'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ReportChartWidget(data: chartData, height: 250),
      ],
    );
  }

  Widget _buildDetailedMetrics() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),

            Table(
              columnWidths: const {
                0: FlexColumnWidth(2),
                1: FlexColumnWidth(1),
              },
              children: [
                _buildTableRow(context.tr('staff_performance_report.completed_appointments'), '${_reportData!.completedAppointments}'),
                _buildTableRow(context.tr('staff_performance_report.cancelled_appointments'), '${_reportData!.cancelledAppointments}'),
                _buildTableRow(context.tr('staff_performance_report.average_revenue_per_appointment'), '${(_reportData!.totalRevenue / _reportData!.totalAppointments).toStringAsFixed(0)} RON'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  TableRow _buildTableRow(String label, String value) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_search,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            context.tr('staff_performance_report.empty_state_title'),
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            context.tr('staff_performance_report.empty_state_subtitle'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _loadStaffList() async {
    setState(() {
      _isLoadingStaff = true;
    });

    try {
      final response = await StaffService.getCurrentSalonStaff(activeOnly: true);

      if (response.success && response.data != null) {
        setState(() {
          _staffList = response.data!.activeStaff;
          _isLoadingStaff = false;

          // Set default selection to first staff member if available
          if (_staffList.isNotEmpty && _selectedStaffId == null) {
            _selectedStaffId = _staffList.first.id;
            _selectedStaffName = _staffList.first.name;
            // Load report data after staff is selected
            _loadReportData();
          }
        });
      } else {
        setState(() {
          _staffList = [];
          _isLoadingStaff = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.tr('staff_performance_report.error_loading_team').replaceAll('{error}', response.error ?? 'Unknown error')),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _staffList = [];
        _isLoadingStaff = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('staff_performance_report.error_loading_team').replaceAll('{error}', e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onDateRangeChanged(DateRangePreset preset, DateRange range) {
    setState(() {
      _selectedPreset = preset;
      _customRange = preset == DateRangePreset.custom ? range : null;
    });
    _loadReportData();
  }

  void _loadReportData() async {
    if (_selectedStaffId == null) return;

    setState(() => _isLoading = true);

    try {
      final range = _getCurrentRange();

      if (_showingSampleData) {
        // Simulate loading delay for sample data
        await Future.delayed(const Duration(milliseconds: 500));
        setState(() {
          _reportData = SampleDataService.generateStaffPerformanceData(
            startDate: range.start,
            endDate: range.end,
            staffId: _selectedStaffId!,
            staffName: _selectedStaffName,
          );
          _isLoading = false;
        });
      } else {
        // Load real data from API
        final response = await ReportsService.getStaffPerformanceReport(
          staffId: _selectedStaffId!,
          startDate: range.start,
          endDate: range.end,
        );

        setState(() {
          if (response.success && response.data != null) {
            _reportData = response.data;
          } else {
            _reportData = null;
            // Show error message
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(context.tr('staff_performance_report.error_loading_data').replaceAll('{error}', response.error ?? '')),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _reportData = null;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('staff_performance_report.error_loading_data').replaceAll('{error}', e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleSampleData() {
    setState(() {
      _showingSampleData = !_showingSampleData;
    });
    _loadReportData();
  }

  DateRange _getCurrentRange() {
    return _customRange ?? _selectedPreset.getDateRange();
  }

  void _exportToExcel() async {
    if (_reportData == null) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr('staff_performance_report.generating_report'))),
      );

      // Create Excel file directly
      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile[context.tr('staff_performance_report.excel_sheet_title')];

      // Add headers
      sheet.cell(excel.CellIndex.indexByString('A1')).value = excel.TextCellValue(context.tr('staff_performance_report.excel_report_title'));
      sheet.cell(excel.CellIndex.indexByString('A2')).value = excel.TextCellValue(context.tr('staff_performance_report.excel_staff_label').replaceAll('{name}', _reportData!.staffName));
      sheet.cell(excel.CellIndex.indexByString('A3')).value = excel.TextCellValue(context.tr('staff_performance_report.excel_total_appointments').replaceAll('{count}', _reportData!.totalAppointments.toString()));

      // Add sample data
      sheet.cell(excel.CellIndex.indexByString('A5')).value = excel.TextCellValue(context.tr('staff_performance_report.excel_service_header'));
      sheet.cell(excel.CellIndex.indexByString('B5')).value = excel.TextCellValue(context.tr('staff_performance_report.excel_number_header'));

      int row = 6;
      for (final item in _reportData!.serviceBreakdown) {
        sheet.cell(excel.CellIndex.indexByString('A$row')).value = excel.TextCellValue(item.label);
        sheet.cell(excel.CellIndex.indexByString('B$row')).value = excel.IntCellValue(item.value.toInt());
        row++;
      }

      // Generate file and download
      final bytes = excelFile.save();
      if (bytes != null) {
        final fileName = context.tr('staff_performance_report.excel_filename').replaceAll('{name}', _reportData!.staffName.replaceAll(' ', '_'));

        await FileDownloadService.downloadFile(
          bytes: Uint8List.fromList(bytes),
          fileName: fileName,
          shareText: context.tr('staff_performance_report.excel_share_text'),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(context.tr('staff_performance_report.report_downloaded_success')), backgroundColor: Colors.green),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('staff_performance_report.error_loading_data').replaceAll('{error}', e.toString())), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _exportChart(String chartType) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('staff_performance_report.export_chart_coming_soon').replaceAll('{type}', chartType)),
      ),
    );
  }
}
