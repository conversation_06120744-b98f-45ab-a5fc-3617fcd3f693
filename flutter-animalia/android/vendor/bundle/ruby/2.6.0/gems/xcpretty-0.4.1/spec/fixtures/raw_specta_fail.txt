=== CLEAN TARGET ReactiveCocoa OF PROJECT ReactiveCocoa WITH CONFIGURATION Debug ===

Check dependencies

Create product structure
/bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework/Versions/A
/bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework/Versions/A/Headers
/bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework/Versions/A/Resources
/bin/ln -s A /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework/Versions/Current
/bin/ln -s Versions/Current/Resources /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework/Resources
/bin/ln -s Versions/Current/Headers /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework/Headers
/bin/ln -s Versions/Current/ReactiveCocoa /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework/ReactiveCocoa

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoa-Prefix-gmozfhieoczwvzcycvwfuuanmwcc/ReactiveCocoa-Prefix.pch.pch
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoa-Prefix-gmozfhieoczwvzcycvwfuuanmwcc/ReactiveCocoa-Prefix.pch.pch

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/ReactiveCocoa.build/Debug/ReactiveCocoa.build
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/ReactiveCocoa.build/Debug/ReactiveCocoa.build

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework.dSYM
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoa.framework.dSYM

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoa-Prefix-alsaekcgtgsqgpecnhdeyegjeoog/ReactiveCocoa-Prefix.pch.pch
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoa-Prefix-alsaekcgtgsqgpecnhdeyegjeoog/ReactiveCocoa-Prefix.pch.pch

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoa-Prefix-hbffkuksupzxqagytwwnzhbdsseb/ReactiveCocoa-Prefix.pch.pch
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoa-Prefix-hbffkuksupzxqagytwwnzhbdsseb/ReactiveCocoa-Prefix.pch.pch

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoa-Prefix-avwnbbroaiarvzccnckskaydvcpf/ReactiveCocoa-Prefix.pch.pch
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoa-Prefix-avwnbbroaiarvzccnckskaydvcpf/ReactiveCocoa-Prefix.pch.pch

=== CLEAN TARGET Expecta OF PROJECT Expecta WITH CONFIGURATION Debug ===

Check dependencies

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPExpect.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPExpect.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/Expecta.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/Expecta.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beFalsy.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beFalsy.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/Expecta-Prefix-errjhdtjxhmuwchdyjcmwlvnvfkl/Expecta-Prefix.pch.pch
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/Expecta-Prefix-errjhdtjxhmuwchdyjcmwlvnvfkl/Expecta-Prefix.pch.pch

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/libExpecta.a
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/libExpecta.a

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPUnsupportedObject.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPUnsupportedObject.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatcherHelpers.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatcherHelpers.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beInstanceOf.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beInstanceOf.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beNil.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beNil.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beKindOf.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beKindOf.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+equal.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+equal.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beSubclassOf.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beSubclassOf.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beTruthy.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beTruthy.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/NSValue+Expecta.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/NSValue+Expecta.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/NSObject+Expecta.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/NSObject+Expecta.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+contain.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+contain.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+conformTo.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+conformTo.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beIdenticalTo.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beIdenticalTo.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beLessThan.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beLessThan.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beInTheRangeOf.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beInTheRangeOf.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beLessThanOrEqualTo.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beLessThanOrEqualTo.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ExpectaSupport.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ExpectaSupport.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beGreaterThan.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beGreaterThan.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beGreaterThanOrEqualTo.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beGreaterThanOrEqualTo.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPFloatTuple.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPFloatTuple.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beCloseTo.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+beCloseTo.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPDoubleTuple.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPDoubleTuple.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+raiseWithReason.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+raiseWithReason.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+haveCountOf.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+haveCountOf.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatcher.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatcher.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+raise.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPMatchers+raise.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPBlockDefinedMatcher.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPBlockDefinedMatcher.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPDefines.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPDefines.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPBackwardCompatibility.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/EXPBackwardCompatibility.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/Expecta.build/Debug/Expecta.build
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/Expecta.build/Debug/Expecta.build

=== CLEAN TARGET Specta OF PROJECT Specta WITH CONFIGURATION Debug ===

Check dependencies

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTExampleGroup.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTExampleGroup.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SpectaUtility.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SpectaUtility.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTExample.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTExample.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/Specta-Prefix.pch
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/Specta-Prefix.pch

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/Specta-Prefix-elhymzxgtgjlpoeyvaenjcznwqhx/Specta-Prefix.pch.pch
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/Specta-Prefix-elhymzxgtgjlpoeyvaenjcznwqhx/Specta-Prefix.pch.pch

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SpectaSupport.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SpectaSupport.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/libSpecta.a
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/libSpecta.a

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/Specta.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/Specta.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTSenTestCase.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTSenTestCase.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTSharedExampleGroups.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTSharedExampleGroups.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTSpec.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTSpec.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SpectaTypes.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SpectaTypes.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTSenTestInvocation.h
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/SPTSenTestInvocation.h

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/Specta.build/Debug/Specta.build
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/Specta.build/Debug/Specta.build

=== CLEAN TARGET ReactiveCocoaTests OF PROJECT ReactiveCocoa WITH CONFIGURATION Debug ===

Check dependencies

Create product structure
/bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoaTests.octest/Contents
/bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoaTests.octest/Contents/MacOS
/bin/mkdir -p /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoaTests.octest/Contents/Resources

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/ReactiveCocoa.build/Debug/ReactiveCocoaTests.build
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/ReactiveCocoa.build/Debug/ReactiveCocoaTests.build

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoaTests-Prefix-crkgsroqkyedntdgjrpxicgazheg/ReactiveCocoaTests-Prefix.pch.pch
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoaTests-Prefix-crkgsroqkyedntdgjrpxicgazheg/ReactiveCocoaTests-Prefix.pch.pch

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoaTests-Prefix-hicqilhvttkxcnfeedwajmztaspz/ReactiveCocoaTests-Prefix.pch.pch
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Intermediates/PrecompiledHeaders/ReactiveCocoaTests-Prefix-hicqilhvttkxcnfeedwajmztaspz/ReactiveCocoaTests-Prefix.pch.pch

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoaTests.octest.dSYM
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoaTests.octest.dSYM

Clean.Remove clean /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoaTests.octest
    builtin-rm -rf /Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Debug/ReactiveCocoaTests.octest

** CLEAN SUCCEEDED **

=== BUILD TARGET ReactiveCocoa OF PROJECT ReactiveCocoa WITH CONFIGURATION Test ===

Check dependencies

=== BUILD TARGET Specta OF PROJECT Specta WITH CONFIGURATION Test ===

Check dependencies

=== BUILD TARGET Expecta OF PROJECT Expecta WITH CONFIGURATION Test ===

Check dependencies

=== BUILD TARGET ReactiveCocoaTests OF PROJECT ReactiveCocoa WITH CONFIGURATION Test ===

Check dependencies

2013-12-10 15:13:09.804 otest[6189:303] *** Enabling asynchronous backtraces
Test Suite '/Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Test/ReactiveCocoaTests.octest(Tests)' started at 2013-12-10 23:13:09 +0000
Test Suite 'NSControlRACSupportSpec' started at 2013-12-10 23:13:09 +0000
Test Case '-[NSControlRACSupportSpec NSButton_RACControlCommandExamples_should_bind_the_control_s_enabledness_to_the_command]' started.
  NSButton RACControlCommandExamples should bind the control's enabledness to the command
Test Case '-[NSControlRACSupportSpec NSButton_RACControlCommandExamples_should_bind_the_control_s_enabledness_to_the_command]' passed (0.091 seconds).
Test Case '-[NSControlRACSupportSpec NSButton_RACControlCommandExamples_should_execute_the_control_s_command_when_activated]' started.
  NSButton RACControlCommandExamples should execute the control's command when activated
Test Case '-[NSControlRACSupportSpec NSButton_RACControlCommandExamples_should_execute_the_control_s_command_when_activated]' passed (0.015 seconds).
Test Case '-[NSControlRACSupportSpec NSButton_RACControlCommandExamples_should_overwrite_an_existing_command_when_setting_a_new_one]' started.
  NSButton RACControlCommandExamples should overwrite an existing command when setting a new one
Test Case '-[NSControlRACSupportSpec NSButton_RACControlCommandExamples_should_overwrite_an_existing_command_when_setting_a_new_one]' passed (0.005 seconds).
Test Case '-[NSControlRACSupportSpec NSTextField_RACControlCommandExamples_should_bind_the_control_s_enabledness_to_the_command]' started.
  NSTextField RACControlCommandExamples should bind the control's enabledness to the command
Test Case '-[NSControlRACSupportSpec NSTextField_RACControlCommandExamples_should_bind_the_control_s_enabledness_to_the_command]' passed (0.045 seconds).
Test Case '-[NSControlRACSupportSpec NSTextField_RACControlCommandExamples_should_execute_the_control_s_command_when_activated]' started.
  NSTextField RACControlCommandExamples should execute the control's command when activated
Test Case '-[NSControlRACSupportSpec NSTextField_RACControlCommandExamples_should_execute_the_control_s_command_when_activated]' passed (0.016 seconds).
Test Case '-[NSControlRACSupportSpec NSTextField_RACControlCommandExamples_should_overwrite_an_existing_command_when_setting_a_new_one]' started.
  NSTextField RACControlCommandExamples should overwrite an existing command when setting a new one
Test Case '-[NSControlRACSupportSpec NSTextField_RACControlCommandExamples_should_overwrite_an_existing_command_when_setting_a_new_one]' passed (0.008 seconds).
Test Case '-[NSControlRACSupportSpec NSTextField__rac_textSignal_should_send_changes]' started.
  NSTextField -rac_textSignal should send changes
Test Case '-[NSControlRACSupportSpec NSTextField__rac_textSignal_should_send_changes]' passed (0.004 seconds).
Test Case '-[NSControlRACSupportSpec NSTextField__rac_textSignal_shouldn_t_give_the_text_field_eternal_life]' started.
  NSTextField -rac_textSignal shouldn't give the text field eternal life
Test Case '-[NSControlRACSupportSpec NSTextField__rac_textSignal_shouldn_t_give_the_text_field_eternal_life]' passed (0.002 seconds).
Test Suite 'NSControlRACSupportSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 8 tests, with 0 failures (0 unexpected) in 0.187 (0.190) seconds
Test Suite 'NSControllerRACSupportSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSControllerRACSupportSpec RACKVOChannel_should_support_NSController]' started.
  RACKVOChannel should support NSController
Test Case '-[NSControllerRACSupportSpec RACKVOChannel_should_support_NSController]' passed (0.001 seconds).
Test Suite 'NSControllerRACSupportSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 1 test, with 0 failures (0 unexpected) in 0.001 (0.002) seconds
Test Suite 'NSEnumeratorRACSequenceAdditionsSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  -rac_sequence RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_return_an_array]' started.
  -rac_sequence RACSequenceExamples should return an array
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  -rac_sequence RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.001 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  -rac_sequence RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.001 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  -rac_sequence RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_be_equal_to_itself]' started.
  -rac_sequence RACSequenceExamples should be equal to itself
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  -rac_sequence RACSequenceExamples should be equal to the same sequence of values
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  -rac_sequence RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  -rac_sequence RACSequenceExamples should return an identical object for -copy
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_archive]' started.
  -rac_sequence RACSequenceExamples should archive
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_fold_right]' started.
  -rac_sequence RACSequenceExamples should fold right
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_fold_right]' passed (0.000 seconds).
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_fold_left]' started.
  -rac_sequence RACSequenceExamples should fold left
Test Case '-[NSEnumeratorRACSequenceAdditionsSpec _rac_sequence_RACSequenceExamples_should_fold_left]' passed (0.000 seconds).
Test Suite 'NSEnumeratorRACSequenceAdditionsSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 12 tests, with 0 failures (0 unexpected) in 0.014 (0.015) seconds
Test Suite 'NSNotificationCenterRACSupportSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSNotificationCenterRACSupportSpec should_send_the_notification_when_posted_by_any_object]' started.
  should send the notification when posted by any object
Test Case '-[NSNotificationCenterRACSupportSpec should_send_the_notification_when_posted_by_any_object]' passed (0.000 seconds).
Test Case '-[NSNotificationCenterRACSupportSpec should_send_the_notification_when_posted_by_a_specific_object]' started.
  should send the notification when posted by a specific object
Test Case '-[NSNotificationCenterRACSupportSpec should_send_the_notification_when_posted_by_a_specific_object]' passed (0.000 seconds).
Test Case '-[NSNotificationCenterRACSupportSpec shouldn_t_strongly_capture_the_notification_object]' started.
  shouldn't strongly capture the notification object
Test Case '-[NSNotificationCenterRACSupportSpec shouldn_t_strongly_capture_the_notification_object]' passed (0.000 seconds).
Test Suite 'NSNotificationCenterRACSupportSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 3 tests, with 0 failures (0 unexpected) in 0.001 (0.001) seconds
Test Suite 'NSObjectRACAppKitBindingsSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_not_send_changes_made_by_the_channel_itself]' started.
  RACViewChannelExamples should not send changes made by the channel itself
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_not_send_changes_made_by_the_channel_itself]' passed (0.002 seconds).
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_not_send_progammatic_changes_made_to_the_view]' started.
  RACViewChannelExamples should not send progammatic changes made to the view
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_not_send_progammatic_changes_made_to_the_view]' passed (0.001 seconds).
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_not_have_a_starting_value]' started.
  RACViewChannelExamples should not have a starting value
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_not_have_a_starting_value]' passed (0.001 seconds).
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_send_view_changes]' started.
  RACViewChannelExamples should send view changes
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_send_view_changes]' passed (0.001 seconds).
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_set_values_on_the_view]' started.
  RACViewChannelExamples should set values on the view
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_set_values_on_the_view]' passed (0.001 seconds).
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_not_echo_changes_back_to_the_channel]' started.
  RACViewChannelExamples should not echo changes back to the channel
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_not_echo_changes_back_to_the_channel]' passed (0.001 seconds).
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_complete_when_the_view_deallocates]' started.
  RACViewChannelExamples should complete when the view deallocates
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_complete_when_the_view_deallocates]' passed (0.001 seconds).
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_deallocate_after_the_view_deallocates]' started.
  RACViewChannelExamples should deallocate after the view deallocates
Test Case '-[NSObjectRACAppKitBindingsSpec RACViewChannelExamples_should_deallocate_after_the_view_deallocates]' passed (0.001 seconds).
Test Suite 'NSObjectRACAppKitBindingsSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 8 tests, with 0 failures (0 unexpected) in 0.009 (0.009) seconds
Test Suite 'NSObjectRACDeallocatingSpecSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSObjectRACDeallocatingSpecSpec _dealloc_swizzling_should_not_invoke_superclass__dealloc_method_twice]' started.
  -dealloc swizzling should not invoke superclass -dealloc method twice
Test Case '-[NSObjectRACDeallocatingSpecSpec _dealloc_swizzling_should_not_invoke_superclass__dealloc_method_twice]' passed (0.000 seconds).
Test Case '-[NSObjectRACDeallocatingSpecSpec _dealloc_swizzling_should_invoke_superclass__dealloc_method_swizzled_in_after_the_subclass]' started.
  -dealloc swizzling should invoke superclass -dealloc method swizzled in after the subclass
Test Case '-[NSObjectRACDeallocatingSpecSpec _dealloc_swizzling_should_invoke_superclass__dealloc_method_swizzled_in_after_the_subclass]' passed (0.000 seconds).
Test Case '-[NSObjectRACDeallocatingSpecSpec _rac_deallocDisposable_should_dispose_of_the_disposable_when_it_is_dealloc_d]' started.
  -rac_deallocDisposable should dispose of the disposable when it is dealloc'd
Test Case '-[NSObjectRACDeallocatingSpecSpec _rac_deallocDisposable_should_dispose_of_the_disposable_when_it_is_dealloc_d]' passed (0.000 seconds).
Test Case '-[NSObjectRACDeallocatingSpecSpec _rac_deallocDisposable_should_be_able_to_use_the_object_during_disposal]' started.
  -rac_deallocDisposable should be able to use the object during disposal
Test Case '-[NSObjectRACDeallocatingSpecSpec _rac_deallocDisposable_should_be_able_to_use_the_object_during_disposal]' passed (0.000 seconds).
Test Case '-[NSObjectRACDeallocatingSpecSpec _rac_willDeallocSignal_should_complete_on_dealloc]' started.
  -rac_willDeallocSignal should complete on dealloc
Test Case '-[NSObjectRACDeallocatingSpecSpec _rac_willDeallocSignal_should_complete_on_dealloc]' passed (0.000 seconds).
Test Case '-[NSObjectRACDeallocatingSpecSpec _rac_willDeallocSignal_should_not_send_anything]' started.
  -rac_willDeallocSignal should not send anything
Test Case '-[NSObjectRACDeallocatingSpecSpec _rac_willDeallocSignal_should_not_send_anything]' passed (0.000 seconds).
Test Case '-[NSObjectRACDeallocatingSpecSpec _rac_willDeallocSignal_should_complete_before_the_object_is_invalid]' started.
  -rac_willDeallocSignal should complete before the object is invalid
Test Case '-[NSObjectRACDeallocatingSpecSpec _rac_willDeallocSignal_should_complete_before_the_object_is_invalid]' passed (0.000 seconds).
Test Suite 'NSObjectRACDeallocatingSpecSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 7 tests, with 0 failures (0 unexpected) in 0.001 (0.001) seconds
Test Suite 'NSObjectRACLiftingSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignals__should_call_the_selector_with_the_value_of_the_signal]' started.
  -rac_liftSelector:withSignals: should call the selector with the value of the signal
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignals__should_call_the_selector_with_the_value_of_the_signal]' passed (0.001 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_call_the_selector_with_the_value_of_the_signal]' started.
  -rac_liftSelector:withSignalsFromArray: should call the selector with the value of the signal
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_call_the_selector_with_the_value_of_the_signal]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_call_the_selector_with_the_value_of_the_signal_unboxed]' started.
  -rac_liftSelector:withSignalsFromArray: should call the selector with the value of the signal unboxed
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_call_the_selector_with_the_value_of_the_signal_unboxed]' passed (0.001 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_with_multiple_arguments]' started.
  -rac_liftSelector:withSignalsFromArray: should work with multiple arguments
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_with_multiple_arguments]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_with_signals_that_immediately_start_with_a_value]' started.
  -rac_liftSelector:withSignalsFromArray: should work with signals that immediately start with a value
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_with_signals_that_immediately_start_with_a_value]' passed (0.001 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_with_signals_that_send_nil]' started.
  -rac_liftSelector:withSignalsFromArray: should work with signals that send nil
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_with_signals_that_send_nil]' passed (0.001 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_with_integers]' started.
  -rac_liftSelector:withSignalsFromArray: should work with integers
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_with_integers]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_convert_between_numeric_types]' started.
  -rac_liftSelector:withSignalsFromArray: should convert between numeric types
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_convert_between_numeric_types]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_with_class_objects]' started.
  -rac_liftSelector:withSignalsFromArray: should work with class objects
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_with_class_objects]' passed (0.001 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_char_pointer]' started.
  -rac_liftSelector:withSignalsFromArray: should work for char pointer
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_char_pointer]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_const_char_pointer]' started.
  -rac_liftSelector:withSignalsFromArray: should work for const char pointer
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_const_char_pointer]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_CGRect]' started.
  -rac_liftSelector:withSignalsFromArray: should work for CGRect
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_CGRect]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_CGSize]' started.
  -rac_liftSelector:withSignalsFromArray: should work for CGSize
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_CGSize]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_CGPoint]' started.
  -rac_liftSelector:withSignalsFromArray: should work for CGPoint
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_CGPoint]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_NSRange]' started.
  -rac_liftSelector:withSignalsFromArray: should work for NSRange
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_NSRange]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for__Bool]' started.
  -rac_liftSelector:withSignalsFromArray: should work for _Bool
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for__Bool]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_primitive_pointers]' started.
  -rac_liftSelector:withSignalsFromArray: should work for primitive pointers
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_primitive_pointers]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_custom_structs]' started.
  -rac_liftSelector:withSignalsFromArray: should work for custom structs
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_work_for_custom_structs]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_send_the_latest_value_of_the_signal_as_the_right_argument]' started.
  -rac_liftSelector:withSignalsFromArray: should send the latest value of the signal as the right argument
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__should_send_the_latest_value_of_the_signal_as_the_right_argument]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_send_the_return_value_of_the_method_invocation]' started.
  -rac_liftSelector:withSignalsFromArray: the returned signal should send the return value of the method invocation
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_send_the_return_value_of_the_method_invocation]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_send_RACUnit_defaultUnit_for_void_returning_methods]' started.
  -rac_liftSelector:withSignalsFromArray: the returned signal should send RACUnit.defaultUnit for void-returning methods
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_send_RACUnit_defaultUnit_for_void_returning_methods]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_support_integer_returning_methods]' started.
  -rac_liftSelector:withSignalsFromArray: the returned signal should support integer returning methods
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_support_integer_returning_methods]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_support_char___returning_methods]' started.
  -rac_liftSelector:withSignalsFromArray: the returned signal should support char * returning methods
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_support_char___returning_methods]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_support_const_char___returning_methods]' started.
  -rac_liftSelector:withSignalsFromArray: the returned signal should support const char * returning methods
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_support_const_char___returning_methods]' passed (0.001 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_support_struct_returning_methods]' started.
  -rac_liftSelector:withSignalsFromArray: the returned signal should support struct returning methods
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_support_struct_returning_methods]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_support_block_arguments_and_returns]' started.
  -rac_liftSelector:withSignalsFromArray: the returned signal should support block arguments and returns
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_support_block_arguments_and_returns]' passed (0.000 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_replay_the_last_value]' started.
  -rac_liftSelector:withSignalsFromArray: the returned signal should replay the last value
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__the_returned_signal_should_replay_the_last_value]' passed (0.001 seconds).
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__shouldn_t_strongly_capture_the_receiver]' started.
  -rac_liftSelector:withSignalsFromArray: shouldn't strongly capture the receiver
Test Case '-[NSObjectRACLiftingSpec _rac_liftSelector_withSignalsFromArray__shouldn_t_strongly_capture_the_receiver]' passed (0.001 seconds).
Test Suite 'NSObjectRACLiftingSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 28 tests, with 0 failures (0 unexpected) in 0.014 (0.017) seconds
Test Suite 'NSObjectRACPropertySubscribingSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_should_send_the_current_value_once_on_subscription]' started.
  -rac_valuesForKeyPath:observer: RACPropertySubscribingExamples should send the current value once on subscription
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_should_send_the_current_value_once_on_subscription]' passed (0.001 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_should_send_the_new_value_when_it_changes]' started.
  -rac_valuesForKeyPath:observer: RACPropertySubscribingExamples should send the new value when it changes
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_should_send_the_new_value_when_it_changes]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_should_stop_observing_when_disposed]' started.
  -rac_valuesForKeyPath:observer: RACPropertySubscribingExamples should stop observing when disposed
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_should_stop_observing_when_disposed]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_shouldn_t_send_any_more_values_after_the_observer_is_gone]' started.
  -rac_valuesForKeyPath:observer: RACPropertySubscribingExamples shouldn't send any more values after the observer is gone
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_shouldn_t_send_any_more_values_after_the_observer_is_gone]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_shouldn_t_keep_either_object_alive_unnaturally_long]' started.
  -rac_valuesForKeyPath:observer: RACPropertySubscribingExamples shouldn't keep either object alive unnaturally long
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_shouldn_t_keep_either_object_alive_unnaturally_long]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_shouldn_t_keep_the_signal_alive_past_the_lifetime_of_the_object]' started.
  -rac_valuesForKeyPath:observer: RACPropertySubscribingExamples shouldn't keep the signal alive past the lifetime of the object
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_shouldn_t_keep_the_signal_alive_past_the_lifetime_of_the_object]' passed (0.011 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_shouldn_t_crash_when_the_value_is_changed_on_a_different_queue]' started.
  -rac_valuesForKeyPath:observer: RACPropertySubscribingExamples shouldn't crash when the value is changed on a different queue
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_shouldn_t_crash_when_the_value_is_changed_on_a_different_queue]' passed (0.001 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_mutating_collections_sends_the_newest_object_when_inserting_values_into_an_observed_object]' started.
  -rac_valuesForKeyPath:observer: RACPropertySubscribingExamples mutating collections sends the newest object when inserting values into an observed object
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_mutating_collections_sends_the_newest_object_when_inserting_values_into_an_observed_object]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_mutating_collections_sends_the_newest_object_when_removing_values_in_an_observed_object]' started.
  -rac_valuesForKeyPath:observer: RACPropertySubscribingExamples mutating collections sends the newest object when removing values in an observed object
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_mutating_collections_sends_the_newest_object_when_removing_values_in_an_observed_object]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_mutating_collections_sends_the_newest_object_when_replacing_values_in_an_observed_object]' started.
  -rac_valuesForKeyPath:observer: RACPropertySubscribingExamples mutating collections sends the newest object when replacing values in an observed object
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesForKeyPath_observer__RACPropertySubscribingExamples_mutating_collections_sends_the_newest_object_when_replacing_values_in_an_observed_object]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_a_KVO_dictionary]' started.
  +rac_signalWithChangesFor:keyPath:options:observer: KVO options argument sends a KVO dictionary
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_a_KVO_dictionary]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_a_kind_key_by_default]' started.
  +rac_signalWithChangesFor:keyPath:options:observer: KVO options argument sends a kind key by default
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_a_kind_key_by_default]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_the_newest_changes_with_NSKeyValueObservingOptionNew]' started.
  +rac_signalWithChangesFor:keyPath:options:observer: KVO options argument sends the newest changes with NSKeyValueObservingOptionNew
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_the_newest_changes_with_NSKeyValueObservingOptionNew]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_an_additional_change_value_with_NSKeyValueObservingOptionPrior]' started.
  +rac_signalWithChangesFor:keyPath:options:observer: KVO options argument sends an additional change value with NSKeyValueObservingOptionPrior
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_an_additional_change_value_with_NSKeyValueObservingOptionPrior]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_index_changes_when_adding__inserting_or_removing_a_value_from_an_observed_object]' started.
  +rac_signalWithChangesFor:keyPath:options:observer: KVO options argument sends index changes when adding, inserting or removing a value from an observed object
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_index_changes_when_adding__inserting_or_removing_a_value_from_an_observed_object]' passed (0.001 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_the_previous_value_with_NSKeyValueObservingOptionOld]' started.
  +rac_signalWithChangesFor:keyPath:options:observer: KVO options argument sends the previous value with NSKeyValueObservingOptionOld
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_the_previous_value_with_NSKeyValueObservingOptionOld]' passed (0.001 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_the_initial_value_with_NSKeyValueObservingOptionInitial]' started.
  +rac_signalWithChangesFor:keyPath:options:observer: KVO options argument sends the initial value with NSKeyValueObservingOptionInitial
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_signalWithChangesFor_keyPath_options_observer__KVO_options_argument_sends_the_initial_value_with_NSKeyValueObservingOptionInitial]' passed (0.000 seconds).
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesAndChangesForKeyPath_options_observer__should_complete_immediately_if_the_receiver_or_observer_have_deallocated]' started.
  -rac_valuesAndChangesForKeyPath:options:observer: should complete immediately if the receiver or observer have deallocated
Test Case '-[NSObjectRACPropertySubscribingSpec _rac_valuesAndChangesForKeyPath_options_observer__should_complete_immediately_if_the_receiver_or_observer_have_deallocated]' passed (0.001 seconds).
Test Suite 'NSObjectRACPropertySubscribingSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 18 tests, with 0 failures (0 unexpected) in 0.019 (0.021) seconds
Test Suite 'NSObjectRACSelectorSignalSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_the_argument_for_each_invocation]' started.
  RACTestObject should send the argument for each invocation
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_the_argument_for_each_invocation]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_completed_on_deallocation]' started.
  RACTestObject should send completed on deallocation
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_completed_on_deallocation]' passed (0.001 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_for_a_zero_argument_method]' started.
  RACTestObject should send for a zero-argument method
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_for_a_zero_argument_method]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_the_argument_for_each_invocation_to_the_instance_s_own_signal]' started.
  RACTestObject should send the argument for each invocation to the instance's own signal
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_the_argument_for_each_invocation_to_the_instance_s_own_signal]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_multiple_arguments_for_each_invocation]' started.
  RACTestObject should send multiple arguments for each invocation
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_multiple_arguments_for_each_invocation]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_arguments_for_invocation_of_non_existant_methods]' started.
  RACTestObject should send arguments for invocation of non-existant methods
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_arguments_for_invocation_of_non_existant_methods]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_arguments_for_invocation_and_invoke_the_original_method_on_previously_KVO_d_receiver]' started.
  RACTestObject should send arguments for invocation and invoke the original method on previously KVO'd receiver
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_arguments_for_invocation_and_invoke_the_original_method_on_previously_KVO_d_receiver]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_arguments_for_invocation_and_invoke_the_original_method_when_receiver_is_subsequently_KVO_d]' started.
  RACTestObject should send arguments for invocation and invoke the original method when receiver is subsequently KVO'd
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_arguments_for_invocation_and_invoke_the_original_method_when_receiver_is_subsequently_KVO_d]' passed (0.002 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_properly_implement__respondsToSelector__when_called_on_KVO_d_receiver]' started.
  RACTestObject should properly implement -respondsToSelector: when called on KVO'd receiver
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_properly_implement__respondsToSelector__when_called_on_KVO_d_receiver]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_properly_implement__respondsToSelector__for_optional_method_from_a_protocol]' started.
  RACTestObject should properly implement -respondsToSelector: for optional method from a protocol
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_properly_implement__respondsToSelector__for_optional_method_from_a_protocol]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_non_object_arguments]' started.
  RACTestObject should send non-object arguments
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_non_object_arguments]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_on_signal_after_the_original_method_is_invoked]' started.
  RACTestObject should send on signal after the original method is invoked
Test Case '-[NSObjectRACSelectorSignalSpec RACTestObject_should_send_on_signal_after_the_original_method_is_invoked]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec should_swizzle_an_NSObject_method]' started.
  should swizzle an NSObject method
Test Case '-[NSObjectRACSelectorSignalSpec should_swizzle_an_NSObject_method]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec should_work_on_a_class_that_already_overrides__forwardInvocation_]' started.
  should work on a class that already overrides -forwardInvocation:
Test Case '-[NSObjectRACSelectorSignalSpec should_work_on_a_class_that_already_overrides__forwardInvocation_]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec two_classes_in_the_same_hierarchy_should_not_collide]' started.
  two classes in the same hierarchy should not collide
Test Case '-[NSObjectRACSelectorSignalSpec two_classes_in_the_same_hierarchy_should_not_collide]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec two_classes_in_the_same_hierarchy_should_not_collide_when_the_superclass_is_invoked_asynchronously]' started.
  two classes in the same hierarchy should not collide when the superclass is invoked asynchronously
Test Case '-[NSObjectRACSelectorSignalSpec two_classes_in_the_same_hierarchy_should_not_collide_when_the_superclass_is_invoked_asynchronously]' passed (0.011 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec _rac_signalForSelector_fromProtocol_should_not_clobber_a_required_method_already_implemented]' started.
  -rac_signalForSelector:fromProtocol should not clobber a required method already implemented
Test Case '-[NSObjectRACSelectorSignalSpec _rac_signalForSelector_fromProtocol_should_not_clobber_a_required_method_already_implemented]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec _rac_signalForSelector_fromProtocol_should_not_clobber_an_optional_method_already_implemented]' started.
  -rac_signalForSelector:fromProtocol should not clobber an optional method already implemented
Test Case '-[NSObjectRACSelectorSignalSpec _rac_signalForSelector_fromProtocol_should_not_clobber_an_optional_method_already_implemented]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec _rac_signalForSelector_fromProtocol_should_inject_a_required_method]' started.
  -rac_signalForSelector:fromProtocol should inject a required method
Test Case '-[NSObjectRACSelectorSignalSpec _rac_signalForSelector_fromProtocol_should_inject_a_required_method]' passed (0.000 seconds).
Test Case '-[NSObjectRACSelectorSignalSpec _rac_signalForSelector_fromProtocol_should_inject_an_optional_method]' started.
  -rac_signalForSelector:fromProtocol should inject an optional method
Test Case '-[NSObjectRACSelectorSignalSpec _rac_signalForSelector_fromProtocol_should_inject_an_optional_method]' passed (0.000 seconds).
Test Suite 'NSObjectRACSelectorSignalSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 20 tests, with 0 failures (0 unexpected) in 0.017 (0.020) seconds
Test Suite 'NSStringRACKeyPathUtilitiesSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathComponents_should_return_components_in_the_key_path]' started.
  -keyPathComponents should return components in the key path
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathComponents_should_return_components_in_the_key_path]' passed (0.000 seconds).
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathComponents_should_return_nil_if_given_an_empty_string]' started.
  -keyPathComponents should return nil if given an empty string
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathComponents_should_return_nil_if_given_an_empty_string]' passed (0.000 seconds).
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingLastKeyPathComponent_should_return_the_parent_key_path]' started.
  -keyPathByDeletingLastKeyPathComponent should return the parent key path
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingLastKeyPathComponent_should_return_the_parent_key_path]' passed (0.000 seconds).
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingLastKeyPathComponent_should_return_nil_if_given_an_empty_string]' started.
  -keyPathByDeletingLastKeyPathComponent should return nil if given an empty string
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingLastKeyPathComponent_should_return_nil_if_given_an_empty_string]' passed (0.000 seconds).
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingLastKeyPathComponent_should_return_nil_if_given_a_key_path_with_only_one_component]' started.
  -keyPathByDeletingLastKeyPathComponent should return nil if given a key path with only one component
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingLastKeyPathComponent_should_return_nil_if_given_a_key_path_with_only_one_component]' passed (0.000 seconds).
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingFirstKeyPathComponent_should_return_the_remaining_key_path]' started.
  -keyPathByDeletingFirstKeyPathComponent should return the remaining key path
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingFirstKeyPathComponent_should_return_the_remaining_key_path]' passed (0.000 seconds).
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingFirstKeyPathComponent_should_return_nil_if_given_an_empty_string]' started.
  -keyPathByDeletingFirstKeyPathComponent should return nil if given an empty string
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingFirstKeyPathComponent_should_return_nil_if_given_an_empty_string]' passed (0.000 seconds).
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingFirstKeyPathComponent_should_return_nil_if_given_a_key_path_with_only_one_component]' started.
  -keyPathByDeletingFirstKeyPathComponent should return nil if given a key path with only one component
Test Case '-[NSStringRACKeyPathUtilitiesSpec _keyPathByDeletingFirstKeyPathComponent_should_return_nil_if_given_a_key_path_with_only_one_component]' passed (0.000 seconds).
Test Suite 'NSStringRACKeyPathUtilitiesSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 8 tests, with 0 failures (0 unexpected) in 0.001 (0.002) seconds
Test Suite 'NSTextRACSupportSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSTextRACSupportSpec NSTextView_should_send_changes_on_rac_textSignal]' started.
  NSTextView should send changes on rac_textSignal
Test Case '-[NSTextRACSupportSpec NSTextView_should_send_changes_on_rac_textSignal]' passed (0.001 seconds).
Test Suite 'NSTextRACSupportSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 1 test, with 0 failures (0 unexpected) in 0.001 (0.001) seconds
Test Suite 'NSURLConnectionRACSupportSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[NSURLConnectionRACSupportSpec should_fetch_a_JSON_file]' started.
  should fetch a JSON file
Test Case '-[NSURLConnectionRACSupportSpec should_fetch_a_JSON_file]' passed (0.005 seconds).
Test Suite 'NSURLConnectionRACSupportSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 1 test, with 0 failures (0 unexpected) in 0.005 (0.005) seconds
Test Suite 'RACBacktraceSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACBacktraceSpec should_capture_the_current_backtrace]' started.
  should capture the current backtrace
Test Case '-[RACBacktraceSpec should_capture_the_current_backtrace]' passed (0.000 seconds).
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_async]' started.
  with a GCD queue should trace across dispatch_async
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_async]' passed (0.011 seconds).
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_async_to_the_main_thread]' started.
  with a GCD queue should trace across dispatch_async to the main thread
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_async_to_the_main_thread]' passed (0.011 seconds).
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_async_f]' started.
  with a GCD queue should trace across dispatch_async_f
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_async_f]' passed (0.011 seconds).
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_barrier_async]' started.
  with a GCD queue should trace across dispatch_barrier_async
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_barrier_async]' passed (0.011 seconds).
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_barrier_async_f]' started.
  with a GCD queue should trace across dispatch_barrier_async_f
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_barrier_async_f]' passed (0.011 seconds).
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_after]' started.
  with a GCD queue should trace across dispatch_after
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_after]' passed (0.011 seconds).
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_after_f]' started.
  with a GCD queue should trace across dispatch_after_f
Test Case '-[RACBacktraceSpec with_a_GCD_queue_should_trace_across_dispatch_after_f]' passed (0.011 seconds).
Test Case '-[RACBacktraceSpec should_trace_across_a_RACScheduler]' started.
  should trace across a RACScheduler
Test Case '-[RACBacktraceSpec should_trace_across_a_RACScheduler]' passed (0.011 seconds).
Test Case '-[RACBacktraceSpec shouldn_t_go_bonkers_with_RACScheduler]' started.
  shouldn't go bonkers with RACScheduler
Test Case '-[RACBacktraceSpec shouldn_t_go_bonkers_with_RACScheduler]' passed (0.001 seconds).
Test Case '-[RACBacktraceSpec should_trace_across_an_NSOperationQueue]' started.
  should trace across an NSOperationQueue
Test Case '-[RACBacktraceSpec should_trace_across_an_NSOperationQueue]' passed (0.000 seconds).
Test Suite 'RACBacktraceSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 11 tests, with 0 failures (0 unexpected) in 0.089 (0.090) seconds
Test Suite 'RACBlockTrampolineSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACBlockTrampolineSpec should_invoke_the_block_with_the_given_arguments]' started.
  should invoke the block with the given arguments
Test Case '-[RACBlockTrampolineSpec should_invoke_the_block_with_the_given_arguments]' passed (0.000 seconds).
Test Case '-[RACBlockTrampolineSpec should_return_the_result_of_the_block_invocation]' started.
  should return the result of the block invocation
Test Case '-[RACBlockTrampolineSpec should_return_the_result_of_the_block_invocation]' passed (0.000 seconds).
Test Case '-[RACBlockTrampolineSpec should_pass_RACTupleNils_as_nil]' started.
  should pass RACTupleNils as nil
Test Case '-[RACBlockTrampolineSpec should_pass_RACTupleNils_as_nil]' passed (0.000 seconds).
Test Suite 'RACBlockTrampolineSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 3 tests, with 0 failures (0 unexpected) in 0.000 (0.001) seconds
Test Suite 'RACChannelSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_not_send_any_leadingTerminal_value_on_subscription]' started.
  RACChannel RACChannelExamples should not send any leadingTerminal value on subscription
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_not_send_any_leadingTerminal_value_on_subscription]' passed (0.000 seconds).
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_send_the_latest_followingTerminal_value_on_subscription]' started.
  RACChannel RACChannelExamples should send the latest followingTerminal value on subscription
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_send_the_latest_followingTerminal_value_on_subscription]' passed (0.001 seconds).
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_send_leadingTerminal_values_as_they_change]' started.
  RACChannel RACChannelExamples should send leadingTerminal values as they change
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_send_leadingTerminal_values_as_they_change]' passed (0.000 seconds).
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_send_followingTerminal_values_as_they_change]' started.
  RACChannel RACChannelExamples should send followingTerminal values as they change
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_send_followingTerminal_values_as_they_change]' passed (0.000 seconds).
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_complete_both_signals_when_the_leadingTerminal_is_completed]' started.
  RACChannel RACChannelExamples should complete both signals when the leadingTerminal is completed
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_complete_both_signals_when_the_leadingTerminal_is_completed]' passed (0.001 seconds).
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_complete_both_signals_when_the_followingTerminal_is_completed]' started.
  RACChannel RACChannelExamples should complete both signals when the followingTerminal is completed
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_complete_both_signals_when_the_followingTerminal_is_completed]' passed (0.000 seconds).
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_replay_completion_to_new_subscribers]' started.
  RACChannel RACChannelExamples should replay completion to new subscribers
Test Case '-[RACChannelSpec RACChannel_RACChannelExamples_should_replay_completion_to_new_subscribers]' passed (0.000 seconds).
Test Case '-[RACChannelSpec RACChannel_memory_management_should_dealloc_when_its_subscribers_are_disposed]' started.
  RACChannel memory management should dealloc when its subscribers are disposed
Test Case '-[RACChannelSpec RACChannel_memory_management_should_dealloc_when_its_subscribers_are_disposed]' passed (0.000 seconds).
Test Case '-[RACChannelSpec RACChannel_memory_management_should_dealloc_when_its_subscriptions_are_disposed]' started.
  RACChannel memory management should dealloc when its subscriptions are disposed
Test Case '-[RACChannelSpec RACChannel_memory_management_should_dealloc_when_its_subscriptions_are_disposed]' passed (0.000 seconds).
Test Suite 'RACChannelSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 9 tests, with 0 failures (0 unexpected) in 0.004 (0.005) seconds
Test Suite 'RACCommandSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_be_enabled_by_default]' started.
  with a simple signal block should be enabled by default
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_be_enabled_by_default]' passed (0.003 seconds).
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_not_be_executing_by_default]' started.
  with a simple signal block should not be executing by default
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_not_be_executing_by_default]' passed (0.004 seconds).
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_create_an_execution_signal]' started.
  with a simple signal block should create an execution signal
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_create_an_execution_signal]' passed (0.015 seconds).
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_return_the_execution_signal_from__execute_]' started.
  with a simple signal block should return the execution signal from -execute:
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_return_the_execution_signal_from__execute_]' passed (0.015 seconds).
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_always_send_executionSignals_on_the_main_thread]' started.
  with a simple signal block should always send executionSignals on the main thread
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_always_send_executionSignals_on_the_main_thread]' passed (0.014 seconds).
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_not_send_anything_on__errors__by_default]' started.
  with a simple signal block should not send anything on 'errors' by default
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_not_send_anything_on__errors__by_default]' passed (0.006 seconds).
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_be_executing_while_an_execution_signal_is_running]' started.
  with a simple signal block should be executing while an execution signal is running
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_be_executing_while_an_execution_signal_is_running]' passed (0.011 seconds).
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_always_update_executing_on_the_main_thread]' started.
  with a simple signal block should always update executing on the main thread
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_always_update_executing_on_the_main_thread]' passed (0.016 seconds).
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_dealloc_without_subscribers]' started.
  with a simple signal block should dealloc without subscribers
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_dealloc_without_subscribers]' passed (0.014 seconds).
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_complete_signals_on_the_main_thread_when_deallocated]' started.
  with a simple signal block should complete signals on the main thread when deallocated
Test Case '-[RACCommandSpec with_a_simple_signal_block_should_complete_signals_on_the_main_thread_when_deallocated]' passed (0.015 seconds).
Test Case '-[RACCommandSpec should_invoke_the_signalBlock_once_per_execution]' started.
  should invoke the signalBlock once per execution
Test Case '-[RACCommandSpec should_invoke_the_signalBlock_once_per_execution]' passed (0.009 seconds).
Test Case '-[RACCommandSpec should_send_on_executionSignals_in_order_of_execution]' started.
  should send on executionSignals in order of execution
Test Case '-[RACCommandSpec should_send_on_executionSignals_in_order_of_execution]' passed (0.010 seconds).
Test Case '-[RACCommandSpec should_wait_for_all_signals_to_complete_or_error_before_executing_sends_NO]' started.
  should wait for all signals to complete or error before executing sends NO
Test Case '-[RACCommandSpec should_wait_for_all_signals_to_complete_or_error_before_executing_sends_NO]' passed (0.029 seconds).
Test Case '-[RACCommandSpec should_not_deliver_errors_from_executionSignals]' started.
  should not deliver errors from executionSignals
Test Case '-[RACCommandSpec should_not_deliver_errors_from_executionSignals]' passed (0.028 seconds).
Test Case '-[RACCommandSpec should_deliver_errors_from__execute_]' started.
  should deliver errors from -execute:
Test Case '-[RACCommandSpec should_deliver_errors_from__execute_]' passed (0.027 seconds).
Test Case '-[RACCommandSpec should_deliver_errors_onto__errors_]' started.
  should deliver errors onto 'errors'
Test Case '-[RACCommandSpec should_deliver_errors_onto__errors_]' passed (0.044 seconds).
Test Case '-[RACCommandSpec should_not_deliver_non_error_events_onto__errors_]' started.
  should not deliver non-error events onto 'errors'
Test Case '-[RACCommandSpec should_not_deliver_non_error_events_onto__errors_]' passed (0.025 seconds).
Test Case '-[RACCommandSpec should_send_errors_on_the_main_thread]' started.
  should send errors on the main thread
2013-12-10 15:13:10.591 otest[6189:1603] /Users/<USER>/code/OSS/ReactiveCocoa/ReactiveCocoaFramework/ReactiveCocoaTests/RACCommandSpec.m:393 expected: a truthy value, got: 0, which is falsy
Test Case '-[RACCommandSpec should_send_errors_on_the_main_thread]' passed (0.013 seconds).
Test Case '-[RACCommandSpec enabled_signal_should_send_YES_by_default]' started.
  enabled signal should send YES by default
Test Case '-[RACCommandSpec enabled_signal_should_send_YES_by_default]' passed (0.002 seconds).
Test Case '-[RACCommandSpec enabled_signal_should_send_whatever_the_enabledSignal_has_sent_most_recently]' started.
  enabled signal should send whatever the enabledSignal has sent most recently
Test Case '-[RACCommandSpec enabled_signal_should_send_whatever_the_enabledSignal_has_sent_most_recently]' passed (0.035 seconds).
Test Case '-[RACCommandSpec enabled_signal_should_sample_enabledSignal_synchronously_at_initialization_time]' started.
  enabled signal should sample enabledSignal synchronously at initialization time
Test Case '-[RACCommandSpec enabled_signal_should_sample_enabledSignal_synchronously_at_initialization_time]' passed (0.005 seconds).
Test Case '-[RACCommandSpec enabled_signal_should_send_NO_while_executing_is_YES_and_allowsConcurrentExecution_is_NO]' started.
  enabled signal should send NO while executing is YES and allowsConcurrentExecution is NO
Test Case '-[RACCommandSpec enabled_signal_should_send_NO_while_executing_is_YES_and_allowsConcurrentExecution_is_NO]' passed (0.006 seconds).
Test Case '-[RACCommandSpec enabled_signal_should_send_YES_while_executing_is_YES_and_allowsConcurrentExecution_is_YES]' started.
  enabled signal should send YES while executing is YES and allowsConcurrentExecution is YES
Test Case '-[RACCommandSpec enabled_signal_should_send_YES_while_executing_is_YES_and_allowsConcurrentExecution_is_YES]' passed (0.016 seconds).
Test Case '-[RACCommandSpec enabled_signal_should_send_an_error_from__execute__when_NO]' started.
  enabled signal should send an error from -execute: when NO
Test Case '-[RACCommandSpec enabled_signal_should_send_an_error_from__execute__when_NO]' passed (0.004 seconds).
Test Case '-[RACCommandSpec enabled_signal_should_always_update_on_the_main_thread]' started.
  enabled signal should always update on the main thread
Test Case '-[RACCommandSpec enabled_signal_should_always_update_on_the_main_thread]' passed (0.013 seconds).
Test Case '-[RACCommandSpec enabled_signal_should_complete_when_the_command_is_deallocated_even_if_the_input_signal_hasn_t]' started.
  enabled signal should complete when the command is deallocated even if the input signal hasn't
Test Case '-[RACCommandSpec enabled_signal_should_complete_when_the_command_is_deallocated_even_if_the_input_signal_hasn_t]' passed (0.016 seconds).
Test Suite 'RACCommandSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 26 tests, with 0 failures (0 unexpected) in 0.397 (0.405) seconds
Test Suite 'RACCompoundDisposableSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACCompoundDisposableSpec should_dispose_of_all_its_contained_disposables]' started.
  should dispose of all its contained disposables
Test Case '-[RACCompoundDisposableSpec should_dispose_of_all_its_contained_disposables]' passed (0.000 seconds).
Test Case '-[RACCompoundDisposableSpec should_dispose_of_any_added_disposables_immediately_if_it_s_already_been_disposed]' started.
  should dispose of any added disposables immediately if it's already been disposed
Test Case '-[RACCompoundDisposableSpec should_dispose_of_any_added_disposables_immediately_if_it_s_already_been_disposed]' passed (0.000 seconds).
Test Case '-[RACCompoundDisposableSpec should_work_when_initialized_with__init]' started.
  should work when initialized with -init
Test Case '-[RACCompoundDisposableSpec should_work_when_initialized_with__init]' passed (0.000 seconds).
Test Case '-[RACCompoundDisposableSpec should_work_when_initialized_with__disposableWithBlock_]' started.
  should work when initialized with +disposableWithBlock:
Test Case '-[RACCompoundDisposableSpec should_work_when_initialized_with__disposableWithBlock_]' passed (0.000 seconds).
Test Case '-[RACCompoundDisposableSpec should_allow_disposables_to_be_removed]' started.
  should allow disposables to be removed
Test Case '-[RACCompoundDisposableSpec should_allow_disposables_to_be_removed]' passed (0.000 seconds).
Test Suite 'RACCompoundDisposableSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 5 tests, with 0 failures (0 unexpected) in 0.001 (0.001) seconds
Test Suite 'RACDisposableSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACDisposableSpec should_initialize_without_a_block]' started.
  should initialize without a block
Test Case '-[RACDisposableSpec should_initialize_without_a_block]' passed (0.000 seconds).
Test Case '-[RACDisposableSpec should_execute_a_block_upon_disposal]' started.
  should execute a block upon disposal
Test Case '-[RACDisposableSpec should_execute_a_block_upon_disposal]' passed (0.000 seconds).
Test Case '-[RACDisposableSpec should_not_dispose_upon_deallocation]' started.
  should not dispose upon deallocation
Test Case '-[RACDisposableSpec should_not_dispose_upon_deallocation]' passed (0.001 seconds).
Test Case '-[RACDisposableSpec should_create_a_scoped_disposable]' started.
  should create a scoped disposable
Test Case '-[RACDisposableSpec should_create_a_scoped_disposable]' passed (0.000 seconds).
Test Suite 'RACDisposableSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 4 tests, with 0 failures (0 unexpected) in 0.002 (0.002) seconds
Test Suite 'RACEventSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACEventSpec should_return_the_singleton_completed_event]' started.
  should return the singleton completed event
Test Case '-[RACEventSpec should_return_the_singleton_completed_event]' passed (0.000 seconds).
Test Case '-[RACEventSpec should_return_an_error_event]' started.
  should return an error event
Test Case '-[RACEventSpec should_return_an_error_event]' passed (0.000 seconds).
Test Case '-[RACEventSpec should_return_an_error_event_with_a_nil_error]' started.
  should return an error event with a nil error
Test Case '-[RACEventSpec should_return_an_error_event_with_a_nil_error]' passed (0.001 seconds).
Test Case '-[RACEventSpec should_return_a_next_event]' started.
  should return a next event
Test Case '-[RACEventSpec should_return_a_next_event]' passed (0.000 seconds).
Test Case '-[RACEventSpec should_return_a_next_event_with_a_nil_value]' started.
  should return a next event with a nil value
Test Case '-[RACEventSpec should_return_a_next_event_with_a_nil_value]' passed (0.000 seconds).
Test Suite 'RACEventSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 5 tests, with 0 failures (0 unexpected) in 0.001 (0.002) seconds
Test Suite 'RACKVOChannelSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_set_the_value_of_the_property_with_the_latest_value_from_the_signal]' started.
  RACKVOChannel RACPropertySignalExamples should set the value of the property with the latest value from the signal
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_set_the_value_of_the_property_with_the_latest_value_from_the_signal]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_set_the_given_nilValue_for_an_object_property]' started.
  RACKVOChannel RACPropertySignalExamples should set the given nilValue for an object property
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_set_the_given_nilValue_for_an_object_property]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_leave_the_value_of_the_property_alone_after_the_signal_completes]' started.
  RACKVOChannel RACPropertySignalExamples should leave the value of the property alone after the signal completes
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_leave_the_value_of_the_property_alone_after_the_signal_completes]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_set_the_value_of_a_non_object_property_with_the_latest_value_from_the_signal]' started.
  RACKVOChannel RACPropertySignalExamples should set the value of a non-object property with the latest value from the signal
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_set_the_value_of_a_non_object_property_with_the_latest_value_from_the_signal]' passed (0.002 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_set_the_given_nilValue_for_a_non_object_property]' started.
  RACKVOChannel RACPropertySignalExamples should set the given nilValue for a non-object property
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_set_the_given_nilValue_for_a_non_object_property]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_not_invoke__setNilValueForKey__with_a_nilValue]' started.
  RACKVOChannel RACPropertySignalExamples should not invoke -setNilValueForKey: with a nilValue
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_not_invoke__setNilValueForKey__with_a_nilValue]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_invoke__setNilValueForKey__without_a_nilValue]' started.
  RACKVOChannel RACPropertySignalExamples should invoke -setNilValueForKey: without a nilValue
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_invoke__setNilValueForKey__without_a_nilValue]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_retain_intermediate_signals_when_binding]' started.
  RACKVOChannel RACPropertySignalExamples should retain intermediate signals when binding
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACPropertySignalExamples_should_retain_intermediate_signals_when_binding]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_not_send_any_leadingTerminal_value_on_subscription]' started.
  RACKVOChannel RACChannelExamples should not send any leadingTerminal value on subscription
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_not_send_any_leadingTerminal_value_on_subscription]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_send_the_latest_followingTerminal_value_on_subscription]' started.
  RACKVOChannel RACChannelExamples should send the latest followingTerminal value on subscription
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_send_the_latest_followingTerminal_value_on_subscription]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_send_leadingTerminal_values_as_they_change]' started.
  RACKVOChannel RACChannelExamples should send leadingTerminal values as they change
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_send_leadingTerminal_values_as_they_change]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_send_followingTerminal_values_as_they_change]' started.
  RACKVOChannel RACChannelExamples should send followingTerminal values as they change
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_send_followingTerminal_values_as_they_change]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_complete_both_signals_when_the_leadingTerminal_is_completed]' started.
  RACKVOChannel RACChannelExamples should complete both signals when the leadingTerminal is completed
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_complete_both_signals_when_the_leadingTerminal_is_completed]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_complete_both_signals_when_the_followingTerminal_is_completed]' started.
  RACKVOChannel RACChannelExamples should complete both signals when the followingTerminal is completed
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_complete_both_signals_when_the_followingTerminal_is_completed]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_replay_completion_to_new_subscribers]' started.
  RACKVOChannel RACChannelExamples should replay completion to new subscribers
Test Case '-[RACKVOChannelSpec RACKVOChannel_RACChannelExamples_should_replay_completion_to_new_subscribers]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_send_the_object_s_current_value_when_subscribed_to_followingTerminal]' started.
  RACKVOChannel should send the object's current value when subscribed to followingTerminal
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_send_the_object_s_current_value_when_subscribed_to_followingTerminal]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_send_the_object_s_new_value_on_followingTerminal_when_it_s_changed]' started.
  RACKVOChannel should send the object's new value on followingTerminal when it's changed
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_send_the_object_s_new_value_on_followingTerminal_when_it_s_changed]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_set_the_object_s_value_using_values_sent_to_the_followingTerminal]' started.
  RACKVOChannel should set the object's value using values sent to the followingTerminal
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_set_the_object_s_value_using_values_sent_to_the_followingTerminal]' passed (0.000 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_be_able_to_subscribe_to_signals]' started.
  RACKVOChannel should be able to subscribe to signals
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_be_able_to_subscribe_to_signals]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_complete_both_terminals_when_the_target_deallocates]' started.
  RACKVOChannel should complete both terminals when the target deallocates
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_complete_both_terminals_when_the_target_deallocates]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_deallocate_when_the_target_deallocates]' started.
  RACKVOChannel should deallocate when the target deallocates
Test Case '-[RACKVOChannelSpec RACKVOChannel_should_deallocate_when_the_target_deallocates]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_keep_objects__properties_in_sync]' started.
  RACChannelTo should keep objects' properties in sync
Test Case '-[RACKVOChannelSpec RACChannelTo_should_keep_objects__properties_in_sync]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_keep_properties_identified_by_keypaths_in_sync]' started.
  RACChannelTo should keep properties identified by keypaths in sync
Test Case '-[RACKVOChannelSpec RACChannelTo_should_keep_properties_identified_by_keypaths_in_sync]' passed (0.002 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_update_properties_identified_by_keypaths_when_the_intermediate_values_change]' started.
  RACChannelTo should update properties identified by keypaths when the intermediate values change
Test Case '-[RACKVOChannelSpec RACChannelTo_should_update_properties_identified_by_keypaths_when_the_intermediate_values_change]' passed (0.002 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_update_properties_identified_by_keypaths_when_the_channel_was_created_when_one_of_the_two_objects_had_an_intermediate_nil_value]' started.
  RACChannelTo should update properties identified by keypaths when the channel was created when one of the two objects had an intermediate nil value
Test Case '-[RACKVOChannelSpec RACChannelTo_should_update_properties_identified_by_keypaths_when_the_channel_was_created_when_one_of_the_two_objects_had_an_intermediate_nil_value]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_take_the_value_of_the_object_being_bound_to_at_the_start]' started.
  RACChannelTo should take the value of the object being bound to at the start
Test Case '-[RACKVOChannelSpec RACChannelTo_should_take_the_value_of_the_object_being_bound_to_at_the_start]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_update_the_value_even_if_it_s_the_same_value_the_object_had_before_it_was_bound]' started.
  RACChannelTo should update the value even if it's the same value the object had before it was bound
Test Case '-[RACKVOChannelSpec RACChannelTo_should_update_the_value_even_if_it_s_the_same_value_the_object_had_before_it_was_bound]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_bind_transitively]' started.
  RACChannelTo should bind transitively
Test Case '-[RACKVOChannelSpec RACChannelTo_should_bind_transitively]' passed (0.002 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_bind_changes_made_by_KVC_on_arrays]' started.
  RACChannelTo should bind changes made by KVC on arrays
Test Case '-[RACKVOChannelSpec RACChannelTo_should_bind_changes_made_by_KVC_on_arrays]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_bind_changes_made_by_KVC_on_sets]' started.
  RACChannelTo should bind changes made by KVC on sets
Test Case '-[RACKVOChannelSpec RACChannelTo_should_bind_changes_made_by_KVC_on_sets]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_bind_changes_made_by_KVC_on_ordered_sets]' started.
  RACChannelTo should bind changes made by KVC on ordered sets
Test Case '-[RACKVOChannelSpec RACChannelTo_should_bind_changes_made_by_KVC_on_ordered_sets]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_handle_deallocation_of_intermediate_objects_correctly_even_without_support_from_KVO]' started.
  RACChannelTo should handle deallocation of intermediate objects correctly even without support from KVO
Test Case '-[RACKVOChannelSpec RACChannelTo_should_handle_deallocation_of_intermediate_objects_correctly_even_without_support_from_KVO]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_stop_binding_when_disposed]' started.
  RACChannelTo should stop binding when disposed
Test Case '-[RACKVOChannelSpec RACChannelTo_should_stop_binding_when_disposed]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_use_the_nilValue_when_sent_nil]' started.
  RACChannelTo should use the nilValue when sent nil
Test Case '-[RACKVOChannelSpec RACChannelTo_should_use_the_nilValue_when_sent_nil]' passed (0.001 seconds).
Test Case '-[RACKVOChannelSpec RACChannelTo_should_use_the_nilValue_when_an_intermediate_object_is_nil]' started.
  RACChannelTo should use the nilValue when an intermediate object is nil
Test Case '-[RACKVOChannelSpec RACChannelTo_should_use_the_nilValue_when_an_intermediate_object_is_nil]' passed (0.001 seconds).
Test Suite 'RACKVOChannelSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 35 tests, with 0 failures (0 unexpected) in 0.037 (0.040) seconds
Test Suite 'RACKVOWrapperSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_not_call_the_callback_block_on_add_if_called_without_NSKeyValueObservingOptionInitial]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperExamples should not call the callback block on add if called without NSKeyValueObservingOptionInitial
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_not_call_the_callback_block_on_add_if_called_without_NSKeyValueObservingOptionInitial]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_call_the_callback_block_on_add_if_called_with_NSKeyValueObservingOptionInitial]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperExamples should call the callback block on add if called with NSKeyValueObservingOptionInitial
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_call_the_callback_block_on_add_if_called_with_NSKeyValueObservingOptionInitial]' passed (0.001 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_call_the_callback_block_twice_per_change__once_prior_and_once_posterior]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperExamples should call the callback block twice per change, once prior and once posterior
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_call_the_callback_block_twice_per_change__once_prior_and_once_posterior]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_call_the_callback_block_with_NSKeyValueChangeNotificationIsPriorKey_set_before_the_value_is_changed__and_not_set_after_the_value_is_changed]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperExamples should call the callback block with NSKeyValueChangeNotificationIsPriorKey set before the value is changed, and not set after the value is changed
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_call_the_callback_block_with_NSKeyValueChangeNotificationIsPriorKey_set_before_the_value_is_changed__and_not_set_after_the_value_is_changed]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_not_call_the_callback_block_after_it_s_been_disposed]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperExamples should not call the callback block after it's been disposed
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_not_call_the_callback_block_after_it_s_been_disposed]' passed (0.001 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_call_the_callback_block_only_once_with_NSKeyValueChangeNotificationIsPriorKey_not_set_when_the_value_is_deallocated]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperExamples should call the callback block only once with NSKeyValueChangeNotificationIsPriorKey not set when the value is deallocated
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperExamples_should_call_the_callback_block_only_once_with_NSKeyValueChangeNotificationIsPriorKey_not_set_when_the_value_is_deallocated]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperCollectionExamples_should_support_inserting_elements_into_ordered_collections]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperCollectionExamples should support inserting elements into ordered collections
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperCollectionExamples_should_support_inserting_elements_into_ordered_collections]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperCollectionExamples_should_support_removing_elements_from_ordered_collections]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperCollectionExamples should support removing elements from ordered collections
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperCollectionExamples_should_support_removing_elements_from_ordered_collections]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperCollectionExamples_should_support_replacing_elements_in_ordered_collections]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperCollectionExamples should support replacing elements in ordered collections
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperCollectionExamples_should_support_replacing_elements_in_ordered_collections]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperCollectionExamples_should_support_adding_elements_to_unordered_collections]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperCollectionExamples should support adding elements to unordered collections
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperCollectionExamples_should_support_adding_elements_to_unordered_collections]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperCollectionExamples_should_support_removing_elements_from_unordered_collections]' started.
  -rac_observeKeyPath:options:observer:block: on simple keys RACKVOWrapperCollectionExamples should support removing elements from unordered collections
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_simple_keys_RACKVOWrapperCollectionExamples_should_support_removing_elements_from_unordered_collections]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_not_call_the_callback_block_on_add_if_called_without_NSKeyValueObservingOptionInitial]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperExamples should not call the callback block on add if called without NSKeyValueObservingOptionInitial
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_not_call_the_callback_block_on_add_if_called_without_NSKeyValueObservingOptionInitial]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_on_add_if_called_with_NSKeyValueObservingOptionInitial]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperExamples should call the callback block on add if called with NSKeyValueObservingOptionInitial
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_on_add_if_called_with_NSKeyValueObservingOptionInitial]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_twice_per_change__once_prior_and_once_posterior]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperExamples should call the callback block twice per change, once prior and once posterior
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_twice_per_change__once_prior_and_once_posterior]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_with_NSKeyValueChangeNotificationIsPriorKey_set_before_the_value_is_changed__and_not_set_after_the_value_is_changed]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperExamples should call the callback block with NSKeyValueChangeNotificationIsPriorKey set before the value is changed, and not set after the value is changed
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_with_NSKeyValueChangeNotificationIsPriorKey_set_before_the_value_is_changed__and_not_set_after_the_value_is_changed]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_not_call_the_callback_block_after_it_s_been_disposed]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperExamples should not call the callback block after it's been disposed
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_not_call_the_callback_block_after_it_s_been_disposed]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_only_once_with_NSKeyValueChangeNotificationIsPriorKey_not_set_when_the_value_is_deallocated]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperExamples should call the callback block only once with NSKeyValueChangeNotificationIsPriorKey not set when the value is deallocated
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_only_once_with_NSKeyValueChangeNotificationIsPriorKey_not_set_when_the_value_is_deallocated]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperCollectionExamples_should_support_inserting_elements_into_ordered_collections]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperCollectionExamples should support inserting elements into ordered collections
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperCollectionExamples_should_support_inserting_elements_into_ordered_collections]' passed (0.001 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperCollectionExamples_should_support_removing_elements_from_ordered_collections]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperCollectionExamples should support removing elements from ordered collections
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperCollectionExamples_should_support_removing_elements_from_ordered_collections]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperCollectionExamples_should_support_replacing_elements_in_ordered_collections]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperCollectionExamples should support replacing elements in ordered collections
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperCollectionExamples_should_support_replacing_elements_in_ordered_collections]' passed (0.001 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperCollectionExamples_should_support_adding_elements_to_unordered_collections]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperCollectionExamples should support adding elements to unordered collections
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperCollectionExamples_should_support_adding_elements_to_unordered_collections]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperCollectionExamples_should_support_removing_elements_from_unordered_collections]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' last key path components RACKVOWrapperCollectionExamples should support removing elements from unordered collections
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__last_key_path_components_RACKVOWrapperCollectionExamples_should_support_removing_elements_from_unordered_collections]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_not_call_the_callback_block_on_add_if_called_without_NSKeyValueObservingOptionInitial]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' intermediate key path components RACKVOWrapperExamples should not call the callback block on add if called without NSKeyValueObservingOptionInitial
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_not_call_the_callback_block_on_add_if_called_without_NSKeyValueObservingOptionInitial]' passed (0.001 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_on_add_if_called_with_NSKeyValueObservingOptionInitial]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' intermediate key path components RACKVOWrapperExamples should call the callback block on add if called with NSKeyValueObservingOptionInitial
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_on_add_if_called_with_NSKeyValueObservingOptionInitial]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_twice_per_change__once_prior_and_once_posterior]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' intermediate key path components RACKVOWrapperExamples should call the callback block twice per change, once prior and once posterior
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_twice_per_change__once_prior_and_once_posterior]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_with_NSKeyValueChangeNotificationIsPriorKey_set_before_the_value_is_changed__and_not_set_after_the_value_is_changed]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' intermediate key path components RACKVOWrapperExamples should call the callback block with NSKeyValueChangeNotificationIsPriorKey set before the value is changed, and not set after the value is changed
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_with_NSKeyValueChangeNotificationIsPriorKey_set_before_the_value_is_changed__and_not_set_after_the_value_is_changed]' passed (0.001 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_not_call_the_callback_block_after_it_s_been_disposed]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' intermediate key path components RACKVOWrapperExamples should not call the callback block after it's been disposed
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_not_call_the_callback_block_after_it_s_been_disposed]' passed (0.001 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_only_once_with_NSKeyValueChangeNotificationIsPriorKey_not_set_when_the_value_is_deallocated]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' intermediate key path components RACKVOWrapperExamples should call the callback block only once with NSKeyValueChangeNotificationIsPriorKey not set when the value is deallocated
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__intermediate_key_path_components_RACKVOWrapperExamples_should_call_the_callback_block_only_once_with_NSKeyValueChangeNotificationIsPriorKey_not_set_when_the_value_is_deallocated]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__should_not_notice_deallocation_of_the_object_returned_by_a_dynamic_final_property]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' should not notice deallocation of the object returned by a dynamic final property
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__should_not_notice_deallocation_of_the_object_returned_by_a_dynamic_final_property]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__should_not_notice_deallocation_of_the_object_returned_by_a_dynamic_intermediate_property]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' should not notice deallocation of the object returned by a dynamic intermediate property
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__should_not_notice_deallocation_of_the_object_returned_by_a_dynamic_intermediate_property]' passed (0.003 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__should_not_notice_deallocation_of_the_object_returned_by_a_dynamic_method]' started.
  -rac_observeKeyPath:options:observer:block: on composite key paths' should not notice deallocation of the object returned by a dynamic method
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__on_composite_key_paths__should_not_notice_deallocation_of_the_object_returned_by_a_dynamic_method]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__should_not_call_the_callback_block_when_the_value_is_the_observer]' started.
  -rac_observeKeyPath:options:observer:block: should not call the callback block when the value is the observer
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__should_not_call_the_callback_block_when_the_value_is_the_observer]' passed (0.001 seconds).
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__should_call_the_callback_block_for_deallocation_of_the_initial_value_of_a_single_key_key_path]' started.
  -rac_observeKeyPath:options:observer:block: should call the callback block for deallocation of the initial value of a single-key key path
Test Case '-[RACKVOWrapperSpec _rac_observeKeyPath_options_observer_block__should_call_the_callback_block_for_deallocation_of_the_initial_value_of_a_single_key_key_path]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__should_add_and_remove_an_observer]' started.
  rac_addObserver:forKeyPath:options:block: should add and remove an observer
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__should_add_and_remove_an_observer]' passed (0.001 seconds).
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__should_accept_a_nil_observer]' started.
  rac_addObserver:forKeyPath:options:block: should accept a nil observer
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__should_accept_a_nil_observer]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__automatically_stops_KVO_on_subclasses_when_the_target_deallocates]' started.
  rac_addObserver:forKeyPath:options:block: automatically stops KVO on subclasses when the target deallocates
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__automatically_stops_KVO_on_subclasses_when_the_target_deallocates]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__should_automatically_stop_KVO_when_the_observer_deallocates]' started.
  rac_addObserver:forKeyPath:options:block: should automatically stop KVO when the observer deallocates
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__should_automatically_stop_KVO_when_the_observer_deallocates]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__should_stop_KVO_when_the_observer_is_disposed]' started.
  rac_addObserver:forKeyPath:options:block: should stop KVO when the observer is disposed
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__should_stop_KVO_when_the_observer_is_disposed]' passed (0.000 seconds).
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__should_distinguish_between_observers_being_disposed]' started.
  rac_addObserver:forKeyPath:options:block: should distinguish between observers being disposed
Test Case '-[RACKVOWrapperSpec rac_addObserver_forKeyPath_options_block__should_distinguish_between_observers_being_disposed]' passed (0.000 seconds).
Test Suite 'RACKVOWrapperSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 39 tests, with 0 failures (0 unexpected) in 0.018 (0.027) seconds
Test Suite 'RACMulticastConnectionSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACMulticastConnectionSpec _connect_should_subscribe_to_the_underlying_signal]' started.
  -connect should subscribe to the underlying signal
Test Case '-[RACMulticastConnectionSpec _connect_should_subscribe_to_the_underlying_signal]' passed (0.000 seconds).
Test Case '-[RACMulticastConnectionSpec _connect_should_return_the_same_disposable_for_each_invocation]' started.
  -connect should return the same disposable for each invocation
Test Case '-[RACMulticastConnectionSpec _connect_should_return_the_same_disposable_for_each_invocation]' passed (0.003 seconds).
Test Case '-[RACMulticastConnectionSpec _connect_shouldn_t_reconnect_after_disposal]' started.
  -connect shouldn't reconnect after disposal
Test Case '-[RACMulticastConnectionSpec _connect_shouldn_t_reconnect_after_disposal]' passed (0.000 seconds).
Test Case '-[RACMulticastConnectionSpec _connect_shouldn_t_race_when_connecting]' started.
  -connect shouldn't race when connecting
Test Case '-[RACMulticastConnectionSpec _connect_shouldn_t_race_when_connecting]' passed (0.000 seconds).
Test Case '-[RACMulticastConnectionSpec _autoconnect_should_subscribe_to_the_multicasted_signal_on_the_first_subscription]' started.
  -autoconnect should subscribe to the multicasted signal on the first subscription
Test Case '-[RACMulticastConnectionSpec _autoconnect_should_subscribe_to_the_multicasted_signal_on_the_first_subscription]' passed (0.000 seconds).
Test Case '-[RACMulticastConnectionSpec _autoconnect_should_dispose_of_the_multicasted_subscription_when_the_signal_has_no_subscribers]' started.
  -autoconnect should dispose of the multicasted subscription when the signal has no subscribers
Test Case '-[RACMulticastConnectionSpec _autoconnect_should_dispose_of_the_multicasted_subscription_when_the_signal_has_no_subscribers]' passed (0.000 seconds).
Test Case '-[RACMulticastConnectionSpec _autoconnect_shouldn_t_reconnect_after_disposal]' started.
  -autoconnect shouldn't reconnect after disposal
Test Case '-[RACMulticastConnectionSpec _autoconnect_shouldn_t_reconnect_after_disposal]' passed (0.000 seconds).
Test Case '-[RACMulticastConnectionSpec _autoconnect_should_replay_values_after_disposal_when_multicasted_to_a_replay_subject]' started.
  -autoconnect should replay values after disposal when multicasted to a replay subject
Test Case '-[RACMulticastConnectionSpec _autoconnect_should_replay_values_after_disposal_when_multicasted_to_a_replay_subject]' passed (0.001 seconds).
Test Suite 'RACMulticastConnectionSpec' finished at 2013-12-10 23:13:10 +0000.
Executed 8 tests, with 0 failures (0 unexpected) in 0.004 (0.008) seconds
Test Suite 'RACSchedulerSpec' started at 2013-12-10 23:13:10 +0000
Test Case '-[RACSchedulerSpec should_know_its_current_scheduler]' started.
  should know its current scheduler
Test Case '-[RACSchedulerSpec should_know_its_current_scheduler]' passed (0.043 seconds).
Test Case '-[RACSchedulerSpec _mainThreadScheduler_should_cancel_scheduled_blocks_when_disposed]' started.
  +mainThreadScheduler should cancel scheduled blocks when disposed
Test Case '-[RACSchedulerSpec _mainThreadScheduler_should_cancel_scheduled_blocks_when_disposed]' passed (0.010 seconds).
Test Case '-[RACSchedulerSpec _mainThreadScheduler_should_schedule_future_blocks]' started.
  +mainThreadScheduler should schedule future blocks
Test Case '-[RACSchedulerSpec _mainThreadScheduler_should_schedule_future_blocks]' passed (0.010 seconds).
Test Case '-[RACSchedulerSpec _mainThreadScheduler_should_cancel_future_blocks_when_disposed]' started.
  +mainThreadScheduler should cancel future blocks when disposed
Test Case '-[RACSchedulerSpec _mainThreadScheduler_should_cancel_future_blocks_when_disposed]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _mainThreadScheduler_should_schedule_recurring_blocks]' started.
  +mainThreadScheduler should schedule recurring blocks
Test Case '-[RACSchedulerSpec _mainThreadScheduler_should_schedule_recurring_blocks]' passed (0.209 seconds).
Test Case '-[RACSchedulerSpec _scheduler_should_cancel_scheduled_blocks_when_disposed]' started.
  +scheduler should cancel scheduled blocks when disposed
Test Case '-[RACSchedulerSpec _scheduler_should_cancel_scheduled_blocks_when_disposed]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _scheduler_should_schedule_future_blocks]' started.
  +scheduler should schedule future blocks
Test Case '-[RACSchedulerSpec _scheduler_should_schedule_future_blocks]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _scheduler_should_cancel_future_blocks_when_disposed]' started.
  +scheduler should cancel future blocks when disposed
Test Case '-[RACSchedulerSpec _scheduler_should_cancel_future_blocks_when_disposed]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _scheduler_should_schedule_recurring_blocks]' started.
  +scheduler should schedule recurring blocks
Test Case '-[RACSchedulerSpec _scheduler_should_schedule_recurring_blocks]' passed (0.210 seconds).
Test Case '-[RACSchedulerSpec _subscriptionScheduler_setting__currentScheduler_should_be_the__mainThreadScheduler_when_scheduled_from_the_main_queue]' started.
  +subscriptionScheduler setting +currentScheduler should be the +mainThreadScheduler when scheduled from the main queue
Test Case '-[RACSchedulerSpec _subscriptionScheduler_setting__currentScheduler_should_be_the__mainThreadScheduler_when_scheduled_from_the_main_queue]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _subscriptionScheduler_setting__currentScheduler_should_be_a__scheduler_when_scheduled_from_an_unknown_queue]' started.
  +subscriptionScheduler setting +currentScheduler should be a +scheduler when scheduled from an unknown queue
Test Case '-[RACSchedulerSpec _subscriptionScheduler_setting__currentScheduler_should_be_a__scheduler_when_scheduled_from_an_unknown_queue]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _subscriptionScheduler_setting__currentScheduler_should_equal_the_background_scheduler_from_which_the_block_was_scheduled]' started.
  +subscriptionScheduler setting +currentScheduler should equal the background scheduler from which the block was scheduled
Test Case '-[RACSchedulerSpec _subscriptionScheduler_setting__currentScheduler_should_equal_the_background_scheduler_from_which_the_block_was_scheduled]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _subscriptionScheduler_should_execute_scheduled_blocks_immediately_if_it_s_in_a_scheduler_already]' started.
  +subscriptionScheduler should execute scheduled blocks immediately if it's in a scheduler already
Test Case '-[RACSchedulerSpec _subscriptionScheduler_should_execute_scheduled_blocks_immediately_if_it_s_in_a_scheduler_already]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _immediateScheduler_should_immediately_execute_scheduled_blocks]' started.
  +immediateScheduler should immediately execute scheduled blocks
Test Case '-[RACSchedulerSpec _immediateScheduler_should_immediately_execute_scheduled_blocks]' passed (0.000 seconds).
Test Case '-[RACSchedulerSpec _immediateScheduler_should_block_for_future_scheduled_blocks]' started.
  +immediateScheduler should block for future scheduled blocks
Test Case '-[RACSchedulerSpec _immediateScheduler_should_block_for_future_scheduled_blocks]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_a_synchronous_scheduler_should_behave_like_a_normal_block_when_it_doesn_t_invoke_itself]' started.
  -scheduleRecursiveBlock: with a synchronous scheduler should behave like a normal block when it doesn't invoke itself
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_a_synchronous_scheduler_should_behave_like_a_normal_block_when_it_doesn_t_invoke_itself]' passed (0.000 seconds).
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_a_synchronous_scheduler_should_reschedule_itself_after_the_caller_completes]' started.
  -scheduleRecursiveBlock: with a synchronous scheduler should reschedule itself after the caller completes
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_a_synchronous_scheduler_should_reschedule_itself_after_the_caller_completes]' passed (0.000 seconds).
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_an_asynchronous_scheduler_should_behave_like_a_normal_block_when_it_doesn_t_invoke_itself]' started.
  -scheduleRecursiveBlock: with an asynchronous scheduler should behave like a normal block when it doesn't invoke itself
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_an_asynchronous_scheduler_should_behave_like_a_normal_block_when_it_doesn_t_invoke_itself]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_an_asynchronous_scheduler_should_reschedule_itself_after_the_caller_completes]' started.
  -scheduleRecursiveBlock: with an asynchronous scheduler should reschedule itself after the caller completes
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_an_asynchronous_scheduler_should_reschedule_itself_after_the_caller_completes]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_an_asynchronous_scheduler_should_reschedule_when_invoked_asynchronously]' started.
  -scheduleRecursiveBlock: with an asynchronous scheduler should reschedule when invoked asynchronously
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_an_asynchronous_scheduler_should_reschedule_when_invoked_asynchronously]' passed (0.033 seconds).
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_an_asynchronous_scheduler_shouldn_t_reschedule_itself_when_disposed]' started.
  -scheduleRecursiveBlock: with an asynchronous scheduler shouldn't reschedule itself when disposed
Test Case '-[RACSchedulerSpec _scheduleRecursiveBlock__with_an_asynchronous_scheduler_shouldn_t_reschedule_itself_when_disposed]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec subclassing_should_invoke_blocks_scheduled_with__schedule_]' started.
  subclassing should invoke blocks scheduled with -schedule:
Test Case '-[RACSchedulerSpec subclassing_should_invoke_blocks_scheduled_with__schedule_]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec subclassing_should_invoke_blocks_scheduled_with__after_schedule_]' started.
  subclassing should invoke blocks scheduled with -after:schedule:
Test Case '-[RACSchedulerSpec subclassing_should_invoke_blocks_scheduled_with__after_schedule_]' passed (0.011 seconds).
Test Case '-[RACSchedulerSpec subclassing_should_set_a_valid_current_scheduler]' started.
  subclassing should set a valid current scheduler
Test Case '-[RACSchedulerSpec subclassing_should_set_a_valid_current_scheduler]' passed (0.011 seconds).
Test Suite 'RACSchedulerSpec' finished at 2013-12-10 23:13:11 +0000.
Executed 24 tests, with 0 failures (0 unexpected) in 0.684 (0.688) seconds
Test Suite 'RACSequenceAdditionsSpec' started at 2013-12-10 23:13:11 +0000
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSArray sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_return_an_array]' started.
  NSArray sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSArray sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.003 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSArray sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.003 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSArray sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSArray sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSArray sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.023 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSArray sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSArray sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_archive]' started.
  NSArray sequences RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_fold_right]' started.
  NSArray sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_fold_right]' passed (0.004 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_fold_left]' started.
  NSArray sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_RACSequenceExamples_should_fold_left]' passed (0.022 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSArray sequences should be immutable RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' started.
  NSArray sequences should be immutable RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSArray sequences should be immutable RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.002 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSArray sequences should be immutable RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.003 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSArray sequences should be immutable RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSArray sequences should be immutable RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSArray sequences should be immutable RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.023 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSArray sequences should be immutable RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSArray sequences should be immutable RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_archive]' started.
  NSArray sequences should be immutable RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' started.
  NSArray sequences should be immutable RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' passed (0.004 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' started.
  NSArray sequences should be immutable RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' passed (0.022 seconds).
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_fast_enumerate_after_zipping]' started.
  NSArray sequences should fast enumerate after zipping
Test Case '-[RACSequenceAdditionsSpec NSArray_sequences_should_fast_enumerate_after_zipping]' passed (0.002 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSDictionary sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_array]' started.
  NSDictionary sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSDictionary sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSDictionary sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSDictionary sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSDictionary sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSDictionary sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSDictionary sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSDictionary sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_archive]' started.
  NSDictionary sequences RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_archive]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_right]' started.
  NSDictionary sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_right]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_left]' started.
  NSDictionary sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_left]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSDictionary sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_array]' started.
  NSDictionary sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSDictionary sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.003 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSDictionary sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSDictionary sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSDictionary sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSDictionary sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSDictionary sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSDictionary sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_archive]' started.
  NSDictionary sequences RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_right]' started.
  NSDictionary sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_right]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_left]' started.
  NSDictionary sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_left]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSDictionary sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_array]' started.
  NSDictionary sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSDictionary sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSDictionary sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSDictionary sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSDictionary sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSDictionary sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSDictionary sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSDictionary sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_archive]' started.
  NSDictionary sequences RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_archive]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_right]' started.
  NSDictionary sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_right]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_left]' started.
  NSDictionary sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_RACSequenceExamples_should_fold_left]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSDictionary sequences should be immutable RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSDictionary sequences should be immutable RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSDictionary sequences should be immutable RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_archive]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_archive]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSDictionary sequences should be immutable RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.003 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSDictionary sequences should be immutable RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSDictionary sequences should be immutable RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.012 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_archive]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSDictionary sequences should be immutable RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSDictionary sequences should be immutable RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSDictionary sequences should be immutable RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.012 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_archive]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' started.
  NSDictionary sequences should be immutable RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSDictionary_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSOrderedSet sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_return_an_array]' started.
  NSOrderedSet sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSOrderedSet sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.002 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSOrderedSet sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.003 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSOrderedSet sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSOrderedSet sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSOrderedSet sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.023 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSOrderedSet sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSOrderedSet sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_archive]' started.
  NSOrderedSet sequences RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_fold_right]' started.
  NSOrderedSet sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_fold_right]' passed (0.004 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_fold_left]' started.
  NSOrderedSet sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_RACSequenceExamples_should_fold_left]' passed (0.024 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.002 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.003 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.023 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_archive]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_archive]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' passed (0.005 seconds).
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' started.
  NSOrderedSet sequences should be immutable RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSOrderedSet_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' passed (0.022 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSSet sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_return_an_array]' started.
  NSSet sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSSet sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.002 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSSet sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.004 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSSet sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSSet sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSSet sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.024 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSSet sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSSet sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_archive]' started.
  NSSet sequences RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_archive]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_fold_right]' started.
  NSSet sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_fold_right]' passed (0.008 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_fold_left]' started.
  NSSet sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_RACSequenceExamples_should_fold_left]' passed (0.024 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSSet sequences should be immutable RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' started.
  NSSet sequences should be immutable RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSSet sequences should be immutable RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.002 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSSet sequences should be immutable RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.003 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSSet sequences should be immutable RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSSet sequences should be immutable RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSSet sequences should be immutable RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.028 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSSet sequences should be immutable RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSSet sequences should be immutable RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_archive]' started.
  NSSet sequences should be immutable RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_archive]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' started.
  NSSet sequences should be immutable RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' passed (0.005 seconds).
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' started.
  NSSet sequences should be immutable RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSSet_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' passed (0.022 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSString sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_return_an_array]' started.
  NSString sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSString sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSString sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSString sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.017 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSString sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSString sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSString sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSString sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_archive]' started.
  NSString sequences RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_archive]' passed (0.002 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_fold_right]' started.
  NSString sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_fold_right]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_fold_left]' started.
  NSString sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_RACSequenceExamples_should_fold_left]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  NSString sequences should be immutable RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' started.
  NSString sequences should be immutable RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_return_an_array]' passed (0.003 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  NSString sequences should be immutable RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  NSString sequences should be immutable RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.004 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  NSString sequences should be immutable RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' started.
  NSString sequences should be immutable RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  NSString sequences should be immutable RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  NSString sequences should be immutable RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  NSString sequences should be immutable RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_archive]' started.
  NSString sequences should be immutable RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' started.
  NSString sequences should be immutable RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_fold_right]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' started.
  NSString sequences should be immutable RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_be_immutable_RACSequenceExamples_should_fold_left]' passed (0.001 seconds).
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_work_with_composed_characters]' started.
  NSString sequences should work with composed characters
Test Case '-[RACSequenceAdditionsSpec NSString_sequences_should_work_with_composed_characters]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  RACTuple sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_return_an_array]' started.
  RACTuple sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  RACTuple sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  RACTuple sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  RACTuple sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  RACTuple sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  RACTuple sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  RACTuple sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.009 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  RACTuple sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_archive]' started.
  RACTuple sequences RACSequenceExamples should archive
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_fold_right]' started.
  RACTuple sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_fold_right]' passed (0.002 seconds).
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_fold_left]' started.
  RACTuple sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceAdditionsSpec RACTuple_sequences_RACSequenceExamples_should_fold_left]' passed (0.000 seconds).
Test Suite 'RACSequenceAdditionsSpec' finished at 2013-12-10 23:13:12 +0000.
Executed 182 tests, with 0 failures (0 unexpected) in 0.579 (0.631) seconds
Test Suite 'RACSequenceSpec' started at 2013-12-10 23:13:12 +0000
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_return_an_empty_stream]' started.
  RACStream RACStreamExamples should return an empty stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_return_an_empty_stream]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_lift_a_value_into_a_stream]' started.
  RACStream RACStreamExamples should lift a value into a stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_lift_a_value_into_a_stream]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__concat__should_concatenate_two_streams]' started.
  RACStream RACStreamExamples -concat: should concatenate two streams
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__concat__should_concatenate_two_streams]' passed (0.007 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__concat__should_concatenate_three_streams]' started.
  RACStream RACStreamExamples -concat: should concatenate three streams
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__concat__should_concatenate_three_streams]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__concat__should_concatenate_around_an_empty_stream]' started.
  RACStream RACStreamExamples -concat: should concatenate around an empty stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__concat__should_concatenate_around_an_empty_stream]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_flatten]' started.
  RACStream RACStreamExamples should flatten
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_flatten]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_return_the_result_of_binding_a_single_value]' started.
  RACStream RACStreamExamples -bind: should return the result of binding a single value
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_return_the_result_of_binding_a_single_value]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_concatenate_the_result_of_binding_multiple_values]' started.
  RACStream RACStreamExamples -bind: should concatenate the result of binding multiple values
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_concatenate_the_result_of_binding_multiple_values]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_concatenate_with_an_empty_result_from_binding_a_value]' started.
  RACStream RACStreamExamples -bind: should concatenate with an empty result from binding a value
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_concatenate_with_an_empty_result_from_binding_a_value]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_terminate_immediately_when_returning_nil]' started.
  RACStream RACStreamExamples -bind: should terminate immediately when returning nil
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_terminate_immediately_when_returning_nil]' passed (0.004 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_terminate_after_one_value_when_setting__stop_]' started.
  RACStream RACStreamExamples -bind: should terminate after one value when setting 'stop'
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_terminate_after_one_value_when_setting__stop_]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_terminate_immediately_when_returning_nil_and_setting__stop_]' started.
  RACStream RACStreamExamples -bind: should terminate immediately when returning nil and setting 'stop'
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_terminate_immediately_when_returning_nil_and_setting__stop_]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_be_restartable_even_with_block_state]' started.
  RACStream RACStreamExamples -bind: should be restartable even with block state
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_be_restartable_even_with_block_state]' passed (0.002 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_be_interleavable_even_with_block_state]' started.
  RACStream RACStreamExamples -bind: should be interleavable even with block state
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__bind__should_be_interleavable_even_with_block_state]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__flattenMap__should_return_a_single_mapped_result]' started.
  RACStream RACStreamExamples -flattenMap: should return a single mapped result
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__flattenMap__should_return_a_single_mapped_result]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__flattenMap__should_concatenate_the_results_of_mapping_multiple_values]' started.
  RACStream RACStreamExamples -flattenMap: should concatenate the results of mapping multiple values
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__flattenMap__should_concatenate_the_results_of_mapping_multiple_values]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__flattenMap__should_concatenate_with_an_empty_result_from_mapping_a_value]' started.
  RACStream RACStreamExamples -flattenMap: should concatenate with an empty result from mapping a value
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__flattenMap__should_concatenate_with_an_empty_result_from_mapping_a_value]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_map]' started.
  RACStream RACStreamExamples should map
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_map]' passed (0.002 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_map_and_replace]' started.
  RACStream RACStreamExamples should map and replace
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_map_and_replace]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_filter]' started.
  RACStream RACStreamExamples should filter
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_filter]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__ignore__should_ignore_a_value]' started.
  RACStream RACStreamExamples -ignore: should ignore a value
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__ignore__should_ignore_a_value]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__ignore__should_ignore_based_on_object_equality]' started.
  RACStream RACStreamExamples -ignore: should ignore based on object equality
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__ignore__should_ignore_based_on_object_equality]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_start_with_a_value]' started.
  RACStream RACStreamExamples should start with a value
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_start_with_a_value]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__skip__should_skip_any_valid_number_of_values]' started.
  RACStream RACStreamExamples -skip: should skip any valid number of values
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__skip__should_skip_any_valid_number_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__skip__should_return_an_empty_stream_when_skipping_too_many_values]' started.
  RACStream RACStreamExamples -skip: should return an empty stream when skipping too many values
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__skip__should_return_an_empty_stream_when_skipping_too_many_values]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__take__with_three_values_should_take_any_valid_number_of_values]' started.
  RACStream RACStreamExamples -take: with three values should take any valid number of values
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__take__with_three_values_should_take_any_valid_number_of_values]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__take__with_three_values_should_return_the_same_stream_when_taking_too_many_values]' started.
  RACStream RACStreamExamples -take: with three values should return the same stream when taking too many values
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__take__with_three_values_should_return_the_same_stream_when_taking_too_many_values]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__take__should_take_and_terminate_from_an_infinite_stream]' started.
  RACStream RACStreamExamples -take: should take and terminate from an infinite stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__take__should_take_and_terminate_from_an_infinite_stream]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__take__should_take_and_terminate_from_a_single_item_stream]' started.
  RACStream RACStreamExamples -take: should take and terminate from a single-item stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__take__should_take_and_terminate_from_a_single_item_stream]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_make_a_stream_of_tuples]' started.
  RACStream RACStreamExamples zip stream creation methods -zipWith: should make a stream of tuples
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_make_a_stream_of_tuples]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_truncate_streams]' started.
  RACStream RACStreamExamples zip stream creation methods -zipWith: should truncate streams
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_truncate_streams]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_work_on_infinite_streams]' started.
  RACStream RACStreamExamples zip stream creation methods -zipWith: should work on infinite streams
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_work_on_infinite_streams]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_handle_multiples_of_the_same_stream]' started.
  RACStream RACStreamExamples zip stream creation methods -zipWith: should handle multiples of the same stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_handle_multiples_of_the_same_stream]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_reduce_values]' started.
  RACStream RACStreamExamples zip stream creation methods +zip:reduce: should reduce values
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_reduce_values]' passed (0.003 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_truncate_streams]' started.
  RACStream RACStreamExamples zip stream creation methods +zip:reduce: should truncate streams
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_truncate_streams]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_work_on_infinite_streams]' started.
  RACStream RACStreamExamples zip stream creation methods +zip:reduce: should work on infinite streams
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_work_on_infinite_streams]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_handle_multiples_of_the_same_stream]' started.
  RACStream RACStreamExamples zip stream creation methods +zip:reduce: should handle multiples of the same stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_handle_multiples_of_the_same_stream]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_single_value]' started.
  RACStream RACStreamExamples zip stream creation methods +zip: should make a stream of tuples out of single value
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_single_value]' passed (0.002 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_an_array_of_streams]' started.
  RACStream RACStreamExamples zip stream creation methods +zip: should make a stream of tuples out of an array of streams
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_an_array_of_streams]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_an_empty_stream_if_given_an_empty_array]' started.
  RACStream RACStreamExamples zip stream creation methods +zip: should make an empty stream if given an empty array
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_an_empty_stream_if_given_an_empty_array]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_an_enumerator_of_streams]' started.
  RACStream RACStreamExamples zip stream creation methods +zip: should make a stream of tuples out of an enumerator of streams
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_an_enumerator_of_streams]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_an_empty_stream_if_given_an_empty_enumerator]' started.
  RACStream RACStreamExamples zip stream creation methods +zip: should make an empty stream if given an empty enumerator
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_an_empty_stream_if_given_an_empty_enumerator]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__concat__should_concatenate_an_array_of_streams]' started.
  RACStream RACStreamExamples +concat: should concatenate an array of streams
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__concat__should_concatenate_an_array_of_streams]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__concat__should_concatenate_an_enumerator_of_streams]' started.
  RACStream RACStreamExamples +concat: should concatenate an enumerator of streams
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__concat__should_concatenate_an_enumerator_of_streams]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_scan]' started.
  RACStream RACStreamExamples should scan
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_scan]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_until_a_predicate_is_true]' started.
  RACStream RACStreamExamples taking with a predicate should take until a predicate is true
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_until_a_predicate_is_true]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_while_a_predicate_is_true]' started.
  RACStream RACStreamExamples taking with a predicate should take while a predicate is true
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_while_a_predicate_is_true]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_a_full_stream]' started.
  RACStream RACStreamExamples taking with a predicate should take a full stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_a_full_stream]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_return_an_empty_stream]' started.
  RACStream RACStreamExamples taking with a predicate should return an empty stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_return_an_empty_stream]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_terminate_an_infinite_stream]' started.
  RACStream RACStreamExamples taking with a predicate should terminate an infinite stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_terminate_an_infinite_stream]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_until_a_predicate_is_true]' started.
  RACStream RACStreamExamples skipping with a predicate should skip until a predicate is true
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_until_a_predicate_is_true]' passed (0.002 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_while_a_predicate_is_true]' started.
  RACStream RACStreamExamples skipping with a predicate should skip while a predicate is true
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_while_a_predicate_is_true]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_a_full_stream]' started.
  RACStream RACStreamExamples skipping with a predicate should skip a full stream
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_a_full_stream]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_finish_skipping_immediately]' started.
  RACStream RACStreamExamples skipping with a predicate should finish skipping immediately
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_finish_skipping_immediately]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__combinePreviousWithStart_reduce__should_pass_the_previous_next_into_the_reduce_block]' started.
  RACStream RACStreamExamples -combinePreviousWithStart:reduce: should pass the previous next into the reduce block
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__combinePreviousWithStart_reduce__should_pass_the_previous_next_into_the_reduce_block]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__combinePreviousWithStart_reduce__should_send_the_combined_value]' started.
  RACStream RACStreamExamples -combinePreviousWithStart:reduce: should send the combined value
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples__combinePreviousWithStart_reduce__should_send_the_combined_value]' passed (0.003 seconds).
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_reduce_tuples]' started.
  RACStream RACStreamExamples should reduce tuples
Test Case '-[RACSequenceSpec RACStream_RACStreamExamples_should_reduce_tuples]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _sequenceWithHeadBlock_tailBlock__should_use_the_values_from_the_head_and_tail_blocks]' started.
  +sequenceWithHeadBlock:tailBlock: should use the values from the head and tail blocks
Test Case '-[RACSequenceSpec _sequenceWithHeadBlock_tailBlock__should_use_the_values_from_the_head_and_tail_blocks]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _sequenceWithHeadBlock_tailBlock__should_lazily_invoke_head_and_tail_blocks]' started.
  +sequenceWithHeadBlock:tailBlock: should lazily invoke head and tail blocks
Test Case '-[RACSequenceSpec _sequenceWithHeadBlock_tailBlock__should_lazily_invoke_head_and_tail_blocks]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  empty sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_return_an_array]' started.
  empty sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  empty sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  empty sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.004 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  empty sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  empty sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  empty sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  empty sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  empty sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_archive]' started.
  empty sequences RACSequenceExamples should archive
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_fold_right]' started.
  empty sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_fold_right]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_fold_left]' started.
  empty sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceSpec empty_sequences_RACSequenceExamples_should_fold_left]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  non-empty sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.002 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_return_an_array]' started.
  non-empty sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  non-empty sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  non-empty sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  non-empty sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  non-empty sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  non-empty sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  non-empty sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.004 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  non-empty sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_archive]' started.
  non-empty sequences RACSequenceExamples should archive
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_fold_right]' started.
  non-empty sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_fold_right]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_fold_left]' started.
  non-empty sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceSpec non_empty_sequences_RACSequenceExamples_should_fold_left]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  eager sequences RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_return_an_array]' started.
  eager sequences RACSequenceExamples should return an array
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  eager sequences RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.005 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  eager sequences RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  eager sequences RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_be_equal_to_itself]' started.
  eager sequences RACSequenceExamples should be equal to itself
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  eager sequences RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  eager sequences RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  eager sequences RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_archive]' started.
  eager sequences RACSequenceExamples should archive
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_archive]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_fold_right]' started.
  eager sequences RACSequenceExamples should fold right
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_fold_right]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_fold_left]' started.
  eager sequences RACSequenceExamples should fold left
Test Case '-[RACSequenceSpec eager_sequences_RACSequenceExamples_should_fold_left]' passed (0.002 seconds).
Test Case '-[RACSequenceSpec eager_sequences_should_evaluate_all_values_immediately]' started.
  eager sequences should evaluate all values immediately
Test Case '-[RACSequenceSpec eager_sequences_should_evaluate_all_values_immediately]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _take__should_complete_take__without_needing_the_head_of_the_second_item_in_the_sequence]' started.
  -take: should complete take: without needing the head of the second item in the sequence
Test Case '-[RACSequenceSpec _take__should_complete_take__without_needing_the_head_of_the_second_item_in_the_sequence]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _bind__should_only_evaluate_head_when_the_resulting_sequence_is_evaluated]' started.
  -bind: should only evaluate head when the resulting sequence is evaluated
Test Case '-[RACSequenceSpec _bind__should_only_evaluate_head_when_the_resulting_sequence_is_evaluated]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _objectEnumerator_should_only_evaluate_head_as_it_s_enumerated]' started.
  -objectEnumerator should only evaluate head as it's enumerated
Test Case '-[RACSequenceSpec _objectEnumerator_should_only_evaluate_head_as_it_s_enumerated]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _objectEnumerator_should_let_the_sequence_dealloc_as_it_s_enumerated]' started.
  -objectEnumerator should let the sequence dealloc as it's enumerated
Test Case '-[RACSequenceSpec _objectEnumerator_should_let_the_sequence_dealloc_as_it_s_enumerated]' passed (0.001 seconds).
Test Case '-[RACSequenceSpec shouldn_t_overflow_the_stack_when_deallocated_on_a_background_queue]' started.
  shouldn't overflow the stack when deallocated on a background queue
Test Case '-[RACSequenceSpec shouldn_t_overflow_the_stack_when_deallocated_on_a_background_queue]' passed (0.139 seconds).
Test Case '-[RACSequenceSpec _foldLeftWithStart_reduce__should_reduce_with_start_first]' started.
  -foldLeftWithStart:reduce: should reduce with start first
Test Case '-[RACSequenceSpec _foldLeftWithStart_reduce__should_reduce_with_start_first]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _foldLeftWithStart_reduce__should_be_left_associative]' started.
  -foldLeftWithStart:reduce: should be left associative
Test Case '-[RACSequenceSpec _foldLeftWithStart_reduce__should_be_left_associative]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _foldRightWithStart_reduce__should_be_lazy]' started.
  -foldRightWithStart:reduce: should be lazy
Test Case '-[RACSequenceSpec _foldRightWithStart_reduce__should_be_lazy]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _foldRightWithStart_reduce__should_reduce_with_start_last]' started.
  -foldRightWithStart:reduce: should reduce with start last
Test Case '-[RACSequenceSpec _foldRightWithStart_reduce__should_reduce_with_start_last]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _foldRightWithStart_reduce__should_be_right_associative]' started.
  -foldRightWithStart:reduce: should be right associative
Test Case '-[RACSequenceSpec _foldRightWithStart_reduce__should_be_right_associative]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _any_should_return_true_when_at_least_one_exists]' started.
  -any should return true when at least one exists
Test Case '-[RACSequenceSpec _any_should_return_true_when_at_least_one_exists]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _any_should_return_false_when_no_such_thing_exists]' started.
  -any should return false when no such thing exists
Test Case '-[RACSequenceSpec _any_should_return_false_when_no_such_thing_exists]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _all_should_return_true_when_all_values_pass]' started.
  -all should return true when all values pass
Test Case '-[RACSequenceSpec _all_should_return_true_when_all_values_pass]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _all_should_return_false_when_at_least_one_value_fails]' started.
  -all should return false when at least one value fails
Test Case '-[RACSequenceSpec _all_should_return_false_when_at_least_one_value_fails]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _objectPassingTest__should_return_leftmost_object_that_passes_the_test]' started.
  -objectPassingTest: should return leftmost object that passes the test
Test Case '-[RACSequenceSpec _objectPassingTest__should_return_leftmost_object_that_passes_the_test]' passed (0.000 seconds).
Test Case '-[RACSequenceSpec _objectPassingTest__should_return_nil_if_no_objects_pass_the_test]' started.
  -objectPassingTest: should return nil if no objects pass the test
Test Case '-[RACSequenceSpec _objectPassingTest__should_return_nil_if_no_objects_pass_the_test]' passed (0.000 seconds).
Test Suite 'RACSequenceSpec' finished at 2013-12-10 23:13:12 +0000.
Executed 112 tests, with 0 failures (0 unexpected) in 0.245 (0.273) seconds
Test Suite 'RACSerialDisposableSpec' started at 2013-12-10 23:13:12 +0000
Test Case '-[RACSerialDisposableSpec should_initialize_with__init]' started.
  should initialize with -init
Test Case '-[RACSerialDisposableSpec should_initialize_with__init]' passed (0.000 seconds).
Test Case '-[RACSerialDisposableSpec should_initialize_an_inner_disposable_with__initWithBlock_]' started.
  should initialize an inner disposable with -initWithBlock:
Test Case '-[RACSerialDisposableSpec should_initialize_an_inner_disposable_with__initWithBlock_]' passed (0.000 seconds).
Test Case '-[RACSerialDisposableSpec should_initialize_with_a_disposable]' started.
  should initialize with a disposable
Test Case '-[RACSerialDisposableSpec should_initialize_with_a_disposable]' passed (0.000 seconds).
Test Case '-[RACSerialDisposableSpec should_dispose_of_the_inner_disposable]' started.
  should dispose of the inner disposable
Test Case '-[RACSerialDisposableSpec should_dispose_of_the_inner_disposable]' passed (0.000 seconds).
Test Case '-[RACSerialDisposableSpec should_dispose_of_a_new_inner_disposable_if_it_s_already_been_disposed]' started.
  should dispose of a new inner disposable if it's already been disposed
Test Case '-[RACSerialDisposableSpec should_dispose_of_a_new_inner_disposable_if_it_s_already_been_disposed]' passed (0.000 seconds).
Test Case '-[RACSerialDisposableSpec should_allow_the_inner_disposable_to_be_set_to_nil]' started.
  should allow the inner disposable to be set to nil
Test Case '-[RACSerialDisposableSpec should_allow_the_inner_disposable_to_be_set_to_nil]' passed (0.000 seconds).
Test Case '-[RACSerialDisposableSpec should_swap_inner_disposables]' started.
  should swap inner disposables
Test Case '-[RACSerialDisposableSpec should_swap_inner_disposables]' passed (0.000 seconds).
Test Case '-[RACSerialDisposableSpec should_release_the_inner_disposable_upon_deallocation]' started.
  should release the inner disposable upon deallocation
Test Case '-[RACSerialDisposableSpec should_release_the_inner_disposable_upon_deallocation]' passed (0.002 seconds).
Test Suite 'RACSerialDisposableSpec' finished at 2013-12-10 23:13:12 +0000.
Executed 8 tests, with 0 failures (0 unexpected) in 0.003 (0.012) seconds
Test Suite 'RACSignalSpec' started at 2013-12-10 23:13:12 +0000
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_return_an_empty_stream]' started.
  RACStream RACStreamExamples should return an empty stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_return_an_empty_stream]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_lift_a_value_into_a_stream]' started.
  RACStream RACStreamExamples should lift a value into a stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_lift_a_value_into_a_stream]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__concat__should_concatenate_two_streams]' started.
  RACStream RACStreamExamples -concat: should concatenate two streams
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__concat__should_concatenate_two_streams]' passed (0.010 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__concat__should_concatenate_three_streams]' started.
  RACStream RACStreamExamples -concat: should concatenate three streams
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__concat__should_concatenate_three_streams]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__concat__should_concatenate_around_an_empty_stream]' started.
  RACStream RACStreamExamples -concat: should concatenate around an empty stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__concat__should_concatenate_around_an_empty_stream]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_flatten]' started.
  RACStream RACStreamExamples should flatten
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_flatten]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_return_the_result_of_binding_a_single_value]' started.
  RACStream RACStreamExamples -bind: should return the result of binding a single value
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_return_the_result_of_binding_a_single_value]' passed (0.003 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_concatenate_the_result_of_binding_multiple_values]' started.
  RACStream RACStreamExamples -bind: should concatenate the result of binding multiple values
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_concatenate_the_result_of_binding_multiple_values]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_concatenate_with_an_empty_result_from_binding_a_value]' started.
  RACStream RACStreamExamples -bind: should concatenate with an empty result from binding a value
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_concatenate_with_an_empty_result_from_binding_a_value]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_terminate_immediately_when_returning_nil]' started.
  RACStream RACStreamExamples -bind: should terminate immediately when returning nil
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_terminate_immediately_when_returning_nil]' passed (0.013 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_terminate_after_one_value_when_setting__stop_]' started.
  RACStream RACStreamExamples -bind: should terminate after one value when setting 'stop'
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_terminate_after_one_value_when_setting__stop_]' passed (0.012 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_terminate_immediately_when_returning_nil_and_setting__stop_]' started.
  RACStream RACStreamExamples -bind: should terminate immediately when returning nil and setting 'stop'
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_terminate_immediately_when_returning_nil_and_setting__stop_]' passed (0.011 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_be_restartable_even_with_block_state]' started.
  RACStream RACStreamExamples -bind: should be restartable even with block state
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_be_restartable_even_with_block_state]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_be_interleavable_even_with_block_state]' started.
  RACStream RACStreamExamples -bind: should be interleavable even with block state
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__bind__should_be_interleavable_even_with_block_state]' passed (0.002 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__flattenMap__should_return_a_single_mapped_result]' started.
  RACStream RACStreamExamples -flattenMap: should return a single mapped result
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__flattenMap__should_return_a_single_mapped_result]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__flattenMap__should_concatenate_the_results_of_mapping_multiple_values]' started.
  RACStream RACStreamExamples -flattenMap: should concatenate the results of mapping multiple values
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__flattenMap__should_concatenate_the_results_of_mapping_multiple_values]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__flattenMap__should_concatenate_with_an_empty_result_from_mapping_a_value]' started.
  RACStream RACStreamExamples -flattenMap: should concatenate with an empty result from mapping a value
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__flattenMap__should_concatenate_with_an_empty_result_from_mapping_a_value]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_map]' started.
  RACStream RACStreamExamples should map
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_map]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_map_and_replace]' started.
  RACStream RACStreamExamples should map and replace
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_map_and_replace]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_filter]' started.
  RACStream RACStreamExamples should filter
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_filter]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__ignore__should_ignore_a_value]' started.
  RACStream RACStreamExamples -ignore: should ignore a value
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__ignore__should_ignore_a_value]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__ignore__should_ignore_based_on_object_equality]' started.
  RACStream RACStreamExamples -ignore: should ignore based on object equality
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__ignore__should_ignore_based_on_object_equality]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_start_with_a_value]' started.
  RACStream RACStreamExamples should start with a value
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_start_with_a_value]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__skip__should_skip_any_valid_number_of_values]' started.
  RACStream RACStreamExamples -skip: should skip any valid number of values
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__skip__should_skip_any_valid_number_of_values]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__skip__should_return_an_empty_stream_when_skipping_too_many_values]' started.
  RACStream RACStreamExamples -skip: should return an empty stream when skipping too many values
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__skip__should_return_an_empty_stream_when_skipping_too_many_values]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__take__with_three_values_should_take_any_valid_number_of_values]' started.
  RACStream RACStreamExamples -take: with three values should take any valid number of values
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__take__with_three_values_should_take_any_valid_number_of_values]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__take__with_three_values_should_return_the_same_stream_when_taking_too_many_values]' started.
  RACStream RACStreamExamples -take: with three values should return the same stream when taking too many values
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__take__with_three_values_should_return_the_same_stream_when_taking_too_many_values]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__take__should_take_and_terminate_from_an_infinite_stream]' started.
  RACStream RACStreamExamples -take: should take and terminate from an infinite stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__take__should_take_and_terminate_from_an_infinite_stream]' passed (0.033 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__take__should_take_and_terminate_from_a_single_item_stream]' started.
  RACStream RACStreamExamples -take: should take and terminate from a single-item stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__take__should_take_and_terminate_from_a_single_item_stream]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_make_a_stream_of_tuples]' started.
  RACStream RACStreamExamples zip stream creation methods -zipWith: should make a stream of tuples
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_make_a_stream_of_tuples]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_truncate_streams]' started.
  RACStream RACStreamExamples zip stream creation methods -zipWith: should truncate streams
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_truncate_streams]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_work_on_infinite_streams]' started.
  RACStream RACStreamExamples zip stream creation methods -zipWith: should work on infinite streams
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_work_on_infinite_streams]' passed (0.012 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_handle_multiples_of_the_same_stream]' started.
  RACStream RACStreamExamples zip stream creation methods -zipWith: should handle multiples of the same stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zipWith__should_handle_multiples_of_the_same_stream]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_reduce_values]' started.
  RACStream RACStreamExamples zip stream creation methods +zip:reduce: should reduce values
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_reduce_values]' passed (0.002 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_truncate_streams]' started.
  RACStream RACStreamExamples zip stream creation methods +zip:reduce: should truncate streams
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_truncate_streams]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_work_on_infinite_streams]' started.
  RACStream RACStreamExamples zip stream creation methods +zip:reduce: should work on infinite streams
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_work_on_infinite_streams]' passed (0.011 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_handle_multiples_of_the_same_stream]' started.
  RACStream RACStreamExamples zip stream creation methods +zip:reduce: should handle multiples of the same stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip_reduce__should_handle_multiples_of_the_same_stream]' passed (0.002 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_single_value]' started.
  RACStream RACStreamExamples zip stream creation methods +zip: should make a stream of tuples out of single value
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_single_value]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_an_array_of_streams]' started.
  RACStream RACStreamExamples zip stream creation methods +zip: should make a stream of tuples out of an array of streams
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_an_array_of_streams]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_an_empty_stream_if_given_an_empty_array]' started.
  RACStream RACStreamExamples zip stream creation methods +zip: should make an empty stream if given an empty array
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_an_empty_stream_if_given_an_empty_array]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_an_enumerator_of_streams]' started.
  RACStream RACStreamExamples zip stream creation methods +zip: should make a stream of tuples out of an enumerator of streams
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_a_stream_of_tuples_out_of_an_enumerator_of_streams]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_an_empty_stream_if_given_an_empty_enumerator]' started.
  RACStream RACStreamExamples zip stream creation methods +zip: should make an empty stream if given an empty enumerator
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_zip_stream_creation_methods__zip__should_make_an_empty_stream_if_given_an_empty_enumerator]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__concat__should_concatenate_an_array_of_streams]' started.
  RACStream RACStreamExamples +concat: should concatenate an array of streams
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__concat__should_concatenate_an_array_of_streams]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__concat__should_concatenate_an_enumerator_of_streams]' started.
  RACStream RACStreamExamples +concat: should concatenate an enumerator of streams
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__concat__should_concatenate_an_enumerator_of_streams]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_scan]' started.
  RACStream RACStreamExamples should scan
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_scan]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_until_a_predicate_is_true]' started.
  RACStream RACStreamExamples taking with a predicate should take until a predicate is true
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_until_a_predicate_is_true]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_while_a_predicate_is_true]' started.
  RACStream RACStreamExamples taking with a predicate should take while a predicate is true
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_while_a_predicate_is_true]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_a_full_stream]' started.
  RACStream RACStreamExamples taking with a predicate should take a full stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_take_a_full_stream]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_return_an_empty_stream]' started.
  RACStream RACStreamExamples taking with a predicate should return an empty stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_return_an_empty_stream]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_terminate_an_infinite_stream]' started.
  RACStream RACStreamExamples taking with a predicate should terminate an infinite stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_taking_with_a_predicate_should_terminate_an_infinite_stream]' passed (0.021 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_until_a_predicate_is_true]' started.
  RACStream RACStreamExamples skipping with a predicate should skip until a predicate is true
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_until_a_predicate_is_true]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_while_a_predicate_is_true]' started.
  RACStream RACStreamExamples skipping with a predicate should skip while a predicate is true
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_while_a_predicate_is_true]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_a_full_stream]' started.
  RACStream RACStreamExamples skipping with a predicate should skip a full stream
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_skip_a_full_stream]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_finish_skipping_immediately]' started.
  RACStream RACStreamExamples skipping with a predicate should finish skipping immediately
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_skipping_with_a_predicate_should_finish_skipping_immediately]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__combinePreviousWithStart_reduce__should_pass_the_previous_next_into_the_reduce_block]' started.
  RACStream RACStreamExamples -combinePreviousWithStart:reduce: should pass the previous next into the reduce block
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__combinePreviousWithStart_reduce__should_pass_the_previous_next_into_the_reduce_block]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__combinePreviousWithStart_reduce__should_send_the_combined_value]' started.
  RACStream RACStreamExamples -combinePreviousWithStart:reduce: should send the combined value
Test Case '-[RACSignalSpec RACStream_RACStreamExamples__combinePreviousWithStart_reduce__should_send_the_combined_value]' passed (0.001 seconds).
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_reduce_tuples]' started.
  RACStream RACStreamExamples should reduce tuples
Test Case '-[RACSignalSpec RACStream_RACStreamExamples_should_reduce_tuples]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _bind__should_dispose_source_signal_when_stopped_with_nil_signal]' started.
  -bind: should dispose source signal when stopped with nil signal
Test Case '-[RACSignalSpec _bind__should_dispose_source_signal_when_stopped_with_nil_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _bind__should_dispose_source_signal_when_stop_flag_set_to_YES]' started.
  -bind: should dispose source signal when stop flag set to YES
Test Case '-[RACSignalSpec _bind__should_dispose_source_signal_when_stop_flag_set_to_YES]' passed (0.000 seconds).
Test Case '-[RACSignalSpec subscribing_should_get_next_values]' started.
  subscribing should get next values
Test Case '-[RACSignalSpec subscribing_should_get_next_values]' passed (0.000 seconds).
Test Case '-[RACSignalSpec subscribing_should_get_completed]' started.
  subscribing should get completed
Test Case '-[RACSignalSpec subscribing_should_get_completed]' passed (0.000 seconds).
Test Case '-[RACSignalSpec subscribing_should_not_get_an_error]' started.
  subscribing should not get an error
Test Case '-[RACSignalSpec subscribing_should_not_get_an_error]' passed (0.003 seconds).
Test Case '-[RACSignalSpec subscribing_shouldn_t_get_anything_after_dispose]' started.
  subscribing shouldn't get anything after dispose
Test Case '-[RACSignalSpec subscribing_shouldn_t_get_anything_after_dispose]' passed (0.000 seconds).
Test Case '-[RACSignalSpec subscribing_should_have_a_current_scheduler_in_didSubscribe_block]' started.
  subscribing should have a current scheduler in didSubscribe block
Test Case '-[RACSignalSpec subscribing_should_have_a_current_scheduler_in_didSubscribe_block]' passed (0.011 seconds).
Test Case '-[RACSignalSpec subscribing_should_automatically_dispose_of_other_subscriptions_from__createSignal_]' started.
  subscribing should automatically dispose of other subscriptions from +createSignal:
Test Case '-[RACSignalSpec subscribing_should_automatically_dispose_of_other_subscriptions_from__createSignal_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntil__should_support_value_as_trigger]' started.
  -takeUntil: should support value as trigger
Test Case '-[RACSignalSpec _takeUntil__should_support_value_as_trigger]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntil__should_support_completion_as_trigger]' started.
  -takeUntil: should support completion as trigger
Test Case '-[RACSignalSpec _takeUntil__should_support_completion_as_trigger]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntil__should_squelch_any_values_sent_immediately_upon_subscription]' started.
  -takeUntil: should squelch any values sent immediately upon subscription
Test Case '-[RACSignalSpec _takeUntil__should_squelch_any_values_sent_immediately_upon_subscription]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntilReplacement__should_forward_values_from_the_receiver_until_it_s_replaced]' started.
  -takeUntilReplacement: should forward values from the receiver until it's replaced
Test Case '-[RACSignalSpec _takeUntilReplacement__should_forward_values_from_the_receiver_until_it_s_replaced]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntilReplacement__should_forward_error_from_the_receiver]' started.
  -takeUntilReplacement: should forward error from the receiver
Test Case '-[RACSignalSpec _takeUntilReplacement__should_forward_error_from_the_receiver]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntilReplacement__should_not_forward_completed_from_the_receiver]' started.
  -takeUntilReplacement: should not forward completed from the receiver
Test Case '-[RACSignalSpec _takeUntilReplacement__should_not_forward_completed_from_the_receiver]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntilReplacement__should_forward_error_from_the_replacement_signal]' started.
  -takeUntilReplacement: should forward error from the replacement signal
Test Case '-[RACSignalSpec _takeUntilReplacement__should_forward_error_from_the_replacement_signal]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _takeUntilReplacement__should_forward_completed_from_the_replacement_signal]' started.
  -takeUntilReplacement: should forward completed from the replacement signal
Test Case '-[RACSignalSpec _takeUntilReplacement__should_forward_completed_from_the_replacement_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntilReplacement__should_not_forward_values_from_the_receiver_if_both_send_synchronously]' started.
  -takeUntilReplacement: should not forward values from the receiver if both send synchronously
Test Case '-[RACSignalSpec _takeUntilReplacement__should_not_forward_values_from_the_receiver_if_both_send_synchronously]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntilReplacement__should_dispose_of_the_receiver_when_it_s_disposed_of]' started.
  -takeUntilReplacement: should dispose of the receiver when it's disposed of
Test Case '-[RACSignalSpec _takeUntilReplacement__should_dispose_of_the_receiver_when_it_s_disposed_of]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntilReplacement__should_dispose_of_the_replacement_signal_when_it_s_disposed_of]' started.
  -takeUntilReplacement: should dispose of the replacement signal when it's disposed of
Test Case '-[RACSignalSpec _takeUntilReplacement__should_dispose_of_the_replacement_signal_when_it_s_disposed_of]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _takeUntilReplacement__should_dispose_of_the_receiver_when_the_replacement_signal_sends_an_event]' started.
  -takeUntilReplacement: should dispose of the receiver when the replacement signal sends an event
Test Case '-[RACSignalSpec _takeUntilReplacement__should_dispose_of_the_receiver_when_the_replacement_signal_sends_an_event]' passed (0.000 seconds).
Test Case '-[RACSignalSpec disposal_should_dispose_of_the_didSubscribe_disposable]' started.
  disposal should dispose of the didSubscribe disposable
Test Case '-[RACSignalSpec disposal_should_dispose_of_the_didSubscribe_disposable]' passed (0.000 seconds).
Test Case '-[RACSignalSpec disposal_should_dispose_of_the_didSubscribe_disposable_asynchronously]' started.
  disposal should dispose of the didSubscribe disposable asynchronously
Test Case '-[RACSignalSpec disposal_should_dispose_of_the_didSubscribe_disposable_asynchronously]' passed (0.010 seconds).
Test Case '-[RACSignalSpec querying_should_return_first__next__value_with__firstOrDefault_success_error_]' started.
  querying should return first 'next' value with -firstOrDefault:success:error:
Test Case '-[RACSignalSpec querying_should_return_first__next__value_with__firstOrDefault_success_error_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec querying_should_return_first_default_value_with__firstOrDefault_success_error_]' started.
  querying should return first default value with -firstOrDefault:success:error:
Test Case '-[RACSignalSpec querying_should_return_first_default_value_with__firstOrDefault_success_error_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec querying_should_return_error_with__firstOrDefault_success_error_]' started.
  querying should return error with -firstOrDefault:success:error:
Test Case '-[RACSignalSpec querying_should_return_error_with__firstOrDefault_success_error_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec querying_shouldn_t_crash_when_returning_an_error_from_a_background_scheduler]' started.
  querying shouldn't crash when returning an error from a background scheduler
Test Case '-[RACSignalSpec querying_shouldn_t_crash_when_returning_an_error_from_a_background_scheduler]' passed (0.003 seconds).
Test Case '-[RACSignalSpec querying_should_terminate_the_subscription_after_returning_from__firstOrDefault_success_error_]' started.
  querying should terminate the subscription after returning from -firstOrDefault:success:error:
Test Case '-[RACSignalSpec querying_should_terminate_the_subscription_after_returning_from__firstOrDefault_success_error_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec querying_should_return_YES_from__waitUntilCompleted__when_successful]' started.
  querying should return YES from -waitUntilCompleted: when successful
Test Case '-[RACSignalSpec querying_should_return_YES_from__waitUntilCompleted__when_successful]' passed (0.000 seconds).
Test Case '-[RACSignalSpec querying_should_return_NO_from__waitUntilCompleted__upon_error]' started.
  querying should return NO from -waitUntilCompleted: upon error
Test Case '-[RACSignalSpec querying_should_return_NO_from__waitUntilCompleted__upon_error]' passed (0.000 seconds).
Test Case '-[RACSignalSpec querying_should_return_a_delayed_value_from__asynchronousFirstOrDefault_success_error_]' started.
  querying should return a delayed value from -asynchronousFirstOrDefault:success:error:
Test Case '-[RACSignalSpec querying_should_return_a_delayed_value_from__asynchronousFirstOrDefault_success_error_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec querying_should_return_a_default_value_from__asynchronousFirstOrDefault_success_error_]' started.
  querying should return a default value from -asynchronousFirstOrDefault:success:error:
Test Case '-[RACSignalSpec querying_should_return_a_default_value_from__asynchronousFirstOrDefault_success_error_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec querying_should_return_a_delayed_error_from__asynchronousFirstOrDefault_success_error_]' started.
  querying should return a delayed error from -asynchronousFirstOrDefault:success:error:
Test Case '-[RACSignalSpec querying_should_return_a_delayed_error_from__asynchronousFirstOrDefault_success_error_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec querying_should_terminate_the_subscription_after_returning_from__asynchronousFirstOrDefault_success_error_]' started.
  querying should terminate the subscription after returning from -asynchronousFirstOrDefault:success:error:
Test Case '-[RACSignalSpec querying_should_terminate_the_subscription_after_returning_from__asynchronousFirstOrDefault_success_error_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec querying_should_return_a_delayed_success_from__asynchronouslyWaitUntilCompleted_]' started.
  querying should return a delayed success from -asynchronouslyWaitUntilCompleted:
Test Case '-[RACSignalSpec querying_should_return_a_delayed_success_from__asynchronouslyWaitUntilCompleted_]' passed (0.006 seconds).
Test Case '-[RACSignalSpec continuation_should_repeat_after_completion]' started.
  continuation should repeat after completion
Test Case '-[RACSignalSpec continuation_should_repeat_after_completion]' passed (0.011 seconds).
Test Case '-[RACSignalSpec continuation_should_stop_repeating_when_disposed]' started.
  continuation should stop repeating when disposed
Test Case '-[RACSignalSpec continuation_should_stop_repeating_when_disposed]' passed (0.000 seconds).
Test Case '-[RACSignalSpec continuation_should_stop_repeating_when_disposed_by__take_]' started.
  continuation should stop repeating when disposed by -take:
Test Case '-[RACSignalSpec continuation_should_stop_repeating_when_disposed_by__take_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatestWith__should_send_next_only_once_both_signals_send_next]' started.
  +combineLatestWith: should send next only once both signals send next
Test Case '-[RACSignalSpec _combineLatestWith__should_send_next_only_once_both_signals_send_next]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatestWith__should_send_nexts_when_either_signal_sends_multiple_times]' started.
  +combineLatestWith: should send nexts when either signal sends multiple times
Test Case '-[RACSignalSpec _combineLatestWith__should_send_nexts_when_either_signal_sends_multiple_times]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatestWith__should_complete_when_only_both_signals_complete]' started.
  +combineLatestWith: should complete when only both signals complete
Test Case '-[RACSignalSpec _combineLatestWith__should_complete_when_only_both_signals_complete]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatestWith__should_error_when_either_signal_errors]' started.
  +combineLatestWith: should error when either signal errors
Test Case '-[RACSignalSpec _combineLatestWith__should_error_when_either_signal_errors]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatestWith__shouldn_t_create_a_retain_cycle]' started.
  +combineLatestWith: shouldn't create a retain cycle
Test Case '-[RACSignalSpec _combineLatestWith__shouldn_t_create_a_retain_cycle]' passed (0.012 seconds).
Test Case '-[RACSignalSpec _combineLatestWith__should_combine_the_same_signal]' started.
  +combineLatestWith: should combine the same signal
Test Case '-[RACSignalSpec _combineLatestWith__should_combine_the_same_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatestWith__should_combine_the_same_side_effecting_signal]' started.
  +combineLatestWith: should combine the same side-effecting signal
Test Case '-[RACSignalSpec _combineLatestWith__should_combine_the_same_side_effecting_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatest__should_return_tuples_even_when_only_combining_one_signal]' started.
  +combineLatest: should return tuples even when only combining one signal
Test Case '-[RACSignalSpec _combineLatest__should_return_tuples_even_when_only_combining_one_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatest__should_complete_immediately_when_not_given_any_signals]' started.
  +combineLatest: should complete immediately when not given any signals
Test Case '-[RACSignalSpec _combineLatest__should_complete_immediately_when_not_given_any_signals]' passed (0.005 seconds).
Test Case '-[RACSignalSpec _combineLatest__should_only_complete_after_all_its_signals_complete]' started.
  +combineLatest: should only complete after all its signals complete
Test Case '-[RACSignalSpec _combineLatest__should_only_complete_after_all_its_signals_complete]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatest_reduce__should_send_nils_for_nil_values]' started.
  +combineLatest:reduce: should send nils for nil values
Test Case '-[RACSignalSpec _combineLatest_reduce__should_send_nils_for_nil_values]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatest_reduce__should_send_the_return_result_of_the_reduce_block]' started.
  +combineLatest:reduce: should send the return result of the reduce block
Test Case '-[RACSignalSpec _combineLatest_reduce__should_send_the_return_result_of_the_reduce_block]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _combineLatest_reduce__should_handle_multiples_of_the_same_signals]' started.
  +combineLatest:reduce: should handle multiples of the same signals
Test Case '-[RACSignalSpec _combineLatest_reduce__should_handle_multiples_of_the_same_signals]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _combineLatest_reduce__should_handle_multiples_of_the_same_side_effecting_signal]' started.
  +combineLatest:reduce: should handle multiples of the same side-effecting signal
Test Case '-[RACSignalSpec _combineLatest_reduce__should_handle_multiples_of_the_same_side_effecting_signal]' passed (0.003 seconds).
Test Case '-[RACSignalSpec distinctUntilChanged_should_only_send_values_that_are_distinct_from_the_previous_value]' started.
  distinctUntilChanged should only send values that are distinct from the previous value
Test Case '-[RACSignalSpec distinctUntilChanged_should_only_send_values_that_are_distinct_from_the_previous_value]' passed (0.000 seconds).
Test Case '-[RACSignalSpec distinctUntilChanged_shouldn_t_consider_nils_to_always_be_distinct]' started.
  distinctUntilChanged shouldn't consider nils to always be distinct
Test Case '-[RACSignalSpec distinctUntilChanged_shouldn_t_consider_nils_to_always_be_distinct]' passed (0.000 seconds).
Test Case '-[RACSignalSpec distinctUntilChanged_should_consider_initial_nil_to_be_distinct]' started.
  distinctUntilChanged should consider initial nil to be distinct
Test Case '-[RACSignalSpec distinctUntilChanged_should_consider_initial_nil_to_be_distinct]' passed (0.004 seconds).
Test Case '-[RACSignalSpec RACObserve_should_work_with_object_properties]' started.
  RACObserve should work with object properties
Test Case '-[RACSignalSpec RACObserve_should_work_with_object_properties]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACObserve_should_work_with_non_object_properties]' started.
  RACObserve should work with non-object properties
Test Case '-[RACSignalSpec RACObserve_should_work_with_non_object_properties]' passed (0.000 seconds).
Test Case '-[RACSignalSpec RACObserve_should_read_the_initial_value_upon_subscription]' started.
  RACObserve should read the initial value upon subscription
Test Case '-[RACSignalSpec RACObserve_should_read_the_initial_value_upon_subscription]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_set_the_value_of_the_property_with_the_latest_value_from_the_signal]' started.
  -setKeyPath:onObject: RACPropertySignalExamples should set the value of the property with the latest value from the signal
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_set_the_value_of_the_property_with_the_latest_value_from_the_signal]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_set_the_given_nilValue_for_an_object_property]' started.
  -setKeyPath:onObject: RACPropertySignalExamples should set the given nilValue for an object property
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_set_the_given_nilValue_for_an_object_property]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_leave_the_value_of_the_property_alone_after_the_signal_completes]' started.
  -setKeyPath:onObject: RACPropertySignalExamples should leave the value of the property alone after the signal completes
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_leave_the_value_of_the_property_alone_after_the_signal_completes]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_set_the_value_of_a_non_object_property_with_the_latest_value_from_the_signal]' started.
  -setKeyPath:onObject: RACPropertySignalExamples should set the value of a non-object property with the latest value from the signal
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_set_the_value_of_a_non_object_property_with_the_latest_value_from_the_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_set_the_given_nilValue_for_a_non_object_property]' started.
  -setKeyPath:onObject: RACPropertySignalExamples should set the given nilValue for a non-object property
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_set_the_given_nilValue_for_a_non_object_property]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_not_invoke__setNilValueForKey__with_a_nilValue]' started.
  -setKeyPath:onObject: RACPropertySignalExamples should not invoke -setNilValueForKey: with a nilValue
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_not_invoke__setNilValueForKey__with_a_nilValue]' passed (0.013 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_invoke__setNilValueForKey__without_a_nilValue]' started.
  -setKeyPath:onObject: RACPropertySignalExamples should invoke -setNilValueForKey: without a nilValue
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_invoke__setNilValueForKey__without_a_nilValue]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_retain_intermediate_signals_when_binding]' started.
  -setKeyPath:onObject: RACPropertySignalExamples should retain intermediate signals when binding
Test Case '-[RACSignalSpec _setKeyPath_onObject__RACPropertySignalExamples_should_retain_intermediate_signals_when_binding]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__shouldn_t_send_values_to_dealloc_d_objects]' started.
  -setKeyPath:onObject: shouldn't send values to dealloc'd objects
Test Case '-[RACSignalSpec _setKeyPath_onObject__shouldn_t_send_values_to_dealloc_d_objects]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__should_allow_a_new_derivation_after_the_signal_s_completed]' started.
  -setKeyPath:onObject: should allow a new derivation after the signal's completed
Test Case '-[RACSignalSpec _setKeyPath_onObject__should_allow_a_new_derivation_after_the_signal_s_completed]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _setKeyPath_onObject__should_set_the_given_value_when_nil_is_received]' started.
  -setKeyPath:onObject: should set the given value when nil is received
Test Case '-[RACSignalSpec _setKeyPath_onObject__should_set_the_given_value_when_nil_is_received]' passed (0.000 seconds).
Test Case '-[RACSignalSpec memory_management_should_dealloc_signals_if_the_signal_does_nothing]' started.
  memory management should dealloc signals if the signal does nothing
Test Case '-[RACSignalSpec memory_management_should_dealloc_signals_if_the_signal_does_nothing]' passed (0.015 seconds).
Test Case '-[RACSignalSpec memory_management_should_retain_signals_for_a_single_run_loop_iteration]' started.
  memory management should retain signals for a single run loop iteration
Test Case '-[RACSignalSpec memory_management_should_retain_signals_for_a_single_run_loop_iteration]' passed (0.010 seconds).
Test Case '-[RACSignalSpec memory_management_should_dealloc_signals_if_the_signal_immediately_completes]' started.
  memory management should dealloc signals if the signal immediately completes
Test Case '-[RACSignalSpec memory_management_should_dealloc_signals_if_the_signal_immediately_completes]' passed (0.010 seconds).
Test Case '-[RACSignalSpec memory_management_should_dealloc_a_replay_subject_if_it_completes_immediately]' started.
  memory management should dealloc a replay subject if it completes immediately
Test Case '-[RACSignalSpec memory_management_should_dealloc_a_replay_subject_if_it_completes_immediately]' passed (0.000 seconds).
Test Case '-[RACSignalSpec memory_management_should_dealloc_if_the_signal_was_created_on_a_background_queue]' started.
  memory management should dealloc if the signal was created on a background queue
Test Case '-[RACSignalSpec memory_management_should_dealloc_if_the_signal_was_created_on_a_background_queue]' passed (0.011 seconds).
Test Case '-[RACSignalSpec memory_management_should_dealloc_if_the_signal_was_created_on_a_background_queue__never_gets_any_subscribers__and_the_background_queue_gets_delayed]' started.
  memory management should dealloc if the signal was created on a background queue, never gets any subscribers, and the background queue gets delayed
Test Case '-[RACSignalSpec memory_management_should_dealloc_if_the_signal_was_created_on_a_background_queue__never_gets_any_subscribers__and_the_background_queue_gets_delayed]' passed (1.003 seconds).
Test Case '-[RACSignalSpec memory_management_should_retain_signals_when_subscribing]' started.
  memory management should retain signals when subscribing
Test Case '-[RACSignalSpec memory_management_should_retain_signals_when_subscribing]' passed (0.011 seconds).
Test Case '-[RACSignalSpec memory_management_should_retain_intermediate_signals_when_subscribing]' started.
  memory management should retain intermediate signals when subscribing
Test Case '-[RACSignalSpec memory_management_should_retain_intermediate_signals_when_subscribing]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _merge__should_send_all_values_from_both_signals]' started.
  +merge: should send all values from both signals
Test Case '-[RACSignalSpec _merge__should_send_all_values_from_both_signals]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _merge__should_send_an_error_if_one_occurs]' started.
  +merge: should send an error if one occurs
Test Case '-[RACSignalSpec _merge__should_send_an_error_if_one_occurs]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _merge__should_complete_only_after_both_signals_complete]' started.
  +merge: should complete only after both signals complete
Test Case '-[RACSignalSpec _merge__should_complete_only_after_both_signals_complete]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _merge__should_complete_immediately_when_not_given_any_signals]' started.
  +merge: should complete immediately when not given any signals
Test Case '-[RACSignalSpec _merge__should_complete_immediately_when_not_given_any_signals]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _merge__should_complete_only_after_both_signals_complete_for_any_number_of_subscribers]' started.
  +merge: should complete only after both signals complete for any number of subscribers
Test Case '-[RACSignalSpec _merge__should_complete_only_after_both_signals_complete_for_any_number_of_subscribers]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _flatten__when_its_max_is_0_should_merge_all_the_signals_concurrently]' started.
  -flatten: when its max is 0 should merge all the signals concurrently
Test Case '-[RACSignalSpec _flatten__when_its_max_is_0_should_merge_all_the_signals_concurrently]' passed (0.002 seconds).
Test Case '-[RACSignalSpec _flatten__when_its_max_is_0_RACSignalMergeConcurrentCompletionExampleGroup_should_complete_only_after_the_source_and_all_its_signals_have_completed]' started.
  -flatten: when its max is 0 RACSignalMergeConcurrentCompletionExampleGroup should complete only after the source and all its signals have completed
Test Case '-[RACSignalSpec _flatten__when_its_max_is_0_RACSignalMergeConcurrentCompletionExampleGroup_should_complete_only_after_the_source_and_all_its_signals_have_completed]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _flatten__when_its_max_is___0_should_merge_only_the_given_number_at_a_time]' started.
  -flatten: when its max is > 0 should merge only the given number at a time
Test Case '-[RACSignalSpec _flatten__when_its_max_is___0_should_merge_only_the_given_number_at_a_time]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _flatten__when_its_max_is___0_RACSignalMergeConcurrentCompletionExampleGroup_should_complete_only_after_the_source_and_all_its_signals_have_completed]' started.
  -flatten: when its max is > 0 RACSignalMergeConcurrentCompletionExampleGroup should complete only after the source and all its signals have completed
Test Case '-[RACSignalSpec _flatten__when_its_max_is___0_RACSignalMergeConcurrentCompletionExampleGroup_should_complete_only_after_the_source_and_all_its_signals_have_completed]' passed (0.008 seconds).
Test Case '-[RACSignalSpec _flatten__shouldn_t_create_a_retain_cycle]' started.
  -flatten: shouldn't create a retain cycle
Test Case '-[RACSignalSpec _flatten__shouldn_t_create_a_retain_cycle]' passed (0.011 seconds).
Test Case '-[RACSignalSpec _flatten__should_not_crash_when_disposing_while_subscribing]' started.
  -flatten: should not crash when disposing while subscribing
Test Case '-[RACSignalSpec _flatten__should_not_crash_when_disposing_while_subscribing]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _switchToLatest_should_send_values_from_the_most_recent_signal]' started.
  -switchToLatest should send values from the most recent signal
Test Case '-[RACSignalSpec _switchToLatest_should_send_values_from_the_most_recent_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _switchToLatest_should_send_errors_from_the_most_recent_signal]' started.
  -switchToLatest should send errors from the most recent signal
Test Case '-[RACSignalSpec _switchToLatest_should_send_errors_from_the_most_recent_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _switchToLatest_should_not_send_completed_if_only_the_switching_signal_completes]' started.
  -switchToLatest should not send completed if only the switching signal completes
Test Case '-[RACSignalSpec _switchToLatest_should_not_send_completed_if_only_the_switching_signal_completes]' passed (0.008 seconds).
Test Case '-[RACSignalSpec _switchToLatest_should_send_completed_when_the_switching_signal_completes_and_the_last_sent_signal_does]' started.
  -switchToLatest should send completed when the switching signal completes and the last sent signal does
Test Case '-[RACSignalSpec _switchToLatest_should_send_completed_when_the_switching_signal_completes_and_the_last_sent_signal_does]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _switchToLatest_should_accept_nil_signals]' started.
  -switchToLatest should accept nil signals
Test Case '-[RACSignalSpec _switchToLatest_should_accept_nil_signals]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _switchToLatest_should_return_a_cold_signal]' started.
  -switchToLatest should return a cold signal
Test Case '-[RACSignalSpec _switchToLatest_should_return_a_cold_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _switch_cases_default__switching_between_values_with_a_default_should_not_send_any_values_before_a_key_is_sent]' started.
  +switch:cases:default: switching between values with a default should not send any values before a key is sent
Test Case '-[RACSignalSpec _switch_cases_default__switching_between_values_with_a_default_should_not_send_any_values_before_a_key_is_sent]' passed (0.002 seconds).
Test Case '-[RACSignalSpec _switch_cases_default__switching_between_values_with_a_default_should_send_events_based_on_the_latest_key]' started.
  +switch:cases:default: switching between values with a default should send events based on the latest key
Test Case '-[RACSignalSpec _switch_cases_default__switching_between_values_with_a_default_should_send_events_based_on_the_latest_key]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _switch_cases_default__switching_between_values_with_a_default_should_not_send_completed_when_only_the_key_signal_completes]' started.
  +switch:cases:default: switching between values with a default should not send completed when only the key signal completes
Test Case '-[RACSignalSpec _switch_cases_default__switching_between_values_with_a_default_should_not_send_completed_when_only_the_key_signal_completes]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _switch_cases_default__switching_between_values_with_a_default_should_send_completed_when_the_key_signal_and_the_latest_sent_signal_complete]' started.
  +switch:cases:default: switching between values with a default should send completed when the key signal and the latest sent signal complete
Test Case '-[RACSignalSpec _switch_cases_default__switching_between_values_with_a_default_should_send_completed_when_the_key_signal_and_the_latest_sent_signal_complete]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _switch_cases_default__should_use_the_default_signal_if_key_that_was_sent_does_not_have_an_associated_signal]' started.
  +switch:cases:default: should use the default signal if key that was sent does not have an associated signal
Test Case '-[RACSignalSpec _switch_cases_default__should_use_the_default_signal_if_key_that_was_sent_does_not_have_an_associated_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _switch_cases_default__should_send_an_error_if_key_that_was_sent_does_not_have_an_associated_signal_and_there_s_no_default]' started.
  +switch:cases:default: should send an error if key that was sent does not have an associated signal and there's no default
Test Case '-[RACSignalSpec _switch_cases_default__should_send_an_error_if_key_that_was_sent_does_not_have_an_associated_signal_and_there_s_no_default]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _switch_cases_default__should_match_RACTupleNil_case_when_a_nil_value_is_sent]' started.
  +switch:cases:default: should match RACTupleNil case when a nil value is sent
Test Case '-[RACSignalSpec _switch_cases_default__should_match_RACTupleNil_case_when_a_nil_value_is_sent]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _if_then_else_should_not_send_any_values_before_a_boolean_is_sent]' started.
  +if:then:else should not send any values before a boolean is sent
Test Case '-[RACSignalSpec _if_then_else_should_not_send_any_values_before_a_boolean_is_sent]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _if_then_else_should_send_events_based_on_the_latest_boolean]' started.
  +if:then:else should send events based on the latest boolean
Test Case '-[RACSignalSpec _if_then_else_should_send_events_based_on_the_latest_boolean]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _if_then_else_should_not_send_completed_when_only_the_BOOL_signal_completes]' started.
  +if:then:else should not send completed when only the BOOL signal completes
Test Case '-[RACSignalSpec _if_then_else_should_not_send_completed_when_only_the_BOOL_signal_completes]' passed (0.014 seconds).
Test Case '-[RACSignalSpec _if_then_else_should_send_completed_when_the_BOOL_signal_and_the_latest_sent_signal_complete]' started.
  +if:then:else should send completed when the BOOL signal and the latest sent signal complete
Test Case '-[RACSignalSpec _if_then_else_should_send_completed_when_the_BOOL_signal_and_the_latest_sent_signal_complete]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _interval_onScheduler__and__interval_onScheduler_withLeeway___interval_onScheduler__should_work_on_the_main_thread_scheduler]' started.
  +interval:onScheduler: and +interval:onScheduler:withLeeway: +interval:onScheduler: should work on the main thread scheduler
Test Case '-[RACSignalSpec _interval_onScheduler__and__interval_onScheduler_withLeeway___interval_onScheduler__should_work_on_the_main_thread_scheduler]' passed (0.303 seconds).
Test Case '-[RACSignalSpec _interval_onScheduler__and__interval_onScheduler_withLeeway___interval_onScheduler__should_work_on_a_background_scheduler]' started.
  +interval:onScheduler: and +interval:onScheduler:withLeeway: +interval:onScheduler: should work on a background scheduler
Test Case '-[RACSignalSpec _interval_onScheduler__and__interval_onScheduler_withLeeway___interval_onScheduler__should_work_on_a_background_scheduler]' passed (0.304 seconds).
Test Case '-[RACSignalSpec _interval_onScheduler__and__interval_onScheduler_withLeeway___interval_onScheduler_withLeeway__should_work_on_the_main_thread_scheduler]' started.
  +interval:onScheduler: and +interval:onScheduler:withLeeway: +interval:onScheduler:withLeeway: should work on the main thread scheduler
Test Case '-[RACSignalSpec _interval_onScheduler__and__interval_onScheduler_withLeeway___interval_onScheduler_withLeeway__should_work_on_the_main_thread_scheduler]' passed (0.305 seconds).
Test Case '-[RACSignalSpec _interval_onScheduler__and__interval_onScheduler_withLeeway___interval_onScheduler_withLeeway__should_work_on_a_background_scheduler]' started.
  +interval:onScheduler: and +interval:onScheduler:withLeeway: +interval:onScheduler:withLeeway: should work on a background scheduler
Test Case '-[RACSignalSpec _interval_onScheduler__and__interval_onScheduler_withLeeway___interval_onScheduler_withLeeway__should_work_on_a_background_scheduler]' passed (0.304 seconds).
Test Case '-[RACSignalSpec _timeout_onScheduler__should_time_out]' started.
  -timeout:onScheduler: should time out
Test Case '-[RACSignalSpec _timeout_onScheduler__should_time_out]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _timeout_onScheduler__should_pass_through_events_while_not_timed_out]' started.
  -timeout:onScheduler: should pass through events while not timed out
Test Case '-[RACSignalSpec _timeout_onScheduler__should_pass_through_events_while_not_timed_out]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _timeout_onScheduler__should_not_time_out_after_disposal]' started.
  -timeout:onScheduler: should not time out after disposal
Test Case '-[RACSignalSpec _timeout_onScheduler__should_not_time_out_after_disposal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _delay__should_delay_nexts]' started.
  -delay: should delay nexts
Test Case '-[RACSignalSpec _delay__should_delay_nexts]' passed (0.020 seconds).
Test Case '-[RACSignalSpec _delay__should_delay_completed]' started.
  -delay: should delay completed
Test Case '-[RACSignalSpec _delay__should_delay_completed]' passed (0.011 seconds).
Test Case '-[RACSignalSpec _delay__should_not_delay_errors]' started.
  -delay: should not delay errors
Test Case '-[RACSignalSpec _delay__should_not_delay_errors]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _delay__should_cancel_delayed_events_when_disposed]' started.
  -delay: should cancel delayed events when disposed
Test Case '-[RACSignalSpec _delay__should_cancel_delayed_events_when_disposed]' passed (0.011 seconds).
Test Case '-[RACSignalSpec _catch__should_subscribe_to_ensuing_signal_on_error]' started.
  -catch: should subscribe to ensuing signal on error
Test Case '-[RACSignalSpec _catch__should_subscribe_to_ensuing_signal_on_error]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _catch__should_prevent_source_error_from_propagating]' started.
  -catch: should prevent source error from propagating
Test Case '-[RACSignalSpec _catch__should_prevent_source_error_from_propagating]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _catch__should_propagate_error_from_ensuing_signal]' started.
  -catch: should propagate error from ensuing signal
Test Case '-[RACSignalSpec _catch__should_propagate_error_from_ensuing_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _catch__should_dispose_ensuing_signal]' started.
  -catch: should dispose ensuing signal
Test Case '-[RACSignalSpec _catch__should_dispose_ensuing_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _try__should_pass_values_while_YES_is_returned_from_the_tryBlock]' started.
  -try: should pass values while YES is returned from the tryBlock
Test Case '-[RACSignalSpec _try__should_pass_values_while_YES_is_returned_from_the_tryBlock]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _try__should_pass_values_until_NO_is_returned_from_the_tryBlock]' started.
  -try: should pass values until NO is returned from the tryBlock
Test Case '-[RACSignalSpec _try__should_pass_values_until_NO_is_returned_from_the_tryBlock]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _tryMap__should_map_values_with_the_mapBlock]' started.
  -tryMap: should map values with the mapBlock
Test Case '-[RACSignalSpec _tryMap__should_map_values_with_the_mapBlock]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _tryMap__should_map_values_with_the_mapBlock__until_the_mapBlock_returns_nil]' started.
  -tryMap: should map values with the mapBlock, until the mapBlock returns nil
Test Case '-[RACSignalSpec _tryMap__should_map_values_with_the_mapBlock__until_the_mapBlock_returns_nil]' passed (0.000 seconds).
Test Case '-[RACSignalSpec throttling__throttle__should_throttle_nexts]' started.
  throttling -throttle: should throttle nexts
Test Case '-[RACSignalSpec throttling__throttle__should_throttle_nexts]' passed (0.022 seconds).
Test Case '-[RACSignalSpec throttling__throttle__should_forward_completed_immediately]' started.
  throttling -throttle: should forward completed immediately
Test Case '-[RACSignalSpec throttling__throttle__should_forward_completed_immediately]' passed (0.000 seconds).
Test Case '-[RACSignalSpec throttling__throttle__should_forward_errors_immediately]' started.
  throttling -throttle: should forward errors immediately
Test Case '-[RACSignalSpec throttling__throttle__should_forward_errors_immediately]' passed (0.000 seconds).
Test Case '-[RACSignalSpec throttling__throttle__should_cancel_future_nexts_when_disposed]' started.
  throttling -throttle: should cancel future nexts when disposed
Test Case '-[RACSignalSpec throttling__throttle__should_cancel_future_nexts_when_disposed]' passed (0.011 seconds).
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__nexts_should_forward_unthrottled_values_immediately]' started.
  throttling -throttle:valuesPassingTest: nexts should forward unthrottled values immediately
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__nexts_should_forward_unthrottled_values_immediately]' passed (0.000 seconds).
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__nexts_should_delay_throttled_values]' started.
  throttling -throttle:valuesPassingTest: nexts should delay throttled values
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__nexts_should_delay_throttled_values]' passed (0.011 seconds).
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__nexts_should_drop_buffered_values_when_a_throttled_value_arrives]' started.
  throttling -throttle:valuesPassingTest: nexts should drop buffered values when a throttled value arrives
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__nexts_should_drop_buffered_values_when_a_throttled_value_arrives]' passed (0.010 seconds).
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__nexts_should_drop_buffered_values_when_an_immediate_value_arrives]' started.
  throttling -throttle:valuesPassingTest: nexts should drop buffered values when an immediate value arrives
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__nexts_should_drop_buffered_values_when_an_immediate_value_arrives]' passed (0.011 seconds).
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__nexts_should_not_be_resent_upon_completion]' started.
  throttling -throttle:valuesPassingTest: nexts should not be resent upon completion
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__nexts_should_not_be_resent_upon_completion]' passed (0.011 seconds).
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__should_forward_completed_immediately]' started.
  throttling -throttle:valuesPassingTest: should forward completed immediately
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__should_forward_completed_immediately]' passed (0.000 seconds).
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__should_forward_errors_immediately]' started.
  throttling -throttle:valuesPassingTest: should forward errors immediately
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__should_forward_errors_immediately]' passed (0.000 seconds).
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__should_cancel_future_nexts_when_disposed]' started.
  throttling -throttle:valuesPassingTest: should cancel future nexts when disposed
Test Case '-[RACSignalSpec throttling__throttle_valuesPassingTest__should_cancel_future_nexts_when_disposed]' passed (0.011 seconds).
Test Case '-[RACSignalSpec _then__should_continue_onto_returned_signal]' started.
  -then: should continue onto returned signal
Test Case '-[RACSignalSpec _then__should_continue_onto_returned_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _then__should_sequence_even_if_no_next_value_is_sent]' started.
  -then: should sequence even if no next value is sent
Test Case '-[RACSignalSpec _then__should_sequence_even_if_no_next_value_is_sent]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_implement__NSFastEnumeration_]' started.
  -sequence RACSequenceExamples should implement <NSFastEnumeration>
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_implement__NSFastEnumeration_]' passed (0.002 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_return_an_array]' started.
  -sequence RACSequenceExamples should return an array
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_return_an_array]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' started.
  -sequence RACSequenceExamples -signalWithScheduler: should return an immediately scheduled signal
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples__signalWithScheduler__should_return_an_immediately_scheduled_signal]' passed (0.002 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' started.
  -sequence RACSequenceExamples -signalWithScheduler: should return a background scheduled signal
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples__signalWithScheduler__should_return_a_background_scheduled_signal]' passed (0.002 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' started.
  -sequence RACSequenceExamples -signalWithScheduler: should only evaluate one value per scheduling
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples__signalWithScheduler__should_only_evaluate_one_value_per_scheduling]' passed (0.011 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_be_equal_to_itself]' started.
  -sequence RACSequenceExamples should be equal to itself
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_be_equal_to_itself]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' started.
  -sequence RACSequenceExamples should be equal to the same sequence of values
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_be_equal_to_the_same_sequence_of_values]' passed (0.002 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' started.
  -sequence RACSequenceExamples should not be equal to a different sequence of values
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_not_be_equal_to_a_different_sequence_of_values]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_return_an_identical_object_for__copy]' started.
  -sequence RACSequenceExamples should return an identical object for -copy
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_return_an_identical_object_for__copy]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_archive]' started.
  -sequence RACSequenceExamples should archive
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_archive]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_fold_right]' started.
  -sequence RACSequenceExamples should fold right
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_fold_right]' passed (0.002 seconds).
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_fold_left]' started.
  -sequence RACSequenceExamples should fold left
Test Case '-[RACSignalSpec _sequence_RACSequenceExamples_should_fold_left]' passed (0.002 seconds).
Test Case '-[RACSignalSpec should_complete_take__even_if_the_original_signal_doesn_t]' started.
  should complete take: even if the original signal doesn't
Test Case '-[RACSignalSpec should_complete_take__even_if_the_original_signal_doesn_t]' passed (0.003 seconds).
Test Case '-[RACSignalSpec _zip__should_complete_as_soon_as_no_new_zipped_values_are_possible]' started.
  +zip: should complete as soon as no new zipped values are possible
Test Case '-[RACSignalSpec _zip__should_complete_as_soon_as_no_new_zipped_values_are_possible]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _zip__outcome_should_not_be_dependent_on_order_of_signals]' started.
  +zip: outcome should not be dependent on order of signals
Test Case '-[RACSignalSpec _zip__outcome_should_not_be_dependent_on_order_of_signals]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _zip__should_forward_errors_sent_earlier_than__time_wise__and_before__position_wise__a_complete]' started.
  +zip: should forward errors sent earlier than (time-wise) and before (position-wise) a complete
Test Case '-[RACSignalSpec _zip__should_forward_errors_sent_earlier_than__time_wise__and_before__position_wise__a_complete]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _zip__should_forward_errors_sent_earlier_than__time_wise__and_after__position_wise__a_complete]' started.
  +zip: should forward errors sent earlier than (time-wise) and after (position-wise) a complete
Test Case '-[RACSignalSpec _zip__should_forward_errors_sent_earlier_than__time_wise__and_after__position_wise__a_complete]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _zip__should_forward_errors_sent_later_than__time_wise__and_before__position_wise__a_complete]' started.
  +zip: should forward errors sent later than (time-wise) and before (position-wise) a complete
Test Case '-[RACSignalSpec _zip__should_forward_errors_sent_later_than__time_wise__and_before__position_wise__a_complete]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _zip__should_ignore_errors_sent_later_than__time_wise__and_after__position_wise__a_complete]' started.
  +zip: should ignore errors sent later than (time-wise) and after (position-wise) a complete
Test Case '-[RACSignalSpec _zip__should_ignore_errors_sent_later_than__time_wise__and_after__position_wise__a_complete]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _zip__should_handle_signals_sending_values_unevenly]' started.
  +zip: should handle signals sending values unevenly
Test Case '-[RACSignalSpec _zip__should_handle_signals_sending_values_unevenly]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _zip__should_handle_multiples_of_the_same_side_effecting_signal]' started.
  +zip: should handle multiples of the same side-effecting signal
Test Case '-[RACSignalSpec _zip__should_handle_multiples_of_the_same_side_effecting_signal]' passed (0.018 seconds).
Test Case '-[RACSignalSpec _sample__should_send_the_latest_value_when_the_sampler_signal_fires]' started.
  -sample: should send the latest value when the sampler signal fires
Test Case '-[RACSignalSpec _sample__should_send_the_latest_value_when_the_sampler_signal_fires]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _collect_should_send_a_single_array_when_the_original_signal_completes]' started.
  -collect should send a single array when the original signal completes
Test Case '-[RACSignalSpec _collect_should_send_a_single_array_when_the_original_signal_completes]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _collect_should_add_NSNull_to_an_array_for_nil_values]' started.
  -collect should add NSNull to an array for nil values
Test Case '-[RACSignalSpec _collect_should_add_NSNull_to_an_array_for_nil_values]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _bufferWithTime__should_buffer_nexts]' started.
  -bufferWithTime: should buffer nexts
Test Case '-[RACSignalSpec _bufferWithTime__should_buffer_nexts]' passed (0.008 seconds).
Test Case '-[RACSignalSpec _bufferWithTime__should_not_perform_buffering_until_a_value_is_sent]' started.
  -bufferWithTime: should not perform buffering until a value is sent
Test Case '-[RACSignalSpec _bufferWithTime__should_not_perform_buffering_until_a_value_is_sent]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _bufferWithTime__should_flush_any_buffered_nexts_upon_completion]' started.
  -bufferWithTime: should flush any buffered nexts upon completion
Test Case '-[RACSignalSpec _bufferWithTime__should_flush_any_buffered_nexts_upon_completion]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _bufferWithTime__should_support_NSNull_values]' started.
  -bufferWithTime: should support NSNull values
Test Case '-[RACSignalSpec _bufferWithTime__should_support_NSNull_values]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _bufferWithTime__should_buffer_nil_values]' started.
  -bufferWithTime: should buffer nil values
Test Case '-[RACSignalSpec _bufferWithTime__should_buffer_nil_values]' passed (0.015 seconds).
Test Case '-[RACSignalSpec _concat_should_concatenate_the_values_of_inner_signals]' started.
  -concat should concatenate the values of inner signals
Test Case '-[RACSignalSpec _concat_should_concatenate_the_values_of_inner_signals]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _concat_should_complete_only_after_all_signals_complete]' started.
  -concat should complete only after all signals complete
Test Case '-[RACSignalSpec _concat_should_complete_only_after_all_signals_complete]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _concat_should_pass_through_errors]' started.
  -concat should pass through errors
Test Case '-[RACSignalSpec _concat_should_pass_through_errors]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _concat_should_concat_signals_sent_later]' started.
  -concat should concat signals sent later
Test Case '-[RACSignalSpec _concat_should_concat_signals_sent_later]' passed (0.011 seconds).
Test Case '-[RACSignalSpec _concat_should_dispose_the_current_signal]' started.
  -concat should dispose the current signal
Test Case '-[RACSignalSpec _concat_should_dispose_the_current_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _concat_should_dispose_later_signals]' started.
  -concat should dispose later signals
Test Case '-[RACSignalSpec _concat_should_dispose_later_signals]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _initially__should_not_run_without_a_subscription]' started.
  -initially: should not run without a subscription
Test Case '-[RACSignalSpec _initially__should_not_run_without_a_subscription]' passed (0.009 seconds).
Test Case '-[RACSignalSpec _initially__should_run_on_subscription]' started.
  -initially: should run on subscription
Test Case '-[RACSignalSpec _initially__should_run_on_subscription]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _initially__should_re_run_for_each_subscription]' started.
  -initially: should re-run for each subscription
Test Case '-[RACSignalSpec _initially__should_re_run_for_each_subscription]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _finally__should_not_run_finally_without_a_subscription]' started.
  -finally: should not run finally without a subscription
Test Case '-[RACSignalSpec _finally__should_not_run_finally_without_a_subscription]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _finally__with_a_subscription_should_not_run_finally_upon_next]' started.
  -finally: with a subscription should not run finally upon next
Test Case '-[RACSignalSpec _finally__with_a_subscription_should_not_run_finally_upon_next]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _finally__with_a_subscription_should_run_finally_upon_completed]' started.
  -finally: with a subscription should run finally upon completed
Test Case '-[RACSignalSpec _finally__with_a_subscription_should_run_finally_upon_completed]' passed (0.026 seconds).
Test Case '-[RACSignalSpec _finally__with_a_subscription_should_run_finally_upon_error]' started.
  -finally: with a subscription should run finally upon error
Test Case '-[RACSignalSpec _finally__with_a_subscription_should_run_finally_upon_error]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _ignoreValues_should_skip_nexts_and_pass_through_completed]' started.
  -ignoreValues should skip nexts and pass through completed
Test Case '-[RACSignalSpec _ignoreValues_should_skip_nexts_and_pass_through_completed]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _ignoreValues_should_skip_nexts_and_pass_through_errors]' started.
  -ignoreValues should skip nexts and pass through errors
Test Case '-[RACSignalSpec _ignoreValues_should_skip_nexts_and_pass_through_errors]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _materialize_should_convert_nexts_and_completed_into_RACEvents]' started.
  -materialize should convert nexts and completed into RACEvents
Test Case '-[RACSignalSpec _materialize_should_convert_nexts_and_completed_into_RACEvents]' passed (0.026 seconds).
Test Case '-[RACSignalSpec _materialize_should_convert_errors_into_RACEvents_and_complete]' started.
  -materialize should convert errors into RACEvents and complete
Test Case '-[RACSignalSpec _materialize_should_convert_errors_into_RACEvents_and_complete]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _dematerialize_should_convert_nexts_from_RACEvents]' started.
  -dematerialize should convert nexts from RACEvents
Test Case '-[RACSignalSpec _dematerialize_should_convert_nexts_from_RACEvents]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _dematerialize_should_convert_completed_from_a_RACEvent]' started.
  -dematerialize should convert completed from a RACEvent
Test Case '-[RACSignalSpec _dematerialize_should_convert_completed_from_a_RACEvent]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _dematerialize_should_convert_error_from_a_RACEvent]' started.
  -dematerialize should convert error from a RACEvent
Test Case '-[RACSignalSpec _dematerialize_should_convert_error_from_a_RACEvent]' passed (0.013 seconds).
Test Case '-[RACSignalSpec _not_should_invert_every_BOOL_sent]' started.
  -not should invert every BOOL sent
Test Case '-[RACSignalSpec _not_should_invert_every_BOOL_sent]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _and_should_return_YES_if_all_YES_values_are_sent]' started.
  -and should return YES if all YES values are sent
Test Case '-[RACSignalSpec _and_should_return_YES_if_all_YES_values_are_sent]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _or_should_return_YES_for_any_YES_values_sent]' started.
  -or should return YES for any YES values sent
Test Case '-[RACSignalSpec _or_should_return_YES_for_any_YES_values_sent]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _groupBy__should_send_completed_to_all_grouped_signals_]' started.
  -groupBy: should send completed to all grouped signals.
Test Case '-[RACSignalSpec _groupBy__should_send_completed_to_all_grouped_signals_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _groupBy__should_send_error_to_all_grouped_signals_]' started.
  -groupBy: should send error to all grouped signals.
Test Case '-[RACSignalSpec _groupBy__should_send_error_to_all_grouped_signals_]' passed (0.000 seconds).
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__RACSignalStartSharedExamplesName_should_send_values_from_the_returned_signal]' started.
  starting signals +startLazilyWithScheduler:block: RACSignalStartSharedExamplesName should send values from the returned signal
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__RACSignalStartSharedExamplesName_should_send_values_from_the_returned_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__RACSignalStartSharedExamplesName_should_replay_all_values]' started.
  starting signals +startLazilyWithScheduler:block: RACSignalStartSharedExamplesName should replay all values
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__RACSignalStartSharedExamplesName_should_replay_all_values]' passed (0.018 seconds).
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__RACSignalStartSharedExamplesName_should_deliver_the_original_results_on_the_given_scheduler]' started.
  starting signals +startLazilyWithScheduler:block: RACSignalStartSharedExamplesName should deliver the original results on the given scheduler
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__RACSignalStartSharedExamplesName_should_deliver_the_original_results_on_the_given_scheduler]' passed (0.010 seconds).
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__RACSignalStartSharedExamplesName_should_deliver_replayed_results_on_the_given_scheduler]' started.
  starting signals +startLazilyWithScheduler:block: RACSignalStartSharedExamplesName should deliver replayed results on the given scheduler
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__RACSignalStartSharedExamplesName_should_deliver_replayed_results_on_the_given_scheduler]' passed (0.022 seconds).
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__should_only_invoke_the_block_on_subscription]' started.
  starting signals +startLazilyWithScheduler:block: should only invoke the block on subscription
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__should_only_invoke_the_block_on_subscription]' passed (0.000 seconds).
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__should_only_invoke_the_block_once]' started.
  starting signals +startLazilyWithScheduler:block: should only invoke the block once
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__should_only_invoke_the_block_once]' passed (0.000 seconds).
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__should_invoke_the_block_on_the_given_scheduler]' started.
  starting signals +startLazilyWithScheduler:block: should invoke the block on the given scheduler
Test Case '-[RACSignalSpec starting_signals__startLazilyWithScheduler_block__should_invoke_the_block_on_the_given_scheduler]' passed (0.011 seconds).
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__RACSignalStartSharedExamplesName_should_send_values_from_the_returned_signal]' started.
  starting signals +startEagerlyWithScheduler:block: RACSignalStartSharedExamplesName should send values from the returned signal
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__RACSignalStartSharedExamplesName_should_send_values_from_the_returned_signal]' passed (0.000 seconds).
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__RACSignalStartSharedExamplesName_should_replay_all_values]' started.
  starting signals +startEagerlyWithScheduler:block: RACSignalStartSharedExamplesName should replay all values
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__RACSignalStartSharedExamplesName_should_replay_all_values]' passed (0.000 seconds).
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__RACSignalStartSharedExamplesName_should_deliver_the_original_results_on_the_given_scheduler]' started.
  starting signals +startEagerlyWithScheduler:block: RACSignalStartSharedExamplesName should deliver the original results on the given scheduler
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__RACSignalStartSharedExamplesName_should_deliver_the_original_results_on_the_given_scheduler]' passed (0.011 seconds).
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__RACSignalStartSharedExamplesName_should_deliver_replayed_results_on_the_given_scheduler]' started.
  starting signals +startEagerlyWithScheduler:block: RACSignalStartSharedExamplesName should deliver replayed results on the given scheduler
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__RACSignalStartSharedExamplesName_should_deliver_replayed_results_on_the_given_scheduler]' passed (0.023 seconds).
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__should_immediately_invoke_the_block]' started.
  starting signals +startEagerlyWithScheduler:block: should immediately invoke the block
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__should_immediately_invoke_the_block]' passed (0.011 seconds).
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__should_only_invoke_the_block_once]' started.
  starting signals +startEagerlyWithScheduler:block: should only invoke the block once
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__should_only_invoke_the_block_once]' passed (0.000 seconds).
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__should_invoke_the_block_on_the_given_scheduler]' started.
  starting signals +startEagerlyWithScheduler:block: should invoke the block on the given scheduler
Test Case '-[RACSignalSpec starting_signals__startEagerlyWithScheduler_block__should_invoke_the_block_on_the_given_scheduler]' passed (0.011 seconds).
Test Case '-[RACSignalSpec _toArray_should_return_an_array_which_contains_NSNulls_for_nil_values]' started.
  -toArray should return an array which contains NSNulls for nil values
Test Case '-[RACSignalSpec _toArray_should_return_an_array_which_contains_NSNulls_for_nil_values]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _toArray_should_return_nil_upon_error]' started.
  -toArray should return nil upon error
Test Case '-[RACSignalSpec _toArray_should_return_nil_upon_error]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _toArray_should_return_nil_upon_error_even_if_some_nexts_were_sent]' started.
  -toArray should return nil upon error even if some nexts were sent
Test Case '-[RACSignalSpec _toArray_should_return_nil_upon_error_even_if_some_nexts_were_sent]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _ignore__should_ignore_nil]' started.
  -ignore: should ignore nil
Test Case '-[RACSignalSpec _ignore__should_ignore_nil]' passed (0.000 seconds).
Test Case '-[RACSignalSpec _replayLazily_should_forward_the_input_signal_upon_subscription]' started.
  -replayLazily should forward the input signal upon subscription
Test Case '-[RACSignalSpec _replayLazily_should_forward_the_input_signal_upon_subscription]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _replayLazily_should_replay_the_input_signal_for_future_subscriptions]' started.
  -replayLazily should replay the input signal for future subscriptions
Test Case '-[RACSignalSpec _replayLazily_should_replay_the_input_signal_for_future_subscriptions]' passed (0.001 seconds).
Test Case '-[RACSignalSpec _replayLazily_should_replay_even_after_disposal]' started.
  -replayLazily should replay even after disposal
Test Case '-[RACSignalSpec _replayLazily_should_replay_even_after_disposal]' passed (0.000 seconds).
Test Suite 'RACSignalSpec' finished at 2013-12-10 23:13:15 +0000.
Executed 269 tests, with 0 failures (0 unexpected) in 2.996 (3.088) seconds
Test Suite 'RACSubjectSpec' started at 2013-12-10 23:13:15 +0000
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_should_accept_a_nil_error]' started.
  RACSubject RACSubscriberExamples should accept a nil error
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_should_accept_a_nil_error]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_with_values_should_send_nexts_serially__even_when_delivered_from_multiple_threads]' started.
  RACSubject RACSubscriberExamples with values should send nexts serially, even when delivered from multiple threads
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_with_values_should_send_nexts_serially__even_when_delivered_from_multiple_threads]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_multiple_subscriptions_should_send_values_from_all_subscriptions]' started.
  RACSubject RACSubscriberExamples multiple subscriptions should send values from all subscriptions
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_multiple_subscriptions_should_send_values_from_all_subscriptions]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_error_from_any_subscription]' started.
  RACSubject RACSubscriberExamples multiple subscriptions should terminate after the first error from any subscription
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_error_from_any_subscription]' passed (0.021 seconds).
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_completed_from_any_subscription]' started.
  RACSubject RACSubscriberExamples multiple subscriptions should terminate after the first completed from any subscription
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_completed_from_any_subscription]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_multiple_subscriptions_should_dispose_of_all_current_subscriptions_upon_termination]' started.
  RACSubject RACSubscriberExamples multiple subscriptions should dispose of all current subscriptions upon termination
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_multiple_subscriptions_should_dispose_of_all_current_subscriptions_upon_termination]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_multiple_subscriptions_should_dispose_of_future_subscriptions_upon_termination]' started.
  RACSubject RACSubscriberExamples multiple subscriptions should dispose of future subscriptions upon termination
Test Case '-[RACSubjectSpec RACSubject_RACSubscriberExamples_multiple_subscriptions_should_dispose_of_future_subscriptions_upon_termination]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_a_capacity_of_1_should_send_the_last_value]' started.
  RACReplaySubject with a capacity of 1 should send the last value
Test Case '-[RACSubjectSpec RACReplaySubject_with_a_capacity_of_1_should_send_the_last_value]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_a_capacity_of_1_should_send_the_last_value_to_new_subscribers_after_completion]' started.
  RACReplaySubject with a capacity of 1 should send the last value to new subscribers after completion
Test Case '-[RACSubjectSpec RACReplaySubject_with_a_capacity_of_1_should_send_the_last_value_to_new_subscribers_after_completion]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_a_capacity_of_1_should_not_send_any_values_to_new_subscribers_if_none_were_sent_originally]' started.
  RACReplaySubject with a capacity of 1 should not send any values to new subscribers if none were sent originally
Test Case '-[RACSubjectSpec RACReplaySubject_with_a_capacity_of_1_should_not_send_any_values_to_new_subscribers_if_none_were_sent_originally]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_a_capacity_of_1_should_resend_errors]' started.
  RACReplaySubject with a capacity of 1 should resend errors
Test Case '-[RACSubjectSpec RACReplaySubject_with_a_capacity_of_1_should_resend_errors]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_a_capacity_of_1_should_resend_nil_errors]' started.
  RACReplaySubject with a capacity of 1 should resend nil errors
Test Case '-[RACSubjectSpec RACReplaySubject_with_a_capacity_of_1_should_resend_nil_errors]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_should_accept_a_nil_error]' started.
  RACReplaySubject with an unlimited capacity RACSubscriberExamples should accept a nil error
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_should_accept_a_nil_error]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_with_values_should_send_nexts_serially__even_when_delivered_from_multiple_threads]' started.
  RACReplaySubject with an unlimited capacity RACSubscriberExamples with values should send nexts serially, even when delivered from multiple threads
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_with_values_should_send_nexts_serially__even_when_delivered_from_multiple_threads]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_multiple_subscriptions_should_send_values_from_all_subscriptions]' started.
  RACReplaySubject with an unlimited capacity RACSubscriberExamples multiple subscriptions should send values from all subscriptions
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_multiple_subscriptions_should_send_values_from_all_subscriptions]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_error_from_any_subscription]' started.
  RACReplaySubject with an unlimited capacity RACSubscriberExamples multiple subscriptions should terminate after the first error from any subscription
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_error_from_any_subscription]' passed (0.001 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_completed_from_any_subscription]' started.
  RACReplaySubject with an unlimited capacity RACSubscriberExamples multiple subscriptions should terminate after the first completed from any subscription
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_completed_from_any_subscription]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_multiple_subscriptions_should_dispose_of_all_current_subscriptions_upon_termination]' started.
  RACReplaySubject with an unlimited capacity RACSubscriberExamples multiple subscriptions should dispose of all current subscriptions upon termination
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_multiple_subscriptions_should_dispose_of_all_current_subscriptions_upon_termination]' passed (0.003 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_multiple_subscriptions_should_dispose_of_future_subscriptions_upon_termination]' started.
  RACReplaySubject with an unlimited capacity RACSubscriberExamples multiple subscriptions should dispose of future subscriptions upon termination
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_RACSubscriberExamples_multiple_subscriptions_should_dispose_of_future_subscriptions_upon_termination]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_send_both_values_to_new_subscribers_after_completion]' started.
  RACReplaySubject with an unlimited capacity should send both values to new subscribers after completion
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_send_both_values_to_new_subscribers_after_completion]' passed (0.000 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_send_values_in_the_same_order_live_as_when_replaying]' started.
  RACReplaySubject with an unlimited capacity should send values in the same order live as when replaying
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_send_values_in_the_same_order_live_as_when_replaying]' passed (0.985 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_have_a_current_scheduler_when_replaying]' started.
  RACReplaySubject with an unlimited capacity should have a current scheduler when replaying
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_have_a_current_scheduler_when_replaying]' passed (0.010 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_stop_replaying_when_the_subscription_is_disposed]' started.
  RACReplaySubject with an unlimited capacity should stop replaying when the subscription is disposed
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_stop_replaying_when_the_subscription_is_disposed]' passed (0.011 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_finish_replaying_before_completing]' started.
  RACReplaySubject with an unlimited capacity should finish replaying before completing
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_finish_replaying_before_completing]' passed (0.011 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_finish_replaying_before_erroring]' started.
  RACReplaySubject with an unlimited capacity should finish replaying before erroring
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_finish_replaying_before_erroring]' passed (0.011 seconds).
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_finish_replaying_before_sending_new_values]' started.
  RACReplaySubject with an unlimited capacity should finish replaying before sending new values
Test Case '-[RACSubjectSpec RACReplaySubject_with_an_unlimited_capacity_should_finish_replaying_before_sending_new_values]' passed (0.010 seconds).
Test Suite 'RACSubjectSpec' finished at 2013-12-10 23:13:16 +0000.
Executed 26 tests, with 0 failures (0 unexpected) in 1.067 (1.127) seconds
Test Suite 'RACSubscriberSpec' started at 2013-12-10 23:13:16 +0000
Test Case '-[RACSubscriberSpec RACSubscriberExamples_should_accept_a_nil_error]' started.
  RACSubscriberExamples should accept a nil error
Test Case '-[RACSubscriberSpec RACSubscriberExamples_should_accept_a_nil_error]' passed (0.000 seconds).
Test Case '-[RACSubscriberSpec RACSubscriberExamples_with_values_should_send_nexts_serially__even_when_delivered_from_multiple_threads]' started.
  RACSubscriberExamples with values should send nexts serially, even when delivered from multiple threads
Test Case '-[RACSubscriberSpec RACSubscriberExamples_with_values_should_send_nexts_serially__even_when_delivered_from_multiple_threads]' passed (0.000 seconds).
Test Case '-[RACSubscriberSpec RACSubscriberExamples_multiple_subscriptions_should_send_values_from_all_subscriptions]' started.
  RACSubscriberExamples multiple subscriptions should send values from all subscriptions
Test Case '-[RACSubscriberSpec RACSubscriberExamples_multiple_subscriptions_should_send_values_from_all_subscriptions]' passed (0.000 seconds).
Test Case '-[RACSubscriberSpec RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_error_from_any_subscription]' started.
  RACSubscriberExamples multiple subscriptions should terminate after the first error from any subscription
Test Case '-[RACSubscriberSpec RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_error_from_any_subscription]' passed (0.000 seconds).
Test Case '-[RACSubscriberSpec RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_completed_from_any_subscription]' started.
  RACSubscriberExamples multiple subscriptions should terminate after the first completed from any subscription
Test Case '-[RACSubscriberSpec RACSubscriberExamples_multiple_subscriptions_should_terminate_after_the_first_completed_from_any_subscription]' passed (0.000 seconds).
Test Case '-[RACSubscriberSpec RACSubscriberExamples_multiple_subscriptions_should_dispose_of_all_current_subscriptions_upon_termination]' started.
  RACSubscriberExamples multiple subscriptions should dispose of all current subscriptions upon termination
Test Case '-[RACSubscriberSpec RACSubscriberExamples_multiple_subscriptions_should_dispose_of_all_current_subscriptions_upon_termination]' passed (0.001 seconds).
Test Case '-[RACSubscriberSpec RACSubscriberExamples_multiple_subscriptions_should_dispose_of_future_subscriptions_upon_termination]' started.
  RACSubscriberExamples multiple subscriptions should dispose of future subscriptions upon termination
Test Case '-[RACSubscriberSpec RACSubscriberExamples_multiple_subscriptions_should_dispose_of_future_subscriptions_upon_termination]' passed (0.000 seconds).
Test Case '-[RACSubscriberSpec finishing_should_never_invoke_next_after_sending_completed]' started.
  finishing should never invoke next after sending completed
Test Case '-[RACSubscriberSpec finishing_should_never_invoke_next_after_sending_completed]' passed (0.000 seconds).
Test Case '-[RACSubscriberSpec finishing_should_never_invoke_next_after_sending_error]' started.
  finishing should never invoke next after sending error
Test Case '-[RACSubscriberSpec finishing_should_never_invoke_next_after_sending_error]' passed (0.000 seconds).
Test Suite 'RACSubscriberSpec' finished at 2013-12-10 23:13:16 +0000.
Executed 9 tests, with 0 failures (0 unexpected) in 0.003 (0.004) seconds
Test Suite 'RACSubscriptingAssignmentTrampolineSpec' started at 2013-12-10 23:13:16 +0000
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_set_the_value_of_the_property_with_the_latest_value_from_the_signal]' started.
  RACPropertySignalExamples should set the value of the property with the latest value from the signal
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_set_the_value_of_the_property_with_the_latest_value_from_the_signal]' passed (0.000 seconds).
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_set_the_given_nilValue_for_an_object_property]' started.
  RACPropertySignalExamples should set the given nilValue for an object property
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_set_the_given_nilValue_for_an_object_property]' passed (0.000 seconds).
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_leave_the_value_of_the_property_alone_after_the_signal_completes]' started.
  RACPropertySignalExamples should leave the value of the property alone after the signal completes
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_leave_the_value_of_the_property_alone_after_the_signal_completes]' passed (0.000 seconds).
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_set_the_value_of_a_non_object_property_with_the_latest_value_from_the_signal]' started.
  RACPropertySignalExamples should set the value of a non-object property with the latest value from the signal
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_set_the_value_of_a_non_object_property_with_the_latest_value_from_the_signal]' passed (0.000 seconds).
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_set_the_given_nilValue_for_a_non_object_property]' started.
  RACPropertySignalExamples should set the given nilValue for a non-object property
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_set_the_given_nilValue_for_a_non_object_property]' passed (0.000 seconds).
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_not_invoke__setNilValueForKey__with_a_nilValue]' started.
  RACPropertySignalExamples should not invoke -setNilValueForKey: with a nilValue
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_not_invoke__setNilValueForKey__with_a_nilValue]' passed (0.000 seconds).
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_invoke__setNilValueForKey__without_a_nilValue]' started.
  RACPropertySignalExamples should invoke -setNilValueForKey: without a nilValue
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_invoke__setNilValueForKey__without_a_nilValue]' passed (0.001 seconds).
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_retain_intermediate_signals_when_binding]' started.
  RACPropertySignalExamples should retain intermediate signals when binding
Test Case '-[RACSubscriptingAssignmentTrampolineSpec RACPropertySignalExamples_should_retain_intermediate_signals_when_binding]' passed (0.000 seconds).
Test Case '-[RACSubscriptingAssignmentTrampolineSpec should_expand_the_RAC_macro_properly]' started.
  should expand the RAC macro properly
Test Case '-[RACSubscriptingAssignmentTrampolineSpec should_expand_the_RAC_macro_properly]' passed (0.000 seconds).
Test Suite 'RACSubscriptingAssignmentTrampolineSpec' finished at 2013-12-10 23:13:16 +0000.
Executed 9 tests, with 0 failures (0 unexpected) in 0.002 (0.004) seconds
Test Suite 'RACTargetQueueSchedulerSpec' started at 2013-12-10 23:13:16 +0000
Test Case '-[RACTargetQueueSchedulerSpec should_have_a_valid_current_scheduler]' started.
  should have a valid current scheduler
Test Case '-[RACTargetQueueSchedulerSpec should_have_a_valid_current_scheduler]' passed (0.011 seconds).
Test Case '-[RACTargetQueueSchedulerSpec should_schedule_blocks_FIFO_even_when_given_a_concurrent_queue]' started.
  should schedule blocks FIFO even when given a concurrent queue
Test Case '-[RACTargetQueueSchedulerSpec should_schedule_blocks_FIFO_even_when_given_a_concurrent_queue]' passed (0.022 seconds).
Test Suite 'RACTargetQueueSchedulerSpec' finished at 2013-12-10 23:13:16 +0000.
Executed 2 tests, with 0 failures (0 unexpected) in 0.033 (0.035) seconds
Test Suite 'RACTestSchedulerSpec' started at 2013-12-10 23:13:16 +0000
Test Case '-[RACTestSchedulerSpec should_do_nothing_when_stepping_while_empty]' started.
  should do nothing when stepping while empty
Test Case '-[RACTestSchedulerSpec should_do_nothing_when_stepping_while_empty]' passed (0.000 seconds).
Test Case '-[RACTestSchedulerSpec should_execute_the_earliest_enqueued_block_when_stepping]' started.
  should execute the earliest enqueued block when stepping
Test Case '-[RACTestSchedulerSpec should_execute_the_earliest_enqueued_block_when_stepping]' passed (0.000 seconds).
Test Case '-[RACTestSchedulerSpec should_step_multiple_times]' started.
  should step multiple times
Test Case '-[RACTestSchedulerSpec should_step_multiple_times]' passed (0.000 seconds).
Test Case '-[RACTestSchedulerSpec should_step_through_all_scheduled_blocks]' started.
  should step through all scheduled blocks
Test Case '-[RACTestSchedulerSpec should_step_through_all_scheduled_blocks]' passed (0.000 seconds).
Test Case '-[RACTestSchedulerSpec should_execute_blocks_in_date_order_when_stepping]' started.
  should execute blocks in date order when stepping
Test Case '-[RACTestSchedulerSpec should_execute_blocks_in_date_order_when_stepping]' passed (0.000 seconds).
Test Case '-[RACTestSchedulerSpec should_execute_delayed_blocks_in_date_order_when_stepping]' started.
  should execute delayed blocks in date order when stepping
Test Case '-[RACTestSchedulerSpec should_execute_delayed_blocks_in_date_order_when_stepping]' passed (0.000 seconds).
Test Case '-[RACTestSchedulerSpec should_execute_a_repeating_blocks_in_date_order]' started.
  should execute a repeating blocks in date order
Test Case '-[RACTestSchedulerSpec should_execute_a_repeating_blocks_in_date_order]' passed (0.000 seconds).
Test Suite 'RACTestSchedulerSpec' finished at 2013-12-10 23:13:16 +0000.
Executed 7 tests, with 0 failures (0 unexpected) in 0.001 (0.001) seconds
Test Suite 'RACTupleSpec' started at 2013-12-10 23:13:16 +0000
Test Case '-[RACTupleSpec RACTupleUnpack_should_unpack_a_single_value]' started.
  RACTupleUnpack should unpack a single value
Test Case '-[RACTupleSpec RACTupleUnpack_should_unpack_a_single_value]' passed (0.000 seconds).
Test Case '-[RACTupleSpec RACTupleUnpack_should_translate_RACTupleNil]' started.
  RACTupleUnpack should translate RACTupleNil
Test Case '-[RACTupleSpec RACTupleUnpack_should_translate_RACTupleNil]' passed (0.000 seconds).
Test Case '-[RACTupleSpec RACTupleUnpack_should_unpack_multiple_values]' started.
  RACTupleUnpack should unpack multiple values
2013-12-10 15:13:16.646 otest[6189:303] /Users/<USER>/code/OSS/ReactiveCocoa/ReactiveCocoaFramework/ReactiveCocoaTests/RACTupleSpec.m:28 expected: foobar, got: seoitns
/Users/<USER>/code/OSS/ReactiveCocoa/ReactiveCocoaFramework/ReactiveCocoaTests/RACTupleSpec.m:28: error: -[RACTupleSpec RACTupleUnpack_should_unpack_multiple_values] : expected: foobar, got: seoitns
Test Case '-[RACTupleSpec RACTupleUnpack_should_unpack_multiple_values]' failed (0.000 seconds).
Test Case '-[RACTupleSpec RACTupleUnpack_should_fill_in_missing_values_with_nil]' started.
  RACTupleUnpack should fill in missing values with nil
Test Case '-[RACTupleSpec RACTupleUnpack_should_fill_in_missing_values_with_nil]' passed (0.000 seconds).
Test Case '-[RACTupleSpec RACTupleUnpack_should_skip_any_values_not_assigned_to]' started.
  RACTupleUnpack should skip any values not assigned to
Test Case '-[RACTupleSpec RACTupleUnpack_should_skip_any_values_not_assigned_to]' passed (0.000 seconds).
Test Case '-[RACTupleSpec RACTupleUnpack_should_keep_an_unpacked_value_alive_when_captured_in_a_block]' started.
  RACTupleUnpack should keep an unpacked value alive when captured in a block
Test Case '-[RACTupleSpec RACTupleUnpack_should_keep_an_unpacked_value_alive_when_captured_in_a_block]' passed (0.000 seconds).
Test Case '-[RACTupleSpec RACTuplePack_should_pack_a_single_value]' started.
  RACTuplePack should pack a single value
Test Case '-[RACTupleSpec RACTuplePack_should_pack_a_single_value]' passed (0.000 seconds).
Test Case '-[RACTupleSpec RACTuplePack_should_translate_nil]' started.
  RACTuplePack should translate nil
Test Case '-[RACTupleSpec RACTuplePack_should_translate_nil]' passed (0.000 seconds).
Test Case '-[RACTupleSpec RACTuplePack_should_pack_multiple_values]' started.
  RACTuplePack should pack multiple values
Test Case '-[RACTupleSpec RACTuplePack_should_pack_multiple_values]' passed (0.000 seconds).
Test Case '-[RACTupleSpec _tupleByAddingObject__should_add_a_non_nil_object]' started.
  -tupleByAddingObject: should add a non-nil object
Test Case '-[RACTupleSpec _tupleByAddingObject__should_add_a_non_nil_object]' passed (0.000 seconds).
Test Case '-[RACTupleSpec _tupleByAddingObject__should_add_nil]' started.
  -tupleByAddingObject: should add nil
Test Case '-[RACTupleSpec _tupleByAddingObject__should_add_nil]' passed (0.000 seconds).
Test Case '-[RACTupleSpec _tupleByAddingObject__should_add_NSNull]' started.
  -tupleByAddingObject: should add NSNull
Test Case '-[RACTupleSpec _tupleByAddingObject__should_add_NSNull]' passed (0.000 seconds).
Test Suite 'RACTupleSpec' finished at 2013-12-10 23:13:16 +0000.
Executed 12 tests, with 1 failure (0 unexpected) in 0.001 (0.004) seconds
Test Suite 'SpectaUtilityTest' started at 2013-12-10 23:13:16 +0000
Test Case '-[SpectaUtilityTest test_SPT_IsSpecClass_returns_no_when_provided_a_sentest_class]' started.
Test Case '-[SpectaUtilityTest test_SPT_IsSpecClass_returns_no_when_provided_a_sentest_class]' passed (0.000 seconds).
Test Case '-[SpectaUtilityTest test_SPT_IsSpecClass_returns_yes_when_provided_a_spec_class]' started.
Test Case '-[SpectaUtilityTest test_SPT_IsSpecClass_returns_yes_when_provided_a_spec_class]' passed (0.000 seconds).
Test Suite 'SpectaUtilityTest' finished at 2013-12-10 23:13:16 +0000.
Executed 2 tests, with 0 failures (0 unexpected) in 0.000 (0.000) seconds
Test Suite '_SpectaUtilityTestSpec' started at 2013-12-10 23:13:16 +0000
Test Suite '_SpectaUtilityTestSpec' finished at 2013-12-10 23:13:16 +0000.
Executed 0 tests, with 0 failures (0 unexpected) in 0.000 (0.000) seconds
Test Suite '/Users/<USER>/Library/Developer/Xcode/DerivedData/ReactiveCocoa-eznxkbqvgfsnrvetemqloysuwagb/Build/Products/Test/ReactiveCocoaTests.octest(Tests)' finished at 2013-12-10 23:13:16 +0000.
Executed 922 tests, with 1 failure (0 unexpected) in 6.436 (6.743) seconds
