# -*- encoding: utf-8 -*-
# stub: rouge 3.28.0 ruby lib

Gem::Specification.new do |s|
  s.name = "rouge".freeze
  s.version = "3.28.0"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/rouge-ruby/rouge/issues", "changelog_uri" => "https://github.com/rouge-ruby/rouge/blob/master/CHANGELOG.md", "documentation_uri" => "https://rouge-ruby.github.io/docs/", "source_code_uri" => "https://github.com/rouge-ruby/rouge" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON><PERSON>".freeze]
  s.date = "2022-02-03"
  s.description = "Rouge aims to a be a simple, easy-to-extend drop-in replacement for pygments.".freeze
  s.email = ["<EMAIL>".freeze]
  s.executables = ["rougify".freeze]
  s.files = ["bin/rougify".freeze]
  s.homepage = "http://rouge.jneen.net/".freeze
  s.licenses = ["MIT".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.0".freeze)
  s.rubygems_version = "*******".freeze
  s.summary = "A pure-ruby colorizer based on pygments".freeze

  s.installed_by_version = "*******" if s.respond_to? :installed_by_version
end
