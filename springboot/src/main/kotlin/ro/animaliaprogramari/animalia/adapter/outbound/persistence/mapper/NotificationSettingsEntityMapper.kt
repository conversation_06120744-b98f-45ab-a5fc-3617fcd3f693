package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationSettings as NotificationSettingsEntity

/**
 * Mapper between notification settings domain model and JPA entity
 */
@Component
class NotificationSettingsEntityMapper {
    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: NotificationSettings): NotificationSettingsEntity {
        return NotificationSettingsEntity(
            userId = domain.userId.value,
            salonId = domain.salonId.value,
            pushNotificationsEnabled = domain.pushNotificationsEnabled,
            soundPreference = domain.soundPreference,
            vibrationEnabled = domain.vibrationEnabled,
            dndEnabled = domain.doNotDisturb.enabled,
            dndStartTime = domain.doNotDisturb.startTime,
            dndEndTime = domain.doNotDisturb.endTime,
            dndAllowCritical = domain.doNotDisturb.allowCritical,
            newAppointments = domain.notificationRules.newAppointments,
            appointmentCancellations = domain.notificationRules.appointmentCancellations,
            appointmentRescheduled = domain.notificationRules.appointmentRescheduled,
            defaultPriority = domain.notificationRules.defaultPriority,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt,
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: NotificationSettingsEntity): NotificationSettings {
        return NotificationSettings(
            userId = UserId.of(entity.userId),
            salonId = SalonId.of(entity.salonId),
            pushNotificationsEnabled = entity.pushNotificationsEnabled,
            soundPreference = entity.soundPreference,
            vibrationEnabled = entity.vibrationEnabled,
            doNotDisturb =
                DoNotDisturbSettings(
                    enabled = entity.dndEnabled,
                    startTime = entity.dndStartTime,
                    endTime = entity.dndEndTime,
                    allowCritical = entity.dndAllowCritical,
                ),
            notificationRules =
                NotificationRules(
                    newAppointments = entity.newAppointments,
                    appointmentCancellations = entity.appointmentCancellations,
                    appointmentRescheduled = entity.appointmentRescheduled,
                    defaultPriority = entity.defaultPriority,
                ),
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
        )
    }
}
