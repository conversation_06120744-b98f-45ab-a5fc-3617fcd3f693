package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

/**
 * JPA entity for tracking processed RevenueCat webhook events for idempotency
 */
@Entity
@Table(
    name = "rc_webhook_events",
    indexes = [
        Index(name = "idx_rc_webhook_events_processed_at", columnList = "processed_at"),
    ]
)
data class RevenueCatWebhookEvent(
    @Id
    @field:NotBlank(message = "Event ID is required")
    @Column(name = "event_id", nullable = false)
    val eventId: String = "",

    @Column(name = "processed_at", nullable = false)
    val processedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        eventId = "",
        processedAt = LocalDateTime.now(),
    )

    companion object {
        /**
         * Create a new webhook event record
         */
        fun create(eventId: String): RevenueCatWebhookEvent {
            return RevenueCatWebhookEvent(
                eventId = eventId,
                processedAt = LocalDateTime.now(),
            )
        }
    }
}
