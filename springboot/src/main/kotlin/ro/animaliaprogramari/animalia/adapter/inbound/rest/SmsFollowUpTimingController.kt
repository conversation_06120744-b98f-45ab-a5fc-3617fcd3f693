package ro.animaliaprogramari.animalia.adapter.inbound.rest

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.usecase.CreateSmsFollowUpTimingCommand
import ro.animaliaprogramari.animalia.application.usecase.SmsFollowUpTimingUseCase
import ro.animaliaprogramari.animalia.application.usecase.UpdateSmsFollowUpTimingCommand
import ro.animaliaprogramari.animalia.domain.model.AppointmentStatus
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsFollowUpTimingId
import ro.animaliaprogramari.animalia.infrastructure.scheduler.FollowUpScheduler
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

@RestController
@RequestMapping("/sms-followup-timings")
class SmsFollowUpTimingController(
    private val smsFollowUpTimingUseCase: SmsFollowUpTimingUseCase,
    private val appointmentRepository: AppointmentRepository,
    private val followUpScheduler: FollowUpScheduler
) {

    @GetMapping("/salon/{salonId}")
    fun getTimingsBySalon(@PathVariable salonId: String): ResponseEntity<ApiResponse<List<SmsFollowUpTimingResponse>>> {
        val timings = smsFollowUpTimingUseCase.getTimingsBySalon(SalonId.of(salonId))
        return ResponseEntity.ok(ApiResponse.success(timings.map { it.toResponse() }))
    }

    @GetMapping("/salon/{salonId}/enabled")
    fun getEnabledTimingsBySalon(@PathVariable salonId: String): ResponseEntity<ApiResponse<List<SmsFollowUpTimingResponse>>> {
        val timings = smsFollowUpTimingUseCase.getEnabledTimingsBySalon(SalonId.of(salonId))
        return ResponseEntity.ok(ApiResponse.success(timings.map { it.toResponse() }))
    }

    @PostMapping
    fun createTiming(@RequestBody request: CreateSmsFollowUpTimingRequest): ResponseEntity<ApiResponse<SmsFollowUpTimingResponse>> {
        val command = CreateSmsFollowUpTimingCommand(
            salonId = SalonId.of(request.salonId),
            hoursAfter = request.hoursAfter,
            isEnabled = request.isEnabled
        )

        val timing = smsFollowUpTimingUseCase.createTiming(command)
        return ResponseEntity.ok(ApiResponse.success(timing.toResponse()))

    }

    @PutMapping("/{timingId}")
    fun updateTiming(
        @PathVariable timingId: String,
        @RequestBody request: UpdateSmsFollowUpTimingRequest
    ): ResponseEntity<ApiResponse<SmsFollowUpTimingResponse>> {
        val command = UpdateSmsFollowUpTimingCommand(
            timingId = SmsFollowUpTimingId.of(timingId),
            hoursAfter = request.hoursAfter,
            isEnabled = request.isEnabled
        )

        val timing = smsFollowUpTimingUseCase.updateTiming(command)
        return ResponseEntity.ok(ApiResponse.success(timing.toResponse()))
    }

    @DeleteMapping("/{timingId}")
    fun deleteTiming(@PathVariable timingId: String): ResponseEntity<ApiResponse<Unit>> {
        smsFollowUpTimingUseCase.deleteTiming(SmsFollowUpTimingId.of(timingId))
        return ResponseEntity.ok(ApiResponse.success(Unit, "Follow-up timing deleted successfully"))
    }

    @GetMapping("/debug/{salonId}")
    fun debugFollowUpStatus(@PathVariable salonId: String): ResponseEntity<ApiResponse<Map<String, Any>>> {
        val now = LocalDateTime.now()
        val salon = SalonId.of(salonId)

        // Get all enabled timings for this salon
        val enabledTimings = smsFollowUpTimingUseCase.getEnabledTimingsBySalon(salon)

        // Get completed appointments from today
        val todayAppointments = appointmentRepository.findByDateAndStatus(now.toLocalDate(), AppointmentStatus.COMPLETED)
            .filter { it.salonId == salon }

        val debugInfo = mutableMapOf<String, Any>()
        debugInfo["currentTime"] = now.toString()
        debugInfo["salonId"] = salonId
        debugInfo["enabledTimings"] = enabledTimings.map {
            mapOf(
                "id" to it.id.value,
                "hoursAfter" to it.hoursAfter,
                "isEnabled" to it.isEnabled
            )
        }
        debugInfo["completedAppointmentsToday"] = todayAppointments.map {
            val appointmentEndDateTime = LocalDateTime.of(it.appointmentDate, it.endTime)
            val minutesSinceCompletion = ChronoUnit.MINUTES.between(appointmentEndDateTime, now)

            mapOf(
                "appointmentId" to it.id.value,
                "clientId" to it.clientId.value,
                "endTime" to it.endTime.toString(),
                "minutesSinceCompletion" to minutesSinceCompletion,
                "status" to it.status.name
            )
        }

        // Check which appointments would trigger follow-ups
        val followUpCandidates = mutableListOf<Map<String, Any>>()
        enabledTimings.forEach { timing ->
            todayAppointments.forEach { appt ->
                val appointmentEndDateTime = LocalDateTime.of(appt.appointmentDate, appt.endTime)
                val minutesSinceCompletion = ChronoUnit.MINUTES.between(appointmentEndDateTime, now)
                val targetMinutes = timing.hoursAfter * 60
                val inWindow = minutesSinceCompletion in (targetMinutes - 15)..(targetMinutes)

                followUpCandidates.add(mapOf(
                    "appointmentId" to appt.id.value,
                    "timingId" to timing.id.value,
                    "hoursAfter" to timing.hoursAfter,
                    "targetMinutes" to targetMinutes,
                    "minutesSinceCompletion" to minutesSinceCompletion,
                    "inWindow" to inWindow,
                    "windowRange" to "${targetMinutes - 15} to $targetMinutes minutes"
                ))
            }
        }
        debugInfo["followUpCandidates"] = followUpCandidates

        return ResponseEntity.ok(ApiResponse.success(debugInfo))
    }
}

data class CreateSmsFollowUpTimingRequest(
    val salonId: String,
    val hoursAfter: Int,
    val isEnabled: Boolean = true
)

data class UpdateSmsFollowUpTimingRequest(
    val hoursAfter: Int,
    val isEnabled: Boolean
)

data class SmsFollowUpTimingResponse(
    val id: String,
    val salonId: String,
    val hoursAfter: Int,
    val isEnabled: Boolean,
    val description: String,
    val createdAt: String,
    val updatedAt: String
)

private fun ro.animaliaprogramari.animalia.domain.model.SmsFollowUpTiming.toResponse(): SmsFollowUpTimingResponse {
    return SmsFollowUpTimingResponse(
        id = this.id.value,
        salonId = this.salonId.value,
        hoursAfter = this.hoursAfter,
        isEnabled = this.isEnabled,
        description = this.getDescription(),
        createdAt = this.createdAt.toString(),
        updatedAt = this.updatedAt.toString()
    )
}
