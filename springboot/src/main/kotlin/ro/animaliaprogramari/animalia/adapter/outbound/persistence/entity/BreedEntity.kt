package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.time.LocalDateTime
import java.util.*

/**
 * JPA entity for breed persistence
 * Maps to the breeds table in the database
 */
@Entity
@Table(
    name = "breeds",
    uniqueConstraints = [
        UniqueConstraint(name = "uk_breeds_name_species", columnNames = ["name", "species"])
    ],
    indexes = [
        Index(name = "idx_breeds_species", columnList = "species"),
        Index(name = "idx_breeds_size", columnList = "size"),
        Index(name = "idx_breeds_active", columnList = "is_active")
    ]
)
data class BreedEntity(
    @Id
    @Column(name = "id", nullable = false)
    val id: String,

    @field:NotBlank(message = "Breed name is required")
    @Column(name = "name", nullable = false)
    val name: String,

    @field:NotBlank(message = "Species is required")
    @Column(name = "species", nullable = false, length = 50)
    val species: String, // 'dog', 'cat', 'other'

    @field:NotBlank(message = "Size is required")
    @Column(name = "size", nullable = false, length = 10)
    val size: String, // 'S', 'M', 'L'

    @field:NotNull(message = "Active status is required")
    @Column(name = "is_active", nullable = false)
    val isActive: Boolean = true,

    @field:NotNull(message = "Created at is required")
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @field:NotNull(message = "Updated at is required")
    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(
        id = UUID.randomUUID().toString(),
        name = "",
        species = "",
        size = "",
        isActive = true,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    /**
     * Get display name for size
     */
    fun getSizeDisplayName(): String {
        return when (size) {
            "S" -> "Mic"
            "M" -> "Mediu"
            "L" -> "Mare"
            else -> "Mediu"
        }
    }

    /**
     * Get display name for species
     */
    fun getSpeciesDisplayName(): String {
        return when (species.lowercase()) {
            "dog" -> "Câine"
            "cat" -> "Pisică"
            "other" -> "Altul"
            else -> species
        }
    }

    /**
     * Check if breed is for dogs
     */
    fun isDogBreed(): Boolean = species.equals("dog", ignoreCase = true)

    /**
     * Check if breed is for cats
     */
    fun isCatBreed(): Boolean = species.equals("cat", ignoreCase = true)

    /**
     * Check if breed is for other animals
     */
    fun isOtherBreed(): Boolean = species.equals("other", ignoreCase = true)

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BreedEntity

        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun toString(): String {
        return "BreedEntity(id='$id', name='$name', species='$species', size='$size', isActive=$isActive)"
    }
}
