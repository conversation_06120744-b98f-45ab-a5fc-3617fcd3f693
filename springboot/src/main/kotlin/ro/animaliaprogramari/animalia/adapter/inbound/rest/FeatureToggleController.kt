package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.application.service.FeatureFlagService
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

@RestController
@RequestMapping("/feature-toggles")
@Tag(name = "Feature Toggles", description = "Operations for retrieving and managing feature toggles")
class FeatureToggleController(
    private val featureFlagService: FeatureFlagService
) {
    @GetMapping
    @Operation(summary = "List feature toggles", description = "Retrieve available feature flags from database")
    @SwaggerApiResponse(responseCode = "200", description = "Feature flags retrieved successfully")
    fun getFeatureToggles(): ApiResponse<Map<String, Any>> {
        val features = featureFlagService.getAllFeatureFlags()
        return ApiResponse.success(features)
    }

    @GetMapping("/{flagName}")
    @Operation(summary = "Get specific feature flag", description = "Retrieve a specific feature flag value")
    @SwaggerApiResponse(responseCode = "200", description = "Feature flag retrieved successfully")
    fun getFeatureFlag(@PathVariable flagName: String): ApiResponse<Boolean> {
        val isEnabled = featureFlagService.isFeatureEnabled(flagName)
        return ApiResponse.success(isEnabled)
    }

    @PutMapping("/{flagName}")
    @Operation(summary = "Update feature flag", description = "Update a specific feature flag value")
    @SwaggerApiResponse(responseCode = "200", description = "Feature flag updated successfully")
    fun updateFeatureFlag(
        @PathVariable flagName: String,
        @RequestBody request: UpdateFeatureFlagRequest
    ): ResponseEntity<ApiResponse<Boolean>> {
        val success = featureFlagService.updateFeatureFlag(flagName, request.enabled)

        return if (success) {
            ResponseEntity.ok(ApiResponse.success(request.enabled))
        } else {
            ResponseEntity.internalServerError()
                .body(ApiResponse.error("Failed to update feature flag"))
        }
    }

    @PostMapping("/clear-cache")
    @Operation(summary = "Clear feature flags cache", description = "Clear the feature flags cache to force refresh")
    @SwaggerApiResponse(responseCode = "200", description = "Cache cleared successfully")
    fun clearCache(): ApiResponse<String> {
        featureFlagService.clearCache()
        return ApiResponse.success("Feature flags cache cleared")
    }

    data class UpdateFeatureFlagRequest(
        val enabled: Boolean
    )
}
