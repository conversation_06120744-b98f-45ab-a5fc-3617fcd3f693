package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationHistory
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringNotificationHistoryRepository
import ro.animaliaprogramari.animalia.application.port.outbound.NotificationHistoryRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import java.time.LocalDateTime

/**
 * Adapter that implements the NotificationHistoryRepository port
 * Bridges the domain layer with the JPA repository
 */
@Repository
class NotificationHistoryRepositoryAdapter(
    private val springRepository: SpringNotificationHistoryRepository
) : NotificationHistoryRepository {

    override fun save(notification: NotificationHistory): NotificationHistory {
        return springRepository.save(notification)
    }

    override fun findById(id: Long): NotificationHistory? {
        return springRepository.findById(id).orElse(null)
    }

    override fun findBySalonIdWithFilters(
        salonId: SalonId,
        type: String?,
        readStatus: Boolean?,
        startDate: LocalDateTime?,
        endDate: LocalDateTime?,
        pageable: Pageable
    ): Page<NotificationHistory> {
        return springRepository.findBySalonIdWithFilters(
            salonId = salonId.value,
            type = type,
            readStatus = readStatus,
            startDate = startDate,
            endDate = endDate,
            pageable = pageable
        )
    }

    override fun findRecentUnreadBySalonId(
        salonId: SalonId,
        pageable: Pageable
    ): Page<NotificationHistory> {
        return springRepository.findRecentUnreadBySalonId(
            salonId = salonId.value,
            pageable = pageable
        )
    }

    override fun countBySalonIdAndReadStatus(
        salonId: SalonId,
        readStatus: Boolean
    ): Long {
        return springRepository.countBySalonIdAndReadStatus(
            salonId = salonId.value,
            readStatus = readStatus
        )
    }

    override fun findByAppointmentIdOrderByTimestampDesc(
        appointmentId: String
    ): List<NotificationHistory> {
        return springRepository.findByAppointmentIdOrderByTimestampDesc(appointmentId)
    }

    override fun findByClientIdOrderByTimestampDesc(
        clientId: String
    ): List<NotificationHistory> {
        return springRepository.findByClientIdOrderByTimestampDesc(clientId)
    }

    override fun markAsReadBatch(
        notificationIds: List<Long>,
        updatedAt: LocalDateTime
    ): Int {
        return springRepository.markAsReadBatch(notificationIds, updatedAt)
    }

    override fun markAllAsReadBySalonId(
        salonId: SalonId,
        updatedAt: LocalDateTime
    ): Int {
        return springRepository.markAllAsReadBySalonId(
            salonId = salonId.value,
            updatedAt = updatedAt
        )
    }

    override fun deleteOldNotifications(cutoffDate: LocalDateTime): Int {
        return springRepository.deleteOldNotifications(cutoffDate)
    }

    override fun getNotificationStatistics(
        salonId: SalonId,
        startDate: LocalDateTime
    ): List<Array<Any>> {
        return springRepository.getNotificationStatistics(
            salonId = salonId.value,
            startDate = startDate
        )
    }

    override fun findByTypeAndDateRange(
        salonId: SalonId,
        type: String,
        startDate: LocalDateTime,
        endDate: LocalDateTime
    ): List<NotificationHistory> {
        return springRepository.findByTypeAndDateRange(
            salonId = salonId.value,
            type = type,
            startDate = startDate,
            endDate = endDate
        )
    }

    override fun existsByAppointmentIdAndType(
        appointmentId: String,
        type: String
    ): Boolean {
        return springRepository.existsByAppointmentIdAndType(appointmentId, type)
    }

    override fun findFirstByAppointmentIdAndTypeOrderByTimestampDesc(
        appointmentId: String,
        type: String
    ): NotificationHistory? {
        return springRepository.findFirstByAppointmentIdAndTypeOrderByTimestampDesc(
            appointmentId,
            type
        )
    }

    override fun getDashboardSummary(
        salonId: SalonId,
        startDate: LocalDateTime
    ): List<Map<String, Any>> {
        return springRepository.getDashboardSummary(
            salonId = salonId.value,
            startDate = startDate
        )
    }
}
