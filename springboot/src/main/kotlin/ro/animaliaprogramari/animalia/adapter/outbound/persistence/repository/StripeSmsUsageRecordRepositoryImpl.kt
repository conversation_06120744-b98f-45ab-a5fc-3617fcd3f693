package ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StripeSmsUsageRecordEntity
import ro.animaliaprogramari.animalia.application.port.outbound.StripeSmsUsageRecordRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.billing.StripeSmsUsageRecord
import ro.animaliaprogramari.animalia.domain.model.billing.StripeSmsUsageRecordId
import java.time.LocalDateTime

@Repository
class StripeSmsUsageRecordRepositoryImpl(
    private val jpaRepository: JpaStripeSmsUsageRecordRepository
) : StripeSmsUsageRecordRepository {

    override fun save(record: StripeSmsUsageRecord): StripeSmsUsageRecord {
        val entity = toEntity(record)
        val saved = jpaRepository.save(entity)
        return toDomain(saved)
    }

    override fun findById(id: StripeSmsUsageRecordId): StripeSmsUsageRecord? {
        return jpaRepository.findById(id.value).map { toDomain(it) }.orElse(null)
    }

    override fun findBySalonId(salonId: SalonId): List<StripeSmsUsageRecord> {
        return jpaRepository.findBySalonId(salonId.value).map { toDomain(it) }
    }

    override fun findBySalonIdAndReportedAtBetween(salonId: SalonId, startDate: LocalDateTime, endDate: LocalDateTime): List<StripeSmsUsageRecord> {
        return jpaRepository.findBySalonIdAndReportedAtBetween(salonId.value, startDate, endDate).map { toDomain(it) }
    }

    private fun toEntity(record: StripeSmsUsageRecord): StripeSmsUsageRecordEntity {
        return StripeSmsUsageRecordEntity(
            id = record.id.value,
            salonId = record.salonId.value,
            smsUnits = record.smsUnits,
            stripeCustomerId = record.stripeCustomerId,
            meterEventName = record.meterEventName,
            reportedAt = record.reportedAt,
            stripeTimestamp = record.stripeTimestamp,
            createdAt = record.createdAt
        )
    }

    private fun toDomain(entity: StripeSmsUsageRecordEntity): StripeSmsUsageRecord {
        return StripeSmsUsageRecord(
            id = StripeSmsUsageRecordId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            smsUnits = entity.smsUnits,
            stripeCustomerId = entity.stripeCustomerId,
            meterEventName = entity.meterEventName,
            reportedAt = entity.reportedAt,
            stripeTimestamp = entity.stripeTimestamp,
            createdAt = entity.createdAt
        )
    }
}