package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.sms.UpdateSmsReminderTimingRequest
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.CreateSmsReminderTimingCommand
import ro.animaliaprogramari.animalia.application.command.UpdateSmsReminderSettingsCommand
import ro.animaliaprogramari.animalia.application.command.UpdateSmsReminderTimingCommand
import ro.animaliaprogramari.animalia.application.port.inbound.SmsReminderManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.MessagingChannelType
import ro.animaliaprogramari.animalia.application.query.GetSmsReminderSettingsQuery
import ro.animaliaprogramari.animalia.application.usecase.SmsReminderTimingUseCase
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.*
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.sms.CreateSmsReminderTimingRequest
import ro.animaliaprogramari.animalia.application.command.DeleteSmsReminderTimingCommand

/**
 * REST controller for salon SMS reminder management operations
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@RestController
@RequestMapping("/salons")
@Tag(name = "SMS Reminders", description = "Manage SMS reminder settings")
class SalonRemindersController(
    private val smsReminderManagementUseCase: SmsReminderManagementUseCase,
    private val smsReminderTimingUseCase: SmsReminderTimingUseCase,
) {
    private val logger = LoggerFactory.getLogger(SalonRemindersController::class.java)

    /**
     * GET /api/salons/{salonId}/sms-reminders
     * Get SMS reminder settings for a salon
     */
    @Operation(summary = "Get SMS reminder settings")
    @SwaggerApiResponse(responseCode = "200", description = "Settings retrieved")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @GetMapping("/{salonId}/sms-reminders")
    fun getSmsReminderSettings(
        @PathVariable salonId: String,
    ): ResponseEntity<ApiResponse<SmsReminderSettingsResponse>> {
        logger.info("REST request to get SMS reminder settings for salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can view SMS settings
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să accesați setările SMS ale acestui salon"))
            }

            val query = GetSmsReminderSettingsQuery(salon)
            val settings = smsReminderManagementUseCase.getSmsReminderSettings(query)

            val response =
                SmsReminderSettingsResponse(
                    salonId = settings.salonId.value,
                    enabled = settings.enabled,
                    appointmentConfirmations = settings.appointmentConfirmations,
                    rescheduleNotifications = settings.rescheduleNotifications,
                    cancellationNotifications = settings.cancellationNotifications,
                    completionMessages = settings.completionMessages,
                    followUpMessages = settings.followUpMessages,
                    birthdayMessages = settings.birthdayMessages,
                    messagingChannel = settings.messagingChannel.name,
                    updatedAt = settings.updatedAt,
                )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error getting SMS reminder settings: ${e.message}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get SMS reminder settings: ${e.message}"))
        }
    }

    /**
     * PUT /api/salons/{salonId}/sms-reminders
     * Update SMS reminder settings for a salon
     */
    @Operation(summary = "Update SMS reminder settings")
    @SwaggerApiResponse(responseCode = "200", description = "Settings updated")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data")
    @PutMapping("/{salonId}/sms-reminders")
    fun updateSmsReminderSettings(
        @PathVariable salonId: String,
        @Valid @RequestBody request: UpdateSmsReminderSettingsRequest,
    ): ResponseEntity<ApiResponse<SmsReminderSettingsResponse>> {
        logger.info("REST request to update SMS reminder settings for salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can update SMS settings
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați setările SMS ale acestui salon"))
            }

            // Parse messaging channel from request
            val messagingChannel = try {
                MessagingChannelType.valueOf(request.messagingChannel.uppercase())
            } catch (e: IllegalArgumentException) {
                logger.warn("Invalid messaging channel: ${request.messagingChannel}, defaulting to SMS")
                MessagingChannelType.SMS
            }

            val command =
                UpdateSmsReminderSettingsCommand(
                    salonId = salon,
                    enabled = request.enabled,
                    appointmentConfirmations = request.appointmentConfirmations,
                    rescheduleNotifications = request.rescheduleNotifications,
                    cancellationNotifications = request.cancellationNotifications,
                    completionMessages = request.completionMessages,
                    followUpMessages = request.followUpMessages,
                    birthdayMessages = request.birthdayMessages,
                    messagingChannel = messagingChannel,
                    updaterUserId = currentUser.userId,
                )

            val updatedSettings = smsReminderManagementUseCase.updateSmsReminderSettings(command)

            val response =
                SmsReminderSettingsResponse(
                    salonId = updatedSettings.salonId.value,
                    enabled = updatedSettings.enabled,
                    appointmentConfirmations = updatedSettings.appointmentConfirmations,
                    rescheduleNotifications = updatedSettings.rescheduleNotifications,
                    cancellationNotifications = updatedSettings.cancellationNotifications,
                    completionMessages = updatedSettings.completionMessages,
                    followUpMessages = updatedSettings.followUpMessages,
                    birthdayMessages = updatedSettings.birthdayMessages,
                    messagingChannel = updatedSettings.messagingChannel.name,
                    updatedAt = updatedSettings.updatedAt,
                )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error updating SMS reminder settings: ${e.message}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to update SMS reminder settings: ${e.message}"))
        }
    }


        @PostMapping("/{salonId}/sms-reminder-timings")
        @Operation(summary = "Create SMS reminder timing")
        @SwaggerApiResponse(responseCode = "200", description = "Timing created successfully")
        @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
        @SwaggerApiResponse(responseCode = "403", description = "Access denied")
        fun createTiming(
            @PathVariable salonId: String,
            @Valid @RequestBody request: CreateSmsReminderTimingRequest,
        ): ResponseEntity<ApiResponse<Map<String, Any>>> {
            logger.info("REST request to create SMS reminder timing for salon: $salonId")

            return try {
                val currentUser =
                    SecurityUtils.getCurrentUser()
                        ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

                val salon = SalonId.of(salonId)

                // Authorization: Only chief groomers can create SMS timings
                if (!currentUser.isChiefGroomerInSalon(salon)) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("Nu aveți permisiunea să creați timing-uri SMS pentru acest salon"))
                }

                val command = CreateSmsReminderTimingCommand(
                    salonId = salon,
                    hoursBefore = request.hoursBefore,
                    isEnabled = request.isEnabled,
                    creatorUserId = currentUser.userId
                )

                val timing = smsReminderTimingUseCase.createTiming(command)

                val response = mapOf(
                    "id" to timing.id.value,
                    "salonId" to timing.salonId.value,
                    "hoursBefore" to timing.hoursBefore,
                    "isEnabled" to timing.isEnabled,
                    "description" to timing.getDescription(),
                    "createdAt" to timing.createdAt.toString(),
                    "updatedAt" to timing.updatedAt.toString()
                )

                ResponseEntity.ok(ApiResponse.success(response))
            } catch (e: Exception) {
                logger.error("Error creating SMS reminder timing: ${e.message}", e)
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to create SMS reminder timing: ${e.message}"))
            }
        }

        @GetMapping("/{salonId}/sms-reminder-timings")
        @Operation(summary = "Get all SMS reminder timings for salon")
        @SwaggerApiResponse(responseCode = "200", description = "Timings retrieved successfully")
        @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
        @SwaggerApiResponse(responseCode = "403", description = "Access denied")
        fun getTimings(@PathVariable salonId: String): ResponseEntity<ApiResponse<List<Map<String, Any>>>> {
            logger.info("REST request to get SMS reminder timings for salon: $salonId")

            return try {
                val currentUser =
                    SecurityUtils.getCurrentUser()
                        ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

                val salon = SalonId.of(salonId)

                // Authorization: Only chief groomers can view SMS timings
                if (!currentUser.isChiefGroomerInSalon(salon)) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("Nu aveți permisiunea să accesați timing-urile SMS ale acestui salon"))
                }

                val timings = smsReminderTimingUseCase.getTimingsBySalon(salon)

                val response = timings.map { timing ->
                    mapOf(
                        "id" to timing.id.value,
                        "salonId" to timing.salonId.value,
                        "hoursBefore" to timing.hoursBefore,
                        "isEnabled" to timing.isEnabled,
                        "description" to timing.getDescription(),
                        "createdAt" to timing.createdAt.toString(),
                        "updatedAt" to timing.updatedAt.toString()
                    )
                }

                ResponseEntity.ok(ApiResponse.success(response))
            } catch (e: Exception) {
                logger.error("Error getting SMS reminder timings: ${e.message}", e)
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to get SMS reminder timings: ${e.message}"))
            }
        }

        @PutMapping("/{salonId}/sms-reminder-timings/{timingId}")
        @Operation(summary = "Update SMS reminder timing")
        @SwaggerApiResponse(responseCode = "200", description = "Timing updated successfully")
        @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
        @SwaggerApiResponse(responseCode = "403", description = "Access denied")
        fun updateTiming(
            @PathVariable salonId: String,
            @PathVariable timingId: String,
            @Valid @RequestBody request: UpdateSmsReminderTimingRequest,
        ): ResponseEntity<ApiResponse<Map<String, Any>>> {
            logger.info("REST request to update SMS reminder timing: $timingId for salon: $salonId")

            return try {
                val currentUser =
                    SecurityUtils.getCurrentUser()
                        ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

                val salon = SalonId.of(salonId)

                // Authorization: Only chief groomers can update SMS timings
                if (!currentUser.isChiefGroomerInSalon(salon)) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("Nu aveți permisiunea să modificați timing-urile SMS ale acestui salon"))
                }

                val command = UpdateSmsReminderTimingCommand(
                    timingId = timingId,
                    salonId = salon,
                    hoursBefore = request.hoursBefore,
                    isEnabled = request.isEnabled,
                    updaterUserId = currentUser.userId
                )

                val timing = smsReminderTimingUseCase.updateTiming(command)

                val response = mapOf(
                    "id" to timing.id.value,
                    "salonId" to timing.salonId.value,
                    "hoursBefore" to timing.hoursBefore,
                    "isEnabled" to timing.isEnabled,
                    "description" to timing.getDescription(),
                    "createdAt" to timing.createdAt.toString(),
                    "updatedAt" to timing.updatedAt.toString()
                )

                ResponseEntity.ok(ApiResponse.success(response))
            } catch (e: Exception) {
                logger.error("Error updating SMS reminder timing: ${e.message}", e)
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to update SMS reminder timing: ${e.message}"))
            }
        }

        @DeleteMapping("/{salonId}/sms-reminder-timings/{timingId}")
        @Operation(summary = "Delete SMS reminder timing")
        @SwaggerApiResponse(responseCode = "200", description = "Timing deleted successfully")
        @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
        @SwaggerApiResponse(responseCode = "403", description = "Access denied")
        fun deleteTiming(
            @PathVariable salonId: String,
            @PathVariable timingId: String,
        ): ResponseEntity<ApiResponse<Map<String, Any>>> {
            logger.info("REST request to delete SMS reminder timing: $timingId for salon: $salonId")

            return try {
                val currentUser =
                    SecurityUtils.getCurrentUser()
                        ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

                val salon = SalonId.of(salonId)

                // Authorization: Only chief groomers can delete SMS timings
                if (!currentUser.isChiefGroomerInSalon(salon)) {
                    return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("Nu aveți permisiunea să ștergeți timing-urile SMS ale acestui salon"))
                }

                val command = DeleteSmsReminderTimingCommand(
                    timingId = timingId,
                    salonId = salon,
                    deleterUserId = currentUser.userId
                )

                val deleted = smsReminderTimingUseCase.deleteTiming(command)

                val response = mapOf(
                    "deleted" to deleted,
                    "timingId" to timingId,
                    "message" to "Timing-ul SMS a fost șters cu succes"
                )

                ResponseEntity.ok(ApiResponse.success(response))
            } catch (e: Exception) {
                logger.error("Error deleting SMS reminder timing: ${e.message}", e)
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to delete SMS reminder timing: ${e.message}"))
            }
        }
}
