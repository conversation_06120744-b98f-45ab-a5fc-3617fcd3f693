package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringMassSmsTemplateRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.MassSmsTemplateEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.MassSmsTemplateRepository
import ro.animaliaprogramari.animalia.domain.model.MassSmsTemplate
import ro.animaliaprogramari.animalia.domain.model.SalonId

@Repository
class MassSmsTemplateRepositoryAdapter(
    private val springRepository: SpringMassSmsTemplateRepository,
    private val mapper: MassSmsTemplateEntityMapper
) : MassSmsTemplateRepository {
    
    override fun save(template: MassSmsTemplate): MassSmsTemplate {
        val entity = mapper.toEntity(template)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }
    
    override fun findById(id: String): MassSmsTemplate? {
        return springRepository.findById(id)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }
    
    override fun findBySalonIdOrDefault(salonId: SalonId): List<MassSmsTemplate> {
        val entities = springRepository.findBySalonIdOrDefault(salonId)
        return mapper.toDomainList(entities)
    }
    
    override fun findBySalonId(salonId: SalonId): List<MassSmsTemplate> {
        val entities = springRepository.findBySalonId(salonId)
        return mapper.toDomainList(entities)
    }
    
    override fun findDefaultTemplates(): List<MassSmsTemplate> {
        // For now, return empty list - can be implemented later if needed
        return emptyList()
    }
    
    override fun findBySalonIdAndCategory(salonId: SalonId, category: String): List<MassSmsTemplate> {
        // For now, return empty list - can be implemented later if needed
        return emptyList()
    }
    
    override fun findBySalonIdAndName(salonId: SalonId, name: String): MassSmsTemplate? {
        // For now, return null - can be implemented later if needed
        return null
    }
    
    override fun existsBySalonIdAndName(salonId: SalonId, name: String): Boolean {
        // For now, return false - can be implemented later if needed
        return false
    }
    
    override fun deleteById(id: String) {
        springRepository.deleteById(id)
    }
    
    override fun countBySalonId(salonId: SalonId): Long {
        // For now, return 0 - can be implemented later if needed
        return 0L
    }
    
    override fun findMostUsedBySalonId(salonId: SalonId, limit: Int): List<MassSmsTemplate> {
        // For now, return empty list - can be implemented later if needed
        return emptyList()
    }
}
