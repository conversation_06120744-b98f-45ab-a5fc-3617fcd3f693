package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Salon

@Repository
interface SpringSalonRepository : JpaRepository<Salon, String> {
    @Query(
        "SELECT s FROM Salon s WHERE " +
            "(:search IS NULL OR LOWER(s.name) LIKE LOWER(CONCAT('%', CAST(:search AS string), '%'))) AND " +
            "(:isActive IS NULL OR s.isActive = :isActive)",
    )
    fun findBySearchAndFilters(
        @Param("search") search: String?,
        @Param("isActive") isActive: Boolean?,
    ): List<Salon>

    @Query("SELECT s FROM Salon s WHERE s.id IN (SELECT c.salonId FROM Client c WHERE c.id = :clientId)")
    fun findSalonsWithClient(
        @Param("clientId") clientId: String,
    ): List<Salon>

    @Query("SELECT s.id FROM Salon s WHERE s.isActive = true")
    fun findAllActiveSalonIds(): List<String>
}
