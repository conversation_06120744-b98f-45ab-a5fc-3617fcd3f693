package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import jakarta.validation.Valid
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.SubscriptionUseCase
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * REST controller for subscription operations
 */
@RestController
@RequestMapping("/subscriptions")
@Tag(name = "Subscriptions", description = "Operations for salon subscription management")
class SubscriptionController(
    private val subscriptionUseCase: SubscriptionUseCase,
) {
    private val logger = LoggerFactory.getLogger(SubscriptionController::class.java)

    @PostMapping("/verify-purchase")
    @Operation(summary = "Verify subscription purchase")
    fun verifyPurchase(@Valid @RequestBody request: VerifyPurchaseRequest): ResponseEntity<ApiResponse<SalonSubscriptionResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Verifying purchase for salon: ${request.salonId}")

            val command = VerifyPurchaseCommand(
                userId = currentUser.userId,
                salonId = SalonId.of(request.salonId),
                packageId = request.packageId,
                revenueCatCustomerId = request.customerInfo["originalAppUserId"] as? String ?: "",
                revenueCatEntitlementId = "", // Extract from customer info
                customerInfo = request.customerInfo
            )

            val subscription = subscriptionUseCase.verifyPurchase(command)
            val response = SalonSubscriptionResponse.fromDomain(subscription)

            logger.info("Purchase verified successfully: ${subscription.id.value}")
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: BusinessRuleViolationException) {
            logger.error("Business rule violation: ${e.message}")
            ResponseEntity.badRequest().body(ApiResponse.error(e.message?.let { "Failed to verify purchase: $it" } ?: "Failed to verify purchase"))
        } catch (e: Exception) {
            logger.error("Unexpected error verifying purchase", e)
            ResponseEntity.internalServerError().body(ApiResponse.error("Failed to verify purchase"))
        }
    }

    @GetMapping("/salon/{salonId}/current")
    @Operation(summary = "Get current subscription for salon")
    fun getCurrentSubscription(@PathVariable salonId: String): ResponseEntity<ApiResponse<SalonSubscriptionResponse?>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("🔍 Getting current subscription for salon: $salonId, user: ${currentUser.userId.value}")
// f106fc8e-e87e-40ec-924a-d8d2de9e7aff, user: a9bcfde9-1ce3-4e19-bc9c-f81bb46a330f
            val query = GetSalonSubscriptionQuery(
                salonId = SalonId.of(salonId),
                userId = currentUser.userId
            )

            val subscription = subscriptionUseCase.getCurrentSubscription(query)
            logger.info("📋 Found subscription: ${subscription?.id?.value ?: "null"}, tier: ${subscription?.tier?.name ?: "null"}, status: ${subscription?.status ?: "null"}")

            val response = subscription?.let { SalonSubscriptionResponse.fromDomain(it) }
            logger.info("✅ Returning subscription response: ${response != null}")

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: BusinessRuleViolationException) {
            logger.error("Access denied: ${e.message}")
            ResponseEntity.status(403).body(ApiResponse.error(e.message.let { "Access denied: $it" } ?: "Access denied"))
        } catch (e: Exception) {
            logger.error("Unexpected error getting subscription", e)
            ResponseEntity.internalServerError().body(ApiResponse.error("Failed to get subscription"))
        }
    }

    @GetMapping("/user/current")
    @Operation(summary = "Get current subscription for user (applies to all their salons)")
    fun getUserSubscription(): ResponseEntity<ApiResponse<SalonSubscriptionResponse?>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Getting current subscription for user: ${currentUser.userId.value}")

            val query = GetUserSubscriptionQuery(userId = currentUser.userId)
            val subscription = subscriptionUseCase.getUserSubscription(query)
            val response = subscription?.let { SalonSubscriptionResponse.fromDomain(it) }

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Unexpected error getting user subscription", e)
            ResponseEntity.internalServerError().body(ApiResponse.error("Failed to get user subscription"))
        }
    }

    @GetMapping("/salon/{salonId}/limits")
    @Operation(summary = "Get subscription limits for salon")
    fun getSubscriptionLimits(@PathVariable salonId: String): ResponseEntity<ApiResponse<SubscriptionLimitsResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            val query = GetSubscriptionLimitsQuery(
                salonId = SalonId.of(salonId),
                userId = currentUser.userId
            )

            val limits = subscriptionUseCase.getSubscriptionLimits(query)
            val response = SubscriptionLimitsResponse.fromDomain(limits)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: BusinessRuleViolationException) {
            logger.warn("Authorization error getting limits: ${e.message}")
            ResponseEntity.status(403).body(ApiResponse.error(e.message ?: "Nu aveți permisiunea să accesați această resursă"))
        } catch (e: Exception) {
            logger.error("Unexpected error getting limits", e)
            ResponseEntity.internalServerError().body(ApiResponse.error("Failed to get limits"))
        }
    }

    @GetMapping("/salon/{salonId}/feature/{feature}")
    @Operation(summary = "Check if salon can access feature")
    fun canAccessFeature(
        @PathVariable salonId: String,
        @PathVariable feature: String
    ): ResponseEntity<ApiResponse<Boolean>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            val query = CheckFeatureAccessQuery(
                salonId = SalonId.of(salonId),
                userId = currentUser.userId,
                feature = feature
            )

            val canAccess = subscriptionUseCase.canAccessFeature(query)
            ResponseEntity.ok(ApiResponse.success(canAccess))
        } catch (e: Exception) {
            logger.error("Unexpected error checking feature access", e)
            ResponseEntity.ok(ApiResponse.success(false))
        }
    }

    /**
     * Track usage for analytics
     */
    @PostMapping("/salon/{salonId}/track-usage")
    @Operation(summary = "Track usage for analytics")
    fun trackUsage(
        @PathVariable salonId: String,
        @RequestBody request: TrackUsageRequest
    ): ResponseEntity<ApiResponse<String>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            val command = TrackUsageCommand(
                salonId = SalonId.of(salonId),
                userId = currentUser.userId,
                usageType = request.type,
                timestamp = request.timestamp
            )
            subscriptionUseCase.trackUsage(command)

            ResponseEntity.ok(ApiResponse.success("Usage tracked successfully"))
        } catch (e: Exception) {
            logger.error("Failed to track usage for salon: $salonId", e)
            ResponseEntity.internalServerError()
                .body(ApiResponse.error("Failed to track usage"))
        }
    }

    /**
     * Check if action is allowed based on subscription limits
     */
    @PostMapping("/salon/{salonId}/check-limit")
    @Operation(summary = "Check if action is allowed based on subscription limits")
    fun checkLimit(
        @PathVariable salonId: String,
        @RequestBody request: CheckLimitRequest
    ): ResponseEntity<ApiResponse<CheckLimitResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            val query = CheckLimitQuery(
                salonId = SalonId.of(salonId),
                userId = currentUser.userId,
                limitType = request.limitType
            )
            val canPerform = subscriptionUseCase.checkLimit(query)

            val response = CheckLimitResponse(
                canPerform = canPerform,
                limitType = request.limitType
            )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Failed to check limit for salon: $salonId", e)
            ResponseEntity.internalServerError()
                .body(ApiResponse.error("Failed to check limit"))
        }
    }

    /**
     * Check if user can create salons based on their highest subscription tier
     */
    @GetMapping("/user/can-create-salons")
    @Operation(summary = "Check if user can create salons based on their highest subscription tier")
    fun canUserCreateSalons(): ResponseEntity<ApiResponse<CanCreateSalonsResponse>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            val query = CanUserCreateSalonsQuery(userId = currentUser.userId)
            val canCreate = subscriptionUseCase.canUserCreateSalons(query)
            val highestTier = subscriptionUseCase.getUserHighestSubscriptionTier(
                GetUserHighestTierQuery(userId = currentUser.userId)
            )

            val response = CanCreateSalonsResponse(
                canCreate = canCreate,
                highestTier = highestTier?.name,
                requiredTier = "Gold"
            )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error checking salon creation permission: ${e.message}", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to check salon creation permission: ${e.message}"))
        }
    }
}

/**
 * Request DTO for tracking usage
 */
data class TrackUsageRequest(
    val type: String, // 'staff_added', 'client_added', 'sms_sent'
    val timestamp: String
)

/**
 * Request DTO for checking limits
 */
data class CheckLimitRequest(
    val limitType: String // 'staff', 'clients', 'sms'
)

/**
 * Response DTO for limit checks
 */
data class CheckLimitResponse(
    val canPerform: Boolean,
    val limitType: String
)

/**
 * Response DTO for salon creation permission checks
 */
data class CanCreateSalonsResponse(
    val canCreate: Boolean,
    val highestTier: String?,
    val requiredTier: String
)
