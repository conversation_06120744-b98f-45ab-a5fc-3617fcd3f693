package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.sms.*
import ro.animaliaprogramari.animalia.application.command.UpdateSmsTemplateCommand
import ro.animaliaprogramari.animalia.application.query.GetSmsTemplateQuery
import ro.animaliaprogramari.animalia.application.query.GetSmsTemplatesQuery
import ro.animaliaprogramari.animalia.application.usecase.SmsTemplateManagementUseCase
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsTemplateType
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for SMS template management
 */
@RestController
@RequestMapping("/salons/{salonId}/sms-templates")
@Tag(name = "SMS Templates", description = "SMS template management operations")
class SmsTemplateController(
    private val smsTemplateManagementUseCase: SmsTemplateManagementUseCase,
) {
    private val logger = LoggerFactory.getLogger(SmsTemplateController::class.java)

    /**
     * GET /api/salons/{salonId}/sms-templates
     * Get all SMS templates for a salon
     */
    @Operation(summary = "Get SMS templates")
    @SwaggerApiResponse(responseCode = "200", description = "Templates retrieved")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @GetMapping
    fun getSmsTemplates(
        @PathVariable salonId: String,
        @RequestParam(defaultValue = "false") activeOnly: Boolean,
    ): ResponseEntity<ApiResponse<List<SmsTemplateResponse>>> {
        logger.info("REST request to get SMS templates for salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only salon members can view templates
            if (!currentUser.hasAccessToSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să vizualizați template-urile SMS ale acestui salon"))
            }

            val query = GetSmsTemplatesQuery(salon, activeOnly)
            val templates = smsTemplateManagementUseCase.getSmsTemplates(query)

            val response = templates.map { template ->
                SmsTemplateResponse(
                    id = template.id.value,
                    salonId = template.salonId.value,
                    templateType = template.templateType.name,
                    templateContent = template.templateContent,
                    isActive = template.isActive,
                    createdAt = template.createdAt,
                    updatedAt = template.updatedAt,
                )
            }

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error getting SMS templates: ${e.message}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get SMS templates: ${e.message}"))
        }
    }

    /**
     * GET /api/salons/{salonId}/sms-templates/{templateType}
     * Get a specific SMS template
     */
    @Operation(summary = "Get SMS template by type")
    @SwaggerApiResponse(responseCode = "200", description = "Template retrieved")
    @SwaggerApiResponse(responseCode = "404", description = "Template not found")
    @GetMapping("/{templateType}")
    fun getSmsTemplate(
        @PathVariable salonId: String,
        @PathVariable templateType: String,
    ): ResponseEntity<ApiResponse<SmsTemplateResponse>> {
        logger.info("REST request to get SMS template: $templateType for salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)
            val type = SmsTemplateType.fromString(templateType)

            // Authorization: Only salon members can view templates
            if (!currentUser.hasAccessToSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să vizualizați template-urile SMS ale acestui salon"))
            }

            val query = GetSmsTemplateQuery(salon, type)
            val template = smsTemplateManagementUseCase.getSmsTemplate(query)
                ?: return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Template not found"))

            val response = SmsTemplateResponse(
                id = template.id.value,
                salonId = template.salonId.value,
                templateType = template.templateType.name,
                templateContent = template.templateContent,
                isActive = template.isActive,
                createdAt = template.createdAt,
                updatedAt = template.updatedAt,
            )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error getting SMS template: ${e.message}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get SMS template: ${e.message}"))
        }
    }

    /**
     * PUT /api/salons/{salonId}/sms-templates/{templateType}
     * Update SMS template
     */
    @Operation(summary = "Update SMS template")
    @SwaggerApiResponse(responseCode = "200", description = "Template updated")
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @PutMapping("/{templateType}")
    fun updateSmsTemplate(
        @PathVariable salonId: String,
        @PathVariable templateType: String,
        @Valid @RequestBody request: UpdateSmsTemplateRequest,
    ): ResponseEntity<ApiResponse<SmsTemplateResponse>> {
        logger.info("REST request to update SMS template: $templateType for salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can update templates
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați template-urile SMS ale acestui salon"))
            }

            // First get the existing template to get its ID
            val type = SmsTemplateType.fromString(templateType)
            val existingTemplate = smsTemplateManagementUseCase.getSmsTemplate(GetSmsTemplateQuery(salon, type))
                ?: return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Template not found"))

            val command = UpdateSmsTemplateCommand(
                templateId = existingTemplate.id.value,
                salonId = salon,
                templateContent = request.templateContent,
                isActive = request.isActive,
                updaterUserId = currentUser.userId,
            )

            val updatedTemplate = smsTemplateManagementUseCase.updateSmsTemplate(command)

            val response = SmsTemplateResponse(
                id = updatedTemplate.id.value,
                salonId = updatedTemplate.salonId.value,
                templateType = updatedTemplate.templateType.name,
                templateContent = updatedTemplate.templateContent,
                isActive = updatedTemplate.isActive,
                createdAt = updatedTemplate.createdAt,
                updatedAt = updatedTemplate.updatedAt,
            )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error updating SMS template: ${e.message}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to update SMS template: ${e.message}"))
        }
    }

    /**
     * POST /api/salons/{salonId}/sms-templates/reset
     * Reset all templates to defaults
     */
    @Operation(summary = "Reset SMS templates to defaults")
    @SwaggerApiResponse(responseCode = "200", description = "Templates reset")
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @PostMapping("/reset")
    fun resetSmsTemplates(
        @PathVariable salonId: String,
    ): ResponseEntity<ApiResponse<List<SmsTemplateResponse>>> {
        logger.info("REST request to reset SMS templates for salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can reset templates
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să resetați template-urile SMS ale acestui salon"))
            }

            val templates = smsTemplateManagementUseCase.resetToDefaults(salonId)

            val response = templates.map { template ->
                SmsTemplateResponse(
                    id = template.id.value,
                    salonId = template.salonId.value,
                    templateType = template.templateType.name,
                    templateContent = template.templateContent,
                    isActive = template.isActive,
                    createdAt = template.createdAt,
                    updatedAt = template.updatedAt,
                )
            }

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error resetting SMS templates: ${e.message}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to reset SMS templates: ${e.message}"))
        }
    }

    /**
     * POST /api/salons/{salonId}/sms-templates/preview
     * Preview template with sample data
     */
    @Operation(summary = "Preview SMS template")
    @SwaggerApiResponse(responseCode = "200", description = "Template previewed")
    @PostMapping("/preview")
    fun previewSmsTemplate(
        @PathVariable salonId: String,
        @Valid @RequestBody request: PreviewSmsTemplateRequest,
    ): ResponseEntity<ApiResponse<PreviewSmsTemplateResponse>> {
        logger.info("REST request to preview SMS template for salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only salon members can preview templates
            if (!currentUser.hasAccessToSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să previzualizați template-urile SMS ale acestui salon"))
            }

            val preview = smsTemplateManagementUseCase.previewTemplate(
                templateContent = request.templateContent,
                sampleData = request.sampleData,
            )

            val response = PreviewSmsTemplateResponse(
                preview = preview,
                characterCount = preview.length,
            )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error previewing SMS template: ${e.message}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to preview SMS template: ${e.message}"))
        }
    }
}
