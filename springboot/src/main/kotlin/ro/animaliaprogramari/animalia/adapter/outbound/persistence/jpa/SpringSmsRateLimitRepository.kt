package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsRateLimit
import java.time.LocalDateTime

@Repository
interface SpringSmsRateLimitRepository : JpaRepository<SmsRateLimit, String> {
    
    fun findByPhoneNumber(phoneNumber: String): SmsRateLimit?

    @Modifying
    @Query(
        "DELETE FROM SmsRateLimit srl WHERE " +
        "srl.windowStart < :expiredBefore"
    )
    fun deleteExpiredRecords(@Param("expiredBefore") expiredBefore: LocalDateTime)
}
