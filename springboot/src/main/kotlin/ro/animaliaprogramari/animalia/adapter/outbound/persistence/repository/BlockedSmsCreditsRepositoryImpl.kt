package ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.BlockedSmsCreditsRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedSmsCredits
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsStatus
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsSource

/**
 * Implementation of BlockedSmsCreditsRepository using JPA
 */
@Component
class BlockedSmsCreditsRepositoryImpl(
    private val jpaRepository: JpaBlockedSmsCreditsRepository
) : BlockedSmsCreditsRepository {

    override fun save(blockedCredits: BlockedSmsCredits): BlockedSmsCredits {
        val entity = ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.BlockedSmsCredits.fromDomain(blockedCredits)
        val savedEntity = jpaRepository.save(entity)
        return savedEntity.toDomain()
    }

    override fun findBySalonId(salonId: SalonId): List<BlockedSmsCredits> {
        return jpaRepository.findBySalonId(salonId.value)
            .map { it.toDomain() }
    }

    override fun findBySalonIdAndStatus(salonId: SalonId, status: BlockedCreditsStatus): List<BlockedSmsCredits> {
        return jpaRepository.findBySalonIdAndStatus(salonId.value, status)
            .map { it.toDomain() }
    }

    override fun findBySourceAndSourceId(source: BlockedCreditsSource, sourceId: String): BlockedSmsCredits? {
        return jpaRepository.findBySourceAndSourceId(source, sourceId)?.toDomain()
    }

    override fun findAllBlocked(): List<BlockedSmsCredits> {
        return jpaRepository.findByStatus(BlockedCreditsStatus.BLOCKED)
            .map { it.toDomain() }
    }

    override fun findBlockedBySalonId(salonId: SalonId): List<BlockedSmsCredits> {
        return findBySalonIdAndStatus(salonId, BlockedCreditsStatus.BLOCKED)
    }

    override fun getTotalBlockedCredits(salonId: SalonId): Int {
        return jpaRepository.getTotalBlockedCredits(salonId.value, BlockedCreditsStatus.BLOCKED)
    }

    override fun delete(blockedCredits: BlockedSmsCredits) {
        jpaRepository.deleteById(blockedCredits.id.value)
    }
}
