package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

@Entity
@Table(
    name = "sms_rate_limits",
    indexes = [
        Index(name = "idx_sms_rate_limit_phone", columnList = "phone_number"),
        Index(name = "idx_sms_rate_limit_window", columnList = "window_start"),
        Index(name = "idx_sms_rate_limit_phone_window", columnList = "phone_number, window_start")
    ]
)
data class SmsRateLimit(
    @Id
    val id: String,
    @field:NotBlank(message = "Phone number is required")
    @field:Size(max = 20, message = "Phone number must not exceed 20 characters")
    @Column(name = "phone_number", nullable = false)
    val phoneNumber: String,
    @Column(name = "sent_count", nullable = false)
    val sentCount: Int = 0,
    @Column(name = "window_start", nullable = false)
    val windowStart: LocalDateTime,
    @Column(name = "window_duration_minutes", nullable = false)
    val windowDurationMinutes: Int = 60,
    @Column(name = "max_sms_per_window", nullable = false)
    val maxSmsPerWindow: Int = 3,
    @Column(name = "last_sent_at")
    val lastSentAt: LocalDateTime? = null,
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        phoneNumber = "",
        sentCount = 0,
        windowStart = LocalDateTime.now(),
        windowDurationMinutes = 60,
        maxSmsPerWindow = 3,
        lastSentAt = null,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as SmsRateLimit
        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}
