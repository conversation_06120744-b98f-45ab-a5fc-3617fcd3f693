package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * JPA entity for salon-wide working hours settings
 * This is an infrastructure concern - data representation for persistence
 * Note: Uses different table from staff-specific working hours to avoid conflicts
 */
@Entity
@Table(name = "salon_working_hours_settings")
data class WorkingHoursSettings(
    @Id
    @Column(name = "salon_id")
    val salonId: String,
    @Column(name = "created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now(),
    @OneToMany(mappedBy = "salonId", cascade = [CascadeType.ALL], fetch = FetchType.LAZY, orphanRemoval = true)
    val weeklySchedule: List<WeeklySchedule> = emptyList(),
    @OneToMany(mappedBy = "salonId", cascade = [CascadeType.ALL], fetch = FetchType.LAZY, orphanRemoval = true)
    val holidays: List<Holiday> = emptyList(),
    @OneToMany(mappedBy = "salonId", cascade = [CascadeType.ALL], fetch = FetchType.LAZY, orphanRemoval = true)
    val customClosures: List<CustomClosure> = emptyList(),
) {
    // Default constructor for JPA
    constructor() : this(
        "",
        LocalDateTime.now(),
        LocalDateTime.now(),
        emptyList(),
        emptyList(),
        emptyList(),
    )
}
