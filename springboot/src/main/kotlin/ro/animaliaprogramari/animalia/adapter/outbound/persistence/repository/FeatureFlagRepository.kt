package ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.FeatureFlag
import java.util.*

/**
 * Repository for managing feature flags
 */
@Repository
interface FeatureFlagRepository : JpaRepository<FeatureFlag, Long> {

    /**
     * Find feature flag by name
     */
    fun findByFlagName(flagName: String): Optional<FeatureFlag>

    /**
     * Get all feature flags as a map of name -> value
     */
    @Query("SELECT f.flagName, f.flagValue FROM FeatureFlag f")
    fun findAllAsMap(): List<Array<Any>>

    /**
     * Get feature flag value by name, with default fallback
     */
    @Query("SELECT f.flagValue FROM FeatureFlag f WHERE f.flagName = :flagName")
    fun findValueByFlagName(@Param("flagName") flagName: String): Optional<Boolean>

}
