package ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository

import org.springframework.data.jpa.repository.JpaRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StripeSmsUsageRecordEntity

interface JpaStripeSmsUsageRecordRepository : JpaRepository<StripeSmsUsageRecordEntity, String> {
    fun findBySalonId(salonId: String): List<StripeSmsUsageRecordEntity>
    fun findBySalonIdAndReportedAtBetween(salonId: String, startDate: java.time.LocalDateTime, endDate: java.time.LocalDateTime): List<StripeSmsUsageRecordEntity>
}