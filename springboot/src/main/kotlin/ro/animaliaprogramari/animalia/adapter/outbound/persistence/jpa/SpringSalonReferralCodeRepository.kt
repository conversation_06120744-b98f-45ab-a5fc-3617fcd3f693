package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonReferralCode

@Repository
interface SpringSalonReferralCodeRepository : JpaRepository<SalonReferralCode, String> {
    
    /**
     * Find salon referral code by salon ID
     */
    fun findBySalonId(salonId: String): SalonReferralCode?
    
    /**
     * Find salon referral code by code string
     */
    fun findByCode(code: String): SalonReferralCode?
    
    /**
     * Check if code exists
     */
    fun existsByCode(code: String): Bo<PERSON>an
    
    /**
     * Check if salon already has a referral code
     */
    fun existsBySalonId(salonId: String): <PERSON><PERSON><PERSON>
}
