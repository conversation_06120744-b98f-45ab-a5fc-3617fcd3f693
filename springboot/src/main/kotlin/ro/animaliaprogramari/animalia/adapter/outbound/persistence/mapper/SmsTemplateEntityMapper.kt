package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsTemplate as SmsTemplateEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsTemplateType as SmsTemplateTypeEntity
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsTemplate as SmsTemplateDomain
import ro.animaliaprogramari.animalia.domain.model.SmsTemplateId
import ro.animaliaprogramari.animalia.domain.model.SmsTemplateType as SmsTemplateTypeDomain

/**
 * Mapper between SMS template domain model and JPA entity
 */
@Component
class SmsTemplateEntityMapper {

    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: SmsTemplateDomain): SmsTemplateEntity {
        return SmsTemplateEntity(
            id = domain.id.value,
            salonId = domain.salonId.value,
            templateType = toEntityType(domain.templateType),
            templateContent = domain.templateContent,
            isActive = domain.isActive,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: SmsTemplateEntity): SmsTemplateDomain {
        return SmsTemplateDomain(
            id = SmsTemplateId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            templateType = toDomainType(entity.templateType),
            templateContent = entity.templateContent,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    /**
     * Convert domain template type to entity template type
     */
    fun toEntityType(domainType: SmsTemplateTypeDomain): SmsTemplateTypeEntity {
        return when (domainType) {
            SmsTemplateTypeDomain.APPOINTMENT_CONFIRMATION -> SmsTemplateTypeEntity.APPOINTMENT_CONFIRMATION
            SmsTemplateTypeDomain.APPOINTMENT_CANCELLATION -> SmsTemplateTypeEntity.APPOINTMENT_CANCELLATION
            SmsTemplateTypeDomain.APPOINTMENT_RESCHEDULE -> SmsTemplateTypeEntity.APPOINTMENT_RESCHEDULE
            SmsTemplateTypeDomain.REMINDER -> SmsTemplateTypeEntity.REMINDER
            SmsTemplateTypeDomain.APPOINTMENT_COMPLETION -> SmsTemplateTypeEntity.APPOINTMENT_COMPLETION
            SmsTemplateTypeDomain.FOLLOW_UP -> SmsTemplateTypeEntity.FOLLOW_UP
//            SmsTemplateTypeDomain.BIRTHDAY -> SmsTemplateTypeEntity.BIRTHDAY
        }
    }

    /**
     * Convert entity template type to domain template type
     */
    fun toDomainType(entityType: SmsTemplateTypeEntity): SmsTemplateTypeDomain {
        return when (entityType) {
            SmsTemplateTypeEntity.APPOINTMENT_CONFIRMATION -> SmsTemplateTypeDomain.APPOINTMENT_CONFIRMATION
            SmsTemplateTypeEntity.APPOINTMENT_CANCELLATION -> SmsTemplateTypeDomain.APPOINTMENT_CANCELLATION
            SmsTemplateTypeEntity.APPOINTMENT_RESCHEDULE -> SmsTemplateTypeDomain.APPOINTMENT_RESCHEDULE
            SmsTemplateTypeEntity.REMINDER -> SmsTemplateTypeDomain.REMINDER
            SmsTemplateTypeEntity.APPOINTMENT_COMPLETION -> SmsTemplateTypeDomain.APPOINTMENT_COMPLETION
            SmsTemplateTypeEntity.FOLLOW_UP -> SmsTemplateTypeDomain.FOLLOW_UP
//            SmsTemplateTypeEntity.BIRTHDAY -> SmsTemplateTypeDomain.BIRTHDAY
        }
    }
}
