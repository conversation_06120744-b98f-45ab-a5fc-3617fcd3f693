package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.RevenueCatWebhookEvent

/**
 * Spring Data JPA repository for RevenueCat webhook events
 */
@Repository
interface SpringRevenueCatWebhookEventRepository : JpaRepository<RevenueCatWebhookEvent, String> {
    
    /**
     * Check if event has already been processed
     */
    fun existsByEventId(eventId: String): <PERSON><PERSON>an
}
