package ro.animaliaprogramari.animalia.adapter.inbound.rest

import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.usecase.AppointmentSubscriptionUseCase
import ro.animaliaprogramari.animalia.application.usecase.SubscriptionModificationType
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.SalonAppointmentDtoMapper
import java.time.LocalTime

/**
 * REST controller for appointment subscription management
 */
@RestController
@RequestMapping("/salons/{salonId}/appointment-subscriptions")
@CrossOrigin(origins = ["*"])
class AppointmentSubscriptionController(
    private val appointmentSubscriptionUseCase: AppointmentSubscriptionUseCase,
    private val appointmentDtoMapper: SalonAppointmentDtoMapper
) {

    private val logger = LoggerFactory.getLogger(AppointmentSubscriptionController::class.java)

    /**
     * Get all subscriptions for a client
     */
    @GetMapping("/client/{clientId}")
    fun getClientSubscriptions(
        @PathVariable salonId: String,
        @PathVariable clientId: String
    ): ResponseEntity<List<AppointmentSubscriptionDto>> {
        return try {
            val subscriptions = appointmentSubscriptionUseCase.getClientSubscriptions(
                ClientId.of(clientId),
                SalonId.of(salonId)
            )
            ResponseEntity.ok(subscriptions.map { AppointmentSubscriptionDto.fromDomain(it) })
        } catch (e: Exception) {
            logger.error("Failed to get client subscriptions", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * Get subscription by ID
     */
    @GetMapping("/{subscriptionId}")
    fun getSubscription(
        @PathVariable salonId: String,
        @PathVariable subscriptionId: String
    ): ResponseEntity<AppointmentSubscriptionDto> {
        return try {
            val subscription = appointmentSubscriptionUseCase.getSubscription(
                AppointmentSubscriptionId.of(subscriptionId),
                SalonId.of(salonId)
            )
            if (subscription != null) {
                ResponseEntity.ok(AppointmentSubscriptionDto.fromDomain(subscription))
            } else {
                ResponseEntity.notFound().build()
            }
        } catch (e: Exception) {
            logger.error("Failed to get subscription", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * Update subscription
     */
    @PutMapping("/{subscriptionId}")
    fun updateSubscription(
        @PathVariable salonId: String,
        @PathVariable subscriptionId: String,
        @RequestBody request: UpdateAppointmentSubscriptionRequest
    ): ResponseEntity<AppointmentSubscriptionDto> {
        return try {
            val command = UpdateAppointmentSubscriptionCommand(
                subscriptionId = AppointmentSubscriptionId.of(subscriptionId),
                salonId = SalonId.of(salonId),
                newStartTime = request.newStartTime,
                newEndTime = request.newEndTime,
                newTotalRepetitions = request.newTotalRepetitions,
                newServiceIds = request.newServiceIds?.map { ServiceId.of(it) },
                newNotes = request.newNotes
            )

            val updatedSubscription = appointmentSubscriptionUseCase.updateSubscription(command)
            ResponseEntity.ok(AppointmentSubscriptionDto.fromDomain(updatedSubscription))
        } catch (e: Exception) {
            logger.error("Failed to update subscription", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * Cancel subscription
     */
    @DeleteMapping("/{subscriptionId}")
    fun cancelSubscription(
        @PathVariable salonId: String,
        @PathVariable subscriptionId: String,
        @RequestParam(defaultValue = "true") cancelFutureAppointments: Boolean
    ): ResponseEntity<AppointmentSubscriptionDto> {
        return try {
            val command = CancelAppointmentSubscriptionCommand(
                subscriptionId = AppointmentSubscriptionId.of(subscriptionId),
                salonId = SalonId.of(salonId),
                cancelFutureAppointments = cancelFutureAppointments
            )

            val cancelledSubscription = appointmentSubscriptionUseCase.cancelSubscription(command)
            ResponseEntity.ok(AppointmentSubscriptionDto.fromDomain(cancelledSubscription))
        } catch (e: Exception) {
            logger.error("Failed to cancel subscription", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * Pause subscription
     */
    @PostMapping("/{subscriptionId}/pause")
    fun pauseSubscription(
        @PathVariable salonId: String,
        @PathVariable subscriptionId: String
    ): ResponseEntity<AppointmentSubscriptionDto> {
        return try {
            val command = PauseAppointmentSubscriptionCommand(
                subscriptionId = AppointmentSubscriptionId.of(subscriptionId),
                salonId = SalonId.of(salonId)
            )

            val pausedSubscription = appointmentSubscriptionUseCase.pauseSubscription(command)
            ResponseEntity.ok(AppointmentSubscriptionDto.fromDomain(pausedSubscription))
        } catch (e: Exception) {
            logger.error("Failed to pause subscription", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * Resume subscription
     */
    @PostMapping("/{subscriptionId}/resume")
    fun resumeSubscription(
        @PathVariable salonId: String,
        @PathVariable subscriptionId: String
    ): ResponseEntity<AppointmentSubscriptionDto> {
        return try {
            val command = ResumeAppointmentSubscriptionCommand(
                subscriptionId = AppointmentSubscriptionId.of(subscriptionId),
                salonId = SalonId.of(salonId)
            )

            val resumedSubscription = appointmentSubscriptionUseCase.resumeSubscription(command)
            ResponseEntity.ok(AppointmentSubscriptionDto.fromDomain(resumedSubscription))
        } catch (e: Exception) {
            logger.error("Failed to resume subscription", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * Get appointments that will be affected by subscription modification
     */
    @GetMapping("/{subscriptionId}/affected-appointments")
    fun getAffectedAppointments(
        @PathVariable salonId: String,
        @PathVariable subscriptionId: String,
        @RequestParam modificationType: String
    ): ResponseEntity<List<AppointmentResponse>> {
        return try {
            val modType = SubscriptionModificationType.valueOf(modificationType.uppercase())
            val appointments = appointmentSubscriptionUseCase.getAffectedAppointments(
                AppointmentSubscriptionId.of(subscriptionId),
                modType
            )
            ResponseEntity.ok(appointments.map { appointmentDtoMapper.toResponse(it) })
        } catch (e: Exception) {
            logger.error("Failed to get affected appointments", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * Get all appointments for a subscription
     */
    @GetMapping("/{subscriptionId}/appointments")
    fun getSubscriptionAppointments(
        @PathVariable salonId: String,
        @PathVariable subscriptionId: String
    ): ResponseEntity<List<AppointmentResponse>> {
        return try {
            val appointments = appointmentSubscriptionUseCase.getSubscriptionAppointments(
                AppointmentSubscriptionId.of(subscriptionId),
                SalonId.of(salonId)
            )
            ResponseEntity.ok(appointments.map { appointmentDtoMapper.toResponse(it) })
        } catch (e: Exception) {
            logger.error("Failed to get subscription appointments", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }
}



/**
 * Request DTO for updating appointment subscriptions
 */
data class UpdateAppointmentSubscriptionRequest(
    val newStartTime: LocalTime? = null,
    val newEndTime: LocalTime? = null,
    val newTotalRepetitions: Int? = null,
    val newServiceIds: List<String>? = null,
    val newNotes: String? = null
)
