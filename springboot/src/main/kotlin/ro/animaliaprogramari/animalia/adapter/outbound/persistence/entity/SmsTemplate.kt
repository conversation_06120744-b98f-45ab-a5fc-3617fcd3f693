package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

/**
 * JPA entity for SMS templates
 */
@Entity
@Table(
    name = "sms_templates",
    indexes = [
        Index(name = "idx_sms_templates_salon_id", columnList = "salon_id"),
        Index(name = "idx_sms_templates_type", columnList = "template_type")
    ],
    uniqueConstraints = [
        UniqueConstraint(name = "uk_salon_template_type", columnNames = ["salon_id", "template_type"])
    ]
)
data class SmsTemplate(
    @Id
    @Column(name = "id", nullable = false)
    val id: String,

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @Enumerated(EnumType.STRING)
    @Column(name = "template_type", nullable = false)
    val templateType: SmsTemplateType,

    @field:NotBlank(message = "Template content is required")
    @Column(name = "template_content", nullable = false, columnDefinition = "TEXT")
    val templateContent: String,

    @Column(name = "is_active", nullable = false)
    val isActive: Boolean = true,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        salonId = "",
        templateType = SmsTemplateType.APPOINTMENT_CONFIRMATION,
        templateContent = "",
        isActive = true,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as SmsTemplate
        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}

/**
 * Enum for SMS template types
 */
enum class SmsTemplateType(val displayName: String) {
    APPOINTMENT_CONFIRMATION("Confirmare programare"),
    APPOINTMENT_CANCELLATION("Anulare programare"),
    APPOINTMENT_RESCHEDULE("Reprogramare"),
    REMINDER("Reminder"),
    APPOINTMENT_COMPLETION("Finalizare programare"),
    FOLLOW_UP("Mesaj de urmărire");
//    BIRTHDAY("Zi de naștere");

    companion object {
        fun fromString(value: String): SmsTemplateType {
            return entries.find { it.name.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Unknown SMS template type: $value")
        }
    }
}
