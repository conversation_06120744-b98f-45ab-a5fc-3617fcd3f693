package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.BlockTimeDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.BlockTimeManagementUseCase
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for salon block time management
 * Implements the block time API contract for salon-specific operations
 */
@RestController
@RequestMapping("/salons/{salonId}/block-time")
@Tag(name = "Block Time Management", description = "Operations for managing blocked time slots")
class SalonBlockTimeController(
    private val blockTimeManagementUseCase: BlockTimeManagementUseCase,
    private val blockTimeDtoMapper: BlockTimeDtoMapper,
) {
    private val logger = LoggerFactory.getLogger(SalonBlockTimeController::class.java)

    /**
     * POST /salons/{salonId}/block-time
     * Create a new block time
     */
    @PostMapping
    @Operation(
        summary = "Create block time",
        description = "Creates a new blocked time slot for specified staff members",
    )
    @SwaggerApiResponse(responseCode = "201", description = "Block time created successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun createBlockTime(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @Valid @RequestBody request: CreateBlockTimeRequest,
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
        logger.info("Creating block time for salon: $salonId")

        val command =
            blockTimeDtoMapper.toCreateCommand(
                request = request,
                salonId = SalonId.of(salonId),
                createdBy = getCurrentUserId(), // This would come from security context
            )

        val result = blockTimeManagementUseCase.createBlockTime(command)

        val responseData: Map<String, Any> =
            mapOf(
                "blockId" to result.blockTime.id.value,
                "salonId" to salonId,
                "startTime" to result.blockTime.startTime,
                "endTime" to result.blockTime.endTime,
                "reason" to result.blockTime.reason.displayName,
                "customReason" to (result.blockTime.customReason ?: ""),
                "staffIds" to result.blockTime.staffIds.map { it.value },
                "createdBy" to result.blockTime.createdBy.value,
                "createdAt" to result.blockTime.createdAt,
                "isRecurring" to result.blockTime.isRecurring,
                "recurrencePattern" to (result.blockTime.recurrencePattern?.toString() ?: ""),
                "notes" to (result.blockTime.notes ?: ""),
                "status" to result.blockTime.status.displayName,
                "affectedAppointments" to
                    result.affectedAppointments.map { appointment ->
                        mapOf(
                            "appointmentId" to appointment.appointmentId,
                            "clientName" to appointment.clientName,
                            "conflictType" to appointment.conflictType,
                            "suggestedAction" to appointment.suggestedAction,
                        )
                    },
            )

        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(responseData, result.message))
    }

    /**
     * GET api/salons/{salonId}/block-time
     * Get block time list with filtering
     */
    @GetMapping
    @Operation(
        summary = "Get block time list",
        description = "Retrieves all blocked time slots for a salon with optional filtering",
    )
    fun getBlockTimeList(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @RequestParam(required = false) startDate: LocalDate?,
        @RequestParam(required = false) endDate: LocalDate?,
        @RequestParam(required = false) staffId: String?,
        @RequestParam(required = false) reason: String?,
        @RequestParam(required = false, defaultValue = "Active") status: String?,
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "50") limit: Int,
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
//        logger.info("Getting block time list for salon: $salonId")

        val query =
            GetBlockTimeListQuery(
                salonId = SalonId.of(salonId),
                startDate = startDate,
                endDate = endDate,
                staffId = staffId?.let { StaffId.of(it) },
                reason = reason?.let { BlockReason.fromString(it) },
                status = status?.let { BlockTimeStatus.fromString(it) },
                page = page,
                limit = limit,
            )

        val result = blockTimeManagementUseCase.getBlockTimeList(query)
        val response = blockTimeDtoMapper.toListResponse(result)

        val responseData: Map<String, Any> =
            mapOf(
                "blocks" to response.blocks,
                "pagination" to response.pagination,
                "summary" to response.summary,
            )

        return ResponseEntity.ok(ApiResponse.success(responseData))
    }

    /**
     * GET /salons/{salonId}/block-time/{blockId}
     * Get block time details
     */
    @GetMapping("/{blockId}")
    @Operation(
        summary = "Get block time details",
        description = "Retrieves detailed information about a specific blocked time slot",
    )
    fun getBlockTimeDetails(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @Parameter(description = "Block time ID") @PathVariable blockId: String,
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
        logger.info("Getting block time details: $blockId")

        val query =
            GetBlockTimeDetailsQuery(
                salonId = SalonId.of(salonId),
                blockId = BlockTimeId.of(blockId),
            )

        val details =
            blockTimeManagementUseCase.getBlockTimeDetails(query)
                ?: return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Block time not found"))

        val response = blockTimeDtoMapper.toDetailsResponse(details)

        val responseData: Map<String, Any> =
            mapOf(
                "blockId" to details.blockTime.id.value,
                "salonId" to salonId,
                "startTime" to details.blockTime.startTime,
                "endTime" to details.blockTime.endTime,
                "duration" to details.blockTime.getDurationMinutes(),
                "reason" to details.blockTime.reason.displayName,
                "customReason" to (details.blockTime.customReason ?: ""),
                "staffIds" to details.blockTime.staffIds.map { it.value },
                "staffDetails" to response.staffDetails,
                "createdBy" to details.blockTime.createdBy.value,
                "createdByName" to details.createdByName,
                "createdAt" to details.blockTime.createdAt,
                "updatedAt" to details.blockTime.updatedAt,
                "isRecurring" to details.blockTime.isRecurring,
                "recurrencePattern" to (details.blockTime.recurrencePattern?.toString() ?: ""),
                "notes" to (details.blockTime.notes ?: ""),
                "status" to details.blockTime.status.displayName,
                "affectedAppointments" to response.affectedAppointments,
                "history" to response.history,
            )

        return ResponseEntity.ok(ApiResponse.success(responseData))
    }

    /**
     * PUT /salons/{salonId}/block-time/{blockId}
     * Update block time
     */
    @PutMapping("/{blockId}")
    @Operation(summary = "Update block time", description = "Updates an existing blocked time slot")
    fun updateBlockTime(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @Parameter(description = "Block time ID") @PathVariable blockId: String,
        @Valid @RequestBody request: UpdateBlockTimeRequest,
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
        logger.info("Updating block time: $blockId")

        val command =
            blockTimeDtoMapper.toUpdateCommand(
                request = request,
                salonId = SalonId.of(salonId),
                blockId = BlockTimeId.of(blockId),
                updatedBy = getCurrentUserId(),
            )

        val result = blockTimeManagementUseCase.updateBlockTime(command)

        val responseData: Map<String, Any> =
            mapOf(
                "blockId" to result.blockTime.id.value,
                "salonId" to salonId,
                "startTime" to result.blockTime.startTime,
                "endTime" to result.blockTime.endTime,
                "reason" to result.blockTime.reason.displayName,
                "customReason" to (result.blockTime.customReason ?: ""),
                "staffIds" to result.blockTime.staffIds.map { it.value },
                "updatedBy" to (result.blockTime.updatedBy?.value ?: ""),
                "updatedAt" to result.blockTime.updatedAt,
                "status" to result.blockTime.status.displayName,
                "changes" to
                    result.changes.map { change ->
                        mapOf(
                            "field" to change.field,
                            "oldValue" to change.oldValue,
                            "newValue" to change.newValue,
                        )
                    },
            )

        return ResponseEntity.ok(ApiResponse.success(responseData, result.message))
    }

    /**
     * DELETE /salons/{salonId}/block-time/{blockId}
     * Delete block time
     */
    @DeleteMapping("/{blockId}")
    @Operation(summary = "Delete block time", description = "Deletes (cancels) a blocked time slot")
    fun deleteBlockTime(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @Parameter(description = "Block time ID") @PathVariable blockId: String,
        @RequestParam(required = false) reason: String?,
        @RequestParam(defaultValue = "true") notifyStaff: Boolean,
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
        logger.info("Deleting block time: $blockId")

        val command =
            DeleteBlockTimeCommand(
                salonId = SalonId.of(salonId),
                blockId = BlockTimeId.of(blockId),
                reason = reason,
                notifyStaff = notifyStaff,
                deletedBy = getCurrentUserId(),
            )

        val result = blockTimeManagementUseCase.deleteBlockTime(command)

        val responseData: Map<String, Any> =
            mapOf(
                "blockId" to result.blockId,
                "status" to result.status,
                "cancelledBy" to result.cancelledBy,
                "cancelledAt" to result.cancelledAt,
                "cancellationReason" to (result.cancellationReason ?: ""),
                "affectedStaff" to
                    result.affectedStaff.map { staff ->
                        mapOf(
                            "staffId" to staff.staffId,
                            "staffName" to staff.staffName,
                            "notified" to staff.notified,
                        )
                    },
            )

        return ResponseEntity.ok(ApiResponse.success(responseData, result.message))
    }

    /**
     * POST /salons/{salonId}/block-time/bulk
     * Bulk block time operations
     */
    @PostMapping("/bulk")
    @Operation(
        summary = "Bulk block time operations",
        description = "Performs bulk operations on multiple block time slots",
    )
    fun bulkBlockTimeOperations(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @Valid @RequestBody request: BulkBlockTimeOperationsRequest,
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
        logger.info("Performing bulk ${request.operation} operation for salon: $salonId")

        val command =
            blockTimeDtoMapper.toBulkCommand(
                request = request,
                salonId = SalonId.of(salonId),
                performedBy = getCurrentUserId(),
            )

        val result = blockTimeManagementUseCase.bulkBlockTimeOperations(command)

        val responseData =
            mapOf(
                "operation" to result.operation,
                "totalRequested" to result.totalRequested,
                "successful" to result.successful,
                "failed" to result.failed,
                "results" to
                    result.results.map { itemResult ->
                        mapOf(
                            "index" to itemResult.index,
                            "success" to itemResult.success,
                            "blockId" to itemResult.blockId,
                            "message" to itemResult.message,
                            "error" to itemResult.error,
                        )
                    },
            )

        return ResponseEntity.ok(ApiResponse.success(responseData, result.message))
    }

    /**
     * POST /salons/{salonId}/block-time/check-availability
     * Check time availability
     */
    @PostMapping("/check-availability")
    @Operation(summary = "Check time availability", description = "Checks if a time slot is available for blocking")
    fun checkTimeAvailability(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @Valid @RequestBody request: CheckTimeAvailabilityRequest,
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
        logger.info("Checking time availability for salon: $salonId")

        val query =
            blockTimeDtoMapper.toAvailabilityQuery(
                request = request,
                salonId = SalonId.of(salonId),
            )

        val result = blockTimeManagementUseCase.checkTimeAvailability(query)
        val responseData = blockTimeDtoMapper.toAvailabilityResponse(result)

        return ResponseEntity.ok(ApiResponse.success(responseData))
    }

    /**
     * GET /salons/{salonId}/block-time/statistics
     * Get block time statistics
     */
    @GetMapping("/statistics")
    @Operation(summary = "Get block time statistics", description = "Retrieves statistics about blocked time usage")
    fun getBlockTimeStatistics(
        @Parameter(description = "Salon ID") @PathVariable salonId: String,
        @RequestParam(required = false) startDate: LocalDate?,
        @RequestParam(required = false) endDate: LocalDate?,
        @RequestParam(required = false) staffId: String?,
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
        logger.info("Getting block time statistics for salon: $salonId")

        val query =
            GetBlockTimeStatisticsQuery(
                salonId = SalonId.of(salonId),
                startDate = startDate,
                endDate = endDate,
                staffId = staffId?.let { StaffId.of(it) },
            )

        val statistics = blockTimeManagementUseCase.getBlockTimeStatistics(query)

        val responseData =
            mapOf(
                "period" to
                    mapOf(
                        "startDate" to statistics.period.startDate,
                        "endDate" to statistics.period.endDate,
                        "totalDays" to statistics.period.totalDays,
                    ),
                "summary" to
                    mapOf(
                        "totalBlocks" to statistics.summary.totalBlocks,
                        "totalHoursBlocked" to statistics.summary.totalHoursBlocked,
                        "averageBlockDuration" to statistics.summary.averageBlockDuration,
                        "mostActiveDay" to statistics.summary.mostActiveDay,
                        "leastActiveDay" to statistics.summary.leastActiveDay,
                    ),
                "byReason" to
                    statistics.byReason.map { reasonStat ->
                        mapOf(
                            "reason" to reasonStat.reason,
                            "count" to reasonStat.count,
                            "totalHours" to reasonStat.totalHours,
                            "percentage" to reasonStat.percentage,
                        )
                    },
                "byStaff" to
                    statistics.byStaff.map { staffStat ->
                        mapOf(
                            "staffId" to staffStat.staffId,
                            "staffName" to staffStat.staffName,
                            "totalBlocks" to staffStat.totalBlocks,
                            "totalHours" to staffStat.totalHours,
                            "averageDuration" to staffStat.averageDuration,
                            "mostCommonReason" to staffStat.mostCommonReason,
                        )
                    },
                "trends" to
                    mapOf(
                        "weeklyPattern" to
                            statistics.trends.weeklyPattern.map { weeklyEntry ->
                                mapOf(
                                    "day" to weeklyEntry.day,
                                    "averageBlocks" to weeklyEntry.averageBlocks,
                                    "averageHours" to weeklyEntry.averageHours,
                                )
                            },
                        "hourlyPattern" to
                            statistics.trends.hourlyPattern.map { hourlyEntry ->
                                mapOf(
                                    "hour" to hourlyEntry.hour,
                                    "averageBlocks" to hourlyEntry.averageBlocks,
                                )
                            },
                    ),
            )

        return ResponseEntity.ok(ApiResponse.success(responseData))
    }

    // Helper method to get current user ID (would be implemented with security context)
    private fun getCurrentUserId(): UserId {
        return SecurityUtils.getCurrentUser()?.userId
            ?: throw IllegalStateException("No authenticated user found")
    }
}
