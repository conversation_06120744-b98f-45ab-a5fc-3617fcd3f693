package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.micrometer.core.annotation.Timed
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.domain.service.QueryPerformanceMonitor
import ro.animaliaprogramari.animalia.domain.service.QueryStatistics

/**
 * REST controller for monitoring database query performance
 * Provides endpoints to track and analyze query execution times
 */
@RestController
@RequestMapping("/performance")
@PreAuthorize("hasRole('CHIEF_GROOMER')")
class PerformanceMonitoringController(
    private val performanceMonitor: QueryPerformanceMonitor,
) {

    /**
     * Get performance statistics for all queries
     */
    @GetMapping("/queries")
    @Timed(value = "api.performance.queries", description = "Get query performance statistics")
    fun getQueryStatistics(): ResponseEntity<PerformanceOverviewDto> {
        val slowQueries = performanceMonitor.getSlowQueries()

        return ResponseEntity.ok(
            PerformanceOverviewDto(
                totalSlowQueries = slowQueries.size,
                slowQueries = slowQueries.map { it.toDto() },
                performanceTarget = PerformanceTargetDto(
                    targetResponseTimeMs = 100,
                    verySlowThresholdMs = 500,
                    recommendations = generateRecommendations(slowQueries)
                )
            )
        )
    }

    /**
     * Get performance statistics for a specific query
     */
    @GetMapping("/queries/{queryName}")
    @Timed(value = "api.performance.query.detail", description = "Get specific query performance")
    fun getQueryStatistics(
        @PathVariable queryName: String,
    ): ResponseEntity<QueryStatisticsDto> {
        val statistics = performanceMonitor.getQueryStatistics(queryName)

        return if (statistics != null) {
            ResponseEntity.ok(statistics.toDto())
        } else {
            ResponseEntity.notFound().build()
        }
    }

    /**
     * Test endpoint to measure conflict detection performance
     */
    @PostMapping("/test/conflict-detection")
    @Timed(value = "api.performance.test.conflict", description = "Test conflict detection performance")
    fun testConflictDetectionPerformance(
        @RequestBody request: ConflictDetectionTestRequest,
    ): ResponseEntity<PerformanceTestResultDto> {
        val startTime = System.currentTimeMillis()

        // This would call the optimized conflict detection service
        // For now, we'll simulate the test
        val executionTime = System.currentTimeMillis() - startTime

        return ResponseEntity.ok(
            PerformanceTestResultDto(
                testType = "conflict_detection",
                executionTimeMs = executionTime,
                targetTimeMs = 100,
                passed = executionTime <= 100,
                details = mapOf(
                    "staffId" to request.staffId,
                    "date" to request.date.toString(),
                    "timeSlot" to "${request.startTime}-${request.endTime}"
                )
            )
        )
    }

    /**
     * Test endpoint to measure appointment suggestion performance
     */
    @PostMapping("/test/suggestions")
    @Timed(value = "api.performance.test.suggestions", description = "Test suggestion performance")
    fun testSuggestionPerformance(
        @RequestBody request: SuggestionTestRequest,
    ): ResponseEntity<PerformanceTestResultDto> {
        val startTime = System.currentTimeMillis()

        // This would call the optimized suggestion service
        // For now, we'll simulate the test
        val executionTime = System.currentTimeMillis() - startTime

        return ResponseEntity.ok(
            PerformanceTestResultDto(
                testType = "appointment_suggestions",
                executionTimeMs = executionTime,
                targetTimeMs = 100,
                passed = executionTime <= 100,
                details = mapOf(
                    "salonId" to request.salonId,
                    "staffCount" to request.staffIds.size.toString(),
                    "limit" to request.limit.toString()
                )
            )
        )
    }

    /**
     * Get performance recommendations based on current metrics
     */
    @GetMapping("/recommendations")
    @Timed(value = "api.performance.recommendations", description = "Get performance recommendations")
    fun getPerformanceRecommendations(): ResponseEntity<List<PerformanceRecommendationDto>> {
        val slowQueries = performanceMonitor.getSlowQueries()
        val recommendations = generateRecommendations(slowQueries)

        return ResponseEntity.ok(recommendations)
    }

    private fun generateRecommendations(slowQueries: List<QueryStatistics>): List<PerformanceRecommendationDto> {
        val recommendations = mutableListOf<PerformanceRecommendationDto>()

        slowQueries.forEach { query ->
            when {
                query.isVerySlowQuery -> {
                    recommendations.add(
                        PerformanceRecommendationDto(
                            priority = "HIGH",
                            queryName = query.queryName,
                            issue = "Very slow query (${query.averageTimeMs.toInt()}ms avg)",
                            recommendation = "Immediate optimization required - add database indexes or rewrite query",
                            impact = "Critical performance impact on user experience"
                        )
                    )
                }
                query.isSlowQuery -> {
                    recommendations.add(
                        PerformanceRecommendationDto(
                            priority = "MEDIUM",
                            queryName = query.queryName,
                            issue = "Slow query (${query.averageTimeMs.toInt()}ms avg)",
                            recommendation = "Consider adding indexes or optimizing query logic",
                            impact = "Moderate performance impact"
                        )
                    )
                }
            }
        }

        // Add general recommendations
        if (slowQueries.isNotEmpty()) {
            recommendations.add(
                PerformanceRecommendationDto(
                    priority = "INFO",
                    queryName = "general",
                    issue = "${slowQueries.size} slow queries detected",
                    recommendation = "Review database indexes and query patterns",
                    impact = "Overall application performance"
                )
            )
        }

        return recommendations
    }
}

// DTOs for performance monitoring

data class PerformanceOverviewDto(
    val totalSlowQueries: Int,
    val slowQueries: List<QueryStatisticsDto>,
    val performanceTarget: PerformanceTargetDto,
)

data class QueryStatisticsDto(
    val queryName: String,
    val count: Long,
    val totalTimeMs: Long,
    val averageTimeMs: Double,
    val maxTimeMs: Double,
    val isSlowQuery: Boolean,
    val isVerySlowQuery: Boolean,
)

data class PerformanceTargetDto(
    val targetResponseTimeMs: Int,
    val verySlowThresholdMs: Int,
    val recommendations: List<PerformanceRecommendationDto>,
)

data class PerformanceRecommendationDto(
    val priority: String, // HIGH, MEDIUM, LOW, INFO
    val queryName: String,
    val issue: String,
    val recommendation: String,
    val impact: String,
)

data class PerformanceTestResultDto(
    val testType: String,
    val executionTimeMs: Long,
    val targetTimeMs: Int,
    val passed: Boolean,
    val details: Map<String, String>,
)

data class ConflictDetectionTestRequest(
    val staffId: String,
    val date: java.time.LocalDate,
    val startTime: java.time.LocalTime,
    val endTime: java.time.LocalTime,
)

data class SuggestionTestRequest(
    val salonId: String,
    val staffIds: List<String>,
    val limit: Int = 10,
)

// Extension functions for mapping
private fun QueryStatistics.toDto(): QueryStatisticsDto {
    return QueryStatisticsDto(
        queryName = this.queryName,
        count = this.count,
        totalTimeMs = this.totalTimeMs,
        averageTimeMs = this.averageTimeMs,
        maxTimeMs = this.maxTimeMs,
        isSlowQuery = this.isSlowQuery,
        isVerySlowQuery = this.isVerySlowQuery,
    )
}
