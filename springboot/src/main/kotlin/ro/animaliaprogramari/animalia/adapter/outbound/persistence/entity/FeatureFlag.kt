package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

/**
 * JPA entity for feature flags
 * Allows dynamic control of application features via database configuration
 */
@Entity
@Table(
    name = "feature_flags",
    indexes = [
        Index(name = "idx_feature_flags_name", columnList = "flag_name")
    ]
)
data class FeatureFlag(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @field:NotBlank(message = "Flag name is required")
    @field:Size(max = 100, message = "Flag name must not exceed 100 characters")
    @Column(name = "flag_name", nullable = false, unique = true, length = 100)
    var flagName: String,

    @Column(name = "flag_value", nullable = false)
    var flagValue: Boolean = false,

    @Column(name = "description", columnDefinition = "TEXT")
    var description: String? = null,

    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    /**
     * Update the flag value and timestamp
     */
    fun updateValue(newValue: Boolean): FeatureFlag {
        this.flagValue = newValue
        this.updatedAt = LocalDateTime.now()
        return this
    }

    override fun toString(): String {
        return "FeatureFlag(id=$id, flagName='$flagName', flagValue=$flagValue, description='$description')"
    }
}
