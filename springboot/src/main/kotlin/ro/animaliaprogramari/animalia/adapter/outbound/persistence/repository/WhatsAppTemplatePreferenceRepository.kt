package ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.WhatsAppTemplatePreference
import java.util.*

/**
 * JPA Repository for WhatsApp template preferences
 */
@Repository
interface WhatsAppTemplatePreferenceRepository : JpaRepository<WhatsAppTemplatePreference, String> {

    /**
     * Find all preferences for a specific salon
     */
    fun findBySalonId(salonId: String): List<WhatsAppTemplatePreference>

    /**
     * Find preference for a specific salon and message type
     */
    fun findBySalonIdAndMessageType(salonId: String, messageType: SmsMessageType): Optional<WhatsAppTemplatePreference>

    /**
     * Check if salon has preference for specific message type
     */
    fun existsBySalonIdAndMessageType(salonId: String, messageType: SmsMessageType): Boolean

    /**
     * Delete all preferences for a salon
     */
    fun deleteBySalonId(salonId: String)

    /**
     * Find all preferences for a specific message type
     */
    fun findByMessageType(messageType: SmsMessageType): List<WhatsAppTemplatePreference>

    /**
     * Get content SID for salon and message type
     */
    @Query("SELECT w.contentSid FROM WhatsAppTemplatePreference w WHERE w.salonId = :salonId AND w.messageType = :messageType")
    fun findContentSidBySalonIdAndMessageType(@Param("salonId") salonId: String, @Param("messageType") messageType: SmsMessageType): Optional<String>
}
