package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

/**
 * JPA entity for SMS quota
 */
@Entity
@Table(
    name = "sms_quota",
    indexes = [
        Index(name = "idx_sms_quota_salon_id", columnList = "salon_id"),
    ]
)
data class SmsQuota(
    @Id
    @Column(name = "id", nullable = false)
    val id: String,

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false, unique = true)
    val salonId: String,

    @field:Min(value = 0, message = "Total quota must be non-negative")
    @Column(name = "total_quota", nullable = false)
    val totalQuota: Int,

    @field:Min(value = 0, message = "Used quota must be non-negative")
    @Column(name = "used_quota", nullable = false)
    val usedQuota: Int = 0,

    @Enumerated(EnumType.STRING)
    @Column(name = "quota_period", nullable = false)
    val quotaPeriod: QuotaPeriod,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        salonId = "",
        totalQuota = 30,
        usedQuota = 0,
        quotaPeriod = QuotaPeriod.MONTHLY,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as SmsQuota
        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}

/**
 * Enum for quota reset periods
 */
enum class QuotaPeriod {
    DAILY,
    WEEKLY,
    MONTHLY
}
