package ro.animaliaprogramari.animalia.adapter.inbound.event

import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.MonitoringService
import ro.animaliaprogramari.animalia.domain.event.ClientRegisteredEvent
import ro.animaliaprogramari.animalia.domain.event.DomainEvent
import ro.animaliaprogramari.animalia.domain.event.PetAddedEvent
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentCancelledEvent
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentCompletedEvent
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentRescheduledEvent
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentScheduledEvent

/**
 * Listener for domain events that records metrics using the monitoring service
 * This is an inbound adapter that listens for domain events published by the application
 */
@Component
class DomainEventMonitoringListener(
    private val monitoringService: MonitoringService,
) {
    private val logger = LoggerFactory.getLogger(DomainEventMonitoringListener::class.java)

    /**
     * Listen for all domain events and record them
     */
    @EventListener
    fun handleDomainEvent(event: DomainEvent) {
        try {
            // Record general event metrics
            val eventType = event::class.simpleName ?: "UnknownEvent"
            val tags =
                mapOf(
                    "eventType" to eventType,
                    "aggregateId" to event.aggregateId,
                )

            // Increment counter for all domain events
            monitoringService.incrementCounter("domain.events.total", tags)

            // Record event-specific metrics
            when (event) {
                is AppointmentScheduledEvent -> handleAppointmentScheduled(event)
                is AppointmentCancelledEvent -> handleAppointmentCancelled(event)
                is AppointmentCompletedEvent -> handleAppointmentCompleted(event)
                is AppointmentRescheduledEvent -> handleAppointmentRescheduled(event)
                is ClientRegisteredEvent -> handleClientRegistered(event)
                is PetAddedEvent -> handlePetAdded(event)
                else -> {
                    // For other event types, just record the occurrence
                    val details =
                        mapOf(
                            "eventId" to event.eventId,
                            "aggregateId" to event.aggregateId,
                            "occurredAt" to event.occurredAt.toString(),
                        )
                    monitoringService.recordEvent(eventType, details)
                }
            }

            logger.info("Recorded metrics for domain event: $eventType")
        } catch (e: Exception) {
            logger.error("Failed to record metrics for domain event", e)
        }
    }

    private fun handleAppointmentScheduled(event: AppointmentScheduledEvent) {
        // Record appointment scheduled metrics
        val tags =
            mapOf(
                "groomerId" to event.userId.value,
                "appointmentDate" to event.appointmentDate.toString(),
            )

        monitoringService.incrementCounter("domain.events.appointment.scheduled", tags)

        // Record appointment price as a metric
        monitoringService.recordMetric(
            "appointment.price",
            event.totalPrice.amount.toDouble(),
            mapOf("groomerId" to event.userId.value),
        )

        // Record detailed event information
        val details =
            mapOf(
                "appointmentId" to event.appointmentId.value,
                "clientId" to event.clientId.value,
                "petId" to event.petId.value,
                "groomerId" to event.userId.value,
                "appointmentDate" to event.appointmentDate.toString(),
                "startTime" to event.startTime.toString(),
                "endTime" to event.endTime.toString(),
                "totalPrice" to event.totalPrice.amount.toString(),
            )

        monitoringService.recordEvent("AppointmentScheduled", details)
    }

    private fun handleAppointmentCancelled(event: AppointmentCancelledEvent) {
        val tags =
            mapOf(
                "groomerId" to event.userId.value,
                "appointmentDate" to event.appointmentDate.toString(),
                "reason" to (event.reason ?: "unknown"),
            )

        monitoringService.incrementCounter("domain.events.appointment.cancelled", tags)

        val details =
            mapOf(
                "appointmentId" to event.appointmentId.value,
                "clientId" to event.clientId.value,
                "groomerId" to event.userId.value,
                "appointmentDate" to event.appointmentDate.toString(),
                "reason" to (event.reason ?: "unknown"),
            )

        monitoringService.recordEvent("AppointmentCancelled", details)
    }

    private fun handleAppointmentCompleted(event: AppointmentCompletedEvent) {
        val tags =
            mapOf(
                "groomerId" to event.userId.value,
            )

        monitoringService.incrementCounter("domain.events.appointment.completed", tags)

        val details =
            mapOf(
                "appointmentId" to event.appointmentId.value,
                "clientId" to event.clientId.value,
                "petId" to event.petId.value,
                "groomerId" to event.userId.value,
                "totalPrice" to event.totalPrice.amount.toString(),
                "servicesCount" to event.services.size.toString(),
            )

        monitoringService.recordEvent("AppointmentCompleted", details)
    }

    private fun handleAppointmentRescheduled(event: AppointmentRescheduledEvent) {
        val tags =
            mapOf(
                "groomerId" to event.userId.value,
                "oldDate" to event.oldDate.toString(),
                "newDate" to event.newDate.toString(),
            )

        monitoringService.incrementCounter("domain.events.appointment.rescheduled", tags)

        val details =
            mapOf(
                "appointmentId" to event.appointmentId.value,
                "clientId" to event.clientId.value,
                "groomerId" to event.userId.value,
                "oldDate" to event.oldDate.toString(),
                "oldStartTime" to event.oldStartTime.toString(),
                "newDate" to event.newDate.toString(),
                "newStartTime" to event.newStartTime.toString(),
            )

        monitoringService.recordEvent("AppointmentRescheduled", details)
    }

    private fun handleClientRegistered(event: ClientRegisteredEvent) {
        monitoringService.incrementCounter("domain.events.client.registered")

        val details =
            mapOf(
                "clientId" to event.clientId.value,
                "clientName" to event.clientName,
                "hasEmail" to (event.email != null).toString(),
                "hasPhone" to (event.phone != null).toString(),
            )

        monitoringService.recordEvent("ClientRegistered", details)
    }

    private fun handlePetAdded(event: PetAddedEvent) {
        val tags =
            mapOf(
                "clientId" to event.clientId.value,
                "hasBreed" to (event.breed != null).toString(),
            )

        monitoringService.incrementCounter("domain.events.pet.added", tags)

        val details =
            mapOf(
                "petId" to event.petId.value,
                "clientId" to event.clientId.value,
                "petName" to event.petName,
                "breed" to (event.breed ?: "unknown"),
            )

        monitoringService.recordEvent("PetAdded", details)
    }
}
