package ro.animaliaprogramari.animalia.adapter.inbound.security

import org.springframework.security.core.context.SecurityContextHolder
import ro.animaliaprogramari.animalia.domain.model.AuthenticatedUser

/**
 * Utility class for accessing security context
 */
object SecurityUtils {
    /**
     * Get the currently authenticated user from Spring Security context
     */
    fun getCurrentUser(): AuthenticatedUser? {
        val logger = org.slf4j.LoggerFactory.getLogger(SecurityUtils::class.java)

        val authentication = SecurityContextHolder.getContext().authentication


        return if (authentication?.principal is AuthenticatedUser) {
            val user = authentication.principal as AuthenticatedUser
            user
        } else {
            logger.error("No authenticated user found in security context")
            logger.error("Principal type: ${authentication?.principal?.javaClass?.simpleName}")
            null
        }
    }

    /**
     * Check if current user has admin role
     */
    fun isAdmin(): Boolean {
        return getCurrentUser()?.isAdmin() ?: false
    }

    /**
     * Check if current user has groomer role
     */
    fun isGroomer(): Boolean {
        return getCurrentUser()?.isGroomer() ?: false
    }

}
