package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationHistory
import java.time.LocalDateTime

/**
 * Optimized Spring Data JPA repository for notification history
 * Uses database-level queries for better performance
 */
@Repository
interface SpringNotificationHistoryRepository : JpaRepository<NotificationHistory, Long> {

    /**
     * Find notifications by salon with pagination and filtering
     * Optimized with proper indexing
     */
    @Query(
        "SELECT n FROM NotificationHistory n WHERE n.salonId = :salonId " +
            "AND (:type IS NULL OR n.type = :type) " +
            "AND (:readStatus IS NULL OR n.readStatus = :readStatus) " +
            "AND (:startDate IS NULL OR n.timestamp >= :startDate) " +
            "AND (:endDate IS NULL OR n.timestamp <= :endDate) " +
            "ORDER BY n.timestamp DESC",
    )
    fun findBySalonIdWithFilters(
        @Param("salonId") salonId: String,
        @Param("type") type: String?,
        @Param("readStatus") readStatus: Boolean?,
        @Param("startDate") startDate: LocalDateTime?,
        @Param("endDate") endDate: LocalDateTime?,
        pageable: Pageable,
    ): Page<NotificationHistory>

    /**
     * Find recent unread notifications for a salon
     * Optimized for dashboard display
     */
    @Query(
        "SELECT n FROM NotificationHistory n WHERE n.salonId = :salonId " +
            "AND n.readStatus = false " +
            "ORDER BY n.timestamp DESC",
    )
    fun findRecentUnreadBySalonId(
        @Param("salonId") salonId: String,
        pageable: Pageable,
    ): Page<NotificationHistory>

    /**
     * Count unread notifications by salon
     */
    fun countBySalonIdAndReadStatus(
        salonId: String,
        readStatus: Boolean = false,
    ): Long

    /**
     * Find notifications by appointment ID
     */
    fun findByAppointmentIdOrderByTimestampDesc(
        appointmentId: String,
    ): List<NotificationHistory>

    /**
     * Find notifications by client ID
     */
    fun findByClientIdOrderByTimestampDesc(
        clientId: String,
    ): List<NotificationHistory>

    /**
     * Mark notifications as read in batch
     */
    @Modifying
    @Query(
        "UPDATE NotificationHistory n SET n.readStatus = true, n.updatedAt = :updatedAt " +
            "WHERE n.id IN :notificationIds",
    )
    fun markAsReadBatch(
        @Param("notificationIds") notificationIds: List<Long>,
        @Param("updatedAt") updatedAt: LocalDateTime = LocalDateTime.now(),
    ): Int

    /**
     * Mark all notifications as read for a salon
     */
    @Modifying
    @Query(
        "UPDATE NotificationHistory n SET n.readStatus = true, n.updatedAt = :updatedAt " +
            "WHERE n.salonId = :salonId AND n.readStatus = false",
    )
    fun markAllAsReadBySalonId(
        @Param("salonId") salonId: String,
        @Param("updatedAt") updatedAt: LocalDateTime = LocalDateTime.now(),
    ): Int

    /**
     * Delete old notifications (cleanup)
     */
    @Modifying
    @Query(
        "DELETE FROM NotificationHistory n WHERE n.timestamp < :cutoffDate",
    )
    fun deleteOldNotifications(
        @Param("cutoffDate") cutoffDate: LocalDateTime,
    ): Int

    /**
     * Get notification statistics by salon
     */
    @Query(
        "SELECT n.type, COUNT(n), SUM(CASE WHEN n.readStatus = false THEN 1 ELSE 0 END) " +
            "FROM NotificationHistory n WHERE n.salonId = :salonId " +
            "AND n.timestamp >= :startDate " +
            "GROUP BY n.type " +
            "ORDER BY COUNT(n) DESC",
    )
    fun getNotificationStatistics(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDateTime,
    ): List<Array<Any>>

    /**
     * Find notifications by type and date range
     */
    @Query(
        "SELECT n FROM NotificationHistory n WHERE n.salonId = :salonId " +
            "AND n.type = :type " +
            "AND n.timestamp BETWEEN :startDate AND :endDate " +
            "ORDER BY n.timestamp DESC",
    )
    fun findByTypeAndDateRange(
        @Param("salonId") salonId: String,
        @Param("type") type: String,
        @Param("startDate") startDate: LocalDateTime,
        @Param("endDate") endDate: LocalDateTime,
    ): List<NotificationHistory>

    /**
     * Check if notification exists for appointment
     */
    fun existsByAppointmentIdAndType(
        appointmentId: String,
        type: String,
    ): Boolean

    /**
     * Find latest notification by appointment and type
     */
    fun findFirstByAppointmentIdAndTypeOrderByTimestampDesc(
        appointmentId: String,
        type: String,
    ): NotificationHistory?

    /**
     * Optimized query for notification dashboard
     */
    @Query(
        "SELECT new map(" +
            "n.type as type, " +
            "COUNT(n) as total, " +
            "SUM(CASE WHEN n.readStatus = false THEN 1 ELSE 0 END) as unread, " +
            "MAX(n.timestamp) as lastNotification" +
            ") FROM NotificationHistory n " +
            "WHERE n.salonId = :salonId " +
            "AND n.timestamp >= :startDate " +
            "GROUP BY n.type",
    )
    fun getDashboardSummary(
        @Param("salonId") salonId: String,
        @Param("startDate") startDate: LocalDateTime,
    ): List<Map<String, Any>>
}
