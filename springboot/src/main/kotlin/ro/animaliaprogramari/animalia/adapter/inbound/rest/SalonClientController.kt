package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ClientResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.RegisterClientRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.UpdateClientRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.ClientDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.ActivateClientCommand
import ro.animaliaprogramari.animalia.application.command.DeactivateClientCommand
import ro.animaliaprogramari.animalia.application.command.RegisterClientCommand
import ro.animaliaprogramari.animalia.application.command.UpdateClientCommand
import ro.animaliaprogramari.animalia.application.port.inbound.ClientManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.PetManagementUseCase
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.DomainException
import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.UserId
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for client management
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@RestController
@RequestMapping()
@Tag(name = "Salon Clients", description = "Manage clients associated with a salon")
class SalonClientController(
    private val clientManagementUseCase: ClientManagementUseCase,
    private val petManagementUseCase: PetManagementUseCase,
    private val clientDtoMapper: ClientDtoMapper,
) {
    private val logger = LoggerFactory.getLogger(SalonClientController::class.java)

    /**
     * GET /clients
     * Get all clients with pagination
     */
    @GetMapping
    @Operation(summary = "List clients", description = "Retrieve all clients with optional filters")
    @SwaggerApiResponse(responseCode = "200", description = "Clients retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun getAllClients(
        @RequestParam(required = false) active: Boolean?,
        @RequestParam(required = false, defaultValue = "1000") limit: Int,
        @RequestParam(required = false, defaultValue = "0") offset: Int,
    ): ResponseEntity<ApiResponse<List<ClientResponse>>> {
        logger.info("REST request to get all clients")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // For now, implement basic authorization logic
            // Admins can see all clients, groomers see clients based on their salon associations
            if (currentUser.isAdmin()) {
                // Admins see all clients
                val query =
                    GetAllClientsQuery(
                        isActive = active,
                        limit = limit,
                        offset = offset,
                    )

                val clients = clientManagementUseCase.getAllClients(query)
                val responses =
                    clients.map { client ->
                        val pets = petManagementUseCase.getPetsByClient(
                            GetPetsByClientQuery(clientId = client.id, activeOnly = true),
                        )
                        val petNames = pets.map { it.name }
                        clientDtoMapper.toResponse(
                            client = client,
                            petCount = pets.size,
                            petNames = petNames,
                            pets = pets,
                        )
                    }

                ResponseEntity.ok(ApiResponse.success(responses))
            } else {
                // For groomers, check if they have any salon associations with client access
                val accessibleSalonIds = currentUser.getSalonIdsWithClientAccess()

                if (accessibleSalonIds.isEmpty()) {
                    // No access to any salon's client data
                    return ResponseEntity.ok(ApiResponse.success(emptyList()))
                }

                // Get all clients and filter based on salon access
                // Note: This is a simplified implementation. In production, you'd want to
                // implement salon-scoped queries in the repository layer for better performance
                val query =
                    GetAllClientsQuery(
                        isActive = active,
                        limit = limit * 2, // Get more to account for filtering
                        offset = offset,
                    )

                val allClients = clientManagementUseCase.getAllClients(query)

                // For now, return all clients with permission-based filtering
                // In a real implementation, you'd filter by salon membership
                val responses =
                    allClients.take(limit).map { client ->
                        val pets = petManagementUseCase.getPetsByClient(
                            GetPetsByClientQuery(clientId = client.id, activeOnly = true),
                        )
                        val petNames = pets.map { it.name }

                        // Use the first accessible salon's permission level
                        val permissionLevel = currentUser.getClientDataAccessInSalon(accessibleSalonIds.first())
                        clientDtoMapper.toResponse(
                            client = client,
                            petCount = pets.size,
                            permission = permissionLevel,
                            petNames = petNames,
                            pets = pets,
                        )
                    }

                ResponseEntity.ok(ApiResponse.success(responses))
            }
        } catch (e: Exception) {
            logger.error("Error getting all clients", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * GET /clients/{id}
     * Get client by ID
     */
    @GetMapping("/{id}")
    fun getClientById(
        @PathVariable id: String,
    ): ResponseEntity<ApiResponse<ClientResponse>> {
        logger.info("REST request to get client by ID: $id")

        return try {
            val query = GetClientByIdQuery(ClientId.of(id))
            val client =
                clientManagementUseCase.getClientById(query)
                    ?: return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("Client not found"))

            val pets = petManagementUseCase.getPetsByClient(GetPetsByClientQuery(clientId = client.id, activeOnly = true))
            ResponseEntity.ok(
                ApiResponse.success(
                    clientDtoMapper.toResponse(
                        client = client,
                        petCount = pets.size,
                        petNames = pets.map { it.name },
                        pets = pets,
                    ),
                ),
            )
        } catch (e: Exception) {
            logger.error("Error getting client by ID", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * GET /clients/search
     * Search clients
     */
    @GetMapping("/search")
    fun searchClients(
        @RequestParam searchTerm: String,
        @RequestParam(required = false) active: Boolean?,
        @RequestParam(required = false, defaultValue = "1000") limit: Int,
        @RequestParam(required = false, defaultValue = "0") offset: Int,
    ): ResponseEntity<ApiResponse<List<ClientResponse>>> {
        logger.info("REST request to search clients with term: $searchTerm")

        return try {
            val query =
                SearchClientsQuery(
                    searchTerm = searchTerm,
                    isActive = active,
                    limit = limit,
                    offset = offset,
                )

            val clients = clientManagementUseCase.searchClients(query)
            val normalizedSearch = searchTerm.trim().lowercase()
            val responses =
                clients.map { client ->
                    val pets = petManagementUseCase.getPetsByClient(GetPetsByClientQuery(clientId = client.id))
                    val petNames = pets.map { it.name }
                    val matchingPetNames =
                        petNames.filter { petName -> petName.contains(normalizedSearch, ignoreCase = true) }
                    clientDtoMapper.toResponse(
                        client = client,
                        petCount = pets.size,
                        petNames = petNames,
                        matchingPetNames = matchingPetNames,
                        pets = pets,
                    )
                }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error searching clients", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * GET /clients/groomer/{groomerId}
     * Get clients for a specific groomer
     */
    @GetMapping("/groomer/{groomerId}")
    fun getClientsByGroomer(
        @PathVariable groomerId: String,
        @RequestParam(required = false) active: Boolean?,
        @RequestParam(required = false, defaultValue = "1000") limit: Int,
        @RequestParam(required = false, defaultValue = "0") offset: Int,
    ): ResponseEntity<ApiResponse<List<ClientResponse>>> {
        logger.info("REST request to get clients for groomer: $groomerId")

        return try {
            // Check if the current user is an admin or the groomer being requested
            val currentUser = SecurityUtils.getCurrentUser()
            if (!SecurityUtils.isAdmin() &&
                !(SecurityUtils.isGroomer() && currentUser?.userId?.value == groomerId)
            ) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Access denied"))
            }

            val query =
                GetClientsByGroomerQuery(
                    userId = UserId.of(groomerId),
                    isActive = active,
                    limit = limit,
                    offset = offset,
                )

            val clients = clientManagementUseCase.getClientsByGroomer(query)
            val responses =
                clients.map { client ->
                    val pets = petManagementUseCase.getPetsByClient(
                        GetPetsByClientQuery(clientId = client.id, activeOnly = true),
                    )
                    val petNames = pets.map { it.name }
                    clientDtoMapper.toResponse(
                        client = client,
                        petCount = pets.size,
                        petNames = petNames,
                        pets = pets,
                    )
                }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error getting clients for groomer", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * GET /clients/email/{email}
     * Get client by email
     */
    @GetMapping("/email/{email}")
    fun getClientByEmail(
        @PathVariable email: String,
    ): ResponseEntity<ApiResponse<ClientResponse>> {
        logger.info("REST request to get client by email: $email")

        return try {
            val query = GetClientByEmailQuery(Email.of(email))
            val client =
                clientManagementUseCase.getClientByEmail(query)
                    ?: return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error("Client not found"))

            val pets = petManagementUseCase.getPetsByClient(GetPetsByClientQuery(clientId = client.id, activeOnly = true))
            return ResponseEntity.ok(
                ApiResponse.success(
                    clientDtoMapper.toResponse(
                        client = client,
                        petCount = pets.size,
                        petNames = pets.map { it.name },
                        pets = pets,
                    ),
                ),
            )
        } catch (e: Exception) {
            logger.error("Error getting client by email", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * POST /clients
     * Register a new client
     */
    @PostMapping
    fun registerClient(
        @Valid @RequestBody request: RegisterClientRequest,
    ): ResponseEntity<ApiResponse<ClientResponse>> {
        logger.info("REST request to register client: {}", request)

        return try {
            val command =
                RegisterClientCommand(
                    name = request.name,
                    phone = clientDtoMapper.toPhoneNumber(request.phone),
                    email = clientDtoMapper.toEmail(request.email),
                    address = request.address,
                    notes = request.notes,
                )

            val client = clientManagementUseCase.registerClient(command)
            val response = clientDtoMapper.toResponse(client)

            ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error registering client", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to register client"))
        } catch (e: Exception) {
            logger.error("Unexpected error registering client", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * PUT /clients/{id}
     * Update client information
     */
    @PutMapping("salons/{salonId}/clients/{id}")
    fun updateClient(
        @PathVariable id: String,
        @Valid @RequestBody request: UpdateClientRequest,
    ): ResponseEntity<ApiResponse<ClientResponse>> {
        logger.info("REST request to update client: $id")

        return try {
            val command =
                UpdateClientCommand(
                    clientId = ClientId.of(id),
                    name = request.name,
                    phone = clientDtoMapper.toPhoneNumber(request.phone),
                    email = clientDtoMapper.toEmail(request.email),
                    address = request.address,
                    notes = request.notes,
                )

            val client = clientManagementUseCase.updateClient(command)
            val response = clientDtoMapper.toResponse(client)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error updating client", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to update client"))
        } catch (e: Exception) {
            logger.error("Unexpected error updating client", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * PUT /clients/{id}/activate
     * Activate a client
     */
    @PutMapping("/{id}/activate")
    fun activateClient(
        @PathVariable id: String,
    ): ResponseEntity<ApiResponse<ClientResponse>> {
        logger.info("REST request to activate client: $id")

        return try {
            val command = ActivateClientCommand(ClientId.of(id))
            val client = clientManagementUseCase.activateClient(command)
            val response = clientDtoMapper.toResponse(client)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error activating client", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to activate client"))
        } catch (e: Exception) {
            logger.error("Unexpected error activating client", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * PUT /clients/{id}/deactivate
     * Deactivate a client
     */
    @PutMapping("/{id}/deactivate")
    fun deactivateClient(
        @PathVariable id: String,
    ): ResponseEntity<ApiResponse<ClientResponse>> {
        logger.info("REST request to deactivate client: $id")

        return try {
            val command = DeactivateClientCommand(ClientId.of(id))
            val client = clientManagementUseCase.deactivateClient(command)
            val response = clientDtoMapper.toResponse(client)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error deactivating client", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to deactivate client"))
        } catch (e: Exception) {
            logger.error("Unexpected error deactivating client", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }
}
