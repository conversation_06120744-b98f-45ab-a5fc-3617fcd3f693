package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDateTime

/**
 * Response DTO for appointment settings
 * Note: Finalization message templates are now managed through the SMS template system
 */
data class AppointmentSettingsResponse(
    @JsonProperty("salonId")
    val salonId: String,

    @JsonProperty("autoFinalizeEnabled")
    val autoFinalizeEnabled: <PERSON><PERSON><PERSON>,

    @JsonProperty("overdueNotificationsEnabled")
    val overdueNotificationsEnabled: <PERSON><PERSON><PERSON>,

    @JsonProperty("smsCompletionEnabled")
    val smsCompletionEnabled: <PERSON><PERSON><PERSON>,

    @JsonProperty("createdAt")
    val createdAt: LocalDateTime,

    @JsonProperty("updatedAt")
    val updatedAt: LocalDateTime
)
