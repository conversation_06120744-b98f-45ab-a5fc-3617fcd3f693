package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.ImpersonateUserCommand
import ro.animaliaprogramari.animalia.application.port.inbound.AdminManagementUseCase
import ro.animaliaprogramari.animalia.application.query.AdminGetAllUsersQuery
import ro.animaliaprogramari.animalia.application.query.AdminSearchUsersQuery
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.UserId
import ro.animaliaprogramari.animalia.domain.model.User
import ro.animaliaprogramari.animalia.domain.model.AuthenticatedUser
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SubscriptionTier

@RestController
@RequestMapping("/admin")
@Tag(name = "Admin", description = "Admin management operations")
class AdminController(
    private val adminManagementUseCase: AdminManagementUseCase,
) {
    private val logger = LoggerFactory.getLogger(AdminController::class.java)

    /**
     * Execute operation with admin validation
     */
    private inline fun <T> executeWithAdminValidation(
        operation: (AuthenticatedUser) -> ApiResponse<T>
    ): ResponseEntity<ApiResponse<T>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            if (!currentUser.isAdmin()) {
                logger.warn("Non-admin user ${currentUser.userId.value} attempted admin operation")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Only admin users can perform this operation"))
            }

            ResponseEntity.ok(operation(currentUser))
        } catch (e: Exception) {
            logger.error("Error during admin operation", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Operation failed: ${e.message}"))
        }
    }

    /**
     * Convert User to UserSummaryDto with enriched information
     */
    private fun User.toUserSummaryDto(): UserSummaryDto {
        val enrichedData = adminManagementUseCase.getEnrichedUserData(id)

        return UserSummaryDto(
            id = id.value,
            firebaseUid = firebaseUid,
            email = email?.value,
            phoneNumber = phoneNumber,
            name = name,
            role = role.name,
            isActive = isActive,
            createdAt = createdAt.toString(),
            updatedAt = updatedAt.toString(),
            salons = enrichedData.salons,
            smsQuota = enrichedData.smsQuota,
            activeSubscriptions = enrichedData.activeSubscriptions,
            hasPastSubscriptions = enrichedData.hasPastSubscriptions,
        )
    }

    /**
     * Convert list of Users to UserListResponse
     */
    private fun List<User>.toUserListResponse(limit: Int, offset: Int) = UserListResponse(
        users = map { it.toUserSummaryDto() },
        total = size,
        limit = limit,
        offset = offset,
    )

    /**
     * POST /admin/users/{userId}/impersonate
     * Generate impersonation token for admin to login as another user
     */
    @PostMapping("/users/{userId}/impersonate")
    @Operation(
        summary = "Impersonate user",
        description = "Generate JWT token allowing admin to login as another user for account setup"
    )
    fun impersonateUser(
        @PathVariable userId: String,
        @Valid @RequestBody request: ImpersonateUserRequest,
    ): ResponseEntity<ApiResponse<ImpersonateUserResponse>> {
        return executeWithAdminValidation { currentUser ->
            val command = ImpersonateUserCommand(
                adminUserId = currentUser.userId,
                targetUserId = UserId.of(request.targetUserId),
            )

            val token = adminManagementUseCase.impersonateUser(command)
            val targetUser = adminManagementUseCase.getUserById(UserId.of(request.targetUserId))
                ?: throw IllegalStateException("Target user not found after impersonation")

            val response = ImpersonateUserResponse(
                token = token.token,
                expiresAt = token.expiresAt.toString(),
                targetUser = targetUser.toUserSummaryDto(),
                impersonatedBy = currentUser.userId.value,
            )

            logger.info("Admin impersonation successful: admin=${currentUser.userId.value}, target=${request.targetUserId}")
            ApiResponse.success(response)
        }
    }

    /**
     * GET /admin/users
     * Get all users for admin management
     */
    @GetMapping("/users")
    @Operation(
        summary = "Get all users",
        description = "Get paginated list of all users for admin management"
    )
    fun getAllUsers(
        @RequestParam(defaultValue = "true") isActive: Boolean?,
        @RequestParam(defaultValue = "50") limit: Int,
        @RequestParam(defaultValue = "0") offset: Int,
    ): ResponseEntity<ApiResponse<UserListResponse>> {
        return executeWithAdminValidation {
            val query = AdminGetAllUsersQuery(
                isActive = isActive,
                limit = limit,
                offset = offset,
            )

            val users = adminManagementUseCase.getAllUsers(query)
            ApiResponse.success(users.toUserListResponse(limit, offset))
        }
    }

    /**
     * POST /admin/users/search
     * Search users by criteria
     */
    @PostMapping("/users/search")
    @Operation(
        summary = "Search users",
        description = "Search users by name, email, or phone number"
    )
    fun searchUsers(
        @Valid @RequestBody request: SearchUsersRequest,
    ): ResponseEntity<ApiResponse<UserListResponse>> {
        return executeWithAdminValidation {
            val query = AdminSearchUsersQuery(
                searchTerm = request.searchTerm,
                isActive = request.isActive,
                limit = request.limit,
                offset = request.offset,
            )

            val users = adminManagementUseCase.searchUsers(query)
            ApiResponse.success(users.toUserListResponse(request.limit, request.offset))
        }
    }

    /**
     * PUT /admin/users/{userId}/sms-quota
     * Update SMS quota for a user's salon
     */
    @PutMapping("/users/{userId}/sms-quota")
    @Operation(
        summary = "Update user SMS quota",
        description = "Update the SMS quota for a specific salon owned by the user"
    )
    fun updateUserSmsQuota(
        @PathVariable userId: String,
        @Valid @RequestBody request: UpdateUserSmsQuotaRequest,
    ): ResponseEntity<ApiResponse<SmsQuotaSummaryDto>> {
        return executeWithAdminValidation { currentUser ->
            logger.info("Admin ${currentUser.userId.value} updating SMS quota for user $userId, salon ${request.salonId}")

            val updatedQuota = adminManagementUseCase.updateUserSmsQuota(
                userId = UserId.of(userId),
                salonId = SalonId.of(request.salonId),
                totalQuota = request.totalQuota,
            )

            logger.info("SMS quota updated successfully for user $userId, salon ${request.salonId}")
            ApiResponse.success(updatedQuota)
        }
    }

    /**
     * PUT /admin/salons/{salonId}/subscription-tier
     * Update subscription tier for a salon
     */
    @PutMapping("/salons/{salonId}/subscription-tier")
    @Operation(
        summary = "Update subscription tier",
        description = "Update the subscription tier for a salon (hardcoded, simple)"
    )
    fun updateSubscriptionTier(
        @PathVariable salonId: String,
        @Valid @RequestBody request: UpdateSubscriptionTierRequest,
    ): ResponseEntity<ApiResponse<SubscriptionSummaryDto>> {
        return executeWithAdminValidation { currentUser ->
            logger.info("Admin ${currentUser.userId.value} updating subscription tier for salon $salonId to ${request.tier}")

            val tier = SubscriptionTier.fromString(request.tier)
            val updatedSubscription = adminManagementUseCase.updateSubscriptionTier(
                salonId = SalonId.of(salonId),
                tier = tier,
            )

            logger.info("Subscription tier updated successfully for salon $salonId")
            ApiResponse.success(updatedSubscription)
        }
    }
}
