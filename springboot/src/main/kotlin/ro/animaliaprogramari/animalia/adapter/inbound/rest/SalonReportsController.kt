package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.ReportsUseCase
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.StaffId
import java.time.LocalDate

/**
 * REST controller for salon reports and analytics
 * Provides business intelligence data for salon owners and managers
 */
@RestController
@RequestMapping("/salons/{salonId}/reports")
@Tag(name = "Salon Reports", description = "Business analytics and reporting for salons")
class SalonReportsController(
    private val reportsUseCase: ReportsUseCase,
) {
    private val logger = LoggerFactory.getLogger(SalonReportsController::class.java)

    @GetMapping("/pets")
    @Operation(summary = "Get pet distribution report", description = "Returns pet size and breed distribution data")
    fun getPetReport(
        @PathVariable salonId: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDate: LocalDate,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDate: LocalDate,
    ): ResponseEntity<ApiResponse<PetReportResponse>> {
        logger.info("Getting pet report for salon: $salonId, period: $startDate to $endDate")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only salon staff can view reports
            if (!currentUser.hasAccessToSalon(salon)) {
                logger.warn("User ${currentUser.userId.value} denied access to salon $salonId reports")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să vizualizați rapoartele acestui salon"))
            }

            // Additional authorization: Only chief groomers can access reports
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                logger.warn("User ${currentUser.userId.value} denied reports access - not chief groomer in salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Doar proprietarii de salon pot accesa rapoartele"))
            }

            val query = GetPetReportQuery(
                salonId = salon,
                startDate = startDate,
                endDate = endDate
            )

            val reportData = reportsUseCase.getPetReport(query)
            val response = PetReportResponse.fromDomain(reportData)

            logger.info("Pet report generated successfully for salon: $salonId")
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error generating pet report for salon: $salonId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la generarea raportului: ${e.message}"))
        }
    }

    @GetMapping("/services")
    @Operation(summary = "Get service performance report", description = "Returns service requests and revenue data")
    fun getServiceReport(
        @PathVariable salonId: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDate: LocalDate,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDate: LocalDate,
    ): ResponseEntity<ApiResponse<ServiceReportResponse>> {
        logger.info("Getting service report for salon: $salonId, period: $startDate to $endDate")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            if (!currentUser.hasAccessToSalon(salon)) {
                logger.warn("User ${currentUser.userId.value} denied access to salon $salonId reports")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să vizualizați rapoartele acestui salon"))
            }

            // Additional authorization: Only chief groomers can access reports
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                logger.warn("User ${currentUser.userId.value} denied reports access - not chief groomer in salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Doar proprietarii de salon pot accesa rapoartele"))
            }

            val query = GetServiceReportQuery(
                salonId = salon,
                startDate = startDate,
                endDate = endDate
            )

            val reportData = reportsUseCase.getServiceReport(query)
            val response = ServiceReportResponse.fromDomain(reportData)

            logger.info("Service report generated successfully for salon: $salonId")
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error generating service report for salon: $salonId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la generarea raportului: ${e.message}"))
        }
    }

    @GetMapping("/clients")
    @Operation(summary = "Get client analytics report", description = "Returns top clients and client analytics")
    fun getClientReport(
        @PathVariable salonId: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDate: LocalDate,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDate: LocalDate,
    ): ResponseEntity<ApiResponse<ClientReportResponse>> {
        logger.info("Getting client report for salon: $salonId, period: $startDate to $endDate")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            if (!currentUser.hasAccessToSalon(salon)) {
                logger.warn("User ${currentUser.userId.value} denied access to salon $salonId reports")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să vizualizați rapoartele acestui salon"))
            }

            // Additional authorization: Only chief groomers can access reports
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                logger.warn("User ${currentUser.userId.value} denied reports access - not chief groomer in salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Doar proprietarii de salon pot accesa rapoartele"))
            }

            val query = GetClientReportQuery(
                salonId = salon,
                startDate = startDate,
                endDate = endDate
            )

            val reportData = reportsUseCase.getClientReport(query)
            val response = ClientReportResponse.fromDomain(reportData)

            logger.info("Client report generated successfully for salon: $salonId")
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error generating client report for salon: $salonId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la generarea raportului: ${e.message}"))
        }
    }

    @GetMapping("/staff/{staffId}/performance")
    @Operation(summary = "Get staff performance report", description = "Returns individual staff performance metrics")
    fun getStaffPerformanceReport(
        @PathVariable salonId: String,
        @PathVariable staffId: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDate: LocalDate,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDate: LocalDate,
    ): ResponseEntity<ApiResponse<StaffPerformanceReportResponse>> {
        logger.info("Getting staff performance report for salon: $salonId, staff: $staffId, period: $startDate to $endDate")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)
            val staff = StaffId.of(staffId)

            if (!currentUser.hasAccessToSalon(salon)) {
                logger.warn("User ${currentUser.userId.value} denied access to salon $salonId reports")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să vizualizați rapoartele acestui salon"))
            }

            // Additional authorization: Only chief groomers can access reports
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                logger.warn("User ${currentUser.userId.value} denied reports access - not chief groomer in salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Doar proprietarii de salon pot accesa rapoartele"))
            }

            val query = GetStaffPerformanceReportQuery(
                salonId = salon,
                staffId = staff,
                startDate = startDate,
                endDate = endDate
            )

            val reportData = reportsUseCase.getStaffPerformanceReport(query)
            val response = StaffPerformanceReportResponse.fromDomain(reportData)

            logger.info("Staff performance report generated successfully for salon: $salonId, staff: $staffId")
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error generating staff performance report for salon: $salonId, staff: $staffId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la generarea raportului: ${e.message}"))
        }
    }

    @GetMapping("/revenue")
    @Operation(summary = "Get revenue analytics report", description = "Returns revenue analytics and trends")
    fun getRevenueReport(
        @PathVariable salonId: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDate: LocalDate,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDate: LocalDate,
    ): ResponseEntity<ApiResponse<RevenueReportResponse>> {
        logger.info("Getting revenue report for salon: $salonId, period: $startDate to $endDate")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            if (!currentUser.hasAccessToSalon(salon)) {
                logger.warn("User ${currentUser.userId.value} denied access to salon $salonId reports")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să vizualizați rapoartele acestui salon"))
            }

            // Additional authorization: Only chief groomers can access reports
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                logger.warn("User ${currentUser.userId.value} denied reports access - not chief groomer in salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Doar proprietarii de salon pot accesa rapoartele"))
            }

            val query = GetRevenueReportQuery(
                salonId = salon,
                startDate = startDate,
                endDate = endDate
            )

            val reportData = reportsUseCase.getRevenueReport(query)
            val response = RevenueReportResponse.fromDomain(reportData)

            logger.info("Revenue report generated successfully for salon: $salonId")
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error generating revenue report for salon: $salonId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la generarea raportului: ${e.message}"))
        }
    }
}
