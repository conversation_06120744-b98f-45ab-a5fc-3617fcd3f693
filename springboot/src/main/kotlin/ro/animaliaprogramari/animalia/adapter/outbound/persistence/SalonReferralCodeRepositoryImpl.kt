package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonReferralCode as SalonReferralCodeEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringSalonReferralCodeRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonReferralCodeRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.referral.SalonReferralCode
import ro.animaliaprogramari.animalia.domain.model.referral.SalonReferralCodeId

@Repository
class SalonReferralCodeRepositoryImpl(
    private val springRepository: SpringSalonReferralCodeRepository
) : SalonReferralCodeRepository {

    override fun save(salonReferralCode: SalonReferralCode): SalonReferralCode {
        val entity = salonReferralCode.toEntity()
        val savedEntity = springRepository.save(entity)
        return savedEntity.toDomain()
    }

    override fun findById(id: SalonReferralCodeId): SalonReferralCode? {
        return springRepository.findById(id.value).orElse(null)?.toDomain()
    }

    override fun findBySalonId(salonId: SalonId): SalonReferralCode? {
        return springRepository.findBySalonId(salonId.value)?.toDomain()
    }

    override fun findByCode(code: String): SalonReferralCode? {
        return springRepository.findByCode(code)?.toDomain()
    }

    override fun existsByCode(code: String): Boolean {
        return springRepository.existsByCode(code)
    }

    override fun existsBySalonId(salonId: SalonId): Boolean {
        return springRepository.existsBySalonId(salonId.value)
    }



    private fun SalonReferralCode.toEntity(): SalonReferralCodeEntity {
        return SalonReferralCodeEntity(
            id = this.id.value,
            salonId = this.salonId.value,
            code = this.code,
            smsCreditsAwarded = this.smsCreditsAwarded,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt
        )
    }

    private fun SalonReferralCodeEntity.toDomain(): SalonReferralCode {
        return SalonReferralCode(
            id = SalonReferralCodeId.of(this.id),
            salonId = SalonId.of(this.salonId),
            code = this.code,
            smsCreditsAwarded = this.smsCreditsAwarded,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt
        )
    }
}
