package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsReminderTiming as SmsReminderTimingEntity
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsReminderTiming as SmsReminderTimingDomain
import ro.animaliaprogramari.animalia.domain.model.SmsReminderTimingId

/**
 * Mapper between SMS reminder timing domain model and JPA entity
 */
@Component
class SmsReminderTimingEntityMapper {

    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: SmsReminderTimingDomain): SmsReminderTimingEntity {
        return SmsReminderTimingEntity(
            id = domain.id.value,
            salonId = domain.salonId.value,
            hoursBefore = domain.hoursBefore,
            isEnabled = domain.isEnabled,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: SmsReminderTimingEntity): SmsReminderTimingDomain {
        return SmsReminderTimingDomain(
            id = SmsReminderTimingId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            hoursBefore = entity.hoursBefore,
            isEnabled = entity.isEnabled,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
}
