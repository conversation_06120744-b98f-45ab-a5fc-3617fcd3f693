package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.application.port.outbound.SmsQuotaRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.notification.SmsQuota
import ro.animaliaprogramari.animalia.domain.model.notification.SmsQuotaId
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.SmsQuotaEntityMapper
import java.time.LocalDateTime

/**
 * JPA adapter implementing the SmsQuotaRepository port
 */
@Repository
class JpaSmsQuotaRepository(
    private val springRepository: SpringSmsQuotaRepository,
    private val mapper: SmsQuotaEntityMapper
) : SmsQuotaRepository {

    override fun save(smsQuota: SmsQuota): SmsQuota {
        val entity = mapper.toEntity(smsQuota)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findBySalonId(salonId: SalonId): SmsQuota? {
        return springRepository.findBySalonId(salonId.value)
            ?.let { mapper.toDomain(it) }
    }

    override fun findById(id: SmsQuotaId): SmsQuota? {
        return springRepository.findById(id.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun existsBySalonId(salonId: SalonId): Boolean {
        return springRepository.existsBySalonId(salonId.value)
    }

    // findQuotasNeedingReset method removed - SMS credits never expire

    override fun deleteBySalonId(salonId: SalonId) {
        springRepository.deleteBySalonId(salonId.value)
    }
}
