package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.ReferralCode as ReferralCodeEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.ReferralCodeStatus as ReferralCodeStatusEntity
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralCode
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralCodeId
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralCodeStatus

@Component
class ReferralCodeMapper {

    fun toDomain(entity: ReferralCodeEntity): ReferralCode {
        return ReferralCode(
            id = ReferralCodeId.of(entity.id),
            code = entity.code,
            generatorSalonId = SalonId.of(entity.generatorSalonId),
            claimerSalonId = entity.claimerSalonId?.let { SalonId.of(it) },
            status = toDomainStatus(entity.status),
            smsCreditsAwarded = entity.smsCreditsAwarded,
            claimedAt = entity.claimedAt,
            expiresAt = entity.expiresAt,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    fun toEntity(domain: ReferralCode): ReferralCodeEntity {
        return ReferralCodeEntity(
            id = domain.id.value,
            code = domain.code,
            generatorSalonId = domain.generatorSalonId.value,
            claimerSalonId = domain.claimerSalonId?.value,
            status = toEntityStatus(domain.status),
            smsCreditsAwarded = domain.smsCreditsAwarded,
            claimedAt = domain.claimedAt,
            expiresAt = domain.expiresAt,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }

    fun toDomainStatus(entityStatus: ReferralCodeStatusEntity): ReferralCodeStatus {
        return when (entityStatus) {
            ReferralCodeStatusEntity.ACTIVE -> ReferralCodeStatus.ACTIVE
            ReferralCodeStatusEntity.CLAIMED -> ReferralCodeStatus.CLAIMED
            ReferralCodeStatusEntity.EXPIRED -> ReferralCodeStatus.EXPIRED
            ReferralCodeStatusEntity.CANCELLED -> ReferralCodeStatus.CANCELLED
        }
    }

    fun toEntityStatus(domainStatus: ReferralCodeStatus): ReferralCodeStatusEntity {
        return when (domainStatus) {
            ReferralCodeStatus.ACTIVE -> ReferralCodeStatusEntity.ACTIVE
            ReferralCodeStatus.CLAIMED -> ReferralCodeStatusEntity.CLAIMED
            ReferralCodeStatus.EXPIRED -> ReferralCodeStatusEntity.EXPIRED
            ReferralCodeStatus.CANCELLED -> ReferralCodeStatusEntity.CANCELLED
        }
    }
}
