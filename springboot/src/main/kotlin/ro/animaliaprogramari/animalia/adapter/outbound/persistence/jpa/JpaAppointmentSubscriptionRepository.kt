package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.repository.AppointmentSubscriptionRepository
import ro.animaliaprogramari.animalia.infrastructure.persistence.mapper.AppointmentSubscriptionEntityMapper

/**
 * JPA adapter implementing the AppointmentSubscriptionRepository port
 */
@Repository
class JpaAppointmentSubscriptionRepository(
    private val springRepository: SpringAppointmentSubscriptionRepository,
    private val mapper: AppointmentSubscriptionEntityMapper,
) : AppointmentSubscriptionRepository {

    override fun save(subscription: AppointmentSubscription): AppointmentSubscription {
        val entity = mapper.toEntity(subscription)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findById(id: AppointmentSubscriptionId): AppointmentSubscription? {
        return springRepository.findById(id.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findByIdAndSalonId(id: AppointmentSubscriptionId, salonId: SalonId): AppointmentSubscription? {
        return springRepository.findByIdAndSalonId(id.value, salonId.value)
            ?.let { mapper.toDomain(it) }
    }

    override fun findByClientIdAndSalonId(clientId: ClientId, salonId: SalonId): List<AppointmentSubscription> {
        return springRepository.findByClientIdAndSalonIdOrderByCreatedAtDesc(clientId.value, salonId.value)
            .map { mapper.toDomain(it) }
    }

    override fun findActiveBySalonId(salonId: SalonId): List<AppointmentSubscription> {
        return springRepository.findBySalonIdAndStatus(salonId.value, "ACTIVE")
            .map { mapper.toDomain(it) }
    }

    override fun findSubscriptionsNeedingProcessing(): List<AppointmentSubscription> {
        return springRepository.findSubscriptionsNeedingProcessing()
            .map { mapper.toDomain(it) }
    }

    override fun delete(subscription: AppointmentSubscription) {
        springRepository.deleteById(subscription.id.value)
    }

    override fun existsById(id: AppointmentSubscriptionId): Boolean {
        return springRepository.existsById(id.value)
    }

    override fun findByOriginalAppointmentId(appointmentId: AppointmentId): List<AppointmentSubscription> {
        return springRepository.findByOriginalAppointmentId(appointmentId.value)
            .map { mapper.toDomain(it) }
    }
}
