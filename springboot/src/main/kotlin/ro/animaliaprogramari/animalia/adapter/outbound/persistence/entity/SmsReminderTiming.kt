package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

/**
 * JPA entity for SMS reminder timings
 */
@Entity
@Table(
    name = "sms_reminder_timings",
    indexes = [
        Index(name = "idx_sms_reminder_timings_salon_id", columnList = "salon_id"),
        Index(name = "idx_sms_reminder_timings_enabled", columnList = "is_enabled")
    ]
)
data class SmsReminderTiming(
    @Id
    @Column(name = "id", nullable = false)
    val id: String,

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @field:Min(value = 1, message = "Hours before must be positive")
    @Column(name = "hours_before", nullable = false)
    val hoursBefore: Int,

    @Column(name = "is_enabled", nullable = false)
    val isEnabled: Boolean = true,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        salonId = "",
        hoursBefore = 24,
        isEnabled = true,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as SmsReminderTiming
        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}

