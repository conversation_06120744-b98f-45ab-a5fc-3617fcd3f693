package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * JPA repository for salon subscriptions
 */
@Repository
interface SalonSubscriptionJpaRepository : JpaRepository<SalonSubscriptionEntity, String> {

    @Query("""
        SELECT s FROM SalonSubscriptionEntity s
        WHERE s.salonId = :salonId
        AND s.status IN (
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.ACTIVE,
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.TRIAL,
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.CANCELLED
        )
        AND (
            (s.isTrialActive = true AND s.trialEndDate > CURRENT_TIMESTAMP) OR
            (s.endDate IS NULL OR s.endDate > CURRENT_TIMESTAMP)
        )
        ORDER BY s.createdAt DESC
        LIMIT 1
    """)
    fun findActiveBySalonId(@Param("salonId") salonId: String): SalonSubscriptionEntity?

    fun findBySalonId(salonId: String): List<SalonSubscriptionEntity>

    fun findByUserId(userId: String): List<SalonSubscriptionEntity>

    @Query("""
        SELECT s FROM SalonSubscriptionEntity s
        WHERE s.userId = :userId
        AND s.status IN (
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.ACTIVE,
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.TRIAL,
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.CANCELLED
        )
        AND (
            (s.isTrialActive = true AND s.trialEndDate > CURRENT_TIMESTAMP) OR
            (s.endDate IS NULL OR s.endDate > CURRENT_TIMESTAMP)
        )
        ORDER BY s.tier DESC, s.createdAt DESC
    """)
    fun findActiveByUserId(@Param("userId") userId: String): List<SalonSubscriptionEntity>

    fun findByRevenueCatCustomerId(customerId: String): SalonSubscriptionEntity?

    @Query("""
        SELECT s FROM SalonSubscriptionEntity s
        WHERE s.status IN (
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.ACTIVE,
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.TRIAL,
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.CANCELLED
        )
        AND (
            (s.isTrialActive = true AND s.trialEndDate > CURRENT_TIMESTAMP) OR
            (s.endDate IS NULL OR s.endDate > CURRENT_TIMESTAMP)
        )
    """)
    fun findAllActive(): List<SalonSubscriptionEntity>

    @Query("SELECT s FROM SalonSubscriptionEntity s WHERE s.status = ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.EXPIRED")
    fun findAllExpired(): List<SalonSubscriptionEntity>

    @Query("SELECT s FROM SalonSubscriptionEntity s WHERE s.status = ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.TRIAL")
    fun findAllTrials(): List<SalonSubscriptionEntity>

    @Query("SELECT COUNT(s) FROM SalonSubscriptionEntity s WHERE s.tier = :tier")
    fun countByTier(@Param("tier") tier: SubscriptionTier): Long

    @Query("SELECT COUNT(s) FROM SalonSubscriptionEntity s WHERE s.status = :status")
    fun countByStatus(@Param("status") status: SubscriptionStatus): Long

    @Query("""
        SELECT CASE WHEN COUNT(s) > 0 THEN true ELSE false END
        FROM SalonSubscriptionEntity s
        WHERE s.salonId = :salonId
        AND s.status IN (
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.ACTIVE,
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.TRIAL,
            ro.animaliaprogramari.animalia.domain.model.SubscriptionStatus.CANCELLED
        )
        AND (
            (s.isTrialActive = true AND s.trialEndDate > CURRENT_TIMESTAMP) OR
            (s.endDate IS NULL OR s.endDate > CURRENT_TIMESTAMP)
        )
    """)
    fun hasActiveSubscription(@Param("salonId") salonId: String): Boolean
}
