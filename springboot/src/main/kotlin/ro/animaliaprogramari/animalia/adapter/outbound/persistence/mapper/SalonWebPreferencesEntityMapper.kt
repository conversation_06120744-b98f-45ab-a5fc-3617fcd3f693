package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import com.animalia.model.SalonWebPreferences as SalonWebPreferencesEntity

/**
 * Mapper between domain SalonWebPreferences and JPA SalonWebPreferencesEntity
 * This handles the translation between the pure domain model and persistence model
 */
@Component
class SalonWebPreferencesEntityMapper {
    private val objectMapper = ObjectMapper()

    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: SalonWebPreferences): SalonWebPreferencesEntity {
        val entity = SalonWebPreferencesEntity()
        
        entity.id = domain.id.value
        entity.salonId = domain.salonId.value
        entity.bookingWebsiteUrl = domain.bookingWebsiteUrl
        entity.businessName = domain.businessName
        entity.businessDescription = domain.businessDescription
        entity.businessAddress = domain.businessAddress
        entity.contactPhone = domain.contactPhone
        entity.contactEmail = domain.contactEmail
        entity.facebookLink = domain.facebookLink
        entity.instagramLink = domain.instagramLink
        entity.tiktokLink = domain.tiktokLink
        
        // Convert lists and maps to JSON strings
        entity.websitePhotos = if (domain.websitePhotos.isNotEmpty()) {
            objectMapper.writeValueAsString(domain.websitePhotos)
        } else {
            null
        }
        
        entity.businessHours = if (domain.businessHours.isNotEmpty()) {
            objectMapper.writeValueAsString(domain.businessHours)
        } else {
            null
        }
        
        // Convert enums
        entity.cancellationPolicy = when (domain.cancellationPolicy) {
            CancellationPolicy.HOURS_24 -> SalonWebPreferencesEntity.CancellationPolicy.HOURS_24
            CancellationPolicy.HOURS_48 -> SalonWebPreferencesEntity.CancellationPolicy.HOURS_48
            CancellationPolicy.HOURS_72 -> SalonWebPreferencesEntity.CancellationPolicy.HOURS_72
            CancellationPolicy.NO_CHANGES -> SalonWebPreferencesEntity.CancellationPolicy.NO_CHANGES
        }
        
        entity.bookingAcceptance = when (domain.bookingAcceptance) {
            BookingAcceptance.AUTOMATIC -> SalonWebPreferencesEntity.BookingAcceptance.AUTOMATIC
            BookingAcceptance.MANUAL -> SalonWebPreferencesEntity.BookingAcceptance.MANUAL
        }
        
        entity.isActive = domain.isActive
        entity.createdAt = domain.createdAt
        entity.updatedAt = domain.updatedAt
        
        return entity
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: SalonWebPreferencesEntity): SalonWebPreferences {
        return SalonWebPreferences(
            id = SalonWebPreferencesId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            bookingWebsiteUrl = entity.bookingWebsiteUrl ?: "",
            businessName = entity.businessName ?: "",
            businessDescription = entity.businessDescription ?: "",
            businessAddress = entity.businessAddress ?: "",
            contactPhone = entity.contactPhone ?: "",
            contactEmail = entity.contactEmail ?: "",
            facebookLink = entity.facebookLink ?: "",
            instagramLink = entity.instagramLink ?: "",
            tiktokLink = entity.tiktokLink ?: "",
            websitePhotos = parseWebsitePhotos(entity.websitePhotos),
            businessHours = parseBusinessHours(entity.businessHours),
            cancellationPolicy = mapCancellationPolicy(entity.cancellationPolicy),
            bookingAcceptance = mapBookingAcceptance(entity.bookingAcceptance),
            isActive = entity.isActive ?: true,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    /**
     * Parse website photos from JSON string
     */
    private fun parseWebsitePhotos(photosJson: String?): List<String> {
        return if (photosJson.isNullOrBlank()) {
            emptyList()
        } else {
            try {
                objectMapper.readValue<List<String>>(photosJson)
            } catch (e: Exception) {
                emptyList()
            }
        }
    }

    /**
     * Parse business hours from JSON string
     */
    private fun parseBusinessHours(hoursJson: String?): Map<String, Map<String, Any>> {
        return if (hoursJson.isNullOrBlank()) {
            emptyMap()
        } else {
            try {
                objectMapper.readValue<Map<String, Map<String, Any>>>(hoursJson)
            } catch (e: Exception) {
                emptyMap()
            }
        }
    }

    /**
     * Map entity cancellation policy to domain enum
     */
    private fun mapCancellationPolicy(policy: SalonWebPreferencesEntity.CancellationPolicy?): CancellationPolicy {
        return when (policy) {
            SalonWebPreferencesEntity.CancellationPolicy.HOURS_24 -> CancellationPolicy.HOURS_24
            SalonWebPreferencesEntity.CancellationPolicy.HOURS_48 -> CancellationPolicy.HOURS_48
            SalonWebPreferencesEntity.CancellationPolicy.HOURS_72 -> CancellationPolicy.HOURS_72
            SalonWebPreferencesEntity.CancellationPolicy.NO_CHANGES -> CancellationPolicy.NO_CHANGES
            null -> CancellationPolicy.HOURS_24 // Default value
        }
    }

    /**
     * Map entity booking acceptance to domain enum
     */
    private fun mapBookingAcceptance(acceptance: SalonWebPreferencesEntity.BookingAcceptance?): BookingAcceptance {
        return when (acceptance) {
            SalonWebPreferencesEntity.BookingAcceptance.AUTOMATIC -> BookingAcceptance.AUTOMATIC
            SalonWebPreferencesEntity.BookingAcceptance.MANUAL -> BookingAcceptance.MANUAL
            null -> BookingAcceptance.AUTOMATIC // Default value
        }
    }
}
