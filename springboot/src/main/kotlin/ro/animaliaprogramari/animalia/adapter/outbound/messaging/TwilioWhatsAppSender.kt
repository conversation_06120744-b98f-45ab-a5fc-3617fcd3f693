package ro.animaliaprogramari.animalia.adapter.outbound.messaging

import com.fasterxml.jackson.databind.ObjectMapper
import com.twilio.Twilio
import com.twilio.rest.api.v2010.account.Message
import com.twilio.type.PhoneNumber
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType.APPOINTMENT_CANCELLATION
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType.APPOINTMENT_CONFIRMATION
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType.APPOINTMENT_RESCHEDULE
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType.APPOINTMENT_COMPLETION
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType.FOLLOW_UP
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType.REMINDER
import ro.animaliaprogramari.animalia.application.port.outbound.MessageSendResult
import ro.animaliaprogramari.animalia.application.port.outbound.MessagingChannelType
import ro.animaliaprogramari.animalia.application.port.outbound.SmsSender
import ro.animaliaprogramari.animalia.application.service.FeatureFlagService
import ro.animaliaprogramari.animalia.application.service.WhatsAppTemplateService
import ro.animaliaprogramari.animalia.domain.service.MessagingService
import ro.animaliaprogramari.animalia.infrastructure.config.TwilioProperties
import javax.annotation.PostConstruct

/**
 * WhatsApp sender implementation using Twilio SDK.
 * Sends messages via WhatsApp Business API using Content Templates with proper error handling.
 * Uses user-selected template preferences from the database.
 */
@Service
@ConditionalOnProperty(name = ["twilio.whatsapp.enabled"], havingValue = "true", matchIfMissing = false)
class TwilioWhatsAppSender(
    private val twilioProperties: TwilioProperties,
    private val featureFlagService: FeatureFlagService,
    private val whatsAppTemplateService: WhatsAppTemplateService
) : SmsSender {
    private val logger = LoggerFactory.getLogger(TwilioWhatsAppSender::class.java)
    private val objectMapper = ObjectMapper()

    @PostConstruct
    fun init() {
        // Initialize Twilio SDK with credentials
        Twilio.init(twilioProperties.accountSid, twilioProperties.authToken)
        logger.info("Twilio SDK initialized for WhatsApp messaging")
    }

    override fun sendMessage(
        to: String,
        message: String,
        messageType: SmsMessageType,
        messageContext: MessagingService.MessageContext?
    ): MessageSendResult {
        // Check if WhatsApp feature is enabled via feature flag
        requireWhatsappEnabled(to)

        // Ensure the phone number is in E.164 format
        val whatsappTo = PhoneNumber("whatsapp:${formatPhoneNumber(to)}")
        val whatsappFrom = PhoneNumber("whatsapp:${twilioProperties.whatsappNumber}")

        // Get ContentSid and template info based on user preference or default
        val templateInfo = getTemplateInfoForUser(messageType, messageContext)
        val contentSid = templateInfo.contentSid

        // Build ContentVariables JSON from messageContext
        val contentVariables = buildContentVariables(messageContext)

        try {
            // Create message with ContentSid and ContentVariables
            val twilioMessage = Message.creator(
                whatsappTo,
                whatsappFrom,
                "" // Body can be empty when using ContentSid
            )
                .setContentSid(contentSid)
                .setContentVariables(contentVariables)
                .create()

            logger.info("✓ WhatsApp message sent successfully to ${formatPhoneNumber(to)} (type: $messageType, Template: ${templateInfo.displayName}, ContentSid: $contentSid, SID: ${twilioMessage.sid})")

            return MessageSendResult(
                channelType = MessagingChannelType.WHATSAPP,
                templateName = templateInfo.displayName,
                templateId = templateInfo.previewText,  // Store preview text in templateId for now
                success = true
            )
        } catch (ex: Exception) {
            logger.error("✗ Failed to send WhatsApp message to ${formatPhoneNumber(to)} (type: $messageType, ContentSid: $contentSid)", ex)
            throw RuntimeException("Failed to send WhatsApp message: ${ex.message}", ex)
        }
    }

    /**
     * Template info holder
     */
    private data class TemplateInfo(
        val id: String,
        val displayName: String,
        val contentSid: String,
        val previewText: String
    )

    /**
     * Get template info for user's preferred template or default
     */
    private fun getTemplateInfoForUser(messageType: SmsMessageType, messageContext: MessagingService.MessageContext?): TemplateInfo {
        return try {
            // Try to get salon preference if we have salon context
            val salonIdObj = messageContext?.salonId
            if (salonIdObj != null) {
                val salonId = salonIdObj.value
                logger.debug("Getting preferred template for salon: {}, messageType: {}", salonId, messageType)

                // Get the template that salon has selected
                val template = whatsAppTemplateService.getPreferredTemplateForSalon(salonId, messageType)
                if (template != null) {
                    logger.info("Using salon's preferred template: ${template.displayName} for type $messageType")
                    return TemplateInfo(
                        id = template.id,
                        displayName = template.displayName,
                        contentSid = template.contentSid,
                        previewText = template.previewText
                    )
                }
            }

            // Fall back to default
            logger.debug("No salon preference, using default template for messageType: {}", messageType)
            getDefaultTemplateInfo(messageType)
        } catch (e: Exception) {
            logger.warn("Failed to get salon template preference for messageType: $messageType, falling back to default", e)
            getDefaultTemplateInfo(messageType)
        }
    }

    /**
     * Get ContentSid based on user preference or default (legacy method)
     */
    private fun getContentSidForUser(messageType: SmsMessageType, messageContext: MessagingService.MessageContext?): String {
        return getTemplateInfoForUser(messageType, messageContext).contentSid
    }

    /**
     * Build ContentVariables JSON string from MessageContext
     */
    private fun buildContentVariables(messageContext: MessagingService.MessageContext?): String {
        if (messageContext == null) {
            logger.warn("No message context provided, using empty variables")
            return "{}"
        }

        val variables = mutableMapOf<String, String>()

        // Add all available variables from context
        messageContext.clientName?.let { variables["OWNER_NAME"] = it }
        messageContext.petName?.let { variables["PET_NAME"] = it }
        messageContext.salonName?.let { variables["SALON_NAME"] = it }
        messageContext.salonAddress?.let { variables["SALON_ADDRESS"] = it }
        messageContext.salonPhone?.let { variables["SALON_PHONE"] = it }
        messageContext.appointmentDate?.let { variables["APPOINTMENT_DATE"] = it }
        messageContext.appointmentTime?.let { variables["APPOINTMENT_TIME"] = it }
        variables["GOOGLE_LINK"] = messageContext.googleLink ?: ""

        val jsonVariables = objectMapper.writeValueAsString(variables)
        logger.info("📝 Built ContentVariables: $jsonVariables")

        return jsonVariables
    }

    private fun requireWhatsappEnabled(to: String) {
        if (!featureFlagService.isFeatureEnabled("whatsapp_enabled")) {
            logger.warn("WhatsApp feature is disabled via feature flag. Message not sent to $to")
            throw IllegalStateException("WhatsApp messaging is currently disabled")
        }
    }

    /**
     * Get default Twilio Content Template SID for each message type
     * These are fallback values when user has no preferences set
     */
    private fun getDefaultTemplateInfo(type: SmsMessageType): TemplateInfo {
        return whatsAppTemplateService.getAvailableTemplatesForType(type).first().let { template ->
            TemplateInfo(
                id = template.id,
                displayName = template.displayName,
                contentSid = template.contentSid,
                previewText = template.previewText
            )
        }
    }

    override fun getChannelType(): MessagingChannelType = MessagingChannelType.WHATSAPP

    /**
     * Format phone number to E.164 format if not already formatted.
     * Example: 0712345678 -> +40712345678
     */
    private fun formatPhoneNumber(phoneNumber: String): String {
        val cleaned = phoneNumber.replace(Regex("[^0-9+]"), "")

        return when {
            cleaned.startsWith("+") -> cleaned
            cleaned.startsWith("00") -> "+${cleaned.substring(2)}"
            cleaned.startsWith("0") -> "+40${cleaned.substring(1)}" // Romania country code
            else -> "+$cleaned"
        }
    }
}
