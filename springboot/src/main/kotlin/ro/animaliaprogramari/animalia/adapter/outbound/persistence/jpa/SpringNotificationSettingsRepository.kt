package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationSettings
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationSettingsId

/**
 * Spring Data JPA repository for notification settings
 */
@Repository
interface SpringNotificationSettingsRepository : JpaRepository<NotificationSettings, NotificationSettingsId> {
    /**
     * Find notification settings by user ID and salon ID
     */
    fun findByUserIdAndSalonId(userId: String, salonId: String): NotificationSettings?

    /**
     * Check if notification settings exist for a user in a salon
     */
    fun existsByUserIdAndSalonId(userId: String, salonId: String): Boolean

    /**
     * Delete notification settings by user ID and salon ID
     */
    fun deleteByUserIdAndSalonId(userId: String, salonId: String)

    /**
     * Find all notification settings for a salon (for admin purposes)
     */
    fun findBySalonId(salonId: String): List<NotificationSettings>
}
