package ro.animaliaprogramari.animalia.adapter.inbound.event

import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.MulticastMessage
import com.google.firebase.messaging.Notification
import org.springframework.stereotype.Service

@Service
class FirebasePushService(
    private val fcmTokenRepository: FcmTokenRepository,
    private val firebaseMessaging: FirebaseMessaging,
) {
    fun sendToStaff(
        salonId: String,
        title: String,
        body: String,
    ) {
        // Get only active staff FCM tokens for salon
        val tokens: List<String> = fcmTokenRepository.findBySalonIdIgnoreCaseAndIsActiveTrue(salonId).map { it.fcmToken }
        
        println("Sending notification to salon $salonId: found ${tokens.size} active tokens")

        val message: MulticastMessage =
            MulticastMessage.builder()
                .setNotification(
                    Notification.builder()
                        .setTitle(title)
                        .setBody(body)
                        .build(),
                )
                .addAllTokens(tokens)
                .build()

        try {
            val response = firebaseMessaging.sendEachForMulticast(message)
            // Log results if needed
            if (response.failureCount > 0) {
                response.responses.forEachIndexed { index, sendResponse ->
                    if (!sendResponse.isSuccessful) {
                        println("Failed to send to token ${tokens[index]}: ${sendResponse.exception?.message}")
                    }
                }
            }
        } catch (e: Exception) {
            println("Error sending push notifications: ${e.message}")
        }
    }
}
