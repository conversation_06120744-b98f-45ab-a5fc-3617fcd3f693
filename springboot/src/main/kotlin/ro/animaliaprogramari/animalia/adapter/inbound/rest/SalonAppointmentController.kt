package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.exception.GlobalExceptionHandler
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.SalonAppointmentDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.AppointmentManagementUseCase
import ro.animaliaprogramari.animalia.application.usecase.AppointmentSubscriptionUseCase
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.AppointmentSchedulingConflictException
import ro.animaliaprogramari.animalia.domain.exception.DomainException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.exception.UnauthorizedException
import ro.animaliaprogramari.animalia.domain.model.RecurrencePeriod
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository

/**
 * REST controller for salon-specific appointment management
 * Implements the API contract for salon-based appointment operations
 */
@RestController
@RequestMapping("/salons/{salonId}/appointments")
@Tag(name = "Salon Appointments", description = "Manage appointments within a salon")
class SalonAppointmentController(
    private val appointmentManagementUseCase: AppointmentManagementUseCase,
    private val appointmentSubscriptionUseCase: AppointmentSubscriptionUseCase,
    private val appointmentDtoMapper: SalonAppointmentDtoMapper,
    private val staffRepository: StaffRepository,
) {
    private val logger = LoggerFactory.getLogger(SalonAppointmentController::class.java)

    /**
     * SECURITY: Validates that the current user has access to the specified salon
     * This prevents users from accessing other salons' data by manipulating the salonId in URLs
     */
    private fun validateSalonAccess(salonId: String, currentUser: AuthenticatedUser) {
        val staffMember = staffRepository.findByUserIdAndSalonId(currentUser.userId, SalonId.of(salonId))

        if (staffMember == null || !staffMember.isActive) {
            logger.warn("SECURITY VIOLATION: User ${currentUser.userId.value} attempted to access salon $salonId without permission")
            throw UnauthorizedException("You do not have access to this salon")
        }
    }

    @PostMapping
    @Operation(summary = "Create appointment", description = "Schedule a new appointment for a salon")
    @SwaggerApiResponse(responseCode = "201", description = "Appointment scheduled successfully")
    @SwaggerApiResponse(responseCode = "409", description = "Scheduling conflict")
    fun createAppointment(
        @PathVariable salonId: String,
        @Valid @RequestBody request: ScheduleAppointmentRequest,
    ): ResponseEntity<ApiResponse<AppointmentResponse>> {
        val start = System.currentTimeMillis()
        logger.info("Creating appointment in salon: $salonId")
        logger.info(
            "Request: Date={}, Time={}-{}, Staff={}",
            request.appointmentDate,
            request.startTime,
            request.endTime,
            request.staffId,
        )

        val currentUser = SecurityUtils.getCurrentUser()
        if (currentUser == null) {
            logger.warn("AUTHENTICATION FAILED: No current user found")
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))
        }

        // CRITICAL SECURITY CHECK: Validate user has access to this salon
        try {
            validateSalonAccess(salonId, currentUser)
        } catch (e: UnauthorizedException) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Access denied"))
        }

        // Handle client and pet ID scenarios
        val isNewClient = request.clientId.startsWith("new-")
        val isNewPet = request.petId?.startsWith("new-") == true
        logger.info("Entity creation flags: isNewClient={}, isNewPet={}", isNewClient, isNewPet)

        // Validate required fields for new entities
        if (isNewClient && request.clientPhone == null) {
            logger.warn("VALIDATION FAILED: Client phone required for new client")
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Client phone are required for new clients"))
        }

        if (isNewPet && (request.petName.isNullOrBlank() || request.petBreed.isNullOrBlank())) {
            logger.warn("VALIDATION FAILED: Pet name and breed required for new pet")
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Pet name and species are required for new pets"))
        }

        val clientName =
            if (request.clientName.isNullOrBlank()) {
                "client-" + request.clientPhone
            } else {
                request.clientName
            }

        val command =
            ScheduleAppointmentCommand(
                salonId = SalonId.of(salonId),
                clientId = if (isNewClient) ClientId.generate() else ClientId.of(request.clientId),
                clientName = clientName,
                clientPhone = request.clientPhone,
                petId =
                    if (isNewPet) {
                        PetId.generate()
                    } else {
                        request.petId?.takeIf { it.isNotBlank() }
                            ?.let { PetId.of(it) }
                    },
                petName = request.petName,
                petSpecies = request.petSpecies,
                petBreed = request.petBreed,
                petSize = request.petSize,
                staffId = StaffId.of(request.staffId),
                appointmentDate = request.appointmentDate,
                startTime = request.startTime,
                endTime = request.endTime,
                serviceIds = request.serviceIds.map { ServiceId.of(it) },
                customServices = request.customServices?.mapKeys { ServiceId.of(it.key) },
                notes = request.notes,
                // Enhanced recurrence fields
                recurrenceFrequency = request.recurrenceFrequency,
                recurrencePeriod = request.recurrencePeriod?.let {
                    RecurrencePeriod.valueOf(it.uppercase())
                },
                totalRepetitions = request.totalRepetitions,
                // Payment model fields
                paymentModel = request.paymentModel?.let { PaymentModel.fromString(it) },
                discountPercentage = request.discountPercentage,
                isNewClient = isNewClient,
                isNewPet = isNewPet,
            )



        try {
            val appointment = appointmentManagementUseCase.scheduleAppointment(command)

            logger.info("Appointment created successfully: ID={}", appointment.id.value)
            val response = appointmentDtoMapper.toResponse(appointment)
            logger.info("Created in ${System.currentTimeMillis() - start}ms")
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(response))
        } catch (e: AppointmentSchedulingConflictException) {
            return GlobalExceptionHandler.responseEntity(e)
        }
    }

    @PutMapping("/{appointmentId}/photos")
    fun updateAppointmentPhotos(
        @PathVariable salonId: String,
        @PathVariable appointmentId: String,
        @RequestBody request: Map<String, List<String>>,
    ): ResponseEntity<ApiResponse<AppointmentResponse>> {
        logger.info("REST request to update photos for appointment: $appointmentId in salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val photos = request["photos"] ?: emptyList()

            val command =
                UpdateAppointmentPhotosCommand(
                    appointmentId = AppointmentId.of(appointmentId),
                    salonId = SalonId.of(salonId),
                    photos = photos,
                    updaterUserId = currentUser.userId,
                )

            val result = appointmentManagementUseCase.updateAppointmentPhotos(command)
            val response = appointmentDtoMapper.toResponse(result)
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error updating appointment photos", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to update appointment photos"))
        } catch (e: Exception) {
            logger.error("Unexpected error updating appointment photos", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }


    @GetMapping
    fun getSalonAppointments(
        @PathVariable salonId: String,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate?,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDate: LocalDate?,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDate: LocalDate?,
        @RequestParam(required = false) status: String?,
        @RequestParam(required = false) clientId: String?,
        @RequestParam(required = false) staffId: String?,
    ): ResponseEntity<ApiResponse<List<AppointmentResponse>>> {
//        logger.info("REST request to get appointments for salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // CRITICAL SECURITY CHECK: Validate user has access to this salon
            try {
                validateSalonAccess(salonId, currentUser)
            } catch (e: UnauthorizedException) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.message ?: "Access denied"))
            }

            val query =
                GetSalonAppointmentsQuery(
                    salonId = SalonId.of(salonId),
                    date = date,
                    startDate = startDate,
                    endDate = endDate,
                    status = status?.let { AppointmentStatus.valueOf(it.uppercase()) },
                    clientId = clientId?.let { ClientId.of(it) },
                    staffId = staffId?.let { StaffId.of(it) },
                    requesterId = currentUser.userId,
                )

            val appointments = appointmentManagementUseCase.getSalonAppointments(query)
            val responses =
                appointments.mapNotNull { appointment ->
                    try {
                        appointmentDtoMapper.toResponse(appointment)
                    } catch (e: Exception) {
                        logger.warn("Failed to map appointment {} to response: {}", appointment.id.value, e.message)
                        null // Skip this appointment instead of failing the entire request
                    }
                }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: DomainException) {
            logger.warn("Domain error getting appointments", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to get appointments", "SCHEDULING_CONFLICT"))
        } catch (e: Exception) {
            logger.error("Unexpected error getting appointments", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    @GetMapping("/{appointmentId}")
    fun getAppointmentById(
        @PathVariable salonId: String,
        @PathVariable appointmentId: String,
    ): ResponseEntity<ApiResponse<AppointmentResponse>> {
        logger.info("REST request to get appointment: $appointmentId in salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // CRITICAL SECURITY CHECK: Validate user has access to this salon
            try {
                validateSalonAccess(salonId, currentUser)
            } catch (e: UnauthorizedException) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.message ?: "Access denied"))
            }

            val query =
                GetAppointmentByIdQuery(
                    appointmentId = AppointmentId.of(appointmentId),
                    salonId = SalonId.of(salonId),
                    requesterId = currentUser.userId,
                )

            val appointment = appointmentManagementUseCase.getAppointmentById(query)

            if (appointment != null) {
                val response = appointmentDtoMapper.toResponse(appointment)
                ResponseEntity.ok(ApiResponse.success(response))
            } else {
                ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("Appointment not found"))
            }
        } catch (e: DomainException) {
            logger.warn("Domain error getting appointment", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to get appointment"))
        } catch (e: Exception) {
            logger.error("Unexpected error getting appointment", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    @PutMapping("/{appointmentId}")
    fun updateAppointment(
        @PathVariable salonId: String,
        @PathVariable appointmentId: String,
        @Valid @RequestBody request: UpdateAppointmentRequest,
    ): ResponseEntity<ApiResponse<AppointmentResponse>> {
        logger.info("REST request to update appointment: $appointmentId in salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // CRITICAL SECURITY CHECK: Validate user has access to this salon
            try {
                validateSalonAccess(salonId, currentUser)
            } catch (e: UnauthorizedException) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.message ?: "Access denied"))
            }

            val command =
                UpdateAppointmentCommand(
                    appointmentId = AppointmentId.of(appointmentId),
                    salonId = SalonId.of(salonId),
                    clientId = request.clientId?.let { ClientId.of(it) },
                    petId = request.petId?.let { PetId.of(it) },
                    staffId = request.staffId?.let { StaffId.of(it) },
                    appointmentDate = request.appointmentDate,
                    startTime = request.startTime,
                    endTime = request.endTime,
                    serviceIds = request.serviceIds?.map { ServiceId.of(it) },
                    notes = request.notes,
                    updaterUserId = currentUser.userId,
                )

            val appointment = appointmentManagementUseCase.updateAppointment(command)
            val response = appointmentDtoMapper.toResponse(appointment)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error updating appointment", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to update appointment"))
        } catch (e: Exception) {
            logger.error("Unexpected error updating appointment", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    @DeleteMapping("/{appointmentId}")
    fun deleteAppointment(
        @PathVariable salonId: String,
        @PathVariable appointmentId: String,
    ): ResponseEntity<ApiResponse<Boolean>> {
        logger.info("REST request to delete appointment: $appointmentId in salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // CRITICAL SECURITY CHECK: Validate user has access to this salon
            try {
                validateSalonAccess(salonId, currentUser)
            } catch (e: UnauthorizedException) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.message ?: "Access denied"))
            }

            val command =
                DeleteAppointmentCommand(
                    appointmentId = AppointmentId.of(appointmentId),
                    salonId = SalonId.of(salonId),
                    deleterUserId = currentUser.userId,
                )

            val result = appointmentManagementUseCase.deleteAppointment(command)
            ResponseEntity.ok(ApiResponse.success(result))
        } catch (e: DomainException) {
            logger.warn("Domain error deleting appointment", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to delete appointment"))
        } catch (e: Exception) {
            logger.error("Unexpected error deleting appointment", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    @PutMapping("/{appointmentId}/cancel")
    fun cancelAppointment(
        @PathVariable salonId: String,
        @PathVariable appointmentId: String,
        @RequestBody(required = false) request: CancelAppointmentRequest?,
    ): ResponseEntity<ApiResponse<AppointmentResponse>> {
        logger.info("REST request to cancel appointment: $appointmentId in salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // CRITICAL SECURITY CHECK: Validate user has access to this salon
            try {
                validateSalonAccess(salonId, currentUser)
            } catch (e: UnauthorizedException) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.message ?: "Access denied"))
            }

            val command =
                CancelAppointmentCommand(
                    appointmentId = AppointmentId.of(appointmentId),
                    salonId = SalonId.of(salonId),
                    reason = request?.reason,
                    cancellerUserId = currentUser.userId,
                )

            val appointment = appointmentManagementUseCase.cancelAppointment(command)
            val response = appointmentDtoMapper.toResponse(appointment)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error cancelling appointment", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to cancel appointment"))
        } catch (e: Exception) {
            logger.error("Unexpected error cancelling appointment", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    @PutMapping("/{appointmentId}/complete")
    fun completeAppointment(
        @PathVariable salonId: String,
        @PathVariable appointmentId: String,
        @RequestBody(required = false) request: CompleteAppointmentRequest?,
    ): ResponseEntity<ApiResponse<AppointmentResponse>> {
        logger.info("REST request to complete appointment: $appointmentId in salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // CRITICAL SECURITY CHECK: Validate user has access to this salon
            try {
                validateSalonAccess(salonId, currentUser)
            } catch (e: UnauthorizedException) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.message ?: "Access denied"))
            }

            val command =
                CompleteAppointmentCommand(
                    appointmentId = AppointmentId.of(appointmentId),
                    salonId = SalonId.of(salonId),
                    notes = request?.notes,
                    actualServices = request?.actualServiceIds?.map { ServiceId.of(it) },
                    completedAt = request?.completedAt,
                    completerUserId = currentUser.userId,
                )

            val appointment = appointmentManagementUseCase.completeAppointment(command)
            val response = appointmentDtoMapper.toResponse(appointment)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error completing appointment", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to complete appointment"))
        } catch (e: Exception) {
            logger.error("Unexpected error completing appointment", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    @PutMapping("/{appointmentId}/reschedule")
    fun rescheduleAppointment(
        @PathVariable salonId: String,
        @PathVariable appointmentId: String,
        @Valid @RequestBody request: RescheduleAppointmentRequest,
    ): ResponseEntity<ApiResponse<AppointmentResponse>> {
        logger.info("REST request to reschedule appointment: $appointmentId in salon: $salonId")
        val start = System.currentTimeMillis()
        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // CRITICAL SECURITY CHECK: Validate user has access to this salon
            try {
                validateSalonAccess(salonId, currentUser)
            } catch (e: UnauthorizedException) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(e.message ?: "Access denied"))
            }

            val command =
                RescheduleAppointmentCommand(
                    appointmentId = AppointmentId.of(appointmentId),
                    salonId = SalonId.of(salonId),
                    newDate = request.startTime.toLocalDate(),
                    newStartTime = request.startTime.toLocalTime(),
                    newEndTime = request.endTime.toLocalTime(),
                    newStaffId = request.staffId?.let { StaffId.of(it) },
                    reason = request.reason,
                    reschedulerUserId = currentUser.userId,
                )

            val appointment = appointmentManagementUseCase.rescheduleAppointment(command)
            val response = appointmentDtoMapper.toResponse(appointment)

            logger.info("Rescheduled in ${System.currentTimeMillis() - start}ms")
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error rescheduling appointment", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to reschedule appointment"))
        } catch (e: Exception) {
            logger.error("Unexpected error rescheduling appointment", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    /**
     * GET /salons/{salonId}/appointments/{appointmentId}/subscription
     * Get subscription information for an appointment
     */
    @GetMapping("/{appointmentId}/subscription")
    @Operation(
        summary = "Get appointment subscription",
        description = "Get subscription information for a specific appointment"
    )
    @SwaggerApiResponse(responseCode = "200", description = "Subscription retrieved successfully")
    @SwaggerApiResponse(responseCode = "404", description = "Appointment or subscription not found")
    fun getAppointmentSubscription(
        @PathVariable salonId: String,
        @PathVariable appointmentId: String
    ): ResponseEntity<ApiResponse<AppointmentSubscriptionDto>> {
        return try {
            val subscription = appointmentSubscriptionUseCase.getSubscriptionByAppointmentId(
                AppointmentId.of(appointmentId),
                SalonId.of(salonId)
            )

            if (subscription != null) {
                ResponseEntity.ok(ApiResponse.success(AppointmentSubscriptionDto.fromDomain(subscription)))
            } else {
                ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("No subscription found for this appointment"))
            }
        } catch (e: EntityNotFoundException) {
            logger.warn("Appointment not found: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Appointment not found"))
        } catch (e: Exception) {
            logger.error("Failed to get appointment subscription", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }
}

/**
 * REST controller for client-specific appointment management
 * Handles the /salons/{salonId}/clients/{clientId}/appointments endpoint
 */
@RestController
@RequestMapping("/salons/{salonId}/clients/{clientId}/appointments")
@Tag(name = "Client Appointments", description = "Manage appointments for specific clients")
class ClientAppointmentController(
    private val appointmentManagementUseCase: AppointmentManagementUseCase,
    private val appointmentDtoMapper: SalonAppointmentDtoMapper,
) {
    private val logger = LoggerFactory.getLogger(ClientAppointmentController::class.java)

    /**
     * GET /salons/{salonId}/clients/{clientId}/appointments
     * Get all appointments for a specific client in a salon
     */
    @GetMapping
    @Operation(
        summary = "Get client appointments",
        description = "Get all appointments for a specific client in a salon",
    )
    @SwaggerApiResponse(responseCode = "200", description = "Appointments retrieved successfully")
    @SwaggerApiResponse(responseCode = "404", description = "Client not found")
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun getClientAppointments(
        @PathVariable salonId: String,
        @PathVariable clientId: String,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate?,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDate: LocalDate?,
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDate: LocalDate?,
        @RequestParam(required = false) status: String?,
        @RequestParam(required = false) petId: String?,
    ): ResponseEntity<ApiResponse<List<AppointmentResponse>>> {
        logger.info("REST request to get appointments for client: $clientId in salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val query =
                GetClientAppointmentsQuery(
                    salonId = SalonId.of(salonId),
                    clientId = ClientId.of(clientId),
                    date = date,
                    startDate = startDate,
                    endDate = endDate,
                    status = status?.let { AppointmentStatus.valueOf(it.uppercase()) },
                    petId = petId?.let { PetId.of(it) },
                    requesterId = currentUser.userId,
                )

            val appointments = appointmentManagementUseCase.getClientAppointments(query)
            val responses =
                appointments.mapNotNull { appointment ->
                    try {
                        appointmentDtoMapper.toResponse(appointment)
                    } catch (e: Exception) {
                        logger.warn("Failed to map appointment {} to response: {}", appointment.id.value, e.message)
                        null // Skip this appointment instead of failing the entire request
                    }
                }

            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: DomainException) {
            logger.warn("Domain error getting client appointments", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to get client appointments", "DOMAIN_ERROR"))
        } catch (e: UnauthorizedException) {
            logger.warn("Unauthorized access to client appointments", e)
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Access denied", "UNAUTHORIZED"))
        } catch (e: EntityNotFoundException) {
            logger.warn("Client not found", e)
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Client not found", "NOT_FOUND"))
        } catch (e: Exception) {
            logger.error("Unexpected error getting client appointments", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get client appointments"))
        }
    }
}
