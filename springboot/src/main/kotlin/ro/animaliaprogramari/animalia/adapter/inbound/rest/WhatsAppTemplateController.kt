package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.service.WhatsAppTemplateService
import ro.animaliaprogramari.animalia.domain.model.UpdateWhatsAppTemplatePreferenceRequest
import ro.animaliaprogramari.animalia.domain.model.WhatsAppTemplate
import ro.animaliaprogramari.animalia.domain.model.WhatsAppTemplatePreferenceDomain
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for WhatsApp template management
 */
@RestController
@RequestMapping("/whatsapp-templates")
@Tag(name = "WhatsApp Templates", description = "WhatsApp template management operations")
class WhatsAppTemplateController(
    private val whatsAppTemplateService: WhatsAppTemplateService
) {
    private val logger = LoggerFactory.getLogger(WhatsAppTemplateController::class.java)

    @GetMapping
    @Operation(
        summary = "Get available WhatsApp templates (optionally filtered by type)",
        description = "Retrieves available WhatsApp templates. Provide `type` query param to filter by message type."
    )
    @SwaggerApiResponse(responseCode = "200", description = "Available templates retrieved successfully")
    fun getAvailableTemplatesByType(@RequestParam(required = false) type: String?): ResponseEntity<ApiResponse<List<WhatsAppTemplate>>> {
        logger.info("Getting available WhatsApp templates (type={})", type)

        return try {
            if (type.isNullOrBlank()) {
                val templates = whatsAppTemplateService.getAvailableTemplates()
                ResponseEntity.ok(ApiResponse.success(templates))
            } else {
                // Try to parse the provided type to the internal enum (SmsMessageType is used internally)
                val smsType = try {
                    ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType.valueOf(type)
                } catch (ex: IllegalArgumentException) {
                    return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Invalid message type: $type"))
                }

                val templates = whatsAppTemplateService.getAvailableTemplatesForType(smsType)
                ResponseEntity.ok(ApiResponse.success(templates))
            }
        } catch (e: Exception) {
            logger.error("Error getting available WhatsApp templates (type={})", type, e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get available templates: ${e.message}"))
        }
    }

    @GetMapping("/available")
    @Operation(
        summary = "Get available WhatsApp templates",
        description = "Retrieves all available WhatsApp message templates for selection"
    )
    @SwaggerApiResponse(responseCode = "200", description = "Available templates retrieved successfully")
    fun getAvailableTemplates(): ResponseEntity<ApiResponse<List<WhatsAppTemplate>>> {
        logger.info("Getting available WhatsApp templates")

        return try {
            val templates = whatsAppTemplateService.getAvailableTemplates()
            ResponseEntity.ok(ApiResponse.success(templates))
        } catch (e: Exception) {
            logger.error("Error getting available WhatsApp templates", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get available templates: ${e.message}"))
        }
    }

    @GetMapping("/preferences")
    @Operation(
        summary = "Get salon's template preferences",
        description = "Retrieves the current salon's WhatsApp template preferences for each message type"
    )
    @SwaggerApiResponse(responseCode = "200", description = "Template preferences retrieved successfully")
    fun getTemplatePreferences(): ResponseEntity<ApiResponse<List<WhatsAppTemplatePreferenceDomain>>> {
        val currentUser = SecurityUtils.getCurrentUser()
            ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error("User not authenticated"))

        val salonId = currentUser.currentSalonId?.value
            ?: return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("No salon selected for current user"))

        logger.info("Getting WhatsApp template preferences for salon: $salonId")

        return try {
            // Load available templates and existing preferences
            val availableTemplates = whatsAppTemplateService.getAvailableTemplates()
            val preferencesFromDb = whatsAppTemplateService.getSalonTemplatePreferences(salonId)
                .associateBy { it.templateType }

            // Group available templates by their templateType (string)
            val availableByType = availableTemplates.groupBy { it.templateType }

            val mergedPreferences = mutableListOf<WhatsAppTemplatePreferenceDomain>()

            // For each available type, use saved preference if present, otherwise choose default/first
            for ((type, templates) in availableByType) {
                val existing = preferencesFromDb[type]
                if (existing != null) {
                    mergedPreferences.add(existing)
                } else if (templates.isNotEmpty()) {
                    val chosen = templates.find { it.isDefault } ?: templates.first()
                    mergedPreferences.add(
                        WhatsAppTemplatePreferenceDomain(
                            id = "",
                            salonId = salonId,
                            templateType = type,
                            whatsappTemplateId = chosen.id,
                            contentSid = chosen.contentSid
                        )
                    )
                }
            }

            // Include any saved preferences for types not present in AVAILABLE_TEMPLATES
            for ((type, pref) in preferencesFromDb) {
                if (!availableByType.containsKey(type)) {
                    mergedPreferences.add(pref)
                }
            }

            ResponseEntity.ok(ApiResponse.success(mergedPreferences))
        } catch (e: Exception) {
            logger.error("Error getting template preferences for salon: $salonId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to get template preferences: ${e.message}"))
        }
    }

    @PutMapping("/preferences")
    @Operation(
        summary = "Update template preference",
        description = "Updates the salon's preferred template for a specific message type"
    )
    @SwaggerApiResponse(responseCode = "200", description = "Template preference updated successfully")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data")
    fun updateTemplatePreference(
        @Valid @RequestBody request: UpdateWhatsAppTemplatePreferenceRequest
    ): ResponseEntity<ApiResponse<WhatsAppTemplatePreferenceDomain>> {
        val currentUser = SecurityUtils.getCurrentUser()
            ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error("User not authenticated"))

        val salonId = currentUser.currentSalonId?.value
            ?: return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("No salon selected for current user"))

        logger.info("Updating WhatsApp template preference for salon: $salonId, type: ${request.templateType}")

        return try {
            val preference = whatsAppTemplateService.updateTemplatePreference(salonId, request)
            ResponseEntity.ok(ApiResponse.success(preference))
        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid request for updating template preference: ${e.message}")
            ResponseEntity.badRequest()
                .body(ApiResponse.error("Invalid request: ${e.message}"))
        } catch (e: Exception) {
            logger.error("Error updating template preference for salon: $salonId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to update template preference: ${e.message}"))
        }
    }
}
