package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.rest.core.annotation.RepositoryRestResource
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.MassSmsCampaignEntity
import ro.animaliaprogramari.animalia.domain.model.SalonId

@Repository
@RepositoryRestResource(exported = false)
interface SpringMassSmsCampaignRepository : JpaRepository<MassSmsCampaignEntity, String> {

    /**
     * Find campaigns by salon ID ordered by creation date descending
     */
    fun findBySalonIdOrderByCreatedAtDesc(salonId: SalonId, pageable: Pageable): Page<MassSmsCampaignEntity>
}
