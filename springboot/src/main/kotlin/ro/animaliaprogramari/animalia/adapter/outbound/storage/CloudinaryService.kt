package ro.animaliaprogramari.animalia.adapter.outbound.storage

import com.cloudinary.Cloudinary
import com.cloudinary.utils.ObjectUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile

@Service
class CloudinaryService(private val cloudinary: Cloudinary) {
    private val logger = LoggerFactory.getLogger(CloudinaryService::class.java)

    fun uploadImage(file: MultipartFile): String {
        logger.info("🌤️ Starting Cloudinary upload...")
        logger.info("📁 File size: ${file.size} bytes")
        logger.info("📋 Content type: ${file.contentType}")

        return try {
            val result = cloudinary.uploader().upload(file.bytes, ObjectUtils.emptyMap())
            val secureUrl = result["secure_url"] as String
            logger.info("✅ Cloudinary upload successful: $secureUrl")
            logger.info("📊 Upload result keys: ${result.keys}")
            secureUrl
        } catch (e: Exception) {
            logger.error("❌ Cloudinary upload failed: ${e.message}", e)
            logger.error("💥 Exception type: ${e.javaClass.simpleName}")
            throw e
        }
    }
}
