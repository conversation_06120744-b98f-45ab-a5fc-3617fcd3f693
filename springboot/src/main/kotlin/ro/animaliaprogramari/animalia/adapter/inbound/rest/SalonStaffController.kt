package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.JpaStaffRepository
import ro.animaliaprogramari.animalia.application.command.SendSalonInvitationCommand
import ro.animaliaprogramari.animalia.application.port.inbound.InvitationManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.SalonManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.SalonStaffManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.model.*
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for salon staff management operations
 */
@RestController
@RequestMapping("/salons/{salonId}/staff")
@Tag(name = "Salon Staff Management", description = "Operations for managing staff within salons")
class SalonStaffController(
    private val salonStaffManagementUseCase: SalonStaffManagementUseCase,
    private val salonManagementUseCase: SalonManagementUseCase,
    private val invitationManagementUseCase: InvitationManagementUseCase,
    private val userRepository: UserRepository,
    private val jpaStaffRepository: JpaStaffRepository,
) {
    private val logger = LoggerFactory.getLogger(SalonStaffController::class.java)

    /**
     * GET /api/salons/{salonId}/staff
     * Get all staff for a salon
     */
    @GetMapping
    @Operation(summary = "Get salon staff", description = "Get all staff members for a specific salon")
    @SwaggerApiResponse(responseCode = "200", description = "Staff list retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun getSalonStaff(
        @PathVariable salonId: String,
        @RequestParam(required = false, defaultValue = "true") activeOnly: Boolean,
        @RequestParam(required = false) search: String?,
    ): ResponseEntity<ApiResponse<List<SalonStaffResponse>>> {

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            logger.info("Current user: ${currentUser.userId.value}")

            val salon = SalonId.of(salonId)
            val staff = jpaStaffRepository.findByUserIdAndSalonId(userId = currentUser.userId, salonId = salon)?.let { true } ?: false

            // Authorization: User must have access to this salon
            // MODIFIED: Allow any user with a salon association to access staff list
            // This includes users with NO_ACCESS client data permission
            if (!staff && !currentUser.isAdmin()) {
                logger.warn("User ${currentUser.userId.value} denied access to salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să accesați această resursă"))
            }

            val staffList = salonStaffManagementUseCase.getSalonStaff(salon, activeOnly, search)
            val responses =
                staffList.map { staffInfo ->
                    SalonStaffResponse(staffInfo.staff, staffInfo)
                }

            logger.info("Found ${responses.size} staff members")
            ResponseEntity.ok(ApiResponse.success(responses))
        } catch (e: Exception) {
            logger.error("Error getting salon staff", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la obținerea angajaților: ${e.message}"))
        }
    }

    /**
     * POST /api/salons/{salonId}/staff
     * Add staff to salon by phone number (existing user) or send invitation (new user)
     */
    @PostMapping
    @Operation(
        summary = "Add staff to salon",
        description = "Add staff to salon by phone number. If user exists, adds them directly. If user doesn't exist, sends an invitation.",
    )
    @SwaggerApiResponse(responseCode = "201", description = "Staff added successfully (existing user)")
    @SwaggerApiResponse(responseCode = "202", description = "Invitation sent successfully (new user)")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data or user already staff")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun addStaffToSalon(
        @PathVariable salonId: String,
        @Valid @RequestBody request: AddStaffToSalonRequest,
    ): ResponseEntity<ApiResponse<Any>> {
        logger.info("=== ADD STAFF TO SALON REQUEST ===")
        logger.info("Salon ID: $salonId")
        logger.info("Phone Number: ${request.phoneNumber}")
        logger.info("Groomer Role: ${request.groomerRole}")
        logger.info("Client Data Permission: ${request.clientDataPermission}")
        logger.info("Notes: ${request.notes}")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            logger.info("Current user: ${currentUser.userId.value}")

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers or admins can add staff
            if (!currentUser.isChiefGroomerInSalon(salon) && !currentUser.isAdmin()) {
                logger.warn("User ${currentUser.userId.value} denied permission to add staff to salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să adăugați angajați în acest salon"))
            }

            // Validate phone number format - accept international phone numbers
            if (!PhoneNumber.isValidInternationalFormat(request.phoneNumber)) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(
                        ApiResponse.error(
                            "Formatul numărului de telefon nu este valid. Folosiți formatul internațional: +[cod țară][număr]",
                        ),
                    )
            }

            // Validate and map groomer role
            val staffRole =
                try {
                    when (request.groomerRole.uppercase()) {
                        "CHIEF_GROOMER" -> StaffRole.CHIEF_GROOMER
                        "REGULAR_GROOMER", "GROOMER" -> StaffRole.GROOMER
                        "ASSISTANT" -> StaffRole.ASSISTANT
                        else -> throw IllegalArgumentException("Invalid groomer role: ${request.groomerRole}")
                    }
                } catch (e: IllegalArgumentException) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Rolul specificat nu este valid: ${request.groomerRole}"))
                }

            // Additional authorization: Only admins can assign CHIEF_GROOMER role
            if (staffRole == StaffRole.CHIEF_GROOMER && !currentUser.isAdmin()) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Doar administratorii pot atribui rolul de Groomer Șef"))
            }

            // Validate and map client data permission
            val clientDataAccess =
                try {
                    when (request.clientDataPermission.uppercase()) {
                        "FULL_ACCESS", "FULL" -> ClientDataAccess.FULL
                        "READ_ONLY", "LIMITED_ACCESS", "LIMITED" -> ClientDataAccess.FULL
                        "NO_ACCESS", "NONE" -> ClientDataAccess.NONE
                        else -> throw IllegalArgumentException(
                            "Invalid client data permission: ${request.clientDataPermission}",
                        )
                    }
                } catch (e: IllegalArgumentException) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(
                            ApiResponse.error(
                                "Permisiunea pentru datele clientului nu este validă: ${request.clientDataPermission}",
                            ),
                        )
                }

            // Create permissions based on role and client data access
            val permissions =
                when (staffRole) {
                    StaffRole.CHIEF_GROOMER -> StaffPermissions.fullAccess()
                    StaffRole.GROOMER ->
                        StaffPermissions(
                            clientDataAccess = clientDataAccess,
                            canManageAppointments = true,
                            canManageServices = false,
                            canViewReports = clientDataAccess == ClientDataAccess.FULL,
                            canManageSchedule = true,
                        )
                    StaffRole.ASSISTANT ->
                        StaffPermissions(
                            clientDataAccess = clientDataAccess,
                            canManageAppointments = false,
                            canManageServices = false,
                            canViewReports = false,
                            canManageSchedule = false,
                        )
                }

            // Check if user already exists and is already staff
            logger.info("Checking if user exists with phone number: ${request.phoneNumber}")
            val existingUser = userRepository.findByPhoneNumber(PhoneNumber.of(request.phoneNumber))

            if (existingUser != null) {
                // Check if user is already staff in this salon
                val existingStaff =
                    salonStaffManagementUseCase.getSalonStaff(salon, false, null)
                        .find { it.staff.userId == existingUser.id }

                if (existingStaff != null && existingStaff.staff.isActive) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Utilizatorul este deja angajat în acest salon"))
                }

                // User exists but not staff - add directly
                logger.info("User exists, adding directly as staff: ${existingUser.id.value}")
                val staff =
                    salonStaffManagementUseCase.addUserToSalon(
                        salonId = salon,
                        userId = existingUser.id,
                        role = staffRole,
                        permissions = permissions,
                    )

                // Get user details for response
                val staffInfo =
                    salonStaffManagementUseCase.getSalonStaff(salon, false, null)
                        .find { it.staff.id == staff.id }
                        ?: throw RuntimeException("Staff not found after creation")

                val response = SalonStaffResponse(staff, staffInfo)

                logger.info("Staff added successfully: ${staff.id.value}")
                return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success(response))
            } else {
                // User doesn't exist - send invitation
                logger.info("User doesn't exist, sending invitation to: ${request.phoneNumber}")

                val invitationMessage = request.notes ?: "Ați fost invitat să vă alăturați echipei noastre!"

                val command =
                    SendSalonInvitationCommand(
                        salonId = salon,
                        inviterUserId = currentUser.userId,
                        invitedUserPhone = request.phoneNumber,
                        proposedRole = staffRole,
                        proposedPermissions = permissions,
                        proposedNickname = request.proposedNickname,
                        message = invitationMessage,
                    )

                val invitation = invitationManagementUseCase.sendInvitation(command)

                logger.info("Invitation sent successfully: ${invitation.id.value}")

                // Return a different response indicating invitation was sent
                return ResponseEntity.status(HttpStatus.ACCEPTED)
                    .body(
                        ApiResponse.success(
                            mapOf(
                                "type" to "invitation_sent",
                                "invitationId" to invitation.id.value,
                                "phoneNumber" to invitation.invitedUserPhone,
                                "proposedRole" to invitation.proposedRole.name,
                                "message" to "Invitația a fost trimisă cu succes",
                                "expiresAt" to invitation.expiresAt.toString(),
                            ),
                        ),
                    )
            }
        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid request: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Cerere invalidă"))
        } catch (e: BusinessRuleViolationException) {
            logger.warn("Business rule violation: ${e.message}")
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Limită de abonament atinsă"))
        } catch (e: Exception) {
            logger.error("Error adding staff to salon", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la adăugarea angajatului: ${e.message}"))
        }
    }

    /**
     * POST /api/salons/{salonId}/staff/direct
     * Create staff directly without user account
     */
    @PostMapping("/direct")
    @Operation(
        summary = "Create staff directly",
        description = "Create staff member directly without requiring a user account or invitation",
    )
    @SwaggerApiResponse(responseCode = "201", description = "Staff created successfully")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun createStaffDirectly(
        @PathVariable salonId: String,
        @Valid @RequestBody request: CreateStaffDirectlyRequest,
    ): ResponseEntity<ApiResponse<SalonStaffResponse>> {
        logger.info("=== CREATE STAFF DIRECTLY REQUEST ===")
        logger.info("Salon ID: $salonId")
        var nickname = request.nickname
        logger.info("Nickname: $nickname")
        logger.info("Groomer Role: ${request.groomerRole}")
        logger.info("Notes: ${request.notes}")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            logger.info("Current user: ${currentUser.userId.value}")

            val salon = SalonId.of(salonId)

            // Authorization: Chief groomers, admins, or groomers creating slots for themselves
            val isSlotCreation = nickname == "Slot"
            val canCreateStaff = currentUser.isChiefGroomerInSalon(salon) ||
                                currentUser.isAdmin() ||
                                (isSlotCreation && currentUser.hasAccessToSalon(salon))

            if (!canCreateStaff) {
                logger.warn("User ${currentUser.userId.value} denied permission to create staff directly in salon $salonId")
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să creați angajați direct în acest salon"))
            }

            // Validate and map groomer role
            val staffRole =
                try {
                    when (request.groomerRole.uppercase()) {
                        "CHIEF_GROOMER" -> StaffRole.CHIEF_GROOMER
                        "REGULAR_GROOMER", "GROOMER" -> StaffRole.GROOMER
                        "ASSISTANT" -> StaffRole.ASSISTANT
                        else -> throw IllegalArgumentException("Invalid groomer role: ${request.groomerRole}")
                    }
                } catch (e: IllegalArgumentException) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Rolul specificat nu este valid: ${request.groomerRole}"))
                }

            // Additional authorization: Only admins can assign CHIEF_GROOMER role
            if (!isSlotCreation && staffRole == StaffRole.CHIEF_GROOMER && !currentUser.isAdmin()) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Doar administratorii pot atribui rolul de Groomer Șef"))
            }

            if (isSlotCreation) {
                // Increment the additional slots counter for the salon
                val updatedSalon = salonManagementUseCase.incrementAdditionalSlotsCount(salon)
                nickname = "Slot ${updatedSalon.additionalSlotsCount}"
            }

            // Create permissions - for direct creation, use limited access by default
            val permissions = when (staffRole) {
                StaffRole.CHIEF_GROOMER -> StaffPermissions.fullAccess()
                StaffRole.GROOMER -> StaffPermissions(
                    clientDataAccess = ClientDataAccess.FULL,
                    canManageAppointments = true,
                    canManageServices = false,
                    canViewReports = false,
                    canManageSchedule = true,
                )
                StaffRole.ASSISTANT -> StaffPermissions(
                    clientDataAccess = ClientDataAccess.FULL,
                    canManageAppointments = false,
                    canManageServices = false,
                    canViewReports = false,
                    canManageSchedule = false,
                )
            }

            // Create staff directly without user account
            logger.info("Creating staff directly with nickname: $nickname")
            val staff = salonStaffManagementUseCase.createStaffDirectly(
                salonId = salon,
                nickname = nickname,
                role = staffRole,
                permissions = permissions,
                notes = request.notes,
            )

            // Get staff info for response
            val staffInfo = salonStaffManagementUseCase.getSalonStaff(salon, false, null)
                .find { it.staff.id == staff.id }
                ?: throw RuntimeException("Staff not found after creation")

            val response = SalonStaffResponse(staff, staffInfo)

            logger.info("Staff created directly successfully: ${staff.id.value}")
            ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(response))
        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid request: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Cerere invalidă"))
        } catch (e: BusinessRuleViolationException) {
            logger.warn("Business rule violation: ${e.message}")
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Limită de abonament atinsă"))
        } catch (e: Exception) {
            logger.error("Error creating staff directly", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la crearea angajatului: ${e.message}"))
        }
    }

    /**
     * PUT /api/salons/{salonId}/staff/{staffId}
     * Update staff role and permissions
     */
    @PutMapping("/{staffId}")
    @Operation(summary = "Update staff", description = "Update staff role and permissions")
    @SwaggerApiResponse(responseCode = "200", description = "Staff updated successfully")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "404", description = "Staff not found")
    fun updateStaff(
        @PathVariable salonId: String,
        @PathVariable staffId: String,
        @Valid @RequestBody request: UpdateStaffRequest,
    ): ResponseEntity<ApiResponse<SalonStaffResponse>> {
        logger.info("=== UPDATE STAFF REQUEST ===")
        logger.info("Salon ID: $salonId")
        logger.info("User ID: $staffId")
        logger.info("New Role: ${request.groomerRole}")
        logger.info("Client Data Permission: ${request.clientDataPermission}")
        logger.info("Notes: ${request.notes}")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers or admins can update staff
            if (!currentUser.isChiefGroomerInSalon(salon) && !currentUser.isAdmin()) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați angajații din acest salon"))
            }

            val staffRole =
                try {
                    StaffRole.valueOf(request.groomerRole.uppercase())
                } catch (e: IllegalArgumentException) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Rolul specificat nu este valid: ${request.groomerRole}"))
                }

            // Additional authorization: Only admins can assign CHIEF_GROOMER role
            if (staffRole == StaffRole.CHIEF_GROOMER && !currentUser.isAdmin()) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Doar administratorii pot atribui rolul de Groomer Șef"))
            }

            val permissions = StaffPermissions.fromClientDataPermission(request.clientDataPermission)

            val updatedStaff =
                salonStaffManagementUseCase.updateStaffRole(
                    salonId = salon,
                    staffId = StaffId.of(staffId),
                    role = staffRole,
                    permissions = permissions,
                    nickName = request.nickname,
                )

            // Get user details for response
            val staffInfo =
                salonStaffManagementUseCase.getSalonStaff(salon, false, null)
                    .find { it.staff.id == updatedStaff.id }
                    ?: throw RuntimeException("Staff not found after update")

            val response = SalonStaffResponse(updatedStaff, staffInfo)

            logger.info("Staff updated successfully: ${updatedStaff.id.value}")
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid request: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Cerere invalidă"))
        } catch (e: Exception) {
            logger.error("Error updating staff", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la actualizarea angajatului: ${e.message}"))
        }
    }


    /**
     * DELETE /api/salons/{salonId}/staff/{staffId}
     * Remove staff from salon (HARD delete)
     */
    @DeleteMapping("/{staffId}")
    @Operation(summary = "Remove staff from salon", description = "Remove staff from salon (HARD delete)")
    @SwaggerApiResponse(responseCode = "200", description = "Staff removed successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "404", description = "Staff not found")
    fun removeStaffFromSalon(
        @PathVariable salonId: String,
        @PathVariable staffId: String,
    ): ResponseEntity<ApiResponse<String>> {
        logger.info("=== REMOVE STAFF REQUEST ===")
        logger.info("Salon ID: $salonId")
        logger.info("Staff ID: $staffId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            logger.info("Current user: ${currentUser.userId.value}")

            val salon = SalonId.of(salonId)
            val targetStaffId = StaffId.of(staffId)

            // Authorization: Only chief groomers or admins can remove staff
            if (!currentUser.isChiefGroomerInSalon(salon) && !currentUser.isAdmin()) {
                logger.warn(
                    "User ${currentUser.userId.value} denied permission to remove staff from salon $salonId",
                )
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să eliminați angajații din acest salon"))
            }

            // Remove staff using the new staffId-based method
            salonStaffManagementUseCase.removeStaffFromSalon(staffId = targetStaffId)

            logger.info("Staff removed successfully: $staffId")
            ResponseEntity.ok(ApiResponse.success("Angajatul a fost eliminat cu succes"))
        } catch (e: Exception) {
            logger.error("Error removing staff from salon", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la eliminarea angajatului: ${e.message}"))
        }
    }



    /**
     * PATCH /api/salons/{salonId}/staff/{staffId}/toggle-status
     * Toggle staff status (active/inactive) within a salon
     */
    @PatchMapping("/{staffId}/toggle-status")
    @Operation(summary = "Toggle staff status", description = "Toggle staff status (active/inactive) within a salon")
    @SwaggerApiResponse(responseCode = "200", description = "Staff status toggled successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "404", description = "Staff not found")
    fun toggleStaffStatus(
        @PathVariable salonId: String,
        @PathVariable staffId: String,
    ): ResponseEntity<ApiResponse<SalonStaffResponse>> {
        logger.info("=== TOGGLE STAFF STATUS REQUEST ===")
        logger.info("Salon ID: $salonId")
        logger.info("Staff ID: $staffId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            logger.info("Current user: ${currentUser.userId.value}")

            val salon = SalonId.of(salonId)
            val targetStaffId = StaffId.of(staffId)

            // Authorization: Only chief groomers or admins can toggle staff status
            if (!currentUser.isChiefGroomerInSalon(salon) && !currentUser.isAdmin()) {
                logger.warn(
                    "User ${currentUser.userId.value} denied permission to toggle staff status in salon $salonId",
                )
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați statusul angajaților din acest salon"))
            }

            // Toggle staff status using the new staffId-based method
            val updatedStaff =
                salonStaffManagementUseCase.toggleStaffStatusByStaffId(
                    salonId = salon,
                    staffId = targetStaffId,
                )

            // Get user details for response
            val staffInfo =
                salonStaffManagementUseCase.getSalonStaff(salon, false, null)
                    .find { it.staff.id == updatedStaff.id }
                    ?: throw RuntimeException("Staff not found after status toggle")

            val response = SalonStaffResponse(updatedStaff, staffInfo)

            logger.info("Staff status toggled successfully: ${updatedStaff.id.value}")
            logger.info("New status: ${if (updatedStaff.isActive) "Active" else "Inactive"}")
            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: IllegalArgumentException) {
            logger.warn("Staff not found: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Angajatul nu a fost găsit"))
        } catch (e: Exception) {
            logger.error("Error toggling staff status", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare la modificarea statusului angajatului: ${e.message}"))
        }
    }
}
