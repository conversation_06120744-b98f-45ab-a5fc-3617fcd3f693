package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.MessagingChannelType
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.MessagingChannel
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsReminderSettings as SmsReminderSettingsEntity
import ro.animaliaprogramari.animalia.domain.model.SmsReminderSettings as SmsReminderSettingsDomain

/**
 * Mapper between SMS reminder settings domain model and JPA entity
 */
@Component
class SmsReminderSettingsEntityMapper {
    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: SmsReminderSettingsDomain): SmsReminderSettingsEntity {
        return SmsReminderSettingsEntity(
            salonId = domain.salonId.value,
            enabled = domain.enabled,
            appointmentConfirmations = domain.appointmentConfirmations,
            rescheduleNotifications = domain.rescheduleNotifications,
            cancellationNotifications = domain.cancellationNotifications,
            completionMessages = domain.completionMessages,
            followUpMessages = domain.followUpMessages,
            birthdayMessages = domain.birthdayMessages,
            messagingChannel = mapToEntityChannel(domain.messagingChannel),
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt,
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: SmsReminderSettingsEntity): SmsReminderSettingsDomain {
        return SmsReminderSettingsDomain(
            salonId = SalonId.of(entity.salonId),
            enabled = entity.enabled,
            appointmentConfirmations = entity.appointmentConfirmations,
            rescheduleNotifications = entity.rescheduleNotifications,
            cancellationNotifications = entity.cancellationNotifications,
            followUpMessages = entity.followUpMessages,
            completionMessages = entity.completionMessages ?: false,
            birthdayMessages = entity.birthdayMessages,
            messagingChannel = mapToDomainChannel(entity.messagingChannel),
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
        )
    }

    /**
     * Map domain MessagingChannelType to entity MessagingChannel
     */
    private fun mapToEntityChannel(channelType: MessagingChannelType): MessagingChannel {
        return when (channelType) {
            MessagingChannelType.SMS -> MessagingChannel.SMS
            MessagingChannelType.WHATSAPP -> MessagingChannel.WHATSAPP
        }
    }

    /**
     * Map entity MessagingChannel to domain MessagingChannelType
     */
    private fun mapToDomainChannel(channel: MessagingChannel): MessagingChannelType {
        return when (channel) {
            MessagingChannel.SMS -> MessagingChannelType.SMS
            MessagingChannel.WHATSAPP -> MessagingChannelType.WHATSAPP
        }
    }
}
