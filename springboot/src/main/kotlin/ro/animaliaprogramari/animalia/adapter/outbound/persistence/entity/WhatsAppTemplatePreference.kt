package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

/**
 * JPA entity for storing salon preferences for WhatsApp message templates
 * Each salon can select their preferred template for each message type
 */
@Entity
@Table(
    name = "whatsapp_template_preferences",
    indexes = [
        Index(name = "idx_whatsapp_prefs_salon_id", columnList = "salon_id"),
        Index(name = "idx_whatsapp_prefs_message_type", columnList = "message_type")
    ],
    uniqueConstraints = [
        UniqueConstraint(name = "uk_salon_message_type", columnNames = ["salon_id", "message_type"])
    ]
)
data class WhatsAppTemplatePreference(
    @Id
    @Column(name = "id", nullable = false)
    val id: String,

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @Enumerated(EnumType.STRING)
    @Column(name = "message_type", nullable = false)
    val messageType: SmsMessageType,

    @field:NotBlank(message = "WhatsApp template ID is required")
    @Column(name = "whatsapp_template_id", nullable = false)
    val whatsappTemplateId: String,

    @field:NotBlank(message = "Content SID is required")
    @Column(name = "content_sid", nullable = false)
    val contentSid: String,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        salonId = "",
        messageType = SmsMessageType.APPOINTMENT_CONFIRMATION,
        whatsappTemplateId = "",
        contentSid = "",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )
}
