package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.SalonEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.Salon
import ro.animaliaprogramari.animalia.domain.model.SalonId

/**
 * JPA adapter implementing the SalonRepository port
 * This adapter translates between domain models and JPA entities
 */
@Repository
class JpaSalonRepository(
    private val springRepository: SpringSalonRepository,
    private val salonMapper: SalonEntityMapper,
) : SalonRepository {
    override fun save(salon: Salon): Salon {
        val entity = salonMapper.toEntity(salon)
        val savedEntity = springRepository.save(entity)
        return salonMapper.toDomain(savedEntity)
    }

    override fun findById(id: SalonId): Salon? {
        return springRepository.findById(id.value)
            .map { salonMapper.toDomain(it) }
            .orElse(null)
    }

    override fun findAll(
        search: String?,
        isActive: Boolean?,
        limit: Int?,
        offset: Int?,
    ): List<Salon> {
        val salons = springRepository.findBySearchAndFilters(search, isActive)

        return salons
            .let { if (offset != null) it.drop(offset) else it }
            .let { if (limit != null) it.take(limit) else it }
            .map { salonMapper.toDomain(it) }
    }

    override fun existsById(id: SalonId): Boolean {
        return springRepository.existsById(id.value)
    }

    override fun deleteById(id: SalonId): Boolean {
        return if (springRepository.existsById(id.value)) {
            springRepository.deleteById(id.value)
            true
        } else {
            false
        }
    }

    override fun count(
        search: String?,
        isActive: Boolean?,
    ): Long {
        return springRepository.findBySearchAndFilters(search, isActive).size.toLong()
    }

    override fun findSalonsWithClient(clientId: ClientId): List<Salon> {
        return springRepository.findSalonsWithClient(clientId.value)
            .map { salonMapper.toDomain(it) }
    }

    override fun findAllActiveSalonIds(): List<SalonId> {
        return springRepository.findAllActiveSalonIds()
            .map { SalonId.of(it) }
    }
}
