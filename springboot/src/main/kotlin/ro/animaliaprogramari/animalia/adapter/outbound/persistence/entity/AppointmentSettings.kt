package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.time.LocalDateTime

/**
 * Entity representing appointment settings for a salon
 */
@Entity
@Table(
    name = "appointment_settings",
    indexes = [
        Index(name = "idx_appointment_settings_salon_id", columnList = "salon_id")
    ]
)
class AppointmentSettings(
    @Id
    @Column(name = "salon_id", nullable = false)
    @field:NotBlank(message = "Salon ID is required")
    var salonId: String = "",

    @Column(name = "auto_finalize_enabled", nullable = false)
    var autoFinalizeEnabled: Boolean = false,

    @Column(name = "overdue_notifications_enabled", nullable = false)
    var overdueNotificationsEnabled: Boolean = true,

    @Column(name = "sms_completion_enabled", nullable = false)
    var smsCompletionEnabled: Boolean = true,

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as AppointmentSettings

        return salonId == other.salonId
    }

    override fun hashCode(): Int {
        return salonId.hashCode()
    }

    override fun toString(): String {
        return "AppointmentSettings(" +
                "salonId='$salonId', " +
                "autoFinalizeEnabled=$autoFinalizeEnabled, " +
                "overdueNotificationsEnabled=$overdueNotificationsEnabled, " +
                "smsCompletionEnabled=$smsCompletionEnabled, " +
                "createdAt=$createdAt, " +
                "updatedAt=$updatedAt" +
                ")"
    }
}
