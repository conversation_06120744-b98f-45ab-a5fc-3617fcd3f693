package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.WorkingHoursDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.WorkingHoursManagementUseCase
import ro.animaliaprogramari.animalia.application.query.GetWorkingHoursQuery
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.DomainException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.SalonId
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 *  DEPRECTAED - it was used for the general working hours,
 *  now we have only staff related working hours
 *
 * REST controller for working hours management operations
 * This is an inbound adapter that translates HTTP requests to use case calls
 */
@Deprecated("Not used anymore")
@RestController
@RequestMapping("/salons/{salonId}/working-hours")
@Tag(name = "Working Hours", description = "Operations for salon working hours management")
class WorkingHoursController(
    private val workingHoursManagementUseCase: WorkingHoursManagementUseCase,
    private val workingHoursDtoMapper: WorkingHoursDtoMapper,
) {
    private val logger = LoggerFactory.getLogger(WorkingHoursController::class.java)

    /**
     * Get working hours settings for a salon
     * Authorization: CHIEF_GROOMER role required
     */
    @GetMapping
    @Operation(summary = "Get working hours", description = "Retrieve working hours settings for a salon")
    @SwaggerApiResponse(responseCode = "200", description = "Working hours retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    fun getWorkingHours(
        @PathVariable salonId: String,
    ): ResponseEntity<ApiResponse<WorkingHoursResponse>> {
        logger.info("Getting working hours for salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can view working hours settings ?? why?
//            if (!currentUser.isChiefGroomerInSalon(salon)) {
//                return ResponseEntity.status(HttpStatus.FORBIDDEN)
//                    .body(ApiResponse.error("Nu aveți permisiunea să accesați programul de lucru al acestui salon"))
//            }

            val query = GetWorkingHoursQuery(salonId = salon)
            val workingHours = workingHoursManagementUseCase.getWorkingHours(query)
            val response = workingHoursDtoMapper.toResponse(workingHours)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: EntityNotFoundException) {
            logger.warn("Entity not found: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Salonul nu a fost găsit"))
        } catch (e: DomainException) {
            logger.warn("Domain error getting working hours", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Eroare la obținerea programului de lucru"))
        } catch (e: Exception) {
            logger.error("Unexpected error getting working hours", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare internă de server"))
        }
    }

    /**
     * Update working hours settings for a salon
     * Authorization: CHIEF_GROOMER role required
     */
    @PutMapping
    @Operation(summary = "Update working hours", description = "Modify working hours settings for a salon")
    @SwaggerApiResponse(responseCode = "200", description = "Working hours updated successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid request data")
    fun updateWorkingHours(
        @PathVariable salonId: String,
        @RequestBody @Valid request: UpdateWorkingHoursRequest,
    ): ResponseEntity<ApiResponse<WorkingHoursResponse>> {
        logger.info("Updating working hours for salon: $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: Only chief groomers can modify working hours settings
            if (!currentUser.isChiefGroomerInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("Nu aveți permisiunea să modificați programul de lucru al acestui salon"))
            }

            // Validate weekly schedule has all 7 days
            val validationError = validateWeeklySchedule(request.weeklySchedule)
            if (validationError != null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(validationError))
            }

            val command = workingHoursDtoMapper.toUpdateCommand(salon, currentUser.userId, request)
            val updatedWorkingHours = workingHoursManagementUseCase.updateWorkingHours(command)
            val response = workingHoursDtoMapper.toResponse(updatedWorkingHours)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: BusinessRuleViolationException) {
            logger.warn("Business rule violation: ${e.message}")
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Operațiunea nu este permisă"))
        } catch (e: EntityNotFoundException) {
            logger.warn("Entity not found: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Salonul nu a fost găsit"))
        } catch (e: DomainException) {
            logger.warn("Domain error updating working hours", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Eroare la actualizarea programului de lucru"))
        } catch (e: Exception) {
            logger.error("Unexpected error updating working hours", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Eroare internă de server"))
        }
    }

    /**
     * Validate weekly schedule request
     */
    private fun validateWeeklySchedule(weeklySchedule: Map<String, DayScheduleRequest>): String? {
        val requiredDays = setOf("monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday")
        val providedDays = weeklySchedule.keys.map { it.lowercase() }.toSet()

        if (providedDays != requiredDays) {
            return "Programul săptămânal trebuie să conțină toate cele 7 zile"
        }

        // Validate each day schedule
        weeklySchedule.forEach { (dayName, daySchedule) ->
            if (daySchedule.isWorkingDay) {
                // Validate time format and logic
                try {
                    val startTime = daySchedule.startTime?.let { java.time.LocalTime.parse(it) }
                    val endTime = daySchedule.endTime?.let { java.time.LocalTime.parse(it) }

                    if (startTime == null || endTime == null) {
                        return "Ora de început și ora de sfârșit sunt obligatorii pentru zilele de lucru ($dayName)"
                    }

                    if (!startTime.isBefore(endTime)) {
                        return "Ora de început trebuie să fie înainte de ora de sfârșit pentru $dayName"
                    }

                    // Validate break times if present
                    val breakStart = daySchedule.breakStart?.let { java.time.LocalTime.parse(it) }
                    val breakEnd = daySchedule.breakEnd?.let { java.time.LocalTime.parse(it) }

                    if ((breakStart != null && breakEnd == null) || (breakStart == null && breakEnd != null)) {
                        return "Ambele ore pentru pauză trebuie specificate pentru $dayName"
                    }

                    if (breakStart != null && breakEnd != null) {
                        if (!breakStart.isBefore(breakEnd)) {
                            return "Ora de început a pauzei trebuie să fie înainte de ora de sfârșit pentru $dayName"
                        }

                        if (breakStart.isBefore(startTime) || breakEnd.isAfter(endTime)) {
                            return "Pauza trebuie să fie în intervalul de lucru pentru $dayName"
                        }
                    }
                } catch (e: Exception) {
                    return "Format de oră invalid pentru $dayName"
                }
            }
        }

        return null
    }
}
