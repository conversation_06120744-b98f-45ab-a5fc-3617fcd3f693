package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "stripe_sms_usage_records")
data class StripeSmsUsageRecordEntity(
    @Id
    val id: String,

    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @Column(name = "sms_units", nullable = false)
    val smsUnits: Int,

    @Column(name = "stripe_customer_id", nullable = false)
    val stripeCustomerId: String,

    @Column(name = "meter_event_name", nullable = false)
    val meterEventName: String,

    @Column(name = "reported_at", nullable = false)
    val reportedAt: LocalDateTime,

    @Column(name = "stripe_timestamp", nullable = false)
    val stripeTimestamp: Long,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        salonId = "",
        smsUnits = 0,
        stripeCustomerId = "",
        meterEventName = "",
        reportedAt = LocalDateTime.now(),
        stripeTimestamp = 0L,
        createdAt = LocalDateTime.now()
    )
}
