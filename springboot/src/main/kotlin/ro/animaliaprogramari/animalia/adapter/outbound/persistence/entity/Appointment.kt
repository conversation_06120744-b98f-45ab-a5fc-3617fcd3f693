package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

@Entity
@Table(
    name = "appointments",
    indexes = [
        Index(name = "idx_appointments_salon_date", columnList = "salon_id, appointment_date"),
        Index(name = "idx_appointments_staff_date_time", columnList = "staff_id, appointment_date, start_time"),
        Index(name = "idx_appointments_client_date", columnList = "client_id, appointment_date"),
        Index(name = "idx_appointments_status_date", columnList = "status, appointment_date"),
        Index(name = "idx_appointments_salon_staff_date", columnList = "salon_id, staff_id, appointment_date"),
        Index(name = "idx_appointments_date_range", columnList = "appointment_date, start_time, end_time"),
        Index(name = "idx_appointments_pet_date", columnList = "pet_id, appointment_date"),
        Index(name = "idx_appointments_subscription", columnList = "subscription_id"),
        Index(name = "idx_appointments_recurring", columnList = "is_recurring, subscription_id"),
        Index(name = "idx_appointments_sequence", columnList = "subscription_id, sequence_number")
    ]
)
class Appointment(
    @Id
    var id: String = "",
    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    var salonId: String = "",
    @field:NotBlank(message = "Client ID is required")
    @Column(name = "client_id", nullable = false)
    var clientId: String = "",
    @field:NotBlank(message = "Pet ID is required")
    @Column(name = "pet_id", nullable = false)
    var petId: String = "",
    @field:NotBlank(message = "Staff ID is required")
    @Column(name = "staff_id", nullable = false)
    var staffId: String = "",
    @field:NotNull(message = "Appointment date is required")
    @Column(name = "appointment_date", nullable = false)
    var appointmentDate: LocalDate = LocalDate.now(),
    @field:NotNull(message = "Start time is required")
    @Column(name = "start_time", nullable = false)
    var startTime: LocalTime = LocalTime.now(),
    @field:NotNull(message = "End time is required")
    @Column(name = "end_time", nullable = false)
    var endTime: LocalTime = LocalTime.now(),
    @Column(nullable = false)
    var status: String = "scheduled",
    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "appointment_service_ids",
        joinColumns = [JoinColumn(name = "appointment_id")],
    )
    @Column(name = "service_id")
    var serviceIds: List<String> = mutableListOf(),
    @Column(name = "custom_services", columnDefinition = "TEXT")
    var customServices: String? = null, // JSON string of custom service data
    @Column(name = "total_price", precision = 10, scale = 2, nullable = false)
    var totalPrice: BigDecimal = BigDecimal.ZERO,
    @Column(name = "total_duration", nullable = false)
    var totalDurationMinutes: Int = 0,
    @Column(columnDefinition = "TEXT")
    var notes: String? = null,
    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "appointment_photos",
        joinColumns = [JoinColumn(name = "appointment_id")],
    )
    @Column(name = "photo_url")
    var photos: List<String> = mutableListOf(),

    // Recurring appointment support
    @Column(name = "subscription_id")
    var subscriptionId: String? = null,
    @Column(name = "sequence_number")
    var sequenceNumber: Int? = null,
    @Column(name = "is_recurring", nullable = false)
    var isRecurring: Boolean = false,
    @Column(name = "is_prepaid", nullable = false)
    var isPrepaid: Boolean = false,

    @Column(name = "completed_at")
    var completedAt: LocalDateTime? = null,
    @Column(name = "actual_duration_minutes")
    var actualDurationMinutes: Int? = null,
    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
    @Version
    var version: Long = 0,
) {
    // Default constructor for JPA
    constructor() : this("", "", "", "", "", LocalDate.now(), LocalTime.now(), LocalTime.now(), "scheduled", mutableListOf(), null, BigDecimal.ZERO, 0, null, mutableListOf(), null, null, false, false, null, null, LocalDateTime.now(), LocalDateTime.now(), 0)

    // Remove the circular mapping relationships to avoid conflicts
    // Services will be loaded separately through repository queries when needed

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Appointment

        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun toString(): String {
        return "Appointment(id='$id', salonId='$salonId', clientId='$clientId', petId='$petId', staffId='$staffId', appointmentDate=$appointmentDate, startTime=$startTime, endTime=$endTime, status='$status')"
    }
}
