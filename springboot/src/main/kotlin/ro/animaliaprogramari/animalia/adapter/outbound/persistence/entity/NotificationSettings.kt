package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.converter.SoundPreferenceConverter
import ro.animaliaprogramari.animalia.domain.model.NotificationPriority
import ro.animaliaprogramari.animalia.domain.model.SoundPreference
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * JPA entity for notification settings
 * This is an outbound adapter for persistence
 */
@Entity
@Table(name = "notification_settings")
@IdClass(NotificationSettingsId::class)
class NotificationSettings {
    @Id
    @Column(name = "user_id", nullable = false)
    var userId: String = ""

    @Id
    @Column(name = "salon_id", nullable = false)
    var salonId: String = ""

    @Column(name = "push_notifications_enabled", nullable = false)
    var pushNotificationsEnabled: Boolean = true

    @Convert(converter = SoundPreferenceConverter::class)
    @Column(name = "sound_preference", nullable = false)
    var soundPreference: SoundPreference = SoundPreference.DEFAULT

    @Column(name = "vibration_enabled", nullable = false)
    var vibrationEnabled: Boolean = true

    // Do Not Disturb settings
    @Column(name = "dnd_enabled", nullable = false)
    var dndEnabled: Boolean = false

    @Column(name = "dnd_start_time", nullable = false)
    var dndStartTime: LocalTime = LocalTime.of(22, 0)

    @Column(name = "dnd_end_time", nullable = false)
    var dndEndTime: LocalTime = LocalTime.of(8, 0)

    @Column(name = "dnd_allow_critical", nullable = false)
    var dndAllowCritical: Boolean = true

    // Notification rules
    @Column(name = "new_appointments", nullable = false)
    var newAppointments: Boolean = true

    @Column(name = "appointment_cancellations", nullable = false)
    var appointmentCancellations: Boolean = true

    @Column(name = "appointment_rescheduled", nullable = false)
    var appointmentRescheduled: Boolean = true

    @Enumerated(EnumType.STRING)
    @Column(name = "default_priority", nullable = false)
    var defaultPriority: NotificationPriority = NotificationPriority.NORMAL

    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now()

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()

    // Default constructor for JPA
    constructor()

    /**
     * Constructor with all parameters for use in mappers
     */
    constructor(
        userId: String,
        salonId: String,
        pushNotificationsEnabled: Boolean,
        soundPreference: SoundPreference,
        vibrationEnabled: Boolean,
        dndEnabled: Boolean,
        dndStartTime: LocalTime,
        dndEndTime: LocalTime,
        dndAllowCritical: Boolean,
        newAppointments: Boolean,
        appointmentCancellations: Boolean,
        appointmentRescheduled: Boolean,
        defaultPriority: NotificationPriority,
        createdAt: LocalDateTime,
        updatedAt: LocalDateTime,
    ) {
        this.userId = userId
        this.salonId = salonId
        this.pushNotificationsEnabled = pushNotificationsEnabled
        this.soundPreference = soundPreference
        this.vibrationEnabled = vibrationEnabled
        this.dndEnabled = dndEnabled
        this.dndStartTime = dndStartTime
        this.dndEndTime = dndEndTime
        this.dndAllowCritical = dndAllowCritical
        this.newAppointments = newAppointments
        this.appointmentCancellations = appointmentCancellations
        this.appointmentRescheduled = appointmentRescheduled
        this.defaultPriority = defaultPriority
        this.createdAt = createdAt
        this.updatedAt = updatedAt
    }
}

/**
 * Composite ID class for NotificationSettings
 */
data class NotificationSettingsId(
    var userId: String = "",
    var salonId: String = "",
) : java.io.Serializable
