package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.SmsRateLimitEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.SmsRateLimitRepository
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.notification.SmsRateLimit
import ro.animaliaprogramari.animalia.domain.model.notification.SmsRateLimitId
import java.time.LocalDateTime

/**
 * JPA adapter implementing the SmsRateLimitRepository port
 */
@Repository
class JpaSmsRateLimitRepository(
    private val springRepository: SpringSmsRateLimitRepository,
    private val mapper: SmsRateLimitEntityMapper
) : SmsRateLimitRepository {

    override fun save(smsRateLimit: SmsRateLimit): SmsRateLimit {
        val entity = mapper.toEntity(smsRateLimit)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findByPhoneNumber(phoneNumber: PhoneNumber): SmsRateLimit? {
        return springRepository.findByPhoneNumber(phoneNumber.value)
            ?.let { mapper.toDomain(it) }
    }

    override fun findById(id: SmsRateLimitId): SmsRateLimit? {
        return springRepository.findById(id.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    @Transactional
    override fun deleteExpiredRecords() {
        // Delete records older than 24 hours (well beyond the typical 1-hour window)
        val expiredBefore = LocalDateTime.now().minusHours(24)
        springRepository.deleteExpiredRecords(expiredBefore)
    }
}
