package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.CustomClosure

/**
 * Spring Data JPA repository for custom closures
 */
@Repository
interface SpringCustomClosureRepository : JpaRepository<CustomClosure, String> {
    /**
     * Find custom closures by salon ID
     */
    fun findBySalonIdOrderByDate(salonId: String): List<CustomClosure>

    /**
     * Delete custom closures by salon ID
     */
    fun deleteBySalonId(salonId: String)
}
