package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.sms

import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

/**
 * Response DTO for SMS template
 */
@Schema(description = "SMS template response")
data class SmsTemplateResponse(
    @Schema(description = "Template ID")
    val id: String,
    @Schema(description = "Salon ID")
    val salonId: String,
    @Schema(description = "Template type")
    val templateType: String,
    @Schema(description = "Template content")
    val templateContent: String,
    @Schema(description = "Whether template is active")
    val isActive: Boolean,
    @Schema(description = "Creation timestamp")
    val createdAt: LocalDateTime,
    @Schema(description = "Last update timestamp")
    val updatedAt: LocalDateTime,
)

/**
 * Request DTO for updating SMS template
 */
@Schema(description = "Update SMS template request")
data class UpdateSmsTemplateRequest(
    @field:NotBlank(message = "Template content is required")
    @field:Size(max = 1000, message = "Template content cannot exceed 1000 characters")
    @Schema(description = "Template content with variables")
    val templateContent: String,
    @Schema(description = "Whether template is active")
    val isActive: Boolean = true,
)

/**
 * Request DTO for previewing SMS template
 */
@Schema(description = "Preview SMS template request")
data class PreviewSmsTemplateRequest(
    @field:NotBlank(message = "Template content is required")
    @Schema(description = "Template content with variables")
    val templateContent: String,
    @Schema(description = "Sample data for variable replacement")
    val sampleData: Map<String, String>,
)

/**
 * Response DTO for SMS template preview
 */
@Schema(description = "SMS template preview response")
data class PreviewSmsTemplateResponse(
    @Schema(description = "Rendered template preview")
    val preview: String,
    @Schema(description = "Character count of preview")
    val characterCount: Int,
)

/**
 * Request DTO for creating SMS reminder timing
 */
@Schema(description = "Create SMS reminder timing request")
data class CreateSmsReminderTimingRequest(
    @Schema(description = "Hours before appointment", minimum = "1", maximum = "168")
    val hoursBefore: Int,
    @Schema(description = "Whether timing is enabled")
    val isEnabled: Boolean = true,
)

/**
 * Request DTO for updating SMS reminder timing
 */
@Schema(description = "Update SMS reminder timing request")
data class UpdateSmsReminderTimingRequest(
    @Schema(description = "Hours before appointment", minimum = "1", maximum = "168")
    val hoursBefore: Int,
    @Schema(description = "Whether timing is enabled")
    val isEnabled: Boolean,
)

