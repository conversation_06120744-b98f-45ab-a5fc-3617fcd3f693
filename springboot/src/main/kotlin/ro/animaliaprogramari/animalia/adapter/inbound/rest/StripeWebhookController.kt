package ro.animaliaprogramari.animalia.adapter.inbound.rest

import com.stripe.exception.SignatureVerificationException
import com.stripe.model.Event
import com.stripe.model.checkout.Session
import com.stripe.net.Webhook
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.servlet.http.HttpServletRequest
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.application.service.StripeMeteredBillingSettingsService
import ro.animaliaprogramari.animalia.domain.model.SalonId

@RestController
@RequestMapping("/billing/stripe/webhook")
@Tag(name = "Stripe Webhooks", description = "Handle Stripe webhook events")
class StripeWebhookController(
    private val meteredSettingsService: StripeMeteredBillingSettingsService,
    @Value("\${stripe.api.webhook-secret}") private val webhookSecret: String,
) {
    private val logger = LoggerFactory.getLogger(StripeWebhookController::class.java)

    @PostMapping
    @Operation(
        summary = "Handle Stripe webhook events",
        description = """
            Handles Stripe webhook events, particularly checkout.session.completed
            to capture new customers who complete SMS billing subscription payments.
        """
    )
    fun handleWebhook(
        @RequestBody payload: String,
        request: HttpServletRequest
    ): ResponseEntity<Map<String, String>> {
        return try {
            logger.info("Received Stripe webhook")

            // Get Stripe signature from headers
            val sigHeader = request.getHeader("Stripe-Signature")
            if (sigHeader == null) {
                logger.warn("Missing Stripe-Signature header")
                return ResponseEntity.badRequest().body(mapOf("error" to "Missing signature"))
            }

            // Verify webhook signature
            val event = try {
                Webhook.constructEvent(payload, sigHeader, webhookSecret)
            } catch (e: SignatureVerificationException) {
                logger.error("Invalid webhook signature", e)
                return ResponseEntity.badRequest().body(mapOf("error" to "Invalid signature"))
            } catch (e: Exception) {
                logger.error("Error parsing webhook payload", e)
                return ResponseEntity.badRequest().body(mapOf("error" to "Invalid payload"))
            }

            logger.info("Processing Stripe webhook event: ${event.type}")

            // Handle different event types
            when (event.type) {
                "checkout.session.completed" -> handleCheckoutCompleted(event)
                "customer.subscription.created" -> handleSubscriptionCreated(event)
                "customer.subscription.updated" -> handleSubscriptionUpdated(event)
                "customer.subscription.deleted" -> handleSubscriptionDeleted(event)
                else -> {
                    logger.info("Unhandled webhook event type: ${event.type}")
                }
            }

            ResponseEntity.ok(mapOf("status" to "success"))
        } catch (e: Exception) {
            logger.error("Error processing Stripe webhook", e)
            ResponseEntity.status(500).body(mapOf("error" to "Internal server error"))
        }
    }

    private fun handleCheckoutCompleted(event: Event) {
        logger.info("Processing checkout.session.completed webhook")

        try {
            val session = event.dataObjectDeserializer.`object`.orElse(null) as? Session
            if (session == null) {
                logger.error("Failed to deserialize checkout session from webhook")
                return
            }

            logger.info("Checkout session completed: ${session.id}")
            logger.info("Customer ID: ${session.customer}")
            logger.info("Subscription ID: ${session.subscription}")
            logger.info("Customer email: ${session.customerDetails?.email}")

            // Extract customer information
            val customerId = session.customer

            if (customerId == null) {
                logger.warn("No customer ID found in checkout session")
                return
            }

            // Handle different types of checkout sessions based on metadata
            val salonId = session.metadata?.get("salon_id")
            val appUserId = session.metadata?.get("app_user_id")

            if (salonId != null) {
                // This is a metered SMS billing checkout session
                logger.info("Found salon ID in session metadata: $salonId")

                // Update the salon's Stripe billing settings
                meteredSettingsService.updateSettings(
                    salonId = SalonId.of(salonId),
                    customerId = customerId,
                    meterEventName = "sms_sent" // Assuming the meter event name is fixed for SMS billing
                )

                logger.info("Updated Stripe customer ID for salon $salonId: $customerId")
            } else if (appUserId != null) {
                // This is a RevenueCat subscription checkout session
                logger.info("Found app_user_id in session metadata: $appUserId")

                // Note: Billing data is handled by Stripe directly, no additional processing needed
                logger.info("Checkout completed for app_user_id: $appUserId")
            } else {
                logger.warn("No salon_id or app_user_id found in checkout session metadata.")
                logger.info("Session metadata: ${session.metadata}")
            }

            // Log the successful completion
            logger.info("Successfully processed checkout completion for customer $customerId")

        } catch (e: Exception) {
            logger.error("Error handling checkout.session.completed webhook", e)
        }
    }

    private fun handleSubscriptionCreated(event: Event) {
        logger.info("Processing customer.subscription.created webhook")
        // Handle subscription creation if needed
    }

    private fun handleSubscriptionUpdated(event: Event) {
        logger.info("Processing customer.subscription.updated webhook")
        // Handle subscription updates if needed
    }

    private fun handleSubscriptionDeleted(event: Event) {
        logger.info("Processing customer.subscription.deleted webhook")
        // Handle subscription cancellation if needed
    }
}
