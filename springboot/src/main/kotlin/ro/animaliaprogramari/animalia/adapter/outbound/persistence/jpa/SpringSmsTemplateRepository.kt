package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsTemplate
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsTemplateType

@Repository
interface SpringSmsTemplateRepository : JpaRepository<SmsTemplate, String> {
    
    /**
     * Find all templates for a salon
     */
    fun findBySalonId(salonId: String): List<SmsTemplate>

    /**
     * Find template by salon and type
     */
    fun findBySalonIdAndTemplateType(salonId: String, templateType: SmsTemplateType): SmsTemplate?

    /**
     * Find all active templates for a salon
     */
    @Query("SELECT t FROM SmsTemplate t WHERE t.salonId = :salonId AND t.isActive = true")
    fun findActiveBySalonId(@Param("salonId") salonId: String): List<SmsTemplate>

    /**
     * Check if template exists for salon and type
     */
    fun existsBySalonIdAndTemplateType(salonId: String, templateType: SmsTemplateType): Boolean

    /**
     * Delete all templates for a salon
     */
    fun deleteBySalonId(salonId: String)
}
