package ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.BlockedSmsCredits
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsSource
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsStatus

/**
 * JPA repository interface for blocked SMS credits
 */
@Repository
interface JpaBlockedSmsCreditsRepository : JpaRepository<BlockedSmsCredits, String> {

    /**
     * Find blocked SMS credits by salon ID
     */
    fun findBySalonId(salonId: String): List<BlockedSmsCredits>

    /**
     * Find blocked SMS credits by salon ID and status
     */
    fun findBySalonIdAndStatus(salonId: String, status: BlockedCreditsStatus): List<BlockedSmsCredits>

    /**
     * Find blocked SMS credits by source and source ID
     */
    fun findBySourceAndSourceId(source: BlockedCreditsSource, sourceId: String): BlockedSmsCredits?

    /**
     * Find all blocked credits with BLOCKED status
     */
    fun findByStatus(status: BlockedCreditsStatus): List<BlockedSmsCredits>

    /**
     * Get total blocked credits amount for a salon
     */
    @Query("""
        SELECT COALESCE(SUM(b.creditsAmount), 0)
        FROM BlockedSmsCredits b
        WHERE b.salonId = :salonId
        AND b.status = :status
    """)
    fun getTotalBlockedCredits(
        @Param("salonId") salonId: String,
        @Param("status") status: BlockedCreditsStatus
    ): Int

}
