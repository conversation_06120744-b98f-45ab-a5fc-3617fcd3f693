package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.notification.DeactivateFcmTokenRequest
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.NotificationManagementUseCase
import ro.animaliaprogramari.animalia.application.command.DeactivateFcmTokenCommand

@RestController
@RequestMapping("/users")
@Tag(name = "Users", description = "Operations for user management")
class UserController(
    private val notificationManagementUseCase: NotificationManagementUseCase,
) {
    private val logger = LoggerFactory.getLogger(UserController::class.java)

    /**
     * DELETE /api/users/fcm-token
     * Deactivate FCM token for push notifications (used during logout)
     */
    @DeleteMapping("/fcm-token")
    @Operation(summary = "Deactivate FCM token", description = "Deactivates an FCM token for push notifications during logout")
    fun deactivateFcmToken(
        @Valid @RequestBody request: DeactivateFcmTokenRequest
    ): ResponseEntity<ApiResponse<Boolean>> {
        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(401).body(ApiResponse.error("User not authenticated"))

            logger.info("Deactivating FCM token for user: ${currentUser.userId.value}")

            // For logout, we deactivate the token regardless of salon
            val command = DeactivateFcmTokenCommand(
                userId = currentUser.userId,
                salonId = null, // Not needed for token deactivation
                token = request.token
            )

            val result = notificationManagementUseCase.deactivateFcmToken(command)

            if (result) {
                logger.info("FCM token deactivated successfully for user: ${currentUser.userId.value}")
                ResponseEntity.ok(ApiResponse.success(true))
            } else {
                logger.warn("FCM token not found or already deactivated for user: ${currentUser.userId.value}")
                ResponseEntity.ok(ApiResponse.success(false))
            }
        } catch (e: Exception) {
            logger.error("Error deactivating FCM token", e)
            ResponseEntity.status(500).body(ApiResponse.error("Failed to deactivate FCM token: ${e.message}"))
        }
    }
}
