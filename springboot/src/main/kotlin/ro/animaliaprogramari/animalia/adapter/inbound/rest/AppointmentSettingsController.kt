package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.AppointmentSettingsRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.AppointmentSettingsResponse
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.service.AppointmentSettingsService
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.DomainException

/**
 * REST controller for managing appointment settings
 */
@RestController
@RequestMapping("/salons/{salonId}/appointment-settings")
@Tag(name = "Appointment Settings", description = "Appointment settings management API")
@PreAuthorize("hasRole('USER')")
class AppointmentSettingsController(
    private val appointmentSettingsService: AppointmentSettingsService
) {
    private val logger = LoggerFactory.getLogger(AppointmentSettingsController::class.java)

    @GetMapping
    @Operation(
        summary = "Get appointment settings",
        description = "Retrieve appointment settings for a salon"
    )
    @ApiResponses(
        value = [
            SwaggerApiResponse(responseCode = "200", description = "Settings retrieved successfully"),
            SwaggerApiResponse(responseCode = "401", description = "Unauthorized"),
            SwaggerApiResponse(responseCode = "403", description = "Forbidden"),
            SwaggerApiResponse(responseCode = "404", description = "Salon not found"),
        ]
    )
    fun getAppointmentSettings(
        @Parameter(description = "Salon ID", required = true)
        @PathVariable salonId: String
    ): ResponseEntity<ApiResponse<AppointmentSettingsResponse>> {
        logger.info("REST request to get appointment settings for salon: {}", salonId)

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // Verify user has access to this salon
            // This will be handled by the service layer

            val settings = appointmentSettingsService.getAppointmentSettings(salonId)

            val response = AppointmentSettingsResponse(
                salonId = settings.salonId,
                autoFinalizeEnabled = settings.autoFinalizeEnabled,
                overdueNotificationsEnabled = settings.overdueNotificationsEnabled,
                smsCompletionEnabled = settings.smsCompletionEnabled,
                createdAt = settings.createdAt,
                updatedAt = settings.updatedAt
            )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error getting appointment settings", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to get appointment settings"))
        } catch (e: Exception) {
            logger.error("Unexpected error getting appointment settings", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }

    @PutMapping
    @Operation(
        summary = "Update appointment settings",
        description = "Update appointment settings for a salon"
    )
    @ApiResponses(
        value = [
            SwaggerApiResponse(responseCode = "200", description = "Settings updated successfully"),
            SwaggerApiResponse(responseCode = "400", description = "Invalid request"),
            SwaggerApiResponse(responseCode = "401", description = "Unauthorized"),
            SwaggerApiResponse(responseCode = "403", description = "Forbidden"),
            SwaggerApiResponse(responseCode = "404", description = "Salon not found"),
        ]
    )
    fun updateAppointmentSettings(
        @Parameter(description = "Salon ID", required = true)
        @PathVariable salonId: String,
        @Valid @RequestBody request: AppointmentSettingsRequest
    ): ResponseEntity<ApiResponse<AppointmentSettingsResponse>> {
        logger.info("REST request to update appointment settings for salon: {}", salonId)

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // Verify user has access to this salon and appropriate permissions
            // This will be handled by the service layer

            val updatedSettings = appointmentSettingsService.updateAppointmentSettings(
                salonId = salonId,
                autoFinalizeEnabled = request.autoFinalizeEnabled,
                overdueNotificationsEnabled = request.overdueNotificationsEnabled,
                smsCompletionEnabled = request.smsCompletionEnabled
            )

            val response = AppointmentSettingsResponse(
                salonId = updatedSettings.salonId,
                autoFinalizeEnabled = updatedSettings.autoFinalizeEnabled,
                overdueNotificationsEnabled = updatedSettings.overdueNotificationsEnabled,
                smsCompletionEnabled = updatedSettings.smsCompletionEnabled,
                createdAt = updatedSettings.createdAt,
                updatedAt = updatedSettings.updatedAt
            )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: DomainException) {
            logger.warn("Domain error updating appointment settings", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(e.message ?: "Failed to update appointment settings"))
        } catch (e: Exception) {
            logger.error("Unexpected error updating appointment settings", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }
}
