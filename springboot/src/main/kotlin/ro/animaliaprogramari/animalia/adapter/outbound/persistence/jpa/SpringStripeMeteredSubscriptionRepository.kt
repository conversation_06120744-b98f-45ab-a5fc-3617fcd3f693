package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StripeMeteredSubscriptionEntity

@Repository
interface SpringStripeMeteredSubscriptionRepository : JpaRepository<StripeMeteredSubscriptionEntity, String> {
    fun findBySalonId(salonId: String): StripeMeteredSubscriptionEntity?
}

