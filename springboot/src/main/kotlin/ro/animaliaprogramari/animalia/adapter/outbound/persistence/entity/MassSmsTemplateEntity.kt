package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "mass_sms_templates")
data class MassSmsTemplateEntity(
    @Id
    @Column(name = "id", length = 36)
    val id: String,

    @Column(name = "salon_id", length = 36)
    val salonId: String?,

    @Column(name = "name", nullable = false, length = 255)
    val name: String,

    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    val content: String,

    @Column(name = "category", nullable = false, length = 100)
    val category: String,

    @Column(name = "is_default", nullable = false)
    val isDefault: Boolean,

    @Column(name = "usage_count", nullable = false)
    val usageCount: Int,

    @Column(name = "created_by", nullable = false, length = 36)
    val createdBy: String,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime,

    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime
) {
    constructor() : this(
        id = "00000000-0000-0000-0000-000000000000",
        salonId = null,
        name = "",
        content = "",
        category = "",
        isDefault = false,
        usageCount = 0,
        createdBy = "",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )
}
