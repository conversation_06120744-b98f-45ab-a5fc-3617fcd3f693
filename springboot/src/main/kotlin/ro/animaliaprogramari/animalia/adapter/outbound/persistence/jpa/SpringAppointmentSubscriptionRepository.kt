package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.infrastructure.persistence.entity.AppointmentSubscriptionEntity

/**
 * Spring Data JPA repository for appointment subscriptions
 */
@Repository
interface SpringAppointmentSubscriptionRepository : JpaRepository<AppointmentSubscriptionEntity, String> {

    /**
     * Find subscription by ID and salon ID
     */
    fun findByIdAndSalonId(id: String, salonId: String): AppointmentSubscriptionEntity?

    /**
     * Find all subscriptions for a client in a salon
     */
    fun findByClientIdAndSalonIdOrderByCreatedAtDesc(clientId: String, salonId: String): List<AppointmentSubscriptionEntity>

    /**
     * Find all active subscriptions for a salon
     */
    fun findBySalonIdAndStatus(salonId: String, status: String): List<AppointmentSubscriptionEntity>

    /**
     * Find subscriptions that need processing (active with remaining repetitions > 0)
     */
    @Query("""
        SELECT s FROM AppointmentSubscriptionEntity s
        WHERE s.status = 'ACTIVE'
        AND s.remainingRepetitions > 0
        ORDER BY s.updatedAt ASC
    """)
    fun findSubscriptionsNeedingProcessing(): List<AppointmentSubscriptionEntity>

    /**
     * Find subscriptions by original appointment ID
     */
    fun findByOriginalAppointmentId(appointmentId: String): List<AppointmentSubscriptionEntity>

}
