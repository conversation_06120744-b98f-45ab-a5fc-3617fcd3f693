package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.data.rest.core.annotation.RepositoryRestResource
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.MassSmsTemplateEntity
import ro.animaliaprogramari.animalia.domain.model.SalonId

@Repository
@RepositoryRestResource(exported = false)
interface SpringMassSmsTemplateRepository : JpaRepository<MassSmsTemplateEntity, String> {

    /**
     * Find templates by salon ID including default templates
     */
    @Query("""
        SELECT t FROM MassSmsTemplateEntity t
        WHERE t.salonId = :salonId OR t.isDefault = true
        ORDER BY t.isDefault ASC, t.usageCount DESC, t.name ASC
    """)
    fun findBySalonIdOrDefault(@Param("salonId") salonId: SalonId): List<MassSmsTemplateEntity>

    /**
     * Find templates by salon ID only
     */
    fun findBySalonId(salonId: SalonId): List<MassSmsTemplateEntity>
}
