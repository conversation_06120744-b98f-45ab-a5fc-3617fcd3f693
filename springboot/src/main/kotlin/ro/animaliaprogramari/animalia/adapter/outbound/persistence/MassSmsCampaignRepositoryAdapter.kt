package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringMassSmsCampaignRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.MassSmsCampaignEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.MassSmsRepository
import ro.animaliaprogramari.animalia.domain.model.MassSmsCampaign
import ro.animaliaprogramari.animalia.domain.model.SalonId

@Repository
@Transactional
class MassSmsCampaignRepositoryAdapter(
    private val springRepository: SpringMassSmsCampaignRepository,
    private val mapper: MassSmsCampaignEntityMapper
) : MassSmsRepository {

    override fun save(campaign: MassSmsCampaign): MassSmsCampaign {
        val entity = mapper.toEntity(campaign)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findBySalonIdOrderByCreatedAtDesc(salonId: SalonId, pageable: Pageable): Page<MassSmsCampaign> {
        return springRepository.findBySalonIdOrderByCreatedAtDesc(salonId, pageable)
            .map { mapper.toDomain(it) }
    }
}
