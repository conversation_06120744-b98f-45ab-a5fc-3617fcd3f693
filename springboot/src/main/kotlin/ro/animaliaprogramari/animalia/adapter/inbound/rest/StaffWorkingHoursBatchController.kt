package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.BatchStaffWorkingHoursResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.StaffWorkingHoursResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper.StaffWorkingHoursDtoMapper
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.StaffWorkingHoursManagementUseCase
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for batch staff working hours operations
 * This is an inbound adapter that handles batch requests for multiple staff working hours
 */
@RestController
@RequestMapping("/salons/{salonId}/staff/working-hours")
@Tag(name = "Staff Working Hours Batch", description = "Batch operations for staff working hours")
class StaffWorkingHoursBatchController(
    private val staffWorkingHoursManagementUseCase: StaffWorkingHoursManagementUseCase,
    private val staffWorkingHoursDtoMapper: StaffWorkingHoursDtoMapper,
) {
    private val logger = LoggerFactory.getLogger(StaffWorkingHoursBatchController::class.java)

    /**
     * Get working hours settings for multiple staff members
     * Authorization: CHIEF_GROOMER only
     */
    @GetMapping("/batch")
    @Operation(
        summary = "Get batch staff working hours",
        description = "Retrieve working hours for multiple staff members"
    )
    @SwaggerApiResponse(responseCode = "200", description = "Working hours retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(responseCode = "403", description = "Access denied")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid staff IDs")
    fun getBatchStaffWorkingHours(
        @PathVariable salonId: String,
        @RequestParam staffIds: List<String>,
    ): ResponseEntity<ApiResponse<BatchStaffWorkingHoursResponse>> {
        logger.info("Getting batch working hours for ${staffIds.size} staff members in salon: $salonId")

        return try {
            val currentUser = SecurityUtils.getCurrentUser()
                ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))



            // Validate staffIds parameter
            if (staffIds.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("At least one staff ID is required"))
            }

            if (staffIds.size > 50) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("Maximum 50 staff IDs allowed per request"))
            }


            val query = staffWorkingHoursDtoMapper.toBatchWorkingHoursQuery(
                salonId,
                staffIds,
                currentUser.userId
            )
            val workingHoursList = staffWorkingHoursManagementUseCase.getBatchStaffWorkingHours(query)
            val response = staffWorkingHoursDtoMapper.toBatchResponse(salonId, workingHoursList)

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: BusinessRuleViolationException) {
            logger.warn("Business rule violation: ${e.message}")
            ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error(e.message ?: "Operațiunea nu este permisă"))
        } catch (e: EntityNotFoundException) {
            logger.warn("Entity not found: ${e.message}")
            ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error(e.message ?: "Unul sau mai mulți angajați nu au fost găsiți"))
        } catch (e: Exception) {
            logger.error("Unexpected error getting batch staff working hours", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }
}
