package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.SmsReminderTimingEntityMapper
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsReminderTiming
import ro.animaliaprogramari.animalia.domain.model.SmsReminderTimingId
import ro.animaliaprogramari.animalia.domain.port.outbound.SmsReminderTimingRepository

/**
 * JPA adapter implementing the SmsReminderTimingRepository port
 */
@Repository
class JpaSmsReminderTimingRepository(
    private val springRepository: SpringSmsReminderTimingRepository,
    private val mapper: SmsReminderTimingEntityMapper,
) : SmsReminderTimingRepository {

    override fun save(timing: SmsReminderTiming): SmsReminderTiming {
        val entity = mapper.toEntity(timing)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findById(id: SmsReminderTimingId): SmsReminderTiming? {
        return springRepository.findById(id.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findBySalonId(salonId: SalonId): List<SmsReminderTiming> {
        return springRepository.findBySalonId(salonId.value)
            .map { mapper.toDomain(it) }
    }

    override fun findAllEnabled(): List<SmsReminderTiming> {
        return springRepository.findAllByIsEnabledTrue()
            .map { mapper.toDomain(it) }
    }

    override fun deleteById(id: SmsReminderTimingId) {
        springRepository.deleteById(id.value)
    }
}
