package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CustomServiceData
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Appointment as AppointmentEntity

/**
 * Mapper between domain Appointment and JPA AppointmentEntity
 * This handles the translation between the pure domain model and persistence model
 */
@Component
class AppointmentEntityMapper {
    private val objectMapper = ObjectMapper()

    fun toDomain(entity: AppointmentEntity): Appointment {
        return Appointment(
            id = AppointmentId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            clientId = ClientId.of(entity.clientId),
            petId = PetId.of(entity.petId),
            staffId = StaffId.of(entity.staffId),
            appointmentDate = entity.appointmentDate,
            startTime = entity.startTime,
            endTime = entity.endTime,
            status = AppointmentStatus.valueOf(entity.status.uppercase()),
            serviceIds = entity.serviceIds.map { ServiceId.of(it) },
            customServices = parseCustomServices(entity.customServices),
            totalPrice = Money.of(entity.totalPrice),
            totalMyDuration = MyDuration.ofMinutes(entity.totalDurationMinutes),
            notes = entity.notes,
            photos = entity.photos,
            subscriptionId = entity.subscriptionId?.let { AppointmentSubscriptionId.of(it) },
            sequenceNumber = entity.sequenceNumber,
            isRecurring = entity.isRecurring,
            completedAt = entity.completedAt,
            actualDurationMinutes = entity.actualDurationMinutes,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            version = entity.version,
        )
    }

    fun toEntity(domain: Appointment): AppointmentEntity {
        val entity = AppointmentEntity()
        entity.id = domain.id.value
        entity.salonId = domain.salonId.value
        entity.clientId = domain.clientId.value
        entity.petId = domain.petId.value
        entity.staffId = domain.staffId.value
        entity.appointmentDate = domain.appointmentDate
        entity.startTime = domain.startTime
        entity.endTime = domain.endTime
        entity.status = domain.status.name.lowercase()
        entity.serviceIds = domain.serviceIds.map { it.value }
        entity.customServices = serializeCustomServices(domain.customServices)
        entity.totalPrice = domain.totalPrice.amount
        entity.totalDurationMinutes = domain.totalMyDuration.minutes
        entity.notes = domain.notes
        entity.photos = domain.photos.toMutableList()
        entity.subscriptionId = domain.subscriptionId?.value
        entity.sequenceNumber = domain.sequenceNumber
        entity.isRecurring = domain.isRecurring
        entity.completedAt = domain.completedAt
        entity.actualDurationMinutes = domain.actualDurationMinutes
        entity.createdAt = domain.createdAt
        entity.updatedAt = domain.updatedAt
        entity.version = domain.version
        return entity
    }

    private fun parseCustomServices(customServicesJson: String?): Map<ServiceId, CustomServiceData>? {
        if (customServicesJson.isNullOrBlank()) return null

        return try {
            val rawMap: Map<String, Map<String, Any>> = objectMapper.readValue(customServicesJson)
            rawMap.mapKeys { ServiceId.of(it.key) }
                .mapValues { (_, value) ->
                    CustomServiceData(
                        originalServiceName = value["originalServiceName"] as String,
                        customName = value["customName"] as String,
                        customPrice = (value["customPrice"] as Number).toDouble(),
                        customDescription = value["customDescription"] as String?
                    )
                }
        } catch (e: Exception) {
            null
        }
    }

    fun serializeCustomServices(customServices: Map<ServiceId, CustomServiceData>?): String? {
        if (customServices.isNullOrEmpty()) return null

        return try {
            val serializable = customServices.mapKeys { it.key.value }
                .mapValues { (_, value) ->
                    mapOf(
                        "originalServiceName" to value.originalServiceName,
                        "customName" to value.customName,
                        "customPrice" to value.customPrice,
                        "customDescription" to value.customDescription
                    )
                }
            objectMapper.writeValueAsString(serializable)
        } catch (e: Exception) {
            null
        }
    }
}
