package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import jakarta.persistence.*
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

/**
 * JPA entity for salon subscriptions
 */
@Entity
@Table(name = "salon_subscriptions")
class SalonSubscriptionEntity {
    @Id
    var id: String = ""

    @Column(name = "user_id", nullable = false)
    var userId: String = ""

    @Column(name = "salon_id", nullable = false)
    var salonId: String = ""

    @Enumerated(EnumType.STRING)
    @Column(name = "tier", nullable = false)
    var tier: SubscriptionTier = SubscriptionTier.FREE

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: SubscriptionStatus = SubscriptionStatus.EXPIRED

    @Column(name = "start_date", nullable = false)
    var startDate: LocalDateTime = LocalDateTime.now()

    @Column(name = "end_date")
    var endDate: LocalDateTime? = null

    @Column(name = "trial_end_date")
    var trialEndDate: LocalDateTime? = null

    @Column(name = "is_trial_active", nullable = false)
    var isTrialActive: Boolean = false

    @Column(name = "revenue_cat_customer_id")
    var revenueCatCustomerId: String? = null

    @Column(name = "revenue_cat_entitlement_id")
    var revenueCatEntitlementId: String? = null

    @ElementCollection
    @CollectionTable(name = "subscription_metadata", joinColumns = [JoinColumn(name = "subscription_id")])
    @MapKeyColumn(name = "metadata_key")
    @Column(name = "metadata_value")
    var metadata: Map<String, String> = emptyMap()

    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now()

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()

    // No-argument constructor for JPA
    constructor()

    // Constructor with all parameters
    constructor(
        id: String,
        userId: String,
        salonId: String,
        tier: SubscriptionTier,
        status: SubscriptionStatus,
        startDate: LocalDateTime,
        endDate: LocalDateTime? = null,
        trialEndDate: LocalDateTime? = null,
        isTrialActive: Boolean = false,
        revenueCatCustomerId: String? = null,
        revenueCatEntitlementId: String? = null,
        metadata: Map<String, String> = emptyMap(),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now()
    ) {
        this.id = id
        this.userId = userId
        this.salonId = salonId
        this.tier = tier
        this.status = status
        this.startDate = startDate
        this.endDate = endDate
        this.trialEndDate = trialEndDate
        this.isTrialActive = isTrialActive
        this.revenueCatCustomerId = revenueCatCustomerId
        this.revenueCatEntitlementId = revenueCatEntitlementId
        this.metadata = metadata
        this.createdAt = createdAt
        this.updatedAt = updatedAt
    }
    /**
     * Convert to domain model
     */
    fun toDomain(): SalonSubscription {
        return SalonSubscription(
            id = SubscriptionId.of(id),
            userId = UserId.of(userId),
            salonId = SalonId.of(salonId),
            tier = tier,
            status = status,
            startDate = startDate,
            endDate = endDate,
            trialEndDate = trialEndDate,
            isTrialActive = isTrialActive,
            revenueCatCustomerId = revenueCatCustomerId,
            revenueCatEntitlementId = revenueCatEntitlementId,
            metadata = metadata,
            createdAt = createdAt,
            updatedAt = updatedAt,
        )
    }

    companion object {
        /**
         * Create from domain model
         */
        fun fromDomain(subscription: SalonSubscription): SalonSubscriptionEntity {
            return SalonSubscriptionEntity(
                id = subscription.id.value,
                userId = subscription.userId.value,
                salonId = subscription.salonId.value,
                tier = subscription.tier,
                status = subscription.status,
                startDate = subscription.startDate,
                endDate = subscription.endDate,
                trialEndDate = subscription.trialEndDate,
                isTrialActive = subscription.isTrialActive,
                revenueCatCustomerId = subscription.revenueCatCustomerId,
                revenueCatEntitlementId = subscription.revenueCatEntitlementId,
                metadata = subscription.metadata,
                createdAt = subscription.createdAt,
                updatedAt = subscription.updatedAt,
            )
        }
    }
}
