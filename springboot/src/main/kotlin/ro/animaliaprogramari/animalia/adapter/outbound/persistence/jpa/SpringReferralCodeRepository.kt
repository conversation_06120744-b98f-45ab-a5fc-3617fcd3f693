package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.ReferralCode
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.ReferralCodeStatus

@Repository
interface SpringReferralCodeRepository : JpaRepository<ReferralCode, String> {

    /**
     * Find referral code by code string
     */
    fun findByCode(code: String): ReferralCode?

    /**
     * Check if code exists
     */
    fun existsByCode(code: String): Boolean

    /**
     * Find all codes generated by a salon
     */
    fun findByGeneratorSalonIdOrderByCreatedAtDesc(generatorSalonId: String): List<ReferralCode>

    /**
     * Find all codes claimed by a salon
     */
    fun findByClaimerSalonIdOrderByClaimedAtDesc(claimerSalonId: String): List<ReferralCode>

    /**
     * Find codes by status
     */
    fun findByStatusOrderByCreatedAtDesc(status: ReferralCodeStatus): List<ReferralCode>



}
