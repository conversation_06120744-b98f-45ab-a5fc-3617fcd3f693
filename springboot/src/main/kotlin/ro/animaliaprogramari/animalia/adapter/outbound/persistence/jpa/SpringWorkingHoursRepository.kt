package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.WorkingHoursSettings

/**
 * Spring Data JPA repository for working hours settings
 */
@Repository
interface SpringWorkingHoursRepository : JpaRepository<WorkingHoursSettings, String> {
    /**
     * Find working hours settings by salon ID
     */
    fun findBySalonId(salonId: String): WorkingHoursSettings?

    /**
     * Check if working hours settings exist for salon
     */
    fun existsBySalonId(salonId: String): Boolean

    /**
     * Delete working hours settings by salon ID
     */
    fun deleteBySalonId(salonId: String)
}
