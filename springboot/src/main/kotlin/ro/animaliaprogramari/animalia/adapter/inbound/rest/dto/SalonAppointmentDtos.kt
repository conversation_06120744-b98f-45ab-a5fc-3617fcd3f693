package ro.animaliaprogramari.animalia.adapter.inbound.rest.dto

import com.fasterxml.jackson.annotation.JsonFormat
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.*
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * DTO for custom service data in appointments
 */
@Schema(description = "Custom service data for ad-hoc service modifications")
data class CustomServiceData(
    @Schema(description = "Original service name")
    val originalServiceName: String,
    @Schema(description = "Custom service name")
    val customName: String,
    @Schema(description = "Custom price")
    val customPrice: Double,
    @Schema(description = "Custom description")
    val customDescription: String? = null
)

/**
 * Request DTO for scheduling an appointment in a salon
 */
@Schema(description = "Request payload for scheduling an appointment")
data class ScheduleAppointmentRequest(
    @field:NotBlank(message = "Client ID is required")
    val clientId: String,
    // Pet ID is optional - if not provided, a new pet will be created
    val petId: String? = null,
    @field:NotBlank(message = "Staff ID is required")
    val staffId: String,
    @field:NotNull(message = "Appointment date is required")
    @JsonFormat(pattern = "yyyy-MM-dd")
    val appointmentDate: LocalDate,
    @field:NotNull(message = "Start time is required")
    @JsonFormat(pattern = "HH:mm:ss")
    val startTime: LocalTime,
    @field:NotNull(message = "End time is required")
    @JsonFormat(pattern = "HH:mm:ss")
    val endTime: LocalTime,
    @field:NotEmpty(message = "At least one service is required")
    val serviceIds: List<String>,
    // Custom services for ad-hoc modifications (optional)
    val customServices: Map<String, CustomServiceData>? = null,
    val notes: String? = null,

    // Legacy repetition field (kept for backward compatibility)
    @field:Pattern(regexp = "daily|weekly|biweekly|monthly", message = "Invalid repetition frequency")
    val repetitionFrequency: String? = null,

    // Enhanced recurrence fields
    @field:Min(value = 1, message = "Recurrence frequency must be at least 1")
    @field:Max(value = 99, message = "Recurrence frequency cannot exceed 99")
    val recurrenceFrequency: Int? = null,

    @field:Pattern(regexp = "DAYS|WEEKS|MONTHS", message = "Invalid recurrence period. Must be DAYS, WEEKS, or MONTHS")
    val recurrencePeriod: String? = null,

    @field:Min(value = 1, message = "Total repetitions must be at least 1")
    @field:Max(value = 24, message = "Total repetitions cannot exceed 24")
    val totalRepetitions: Int? = null,

    // Payment model options for recurring appointments
    @field:Pattern(regexp = "PER_APPOINTMENT|UPFRONT_WITH_DISCOUNT", message = "Invalid payment model. Must be PER_APPOINTMENT or UPFRONT_WITH_DISCOUNT")
    val paymentModel: String? = null,

    @field:DecimalMin(value = "0.0", message = "Discount percentage must be non-negative")
    @field:DecimalMax(value = "100.0", message = "Discount percentage cannot exceed 100")
    val discountPercentage: BigDecimal? = null,

    val clientName: String?,
    val clientPhone: PhoneNumber?,
    val petName: String?,
    val petSpecies: String?,
    val petBreed: String?,
    @field:Pattern(regexp = "S|M|L|XL", message = "Invalid pet size")
    val petSize: String?,
)

/**
 * Response DTO for appointment information with salon context
 */
@Schema(description = "Response details for a scheduled appointment")
data class AppointmentResponse(
    val id: String,
    val salonId: String,
    val client: ClientSummary,
    val pet: PetSummary,
    val staff: StaffSummary,
    @JsonFormat(pattern = "yyyy-MM-dd")
    val appointmentDate: LocalDate,
    @JsonFormat(pattern = "HH:mm:ss")
    val startTime: LocalTime,
    @JsonFormat(pattern = "HH:mm:ss")
    val endTime: LocalTime,
    val services: List<ServiceSummary>,
    val customServices: Map<String, CustomServiceData>? = null,
    val status: String,
    val notes: String?,
    val photos: List<String> = emptyList(),
    // Recurring appointment fields
    val subscriptionId: String?,
    val sequenceNumber: Int?,
    val isRecurring: Boolean,
    val totalPrice: BigDecimal,
    val totalDuration: Int, // in minutes
    val completedAt: LocalDateTime? = null,
    val actualDurationMinutes: Int? = null,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
)

/**
 * Summary DTO for client information
 */
data class ClientSummary(
    val id: String,
    val name: String,
    val phone: String?,
    val email: String?,
)

/**
 * Summary DTO for pet information
 */
data class PetSummary(
    val id: String,
    val name: String,
    val breed: String?,
    val age: Int?,
    val weight: Double?,
)

/**
 * Summary DTO for staff information
 */
data class StaffSummary(
    val id: String,
    val name: String,
    val role: String,
    val specialties: List<String> = emptyList(),
)

/**
 * Summary DTO for service information
 */
data class ServiceSummary(
    val id: String,
    val name: String,
    val price: BigDecimal,
    val duration: Int, // in minutes
)

/**
 * Request DTO for updating an appointment
 */
data class UpdateAppointmentRequest(
    val clientId: String? = null,
    val petId: String? = null,
    val staffId: String? = null,
    @JsonFormat(pattern = "yyyy-MM-dd")
    val appointmentDate: LocalDate? = null,
    @JsonFormat(pattern = "HH:mm:ss")
    val startTime: LocalTime? = null,
    @JsonFormat(pattern = "HH:mm:ss")
    val endTime: LocalTime? = null,
    val serviceIds: List<String>? = null,
    val notes: String? = null,
)

/**
 * Request DTO for cancelling an appointment
 */
data class CancelAppointmentRequest(
    val reason: String? = null,
)

/**
 * Request DTO for completing an appointment
 */
data class CompleteAppointmentRequest(
    val notes: String? = null,
    val actualServiceIds: List<String>? = null,
    val completedAt: LocalDateTime? = null,
)

/**
 * Request DTO for rescheduling an appointment
 */
data class RescheduleAppointmentRequest(
    @field:NotNull(message = "Start time is required")
    val startTime: LocalDateTime,
    @field:NotNull(message = "End time is required")
    val endTime: LocalDateTime,
    val staffId: String? = null,
    val reason: String? = null,
)

