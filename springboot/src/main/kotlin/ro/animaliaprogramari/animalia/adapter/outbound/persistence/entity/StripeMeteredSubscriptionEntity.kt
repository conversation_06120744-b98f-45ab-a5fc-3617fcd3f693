package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "stripe_metered_subscriptions")
class StripeMeteredSubscriptionEntity(
    @Id
    var id: String = "",
    @Column(name = "salon_id", nullable = false, unique = true)
    var salonId: String = "",
    @Column(name = "stripe_customer_id")
    var stripeCustomerId: String? = null,
    @Column(name = "meter_event_name")
    var meterEventName: String? = null,
    @Column(name = "created_at", nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    constructor() : this(
        id = java.util.UUID.randomUUID().toString(),
        salonId = "",
    )
}

