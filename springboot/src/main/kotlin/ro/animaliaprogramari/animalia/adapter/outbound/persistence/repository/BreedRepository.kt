package ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.BreedEntity

/**
 * Repository interface for breed data access
 */
@Repository
interface BreedRepository : JpaRepository<BreedEntity, String> {

    /**
     * Find all active breeds
     */
    fun findByIsActiveTrue(): List<BreedEntity>

    /**
     * Find all breeds by species
     */
    fun findBySpeciesAndIsActiveTrue(species: String): List<BreedEntity>

    /**
     * Find all breeds by size
     */
    fun findBySizeAndIsActiveTrue(size: String): List<BreedEntity>

    /**
     * Find breed by name and species
     */
    fun findByNameAndSpeciesAndIsActiveTrue(name: String, species: String): BreedEntity?

    /**
     * Find breed by name (case insensitive)
     */
    @Query("SELECT b FROM BreedEntity b WHERE LOWER(b.name) = LOWER(:name) AND b.isActive = true")
    fun findByNameIgnoreCaseAndIsActiveTrue(@Param("name") name: String): List<BreedEntity>

    /**
     * Find all dog breeds
     */
    @Query("SELECT b FROM BreedEntity b WHERE b.species = 'dog' AND b.isActive = true ORDER BY b.name")
    fun findAllDogBreeds(): List<BreedEntity>

    /**
     * Find all cat breeds
     */
    @Query("SELECT b FROM BreedEntity b WHERE b.species = 'cat' AND b.isActive = true ORDER BY b.name")
    fun findAllCatBreeds(): List<BreedEntity>

    /**
     * Find all other animal breeds
     */
    @Query("SELECT b FROM BreedEntity b WHERE b.species = 'other' AND b.isActive = true ORDER BY b.name")
    fun findAllOtherBreeds(): List<BreedEntity>

    /**
     * Find all distinct species
     */
    @Query("SELECT DISTINCT b.species FROM BreedEntity b WHERE b.isActive = true ORDER BY b.species")
    fun findAllDistinctSpecies(): List<String>

    /**
     * Find all distinct sizes
     */
    @Query("SELECT DISTINCT b.size FROM BreedEntity b WHERE b.isActive = true ORDER BY b.size")
    fun findAllDistinctSizes(): List<String>

    /**
     * Search breeds by name containing text (case insensitive)
     */
    @Query("SELECT b FROM BreedEntity b WHERE LOWER(b.name) LIKE LOWER(CONCAT('%', :searchText, '%')) AND b.isActive = true ORDER BY b.name")
    fun searchByNameContaining(@Param("searchText") searchText: String): List<BreedEntity>

    /**
     * Search breeds by name containing text for specific species
     */
    @Query("SELECT b FROM BreedEntity b WHERE LOWER(b.name) LIKE LOWER(CONCAT('%', :searchText, '%')) AND b.species = :species AND b.isActive = true ORDER BY b.name")
    fun searchByNameContainingAndSpecies(@Param("searchText") searchText: String, @Param("species") species: String): List<BreedEntity>

    /**
     * Count breeds by species
     */
    fun countBySpeciesAndIsActiveTrue(species: String): Long

    /**
     * Check if breed exists by name and species
     */
    fun existsByNameAndSpeciesAndIsActiveTrue(name: String, species: String): Boolean
}
