package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.MassSmsTemplateEntity
import ro.animaliaprogramari.animalia.domain.model.MassSmsTemplate
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId

@Component
class MassSmsTemplateEntityMapper {

    fun toEntity(domain: MassSmsTemplate): MassSmsTemplateEntity {
        return MassSmsTemplateEntity(
            id = domain.id,
            salonId = domain.salonId?.value,
            name = domain.name,
            content = domain.content,
            category = domain.category,
            isDefault = domain.isDefault,
            usageCount = domain.usageCount,
            createdBy = domain.createdBy.value,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }

    fun toDomain(entity: MassSmsTemplateEntity): MassSmsTemplate {
        return MassSmsTemplate(
            id = entity.id,
            salonId = entity.salonId?.let { SalonId.of(it) },
            name = entity.name,
            content = entity.content,
            category = entity.category,
            isDefault = entity.isDefault,
            usageCount = entity.usageCount,
            createdBy = UserId.of(entity.createdBy),
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    fun toDomainList(entities: List<MassSmsTemplateEntity>): List<MassSmsTemplate> {
        return entities.map { toDomain(it) }
    }
}
