package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralConstants
import java.time.LocalDateTime

/**
 * JPA entity for salon referral codes
 */
@Entity
@Table(
    name = "salon_referral_codes",
    indexes = [
        Index(name = "idx_salon_referral_codes_salon_id", columnList = "salon_id"),
        Index(name = "idx_salon_referral_codes_code", columnList = "code")
    ]
)
data class SalonReferralCode(
    @Id
    val id: String,

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false, unique = true)
    val salonId: String,

    @field:NotBlank(message = "Referral code is required")
    @field:Size(min = ReferralConstants.CODE_LENGTH, max = ReferralConstants.CODE_LENGTH, message = "Referral code must be exactly ${ReferralConstants.CODE_LENGTH} characters")
    @Column(name = "code", nullable = false, length = ReferralConstants.CODE_LENGTH, unique = true)
    val code: String,

    @Column(name = "sms_credits_awarded", nullable = false)
    val smsCreditsAwarded: Int = ReferralConstants.DEFAULT_SMS_CREDITS,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        salonId = "",
        code = "",
        smsCreditsAwarded = 100,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    @PreUpdate
    fun preUpdate() {
        updatedAt = LocalDateTime.now()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as SalonReferralCode
        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}
