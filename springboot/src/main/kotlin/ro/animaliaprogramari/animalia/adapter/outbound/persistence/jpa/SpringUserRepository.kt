package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.User

@Repository
interface SpringUserRepository : JpaRepository<User, String> {
    fun findByFirebaseUid(firebaseUid: String): User?

    fun findByEmail(email: String): User?

    fun findByPhoneNumber(phoneNumber: String): User?

    fun existsByEmail(email: String): Boolean

    fun findByIsActiveTrue(): List<User>

    fun existsByFirebaseUid(firebaseUid: String): Boolean
}
