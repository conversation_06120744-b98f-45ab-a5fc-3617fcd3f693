package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Salon as SalonEntity

/**
 * Mapper for converting between Salon domain model and JPA entity
 */
@Component
class SalonEntityMapper {
    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(salon: Salon): SalonEntity {
        return SalonEntity(
            id = salon.id.value,
            name = salon.name,
            address = salon.address,
            city = salon.city,
            phone = salon.phone?.value,
            email = salon.email?.value,
            ownerId = salon.ownerId.value,
            isActive = salon.isActive,
            description = salon.description,
            clientIds = null, // No longer using clientIds field
            createdAt = salon.createdAt,
            updatedAt = salon.updatedAt,
            additionalSlotsCount = salon.additionalSlotsCount,
            googleReviewLink = salon.googleReviewLink,
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: SalonEntity): Salon {
        return Salon(
            id = SalonId.of(entity.id),
            name = entity.name,
            address = entity.address,
            city = entity.city,
            phone = entity.phone?.let { PhoneNumber.of(it) },
            email = entity.email?.let { Email.of(it) },
            ownerId = UserId.of(entity.ownerId),
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            description = entity.description,
            additionalSlotsCount = entity.additionalSlotsCount,
            googleReviewLink = entity.googleReviewLink,
        )
    }
}
