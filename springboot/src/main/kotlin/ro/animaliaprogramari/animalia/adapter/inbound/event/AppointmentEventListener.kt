package ro.animaliaprogramari.animalia.adapter.inbound.event

import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.event.appointment.*
import ro.animaliaprogramari.animalia.domain.service.MessagingService
import ro.animaliaprogramari.animalia.application.service.FirebaseService
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SmsReminderSettingsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.config.PushNotificationTemplates
import java.time.format.DateTimeFormatter
import java.time.LocalTime

/**
 * Listener that sends personalized SMS notifications and creates push notifications for appointment related events.
 * Uses PersonalizedSmsService to create warm, professional messages in Romanian.
 * Creates push notifications for staff members to see in the application.
 */
@Component
class AppointmentEventListener(
    private val messagingService: MessagingService,
    private val firebaseService: FirebaseService,
    private val staffRepository: StaffRepository,
    private val smsReminderSettingsRepository: SmsReminderSettingsRepository,
    private val clientRepository: ClientRepository,
    private val petRepository: PetRepository,
    private val appointmentRepository: AppointmentRepository,
) {
    private val logger = LoggerFactory.getLogger(AppointmentEventListener::class.java)

    private val dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy")
    private val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")

    private fun getNames(clientId: ClientId, petId: PetId?): Pair<String?, String?> {
        val clientName = try {
            clientRepository.findById(clientId)?.name
        } catch (_: Exception) {
            null
        }
        val petName = petId?.let {
            try {
                petRepository.findById(it)?.name
            } catch (_: Exception) {
                null
            }
        }
        return clientName to petName
    }

    @EventListener
    fun onAppointmentScheduled(event: AppointmentScheduledEvent) {
        logger.info("📨 [RACE_CONDITION_DEBUG] Event listener triggered for appointment: ${event.appointmentId.value}")
        logger.info("Processing appointment scheduled event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (settings.enabled && settings.appointmentConfirmations) {
            logger.info("📱 [RACE_CONDITION_DEBUG] SMS settings enabled, sending notification for appointment: ${event.appointmentId.value}")
            messagingService.sendAppointmentScheduledNotification(
                clientId = event.clientId,
                petId = event.petId,
                appointmentId = event.appointmentId,
                appointmentDate = event.appointmentDate,
                startTime = event.startTime,
            )
        } else {
            logger.info("📵 [RACE_CONDITION_DEBUG] SMS settings disabled or confirmations off for salon: ${event.salonId.value}")
        }

        // Create push notification for all staff members in the salon
        val (clientName, petName) = getNames(event.clientId, event.petId)

        val title = if (petName.isNullOrBlank()) {
            PushNotificationTemplates.NEW_APPOINTMENT_TITLE
        } else {
            "${PushNotificationTemplates.NEW_APPOINTMENT_TITLE}: $petName"
        }

        val message = PushNotificationTemplates.NEW_APPOINTMENT_MESSAGE
            .replace("{clientName}", clientName ?: PushNotificationTemplates.DEFAULT_CLIENT_NAME)
            .replace("{petName}", petName ?: PushNotificationTemplates.DEFAULT_PET_NAME)
            .replace("{date}", event.appointmentDate.format(dateFormatter))
            .replace("{time}", event.startTime.format(timeFormatter))

        createNotificationForAllStaff(
            salonId = event.salonId,
            appointmentId = event.appointmentId,
            title = title,
            message = message,
            eventType = "APPOINTMENT_SCHEDULED",
            notificationType = NotificationType.NEW_APPOINTMENT,
            startTime = event.startTime
        )
    }

    @EventListener
    fun onAppointmentCancelled(event: AppointmentCancelledEvent) {
        logger.info("Processing appointment cancelled event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (settings.enabled && settings.cancellationNotifications) {
            messagingService.sendAppointmentCancelledNotification(
                clientId = event.clientId,
                petId = fetchPetIdFrom(event.appointmentId),
                appointmentId = event.appointmentId,
                appointmentDate = event.appointmentDate,
            )
        }

        // Create push notification for all staff members in the salon
        val (clientName, petName) = getNames(event.clientId, null)

        val title = if (petName.isNullOrBlank()) {
            PushNotificationTemplates.APPOINTMENT_CANCELLED_TITLE
        } else {
            "${PushNotificationTemplates.APPOINTMENT_CANCELLED_TITLE}: $petName"
        }

        val baseMessage = PushNotificationTemplates.APPOINTMENT_CANCELLED_MESSAGE
            .replace("{clientName}", clientName ?: PushNotificationTemplates.DEFAULT_CLIENT_NAME)
            .replace("{petName}", petName ?: PushNotificationTemplates.DEFAULT_PET_NAME)
            .replace("{date}", event.appointmentDate.format(dateFormatter))
            .replace("{time}", "")  // Time not available in cancelled event

        val message = if (event.reason != null) {
            "$baseMessage. Motiv: ${event.reason}"
        } else {
            baseMessage
        }

        createNotificationForAllStaff(
            salonId = event.salonId,
            appointmentId = event.appointmentId,
            title = title,
            message = message,
            eventType = "APPOINTMENT_CANCELLED",
            notificationType = NotificationType.APPOINTMENT_CANCELLATION
        )
    }

    @EventListener
    fun onAppointmentRescheduled(event: AppointmentRescheduledEvent) {
        logger.info("Processing appointment rescheduled event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (settings.enabled && settings.rescheduleNotifications) {
            messagingService.sendAppointmentRescheduledNotification(
                clientId = event.clientId,
                petId = fetchPetIdFrom(event.appointmentId),
                appointmentId = event.appointmentId,
                oldDate = event.oldDate,
                newDate = event.newDate,
                newStartTime = event.newStartTime,
            )
        }

        // Create push notification for all staff members in the salon
        val (clientName, petName) = getNames(event.clientId, null)

        val title = if (petName.isNullOrBlank()) {
            PushNotificationTemplates.APPOINTMENT_RESCHEDULED_TITLE
        } else {
            "${PushNotificationTemplates.APPOINTMENT_RESCHEDULED_TITLE}: $petName"
        }

        val message = PushNotificationTemplates.APPOINTMENT_RESCHEDULED_MESSAGE
            .replace("{clientName}", clientName ?: PushNotificationTemplates.DEFAULT_CLIENT_NAME)
            .replace("{petName}", petName ?: PushNotificationTemplates.DEFAULT_PET_NAME)
            .replace("{oldDate}", event.oldDate.format(dateFormatter))
            .replace("{oldTime}", event.oldStartTime.format(timeFormatter))
            .replace("{newDate}", event.newDate.format(dateFormatter))
            .replace("{newTime}", event.newStartTime.format(timeFormatter))

        createNotificationForAllStaff(
            salonId = event.salonId,
            appointmentId = event.appointmentId,
            title = title,
            message = message,
            eventType = "APPOINTMENT_RESCHEDULED",
            notificationType = NotificationType.APPOINTMENT_RESCHEDULED,
            startTime = event.newStartTime
        )
    }

    @EventListener
    fun onAppointmentDeleted(event: AppointmentDeletedEvent) {
        logger.info("Processing appointment deleted event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (settings.enabled && settings.cancellationNotifications) {
            messagingService.sendAppointmentDeletedNotification(
                clientId = event.clientId,
                petId = fetchPetIdFrom(event.appointmentId),
                appointmentId = event.appointmentId,
                appointmentDate = event.appointmentDate,
            )
        }

        // Create push notification for all staff members in the salon
        val (clientName, petName) = getNames(event.clientId, null)

        val title = if (petName.isNullOrBlank()) {
            PushNotificationTemplates.APPOINTMENT_DELETED_TITLE
        } else {
            "${PushNotificationTemplates.APPOINTMENT_DELETED_TITLE}: $petName"
        }

        val message = PushNotificationTemplates.APPOINTMENT_DELETED_MESSAGE
            .replace("{clientName}", clientName ?: PushNotificationTemplates.DEFAULT_CLIENT_NAME)
            .replace("{petName}", petName ?: PushNotificationTemplates.DEFAULT_PET_NAME)
            .replace("{date}", event.appointmentDate.format(dateFormatter))
            .replace("{time}", "")

        createNotificationForAllStaff(
            salonId = event.salonId,
            appointmentId = event.appointmentId,
            title = title,
            message = message,
            eventType = "APPOINTMENT_DELETED",
            notificationType = NotificationType.NEW_APPOINTMENT
        )
    }

    /**
     * This should just send the reminders. The chec
     */
    @EventListener
    fun onAppointmentReminder(event: AppointmentReminderEvent) {
        logger.info("Processing appointment reminder event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (settings.enabled) {
            val petId = fetchPetIdFrom(event.appointmentId)


            messagingService.sendAppointmentReminderNotification(
                clientId = event.clientId,
                petId = petId,
                appointmentId = event.appointmentId,
                appointmentDate = event.appointmentDate,
                startTime = event.startTime,
            )
        }
    }

    private fun fetchPetIdFrom(event: AppointmentId): PetId? {
        // Retrieve the appointment to get the pet ID
        val appointment = try {
            appointmentRepository.findById(event)
        } catch (e: Exception) {
            logger.error("Failed to retrieve appointment ${event.value} for reminder", e)
            null
        }

        val petId = appointment?.petId
        logger.info("🐕 Retrieved pet ID for reminder: ${petId?.value ?: "null"}")
        return petId
    }

    @EventListener
    fun onAppointmentCompleted(event: AppointmentCompletedEvent) {
        logger.info("Processing appointment completed event for client: ${event.clientId.value}")

        val messages = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)
        if (messages.enabled && messages.completionMessages) {
            messagingService.sendAppointmentCompletionNotification(
                clientId = event.clientId,
                petId = fetchPetIdFrom(event.appointmentId),
                appointmentId = event.appointmentId,
            )
        }
    }

    @EventListener
    fun onAppointmentFollowUp(event: AppointmentFollowUpEvent) {
        logger.info("Processing appointment follow-up event for client: ${event.clientId.value}")

        val settings = smsReminderSettingsRepository.findBySalonId(event.salonId)
            ?: SmsReminderSettings.createDefault(event.salonId)

        logger.info("📬 Follow-up settings check - enabled: ${settings.enabled}, followUpMessages: ${settings.followUpMessages}")

        if (settings.enabled && settings.followUpMessages) {
            logger.info("📬 Sending follow-up SMS for appointment: ${event.appointmentId.value}")
            messagingService.sendAppointmentFollowUpNotification(
                clientId = event.clientId,
                petId = fetchPetIdFrom(event.appointmentId),
                appointmentId = event.appointmentId,
            )
        } else {
            logger.info("📵 Follow-up SMS disabled - enabled: ${settings.enabled}, followUpMessages: ${settings.followUpMessages}")
        }
    }

    /**
     * Helper method to send push notifications to all staff members in a salon via Firebase
     */
    private fun createNotificationForAllStaff(
        salonId: SalonId?,
        appointmentId: AppointmentId,
        title: String,
        message: String,
        eventType: String,
        notificationType: NotificationType,
        startTime: LocalTime? = null
    ) {
        try {
            if (salonId == null) {
                logger.warn("Cannot send notification: salon ID not found")
                return
            }

            logger.info("Creating push notification for all staff - Salon: $salonId, Appointment: $appointmentId")

            // Get all active staff members for the salon
            val staffMembers = staffRepository.findActiveBySalonWithUserDetails(salonId)

            if (staffMembers.isEmpty()) {
                logger.warn("No active staff members found for salon $salonId")
                return
            }

            // Extract user IDs from staff members
            val userIds = staffMembers.map { staff ->
                staff.userId
            }

            if (userIds.isEmpty()) {
                logger.warn("No staff members with user details found for salon $salonId")
                return
            }

            logger.info("Sending notification to ${userIds.size} staff members in salon $salonId: ${userIds.map { it.value }}")

            val dataMap = mutableMapOf(
                "appointmentId" to appointmentId.value,
                "salonId" to salonId.value,
                "eventType" to eventType,
                "timestamp" to System.currentTimeMillis().toString()
            )
            startTime?.let { dataMap["startTime"] = it.toString() }

            // Send notification to all staff members
            val result = firebaseService.sendNotificationToUsers(
                userIds = userIds,
                salonId = salonId,
                title = title,
                body = message,
                notificationType = notificationType,
                data = dataMap,
                appointmentId = appointmentId
            )

            if (result.successfulUsers > 0) {
                logger.info("Successfully sent push notification to ${result.totalDevicesNotified} devices for ${result.successfulUsers}/${result.totalUsers} staff members: $title")
            } else {
                logger.warn("Failed to send push notification to any staff members. Total users: ${result.totalUsers}")
            }

        } catch (e: Exception) {
            logger.error("Failed to send push notification to staff for appointment $appointmentId", e)
        }
    }

}
