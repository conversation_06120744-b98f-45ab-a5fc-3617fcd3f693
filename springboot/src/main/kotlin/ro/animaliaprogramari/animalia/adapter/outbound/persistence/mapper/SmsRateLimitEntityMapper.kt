package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.notification.SmsRateLimitId
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsRateLimit as SmsRateLimitEntity
import ro.animaliaprogramari.animalia.domain.model.notification.SmsRateLimit as SmsRateLimitDomain

/**
 * Mapper between domain SmsRateLimit and JPA SmsRateLimitEntity
 */
@Component
class SmsRateLimitEntityMapper {
    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: SmsRateLimitEntity): SmsRateLimitDomain {
        return SmsRateLimitDomain(
            id = SmsRateLimitId.of(entity.id),
            phoneNumber = PhoneNumber.of(entity.phoneNumber),
            sentCount = entity.sentCount,
            windowStart = entity.windowStart,
            windowDurationMinutes = entity.windowDurationMinutes,
            maxSmsPerWindow = entity.maxSmsPerWindow,
            lastSentAt = entity.lastSentAt,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: SmsRateLimitDomain): SmsRateLimitEntity {
        return SmsRateLimitEntity(
            id = domain.id.value,
            phoneNumber = domain.phoneNumber.value,
            sentCount = domain.sentCount,
            windowStart = domain.windowStart,
            windowDurationMinutes = domain.windowDurationMinutes,
            maxSmsPerWindow = domain.maxSmsPerWindow,
            lastSentAt = domain.lastSentAt,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }
}
