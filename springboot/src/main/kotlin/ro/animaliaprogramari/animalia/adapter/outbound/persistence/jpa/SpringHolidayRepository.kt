package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Holiday

/**
 * Spring Data JPA repository for holidays
 */
@Repository
interface SpringHolidayRepository : JpaRepository<Holiday, String> {
    /**
     * Find holidays by salon ID
     */
    fun findBySalonIdOrderByDate(salonId: String): List<Holiday>

    /**
     * Delete holidays by salon ID
     */
    fun deleteBySalonId(salonId: String)
}
