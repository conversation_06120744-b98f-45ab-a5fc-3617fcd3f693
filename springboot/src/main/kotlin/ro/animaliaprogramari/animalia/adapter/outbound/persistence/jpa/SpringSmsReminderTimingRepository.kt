package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsReminderTiming

@Repository
interface SpringSmsReminderTimingRepository : JpaRepository<SmsReminderTiming, String> {

    fun findBySalonId(salonId: String): List<SmsReminderTiming>

    fun findAllByIsEnabledTrue(): List<SmsReminderTiming>
}
