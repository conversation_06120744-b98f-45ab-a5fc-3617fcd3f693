package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.BlockTimeEntity
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek

/**
 * Mapper for converting between BlockTime domain model and BlockTimeEntity
 */
@Component
class BlockTimeEntityMapper(
    private val objectMapper: ObjectMapper,
) {
    /**
     * Convert domain model to entity
     */
    fun toEntity(blockTime: BlockTime): BlockTimeEntity {
        return BlockTimeEntity(
            id = blockTime.id.value,
            salonId = blockTime.salonId.value,
            startTime = blockTime.startTime,
            endTime = blockTime.endTime,
            reason = blockTime.reason.name,
            customReason = blockTime.customReason,
            staffIds = blockTime.staffIds.map { it.value }.toTypedArray(),
            createdBy = blockTime.createdBy.value,
            createdAt = blockTime.createdAt,
            updatedBy = blockTime.updatedBy?.value,
            updatedAt = blockTime.updatedAt,
            isRecurring = blockTime.isRecurring,
            recurrencePattern = blockTime.recurrencePattern?.let { serializeRecurrencePattern(it) },
            notes = blockTime.notes,
            status = blockTime.status.name,
        )
    }

    /**
     * Convert entity to domain model
     */
    fun toDomain(entity: BlockTimeEntity): BlockTime {
        return BlockTime(
            id = BlockTimeId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            startTime = entity.startTime,
            endTime = entity.endTime,
            reason = BlockReason.fromString(entity.reason),
            customReason = entity.customReason,
            staffIds = entity.staffIds.map { StaffId.of(it) }.toSet(),
            createdBy = UserId.of(entity.createdBy),
            createdAt = entity.createdAt,
            updatedBy = entity.updatedBy?.let { UserId.of(it) },
            updatedAt = entity.updatedAt,
            isRecurring = entity.isRecurring,
            recurrencePattern = entity.recurrencePattern?.let { deserializeRecurrencePattern(it) },
            notes = entity.notes,
            status = BlockTimeStatus.fromString(entity.status),
        )
    }

    /**
     * Convert list of entities to domain models
     */
    fun toDomainList(entities: List<BlockTimeEntity>): List<BlockTime> {
        return entities.map { toDomain(it) }
    }

    /**
     * Convert list of domain models to entities
     */
    fun toEntityList(blockTimes: List<BlockTime>): List<BlockTimeEntity> {
        return blockTimes.map { toEntity(it) }
    }

    /**
     * Serialize recurrence pattern to JSON
     */
    private fun serializeRecurrencePattern(pattern: RecurrencePattern): String {
        val patternData =
            mapOf(
                "type" to pattern.type.name,
                "interval" to pattern.interval,
                "daysOfWeek" to pattern.daysOfWeek?.map { it.name },
                "dayOfMonth" to pattern.dayOfMonth,
                "endDate" to pattern.endDate?.toString(),
                "occurrences" to pattern.occurrences,
            )
        return objectMapper.writeValueAsString(patternData)
    }

    /**
     * Deserialize recurrence pattern from JSON
     */
    private fun deserializeRecurrencePattern(json: String): RecurrencePattern {
        @Suppress("UNCHECKED_CAST")
        val patternData = objectMapper.readValue(json, Map::class.java) as Map<String, Any?>

        val type = RecurrenceType.fromString(patternData["type"] as String)
        val interval = (patternData["interval"] as Number).toInt()

        val daysOfWeek =
            (patternData["daysOfWeek"] as? List<*>)?.let { days ->
                days.filterIsInstance<String>().map { DayOfWeek.valueOf(it) }.toSet()
            }

        val dayOfMonth = (patternData["dayOfMonth"] as? Number)?.toInt()

        val endDate =
            (patternData["endDate"] as? String)?.let {
                java.time.ZonedDateTime.parse(it)
            }

        val occurrences = (patternData["occurrences"] as? Number)?.toInt()

        return RecurrencePattern(
            type = type,
            interval = interval,
            daysOfWeek = daysOfWeek,
            dayOfMonth = dayOfMonth,
            endDate = endDate,
            occurrences = occurrences,
        )
    }
}
