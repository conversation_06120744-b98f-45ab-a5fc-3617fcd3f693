package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Notification as NotificationEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationType as EntityNotificationType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationStatus as EntityNotificationStatus
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.RecipientType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.ContentType
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.*
import ro.animaliaprogramari.animalia.domain.model.notification.NotificationStatus as DomainNotificationStatus
import ro.animaliaprogramari.animalia.domain.model.notification.SmsNotificationType as DomainNotificationType

/**
 * Mapper between Notification domain model and Notification JPA entity
 * This handles the translation between the pure domain model and the persistence layer
 */
@Component
class NotificationEntityMapper(
    private val objectMapper: ObjectMapper
) {
    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: Notification): NotificationEntity {
        val (recipientType, recipientValue) = when (domain.recipient) {
            is NotificationRecipient.User -> RecipientType.USER to domain.recipient.userId.value
        }

        val contentType: ContentType
        val title: String?
        val body: String
        val data: String?

        when (domain.content) {
            is NotificationContent.Push -> {
                contentType = ContentType.PUSH
                title = domain.content.title
                body = domain.content.body
                data = if (domain.content.data.isNotEmpty()) objectMapper.writeValueAsString(domain.content.data) else null
            }
        }

        return NotificationEntity(
            id = domain.id.value,
            type = mapDomainTypeToEntity(domain.type),
            recipientType = recipientType,
            recipientValue = recipientValue,
            contentType = contentType,
            title = title,
            body = body,
            data = data,
            status = mapDomainStatusToEntity(domain.status),
            appointmentId = domain.appointmentId?.value,
            salonId = domain.salonId.value,
            sentAt = domain.sentAt,
            deliveredAt = domain.deliveredAt,
            failureReason = domain.failureReason,
            retryCount = domain.retryCount,
            maxRetries = domain.maxRetries,
            isRead = domain.isRead,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt,
        )
    }

    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: NotificationEntity): Notification {
        val recipient = when (entity.recipientType) {
            RecipientType.USER -> NotificationRecipient.User(UserId.of(entity.recipientValue))
        }

        val content = when (entity.contentType) {
            ContentType.PUSH -> {
                val dataMap = entity.data?.let {
                    objectMapper.readValue(it, Map::class.java) as Map<String, String>
                } ?: emptyMap()
                NotificationContent.Push(
                    title = entity.title ?: "",
                    body = entity.body,
                    data = dataMap
                )
            }
        }

        return Notification(
            id = NotificationId.of(entity.id),
            type = mapEntityTypeToDomain(entity.type),
            recipient = recipient,
            content = content,
            status = mapEntityStatusToDomain(entity.status),
            appointmentId = entity.appointmentId?.let { AppointmentId.of(it) },
            salonId = SalonId.of(entity.salonId),
            sentAt = entity.sentAt,
            deliveredAt = entity.deliveredAt,
            failureReason = entity.failureReason,
            retryCount = entity.retryCount,
            maxRetries = entity.maxRetries,
            isRead = entity.isRead,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
        )
    }

    private fun mapDomainTypeToEntity(domainType: DomainNotificationType): EntityNotificationType {
        return when (domainType) {
            DomainNotificationType.PUSH -> EntityNotificationType.PUSH
        }
    }

    private fun mapEntityTypeToDomain(entityType: EntityNotificationType): DomainNotificationType {
        return when (entityType) {
            EntityNotificationType.PUSH -> DomainNotificationType.PUSH
        }
    }

    private fun mapDomainStatusToEntity(domainStatus: DomainNotificationStatus): EntityNotificationStatus {
        return when (domainStatus) {
            DomainNotificationStatus.PENDING -> EntityNotificationStatus.PENDING
            DomainNotificationStatus.SENT -> EntityNotificationStatus.SENT
            DomainNotificationStatus.DELIVERED -> EntityNotificationStatus.DELIVERED
            DomainNotificationStatus.FAILED -> EntityNotificationStatus.FAILED
            DomainNotificationStatus.CANCELLED -> EntityNotificationStatus.CANCELLED
        }
    }

    private fun mapEntityStatusToDomain(entityStatus: EntityNotificationStatus): DomainNotificationStatus {
        return when (entityStatus) {
            EntityNotificationStatus.PENDING -> DomainNotificationStatus.PENDING
            EntityNotificationStatus.SENT -> DomainNotificationStatus.SENT
            EntityNotificationStatus.DELIVERED -> DomainNotificationStatus.DELIVERED
            EntityNotificationStatus.FAILED -> DomainNotificationStatus.FAILED
            EntityNotificationStatus.CANCELLED -> DomainNotificationStatus.CANCELLED
        }
    }
}
