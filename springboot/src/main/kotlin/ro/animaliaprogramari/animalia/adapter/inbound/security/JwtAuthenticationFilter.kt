package ro.animaliaprogramari.animalia.adapter.inbound.security

import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationUseCase

/**
 * JWT authentication filter for Spring Security
 * This is an inbound adapter that handles JWT token validation for incoming requests
 */
@Component
class JwtAuthenticationFilter(
    private val authenticationUseCase: AuthenticationUseCase,
) : OncePerRequestFilter() {

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: Fi<PERSON><PERSON>hain,
    ) {
        try {
            val requestUri = request.requestURI
            val method = request.method

            logger.info("Request: $method $requestUri")

            val token = extractTokenFromRequest(request)

            if (token != null) {
                if (SecurityContextHolder.getContext().authentication == null) {
                    val authenticatedUser = authenticationUseCase.validateToken(token)

                    if (authenticatedUser != null) {
                        val authorities = listOf(SimpleGrantedAuthority("ROLE_${authenticatedUser.role.name}"))

                        val authentication =
                            UsernamePasswordAuthenticationToken(
                                authenticatedUser,
                                null,
                                authorities,
                            )

                        authentication.details = WebAuthenticationDetailsSource().buildDetails(request)
                        SecurityContextHolder.getContext().authentication = authentication

                    } else {
                        logger.error("=== JWT VALIDATION FAILED ===")
                        logger.error("Invalid JWT token for request: $method $requestUri")
                    }
                } else {
                    logger.info("Authentication already exists in SecurityContext")
                }
            } else {
                logger.info("No JWT token found in request headers")
            }
        } catch (e: Exception) {
            logger.error("=== JWT AUTHENTICATION ERROR ===")
            logger.error("Exception during JWT authentication for ${request.method} ${request.requestURI}")
            logger.error("Exception type: ${e.javaClass.simpleName}")
            logger.error("Exception message: ${e.message}")
            logger.error("Stack trace: ", e)
        }

        filterChain.doFilter(request, response)
    }

    private fun extractTokenFromRequest(request: HttpServletRequest): String? {
        val bearerToken = request.getHeader("Authorization")
        return if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            bearerToken.substring(7)
        } else {
            null
        }
    }
}
