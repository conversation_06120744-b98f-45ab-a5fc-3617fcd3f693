package ro.animaliaprogramari.animalia.adapter.outbound.persistence.converter

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter
import org.springframework.stereotype.Component
import java.math.BigDecimal

/**
 * JPA converter for Map<String, BigDecimal> to JSON
 * Converts between Map<String, BigDecimal> and its JSON representation in the database
 */
@Converter
@Component
class JsonMapConverter : AttributeConverter<Map<String, BigDecimal>, String> {
    private val objectMapper = ObjectMapper()

    override fun convertToDatabaseColumn(attribute: Map<String, BigDecimal>?): String? {
        return if (attribute.isNullOrEmpty()) {
            null
        } else {
            try {
                objectMapper.writeValueAsString(attribute)
            } catch (e: Exception) {
                throw RuntimeException("Error converting map to JSON", e)
            }
        }
    }

    override fun convertToEntityAttribute(dbData: String?): Map<String, BigDecimal>? {
        return if (dbData.isNullOrBlank()) {
            null
        } else {
            try {
                val typeRef = object : TypeReference<Map<String, BigDecimal>>() {}
                objectMapper.readValue(dbData, typeRef)
            } catch (e: Exception) {
                throw RuntimeException("Error converting JSON to map", e)
            }
        }
    }
}

/**
 * JPA converter for Map<String, Int> to JSON
 * Converts between Map<String, Int> and its JSON representation in the database
 */
@Converter
@Component
class JsonIntMapConverter : AttributeConverter<Map<String, Int>, String> {
    private val objectMapper = ObjectMapper()

    override fun convertToDatabaseColumn(attribute: Map<String, Int>?): String? {
        return if (attribute.isNullOrEmpty()) {
            null
        } else {
            try {
                objectMapper.writeValueAsString(attribute)
            } catch (e: Exception) {
                throw RuntimeException("Error converting int map to JSON", e)
            }
        }
    }

    override fun convertToEntityAttribute(dbData: String?): Map<String, Int>? {
        return if (dbData.isNullOrBlank()) {
            null
        } else {
            try {
                val typeRef = object : TypeReference<Map<String, Int>>() {}
                objectMapper.readValue(dbData, typeRef)
            } catch (e: Exception) {
                throw RuntimeException("Error converting JSON to int map", e)
            }
        }
    }
}
