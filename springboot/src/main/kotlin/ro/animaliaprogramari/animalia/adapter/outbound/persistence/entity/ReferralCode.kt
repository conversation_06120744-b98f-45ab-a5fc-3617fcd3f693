package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralConstants
import java.time.LocalDateTime

@Entity
@Table(
    name = "referral_codes",
    indexes = [
        Index(name = "idx_referral_code", columnList = "code", unique = true),
        Index(name = "idx_referral_generator_salon", columnList = "generator_salon_id"),
        Index(name = "idx_referral_claimer_salon", columnList = "claimer_salon_id"),
        Index(name = "idx_referral_status", columnList = "status"),
        Index(name = "idx_referral_created_at", columnList = "created_at")
    ]
)
data class ReferralCode(
    @Id
    val id: String,

    @field:NotBlank(message = "Referral code is required")
    @field:Size(min = ReferralConstants.CODE_LENGTH, max = ReferralConstants.CODE_LENGTH, message = "Referral code must be exactly ${ReferralConstants.CODE_LENGTH} characters")
    @Column(name = "code", nullable = false, length = ReferralConstants.CODE_LENGTH, unique = true)
    val code: String,

    @field:NotBlank(message = "Generator salon ID is required")
    @Column(name = "generator_salon_id", nullable = false)
    val generatorSalonId: String,

    @Column(name = "claimer_salon_id")
    val claimerSalonId: String? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    val status: ReferralCodeStatus = ReferralCodeStatus.ACTIVE,

    @Column(name = "sms_credits_awarded", nullable = false)
    val smsCreditsAwarded: Int = ReferralConstants.DEFAULT_SMS_CREDITS,

    @Column(name = "claimed_at")
    val claimedAt: LocalDateTime? = null,

    @Column(name = "expires_at")
    val expiresAt: LocalDateTime? = null,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        code = "",
        generatorSalonId = "",
        claimerSalonId = null,
        status = ReferralCodeStatus.ACTIVE,
        smsCreditsAwarded = ReferralConstants.DEFAULT_SMS_CREDITS,
        claimedAt = null,
        expiresAt = null,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    fun isExpired(): Boolean = expiresAt?.isBefore(LocalDateTime.now()) == true

    fun isActive(): Boolean = status == ReferralCodeStatus.ACTIVE && !isExpired()

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as ReferralCode
        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}

enum class ReferralCodeStatus {
    ACTIVE,
    CLAIMED,
    EXPIRED,
    CANCELLED
}
