package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import ro.animaliaprogramari.animalia.domain.model.PaymentModel
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Entity to track payments for recurring appointments
 * Supports both per-appointment and upfront payment models
 */
@Entity
@Table(name = "recurring_appointment_payments")
data class RecurringAppointmentPayment(
    @Id
    val id: String = "",

    @field:NotBlank(message = "Subscription ID is required")
    @Column(name = "subscription_id", nullable = false)
    val subscriptionId: String = "",

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String = "",

    @field:NotBlank(message = "Client ID is required")
    @Column(name = "client_id", nullable = false)
    val clientId: String = "",

    @Enumerated(EnumType.STRING)
    @Column(name = "payment_model", nullable = false)
    val paymentModel: PaymentModel = PaymentModel.PER_APPOINTMENT,

    @field:NotNull(message = "Payment amount is required")
    @field:Positive(message = "Payment amount must be positive")
    @Column(name = "payment_amount", precision = 10, scale = 2, nullable = false)
    val paymentAmount: BigDecimal = BigDecimal.ZERO,

    @Column(name = "original_total_amount", precision = 10, scale = 2)
    val originalTotalAmount: BigDecimal? = null,

    @Column(name = "discount_percentage", precision = 5, scale = 2)
    val discountPercentage: BigDecimal? = null,

    @Column(name = "discount_amount", precision = 10, scale = 2)
    val discountAmount: BigDecimal? = null,

    @field:Positive(message = "Number of appointments must be positive")
    @Column(name = "number_of_appointments", nullable = false)
    val numberOfAppointments: Int = 1,

    @Column(name = "appointment_price", precision = 10, scale = 2, nullable = false)
    val appointmentPrice: BigDecimal = BigDecimal.ZERO,

    @Column(name = "payment_status", nullable = false)
    val paymentStatus: String = "PENDING", // PENDING, COMPLETED, FAILED, REFUNDED

    @Column(name = "payment_method")
    val paymentMethod: String? = null, // CASH, CARD, BANK_TRANSFER, etc.

    @Column(name = "payment_reference")
    val paymentReference: String? = null, // External payment system reference

    @Column(name = "notes", columnDefinition = "TEXT")
    val notes: String? = null,

    @Column(name = "paid_at")
    val paidAt: LocalDateTime? = null,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // JPA requires a no-arg constructor
    constructor() : this(
        id = "",
        subscriptionId = "",
        salonId = "",
        clientId = "",
        paymentModel = PaymentModel.PER_APPOINTMENT,
        paymentAmount = BigDecimal.ZERO,
        originalTotalAmount = null,
        discountPercentage = null,
        discountAmount = null,
        numberOfAppointments = 1,
        appointmentPrice = BigDecimal.ZERO,
        paymentStatus = "PENDING",
        paymentMethod = null,
        paymentReference = null,
        notes = null,
        paidAt = null,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    /**
     * Check if this is an upfront payment
     */
    fun isUpfrontPayment(): Boolean = paymentModel == PaymentModel.UPFRONT_WITH_DISCOUNT

    /**
     * Check if payment is completed
     */
    fun isCompleted(): Boolean = paymentStatus == "COMPLETED"

    /**
     * Calculate effective price per appointment after discount
     */
    fun getEffectivePricePerAppointment(): BigDecimal {
        return if (numberOfAppointments > 0) {
            paymentAmount.divide(BigDecimal(numberOfAppointments), 2, java.math.RoundingMode.HALF_UP)
        } else {
            BigDecimal.ZERO
        }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as RecurringAppointmentPayment
        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun toString(): String {
        return "RecurringAppointmentPayment(id='$id', subscriptionId='$subscriptionId', paymentModel=$paymentModel, paymentAmount=$paymentAmount, paymentStatus='$paymentStatus')"
    }
}
