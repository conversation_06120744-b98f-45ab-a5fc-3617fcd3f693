package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "sms_reminder_settings")
data class SmsReminderSettings(
    @Id
    @Column(name = "salon_id", nullable = false)
    val salonId: String,
    @Column(name = "enabled", nullable = false)
    val enabled: Boolean = true,
    @Column(name = "appointment_confirmations", nullable = false)
    val appointmentConfirmations: Boolean = true,
    @Column(name = "reschedule_notifications", nullable = false)
    val rescheduleNotifications: Boolean = true,
    @Column(name = "cancellation_notifications", nullable = false)
    val cancellationNotifications: Boolean = true,
    @Column(name = "completion_messages", nullable = true)
    val completionMessages: Boolean? = null,
    @Column(name = "follow_up_messages", nullable = false)
    val followUpMessages: Boolean = false,
    @Column(name = "birthday_messages", nullable = false)
    val birthdayMessages: Boolean = false,
    @Column(name = "messaging_channel", nullable = false)
    @Enumerated(EnumType.STRING)
    val messagingChannel: MessagingChannel = MessagingChannel.SMS,
    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Default constructor for JPA
    constructor() : this(
        "",
    )
}

/**
 * Enum for messaging channel preferences
 */
enum class MessagingChannel {
    SMS,
    WHATSAPP
}
