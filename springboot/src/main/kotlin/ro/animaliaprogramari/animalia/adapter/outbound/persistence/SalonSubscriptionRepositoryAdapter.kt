package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.SalonSubscriptionRepository
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * JP<PERSON> adapter for salon subscription repository
 */
@Component
class SalonSubscriptionRepositoryAdapter(
    private val jpaRepository: SalonSubscriptionJpaRepository,
) : SalonSubscriptionRepository {

    override fun save(subscription: SalonSubscription): SalonSubscription {
        val entity = SalonSubscriptionEntity.fromDomain(subscription)
        val savedEntity = jpaRepository.save(entity)
        return savedEntity.toDomain()
    }

    override fun findById(id: SubscriptionId): SalonSubscription? {
        return jpaRepository.findById(id.value)
            .map { it.toDomain() }
            .orElse(null)
    }

    override fun findActiveBySalonId(salonId: SalonId): SalonSubscription? {
        return jpaRepository.findActiveBySalonId(salonId.value)?.toDomain()
    }

    override fun findBySalonId(salonId: SalonId): List<SalonSubscription> {
        return jpaRepository.findBySalonId(salonId.value)
            .map { it.toDomain() }
    }

    override fun findByUserId(userId: UserId): List<SalonSubscription> {
        return jpaRepository.findByUserId(userId.value)
            .map { it.toDomain() }
    }

    override fun findActiveByUserId(userId: UserId): List<SalonSubscription> {
        return jpaRepository.findActiveByUserId(userId.value)
            .map { it.toDomain() }
    }

    override fun findByRevenueCatCustomerId(customerId: String): SalonSubscription? {
        return jpaRepository.findByRevenueCatCustomerId(customerId)?.toDomain()
    }

    override fun findAllActive(): List<SalonSubscription> {
        return jpaRepository.findAllActive()
            .map { it.toDomain() }
    }

    override fun findAllExpired(): List<SalonSubscription> {
        return jpaRepository.findAllExpired()
            .map { it.toDomain() }
    }

    override fun findAllTrials(): List<SalonSubscription> {
        return jpaRepository.findAllTrials()
            .map { it.toDomain() }
    }

    override fun delete(id: SubscriptionId) {
        jpaRepository.deleteById(id.value)
    }

    override fun hasActiveSubscription(salonId: SalonId): Boolean {
        return jpaRepository.hasActiveSubscription(salonId.value)
    }

    override fun countByTier(tier: SubscriptionTier): Long {
        return jpaRepository.countByTier(tier)
    }

    override fun countByStatus(status: SubscriptionStatus): Long {
        return jpaRepository.countByStatus(status)
    }
}
