package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import jakarta.servlet.http.HttpServletRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CustomerInfo
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.Entitlement
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.Entitlements
import ro.animaliaprogramari.animalia.application.port.inbound.SubscriptionUseCase
import ro.animaliaprogramari.animalia.application.command.SyncSubscriptionCommand
import ro.animaliaprogramari.animalia.application.command.CreateSubscriptionFromWebhookCommand
import ro.animaliaprogramari.animalia.application.command.ExpireSubscriptionCommand
import ro.animaliaprogramari.animalia.application.service.RevenueCatWebhookIdempotencyService
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

/**
 * REST controller for RevenueCat webhook events
 */
@RestController
@RequestMapping("/subscriptions/webhook")
@Tag(name = "RevenueCat Webhooks", description = "Handle RevenueCat webhook events")
class RevenueCatWebhookController(
    private val subscriptionUseCase: SubscriptionUseCase,
    private val idempotencyService: RevenueCatWebhookIdempotencyService,
    @Value("\${revenuecat.webhook.secret}") private val webhookSecret: String,
) {
    private val logger = LoggerFactory.getLogger(RevenueCatWebhookController::class.java)

    @GetMapping("/test")
    @Operation(summary = "Test webhook endpoint")
    fun testWebhook(): ResponseEntity<Map<String, String>> {
        logger.info("Webhook test endpoint called")
        return ResponseEntity.ok(mapOf(
            "status" to "success",
            "message" to "Webhook endpoint is working",
            "timestamp" to LocalDateTime.now().toString()
        ))
    }

    @PostMapping
    @Operation(summary = "Handle RevenueCat webhook events")
    fun handleWebhook(
        @RequestBody payload: Map<String, Any>,
        request: HttpServletRequest
    ): ResponseEntity<Map<String, String>> {
        return try {
            val event = payload["event"] as? Map<String, Any>
            val eventType = event?.get("type") as? String
            logger.info("Received RevenueCat webhook: $eventType")

            // Extract event ID for idempotency checking
            val eventId = event?.get("id") as? String

            if (eventId == null) {
                logger.warn("No event ID found in RevenueCat webhook payload")
                return ResponseEntity.badRequest().body(mapOf("error" to "Missing event ID"))
            }

            // Check for duplicate events (idempotency)
            if (idempotencyService.isEventAlreadyProcessed(eventId)) {
                logger.info("RevenueCat webhook event $eventId already processed, returning success")
                return ResponseEntity.ok(mapOf("status" to "success", "message" to "Event already processed"))
            }

            // Verify webhook authenticity (optional but recommended)
            if (!verifyWebhookSignature(request)) {
                logger.warn("Invalid webhook signature")
                return ResponseEntity.status(401).body(mapOf("error" to "Invalid signature"))
            }

            // Process the event with idempotency protection
            val processed = idempotencyService.processEventIdempotently(eventId) {
                when (val eventTypeString = event["type"] as? String) {
                    "INITIAL_PURCHASE" -> handleInitialPurchase(payload)
                    "RENEWAL" -> handleRenewal(payload)
                    "CANCELLATION" -> handleCancellation(payload)
                    "UNCANCELLATION" -> handleUncancellation(payload)
                    "NON_RENEWING_PURCHASE" -> handleNonRenewingPurchase(payload)
                    "EXPIRATION" -> handleExpiration(payload)
                    "BILLING_ISSUE" -> handleBillingIssue(payload)
                    "PRODUCT_CHANGE" -> handleProductChange(payload)
                    //PRODUCT_CHANGE events and webhooks
                    //The expiration_at_ms will always be for the product the customer is changing from (old product).
                    //The PRODUCT_CHANGE webhook should be considered informative, and does not mean that the product change has gone into effect.
                    // When the product change goes into effect you will receive a
                    // RENEWAL event on Apple and Stripe or a
                    // INITIAL_PURCHASE event on Google Play.
                    "TRANSFER" -> handleTransfer()
//                "INVOICE_ISSUANCE" -> handleInvoiceIssuance(payload)
                    else -> {
                        logger.info("Unhandled webhook event type: $eventTypeString")
                    }
                }
            }

            if (processed) {
                ResponseEntity.ok(mapOf("status" to "success"))
            } else {
                ResponseEntity.status(500).body(mapOf("error" to "Failed to process event"))
            }
        } catch (e: Exception) {
            logger.error("Error processing webhook", e)
            ResponseEntity.status(500).body(mapOf("error" to "Internal server error"))
        }
    }

    private fun handleInitialPurchase(payload: Map<String, Any>) {
        logger.info("Processing initial purchase webhook")
        val event = payload["event"] as Map<String, Any>
        val appUserId = event["app_user_id"] as String
        // app_user_id is the actual user ID from RevenueCat
        val userId = appUserId
        try {
            // Create new subscription from webhook data
            val command = CreateSubscriptionFromWebhookCommand(
                userId = UserId.of(userId),
                customerInfo = extractCustomerInfo(payload)
            )
            val subscription = subscriptionUseCase.createSubscription(command)
            if (subscription != null) {
                logger.info("Successfully created subscription from initial purchase for user: $userId, subscription ID: ${subscription.id.value}")
            } else {
                logger.warn("Failed to create subscription from initial purchase for user: $userId")
            }
        } catch (e: Exception) {
            logger.error("Failed to process initial purchase for user: $userId", e)
        }
    }

    private fun handleRenewal(payload: Map<String, Any>) {
        logger.info("Processing renewal webhook")

        val event = payload["event"] as Map<String, Any>
        val appUserId = event["app_user_id"] as String
        val userId = appUserId

        try {
            val command = SyncSubscriptionCommand(
                userId = UserId.of(userId),
                salonId = null, // Will be determined by the use case
                customerInfo = extractCustomerInfo(payload)
            )

            subscriptionUseCase.renewall(command)
            logger.info("Successfully synced renewal for user: $userId")
        } catch (e: Exception) {
            logger.error("Failed to sync renewal for user: $userId", e)
        }
    }

    private fun handleProductChange(payload: Map<String, Any>) {
        logger.info("Processing product change webhook")

        val event = payload["event"] as Map<String, Any>
        val appUserId = event["app_user_id"] as String
        val userId = appUserId

        try {
            // For product changes, we need to update the existing subscription with the new tier
            val command = CreateSubscriptionFromWebhookCommand(
                userId = UserId.of(userId),
                customerInfo = extractCustomerInfo(payload)
            )

            val subscription = subscriptionUseCase.createSubscription(command)
            if (subscription != null) {
                logger.info("Successfully processed product change for user: $userId, new tier: ${subscription.tier.name}")
            } else {
                logger.warn("Failed to process product change for user: $userId")
            }
        } catch (e: Exception) {
            logger.error("Failed to process product change for user: $userId", e)
        }
    }

    private fun handleCancellation(payload: Map<String, Any>) {
        logger.info("Processing cancellation webhook")

        val event = payload["event"] as Map<String, Any>
        val revenueCatCustomerId = event["original_app_user_id"] as String
        val userId = event["app_user_id"] as String

        try {
            val command = ExpireSubscriptionCommand(
                userId = UserId.of(userId),
                revenueCatCustomerId = revenueCatCustomerId,
            )

            // Use cancel() instead of expire() to preserve trial period access
            // When a subscription is cancelled, the user should retain access until the end of their trial/billing period
            subscriptionUseCase.cancel(command)
            logger.info("Successfully processed cancellation for user: $userId")
        } catch (e: Exception) {
            logger.error("Failed to process cancellation for user: $userId", e)
        }
    }

    private fun handleUncancellation(payload: Map<String, Any>) {
        logger.info("Processing uncancellation webhook")

        val event = payload["event"] as Map<String, Any>
        val userId = event["app_user_id"] as String
        val revenueCatCustomerId = event["original_app_user_id"] as String

        try {
            val command = ExpireSubscriptionCommand(
                userId = UserId.of(userId),
                revenueCatCustomerId = revenueCatCustomerId,
            )

            subscriptionUseCase.uncancel(command)
            logger.info("Successfully processed uncancellation for user: $userId")
        } catch (e: Exception) {
            logger.error("Failed to process uncancellation for user: $userId", e)
        }
    }

    private fun handleNonRenewingPurchase(payload: Map<String, Any>) {
        logger.info("Processing non-renewing purchase webhook")
        // Handle one-time purchases (credits, etc.)
    }

    private fun handleExpiration(payload: Map<String, Any>) {
        logger.info("Processing expiration webhook")

        val event = payload["event"] as Map<String, Any>
        val userId = event["app_user_id"] as String
        val revenueCatCustomerId = event["original_app_user_id"] as String

        // Handle subscription expiration
        try {
            val command = ExpireSubscriptionCommand(
                userId = UserId.of(userId),
                revenueCatCustomerId = revenueCatCustomerId,
            )

            subscriptionUseCase.expire(command)
            logger.info("Successfully synced expiration for user: $userId")
        } catch (e: Exception) {
            logger.error("Failed to sync expiration for user: $userId", e)
        }
    }

    private fun handleBillingIssue(payload: Map<String, Any>) {
        logger.info("Processing billing issue webhook")

        val event = payload["event"] as Map<String, Any>
        val appUserId = event["app_user_id"] as String
        val salonId = appUserId

        // TODO: Implement billing issue handling (notifications, grace period, etc.)
        logger.warn("Billing issue detected for salon: $salonId")
    }

    private fun handleTransfer() {
        logger.info("Processing transfer webhook")
        // Handle subscription transfers between users
    }

    private fun extractCustomerInfo(payload: Map<String, Any>): CustomerInfo {
        // Extract relevant customer info from webhook payload
        val event = payload["event"] as Map<String, Any>

        // Convert millisecond timestamps to ISO date strings
        val purchasedAtMs = event["purchased_at_ms"] as? Long
        val expirationAtMs = event["expiration_at_ms"] as? Long

        val purchasedAt = purchasedAtMs?.let {
            java.time.Instant.ofEpochMilli(it).toString()
        } ?: ""

        // Handle null expiration dates - default to 1 month from purchase date
        val expiresAt = expirationAtMs?.let {
            java.time.Instant.ofEpochMilli(it).toString()
        } ?: run {
            // If no expiration date, default to 1 month from purchase date or current time
            val baseTime = purchasedAtMs?.let { java.time.Instant.ofEpochMilli(it) } ?: java.time.Instant.now()
            baseTime.plus(30, java.time.temporal.ChronoUnit.DAYS).toString()
        }

        val isTrial = (event["is_trial_conversion"] as? Boolean == true) || (event["period_type"] as? String == "TRIAL")
        return CustomerInfo(
            originalAppUserId = event["app_user_id"] as String,
            productId = event["new_product_id"] as? String ?: event["product_id"] as? String ?: "",
            purchasedAt = purchasedAt,
            expiresAt = expiresAt,
            isTrialPeriod = isTrial,
            environment = event["environment"] as? String ?: "production",
            entitlements = extractEntitlements(payload)
        )
    }

    private fun extractEntitlements(payload: Map<String, Any>): Entitlements {
        // Extract entitlement information from webhook
        val event = payload["event"] as Map<String, Any>
        val productId = event["new_product_id"] as? String ?: event["product_id"] as? String ?: ""

        // Map product ID to entitlements
        return when {
            productId.contains("freelancer") -> {
                Entitlements(
                    premiumAccess = Entitlement(
                        isActive = true,
                        productIdentifier = productId,
                        subscriptionTier = SubscriptionTier.FREELANCER
                    )
                )
            }
            productId.contains("team") -> {
                Entitlements(
                    premiumAccess = Entitlement(
                        isActive = true,
                        productIdentifier = productId,
                        subscriptionTier = SubscriptionTier.TEAM
                    ),
                    advancedReports = Entitlement(
                        isActive = true,
                        subscriptionTier = SubscriptionTier.TEAM,
                        productIdentifier = productId
                    )
                )
            }
            productId.contains("enterprise") -> {
                Entitlements(
                    premiumAccess = Entitlement(
                        isActive = true,
                        subscriptionTier = SubscriptionTier.ENTERPRISE,
                        productIdentifier = productId
                    ),
                    advancedReports = Entitlement(
                        isActive = true,
                        subscriptionTier = SubscriptionTier.ENTERPRISE,
                        productIdentifier = productId
                    ),
                    multiLocation = Entitlement(
                        isActive = true,
                        subscriptionTier = SubscriptionTier.ENTERPRISE,
                        productIdentifier = productId
                    )
                )
            }
            else -> {
                Entitlements() // Empty entitlements for unknown products
            }
        }
    }

    private fun verifyWebhookSignature(request: HttpServletRequest): Boolean {
        val authHeader = request.getHeader("Authorization")
        if (authHeader == null) {
            logger.warn("Missing Authorization header")
            return false
        }

        // RevenueCat sends the webhook secret directly, not as Bearer token
        val isValid = authHeader == webhookSecret

        if (!isValid) {
            logger.warn("Invalid webhook signature for RevenueCat webhook")
        }

        return isValid
    }
}
