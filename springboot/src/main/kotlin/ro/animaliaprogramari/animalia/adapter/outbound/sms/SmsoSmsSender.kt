package ro.animaliaprogramari.animalia.adapter.outbound.sms

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClient
import reactor.util.retry.Retry
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.application.port.outbound.MessageSendResult
import ro.animaliaprogramari.animalia.application.port.outbound.SmsSender
import ro.animaliaprogramari.animalia.application.port.outbound.MessagingChannelType
import ro.animaliaprogramari.animalia.domain.service.MessagingService
import ro.animaliaprogramari.animalia.infrastructure.config.SmsoProperties
import java.time.Duration

/**
 * SMS sender implementation using the SMSO provider.
 */
@Service
class SmsoSmsSender(
    @Qualifier("smsoWebClient")
    private val webClient: WebClient,
    private val smsoProperties: SmsoProperties,
) : SmsSender {
    private val logger = LoggerFactory.getLogger(SmsoSmsSender::class.java)

    override fun sendMessage(
        to: String,
        message: String,
        messageType: SmsMessageType,
        messageContext: MessagingService.MessageContext?
    ): MessageSendResult {
        val payload =
            mapOf(
                "to" to to,
                "body" to message,
                "sender" to 4,
            )

        try {
            webClient.post()
                .bodyValue(payload)
                .retrieve()
                .toBodilessEntity()
                .timeout(Duration.ofMillis(smsoProperties.timeout))
                .retryWhen(
                    Retry.fixedDelay(
                        smsoProperties.retryAttempts.toLong(),
                        Duration.ofSeconds(1),
                    ),
                )
                .block()
            logger.info("SMSO: sent SMS to $to (type: $messageType)")

            return MessageSendResult(
                channelType = MessagingChannelType.SMS,
                templateName = null,
                templateId = null,
                success = true
            )
        } catch (ex: Exception) {
            logger.error("SMSO: failed to send SMS to $to (type: $messageType)", ex)
            throw ex
        }
    }

    override fun getChannelType(): MessagingChannelType = MessagingChannelType.SMS
}
