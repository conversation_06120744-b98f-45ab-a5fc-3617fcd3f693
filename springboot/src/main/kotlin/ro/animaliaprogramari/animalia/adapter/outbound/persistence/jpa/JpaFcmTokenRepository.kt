package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.inbound.event.FcmTokenRepository as SpringFcmTokenRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.FcmTokenEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.FcmTokenRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.*
import java.time.LocalDateTime

/**
 * JPA adapter implementing the FcmTokenRepository port
 */
@Repository
class JpaFcmTokenRepository(
    private val springRepository: SpringFcmTokenRepository,
    private val mapper: FcmTokenEntityMapper,
) : FcmTokenRepository {

    override fun save(fcmToken: FcmToken): FcmToken {
        val entity = mapper.toEntity(fcmToken)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findById(id: FcmTokenId): FcmToken? {
        return springRepository.findById(id.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findByToken(token: String): FcmToken? {
        return springRepository.findAll()
            .firstOrNull { it.fcmToken == token }
            ?.let { mapper.toDomain(it) }
    }

    override fun findActiveByUserId(userId: UserId): List<FcmToken> {
        return springRepository.findByUserIdAndIsActiveTrue(userId.value)
            .map { mapper.toDomain(it) }
    }

    override fun findActiveBySalonId(salonId: SalonId): List<FcmToken> {
        return springRepository.findBySalonIdAndIsActiveTrue(salonId.value)
            .map { mapper.toDomain(it) }
    }

    override fun findActiveByUserIdAndSalonId(userId: UserId, salonId: SalonId): List<FcmToken> {
        return springRepository.findByUserIdAndSalonIdAndIsActiveTrue(userId.value, salonId.value)
            .map { mapper.toDomain(it) }
    }

    override fun findByDeviceId(deviceId: String): List<FcmToken> {
        return springRepository.findByDeviceIdAndIsActiveTrue(deviceId)
            .map { mapper.toDomain(it) }
    }

    override fun countActiveBySalonId(salonId: SalonId): Long {
        return springRepository.findBySalonIdAndIsActiveTrue(salonId.value).size.toLong()
    }

    override fun countActiveByUserId(userId: UserId): Long {
        return springRepository.findByUserIdAndIsActiveTrue(userId.value).size.toLong()
    }

    override fun findExpiredTokens(): List<FcmToken> {
        val cutoffDate = LocalDateTime.now().minusDays(60)
        return springRepository.findAll()
            .filter { it.lastUsed.isBefore(cutoffDate) && it.isActive }
            .map { mapper.toDomain(it) }
    }

    override fun deleteInactiveOlderThan(cutoffDate: LocalDateTime): Int {
        return springRepository.deleteInactiveTokensOlderThan(cutoffDate)
    }

    override fun deactivateOtherTokens(userId: UserId, salonId: SalonId, keepToken: String): Int {
        val tokensToDeactivate = springRepository.findByUserIdAndSalonIdAndIsActiveTrue(userId.value, salonId.value)
            .filter { it.fcmToken != keepToken }

        var deactivatedCount = 0
        tokensToDeactivate.forEach { token ->
            token.isActive = false
            token.updatedAt = LocalDateTime.now()
            springRepository.save(token)
            deactivatedCount++
        }

        return deactivatedCount
    }

    override fun existsActiveToken(token: String): Boolean {
        return springRepository.findAll()
            .any { it.fcmToken == token && it.isActive }
    }
}
