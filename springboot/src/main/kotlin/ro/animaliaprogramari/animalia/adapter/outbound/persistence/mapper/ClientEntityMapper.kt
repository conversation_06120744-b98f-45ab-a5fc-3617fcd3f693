package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Client as ClientEntity

/**
 * Mapper between domain Client and JPA ClientEntity
 * This handles the translation between pure domain model and persistence model
 */
@Component
class ClientEntityMapper {
    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: ClientEntity): Client {
        return Client(
            id = ClientId.of(entity.id),
            salonId = entity.salonId?.let { SalonId.of(it) },
            name = entity.name,
            phone = entity.phone?.let { PhoneNumber.of(it) },
            email = entity.email?.let { Email.of(it) },
            address = entity.address,
            notes = entity.notes,
            isActive = entity.isActive,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
        )
    }

    /**
     * Convert domain model to JPA entity
     * Now uses salonId from domain model directly
     */
    fun toEntity(domain: Client): ClientEntity {
        return ClientEntity(
            id = domain.id.value,
            salonId = domain.salonId?.value,
            name = domain.name,
            phone = domain.phone?.value,
            email = domain.email?.value,
            address = domain.address,
            notes = domain.notes,
            isActive = domain.isActive,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt,
            pets = emptyList(), // Pets are handled separately
        )
    }
}
