package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsSource
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsStatus
import ro.animaliaprogramari.animalia.domain.model.notification.UnlockConditions
import java.time.LocalDateTime

/**
 * JPA entity for blocked SMS credits
 */
@Entity
@Table(
    name = "blocked_sms_credits",
    indexes = [
        Index(name = "idx_blocked_sms_credits_salon_id", columnList = "salon_id"),
        Index(name = "idx_blocked_sms_credits_status", columnList = "status"),
        Index(name = "idx_blocked_sms_credits_source", columnList = "source, source_id")
    ]
)
data class BlockedSmsCredits(
    @Id
    val id: String = "",

    @field:NotBlank(message = "Salon ID is required")
    @Column(name = "salon_id", nullable = false)
    val salonId: String = "",

    @field:Min(value = 1, message = "Credits amount must be positive")
    @Column(name = "credits_amount", nullable = false)
    val creditsAmount: Int = 0,

    @Enumerated(EnumType.STRING)
    @Column(name = "source", nullable = false)
    val source: BlockedCreditsSource = BlockedCreditsSource.REFERRAL_REWARD,

    @field:NotBlank(message = "Source ID is required")
    @Column(name = "source_id", nullable = false)
    val sourceId: String = "",

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    val status: BlockedCreditsStatus = BlockedCreditsStatus.BLOCKED,

    @Column(name = "blocked_at", nullable = false)
    val blockedAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "unlocked_at")
    val unlockedAt: LocalDateTime? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "unlock_conditions", nullable = false)
    val unlockConditions: UnlockConditions = UnlockConditions.PAID_SUBSCRIPTION_AND_TRIAL_END,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    /**
     * Convert to domain model
     */
    fun toDomain(): ro.animaliaprogramari.animalia.domain.model.notification.BlockedSmsCredits {
        return ro.animaliaprogramari.animalia.domain.model.notification.BlockedSmsCredits(
            id = ro.animaliaprogramari.animalia.domain.model.notification.BlockedSmsCreditsId.of(id),
            salonId = ro.animaliaprogramari.animalia.domain.model.SalonId.of(salonId),
            creditsAmount = creditsAmount,
            source = source,
            sourceId = sourceId,
            status = status,
            blockedAt = blockedAt,
            unlockedAt = unlockedAt,
            unlockConditions = unlockConditions,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }

    companion object {
        /**
         * Create from domain model
         */
        fun fromDomain(domain: ro.animaliaprogramari.animalia.domain.model.notification.BlockedSmsCredits): BlockedSmsCredits {
            return BlockedSmsCredits(
                id = domain.id.value,
                salonId = domain.salonId.value,
                creditsAmount = domain.creditsAmount,
                source = domain.source,
                sourceId = domain.sourceId,
                status = domain.status,
                blockedAt = domain.blockedAt,
                unlockedAt = domain.unlockedAt,
                unlockConditions = domain.unlockConditions,
                createdAt = domain.createdAt,
                updatedAt = domain.updatedAt
            )
        }
    }
}
