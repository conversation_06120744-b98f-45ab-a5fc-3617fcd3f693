package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.PhoneVerificationCodeEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.PhoneVerificationCodeRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.PhoneVerificationCode
import ro.animaliaprogramari.animalia.domain.model.PhoneVerificationCodeId
import java.time.LocalDateTime

/**
 * JPA adapter implementing the PhoneVerificationCodeRepository port
 */
@Repository
class JpaPhoneVerificationCodeRepository(
    private val springRepository: SpringPhoneVerificationCodeRepository,
    private val mapper: PhoneVerificationCodeEntityMapper,
    private val userRepository: UserRepository
) : PhoneVerificationCodeRepository {

    override fun save(verificationCode: PhoneVerificationCode): PhoneVerificationCode {
        val entity = mapper.toEntity(verificationCode)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findById(id: PhoneVerificationCodeId): PhoneVerificationCode? {
        return springRepository.findById(id.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findLatestByPhoneNumber(phoneNumber: PhoneNumber): PhoneVerificationCode? {
        return springRepository.findByPhoneNumberOrderByCreatedAtDesc(phoneNumber.value)
            .firstOrNull()
            ?.let { mapper.toDomain(it) }
    }

    override fun findActiveByPhoneNumber(phoneNumber: PhoneNumber): List<PhoneVerificationCode> {
        val now = LocalDateTime.now()
        return springRepository.findActiveByPhoneNumber(phoneNumber.value, now)
            .map { mapper.toDomain(it) }
    }

    @Transactional
    override fun invalidateAllForPhoneNumber(phoneNumber: PhoneNumber) {
        val now = LocalDateTime.now()
        springRepository.invalidateAllForPhoneNumber(phoneNumber.value, now)
    }

    @Transactional
    override fun deleteExpiredCodes() {
        val now = LocalDateTime.now()
        springRepository.deleteExpiredCodes(now)
    }

    override fun phoneNumberExistsInSystem(phoneNumber: PhoneNumber): Boolean {
        // Check if phone number exists in users table
        return userRepository.findByPhoneNumber(phoneNumber) != null
    }

    override fun findValidCodeForPhoneNumber(phoneNumber: PhoneNumber): PhoneVerificationCode? {
        val now = LocalDateTime.now()
        return springRepository.findActiveByPhoneNumber(phoneNumber.value, now)
            .firstOrNull()
            ?.let { mapper.toDomain(it) }
    }

    override fun markAsUsed(verificationCode: PhoneVerificationCode): PhoneVerificationCode {
        val updatedCode = verificationCode.copy(
            isUsed = true,
            usedAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
        return save(updatedCode)
    }
}
