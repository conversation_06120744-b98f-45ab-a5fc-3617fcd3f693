package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.PhoneVerificationCodeId
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.PhoneVerificationCode as PhoneVerificationCodeEntity
import ro.animaliaprogramari.animalia.domain.model.PhoneVerificationCode as PhoneVerificationCodeDomain

/**
 * Mapper between domain PhoneVerificationCode and JPA PhoneVerificationCodeEntity
 */
@Component
class PhoneVerificationCodeEntityMapper {
    /**
     * Convert JPA entity to domain model
     */
    fun toDomain(entity: PhoneVerificationCodeEntity): PhoneVerificationCodeDomain {
        return PhoneVerificationCodeDomain(
            id = PhoneVerificationCodeId.of(entity.id),
            phoneNumber = PhoneNumber.of(entity.phoneNumber),
            code = entity.code,
            expiresAt = entity.expiresAt,
            isUsed = entity.isUsed,
            usedAt = entity.usedAt,
            attempts = entity.attempts,
            maxAttempts = entity.maxAttempts,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    /**
     * Convert domain model to JPA entity
     */
    fun toEntity(domain: PhoneVerificationCodeDomain): PhoneVerificationCodeEntity {
        return PhoneVerificationCodeEntity(
            id = domain.id.value,
            phoneNumber = domain.phoneNumber.value,
            code = domain.code,
            expiresAt = domain.expiresAt,
            isUsed = domain.isUsed,
            usedAt = domain.usedAt,
            attempts = domain.attempts,
            maxAttempts = domain.maxAttempts,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }
}
