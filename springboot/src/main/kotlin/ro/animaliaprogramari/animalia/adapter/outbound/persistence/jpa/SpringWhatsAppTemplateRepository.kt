// filepath: /Users/<USER>/animalia/springboot/src/main/kotlin/ro/animaliaprogramari/animalia/adapter/outbound/persistence/jpa/SpringWhatsAppTemplateRepository.kt
package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.WhatsAppTemplateEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType

@Repository
interface SpringWhatsAppTemplateRepository : JpaRepository<WhatsAppTemplateEntity, String> {
    fun findByTemplateType(templateType: SmsMessageType): List<WhatsAppTemplateEntity>
}

