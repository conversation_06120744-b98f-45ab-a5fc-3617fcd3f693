package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsQuota
import java.time.LocalDateTime

/**
 * Spring Data JPA repository for SMS quota
 */
@Repository
interface SpringSmsQuotaRepository : JpaRepository<SmsQuota, String> {

    /**
     * Find SMS quota by salon ID
     */
    fun findBySalonId(salonId: String): SmsQuota?

    /**
     * Check if SMS quota exists for a salon
     */
    fun existsBySalonId(salonId: String): Boolean

    // findQuotasNeedingReset method removed - SMS credits never expire

    /**
     * Delete SMS quota by salon ID
     */
    fun deleteBySalonId(salonId: String): Int
}
