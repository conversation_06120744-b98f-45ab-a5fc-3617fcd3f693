package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.service.ClientStatisticsService
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal
import java.time.LocalDateTime
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

/**
 * REST controller for client details operations
 * Handles client statistics, reviews, subscriptions, and appointments
 */
@RestController
@RequestMapping("/salons")
@Tag(name = "Client Details", description = "Operations for retrieving client information")
class ClientDetailsController(
    private val clientStatisticsService: ClientStatisticsService,
) {
    private val logger = LoggerFactory.getLogger(ClientDetailsController::class.java)

    /**
     * GET /api/salons/{salonId}/clients/{clientId}/stats
     * Get client statistics for a specific salon
     */
    @GetMapping("/{salonId}/clients/{clientId}/stats")
    @Operation(summary = "Client statistics", description = "Retrieve statistics for a salon client")
    @SwaggerApiResponse(responseCode = "200", description = "Statistics retrieved successfully")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    @SwaggerApiResponse(
        responseCode = "403",
        description = AnimaliaConstants.NU_AVETI_PERMISIUNEA_SA_ACCESATI_ACEASTA_RESURSA,
    )
    fun getClientStats(
        @PathVariable salonId: String,
        @PathVariable clientId: String,
    ): ResponseEntity<ApiResponse<ClientStatisticsResponse>> {
        logger.info("REST request to get stats for client $clientId in salon $salonId")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            val salon = SalonId.of(salonId)

            // Authorization: User must have client data access in this salon
            if (!currentUser.hasClientDataAccessInSalon(salon)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error(AnimaliaConstants.NU_AVETI_PERMISIUNEA_SA_ACCESATI_ACEASTA_RESURSA))
            }

            // Calculate real statistics for the client
            val stats = clientStatisticsService.calculateClientStatistics(
                clientId = ClientId.of(clientId),
                salonId = salon
            )

            val response =
                ClientStatisticsResponse(
                    totalAppointments = stats.totalAppointments,
                    completedAppointments = stats.completedAppointments,
                    cancelledAppointments = stats.cancelledAppointments,
                    noShowAppointments = stats.noShowAppointments,
                    upcomingAppointments = stats.upcomingAppointments,
                    lastVisitDate = stats.lastVisitDate,
                    totalRevenue = stats.totalRevenue.toDouble(),
                    averageAppointmentValue = stats.averageAppointmentValue.toDouble(),
                    totalPets = stats.totalPets,
                    loyaltyScore = stats.loyaltyScore.toDouble(),
                    favoriteServices = stats.favoriteServices,
                    averageRating = stats.averageRating?.toDouble(),
                    totalReviews = stats.totalReviews,
                    activeSubscriptions = stats.activeSubscriptions,
                    completionRate = stats.completionRate().toDouble(),
                    cancellationRate = stats.cancellationRate().toDouble(),
                    noShowRate = stats.noShowRate().toDouble(),
                    clientTier = stats.getClientTier().name,
                    isVip = stats.isVip(),
                )

            ResponseEntity.ok(ApiResponse.success(response))
        } catch (e: Exception) {
            logger.error("Error getting client stats", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(AnimaliaConstants.INTERNAL_SERVER_ERROR))
        }
    }
}
