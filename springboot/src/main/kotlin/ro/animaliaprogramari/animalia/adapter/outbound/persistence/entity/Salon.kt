package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime

@Entity
@Table(name = "salons")
data class Salon(
    @Id
    val id: String,
    @field:NotBlank(message = "Salon name is required")
    @field:Size(max = 255, message = "Name must not exceed 255 characters")
    @Column(nullable = false)
    val name: String,
    @field:Size(max = 1000, message = "Address must not exceed 1000 characters")
    @Column(columnDefinition = "TEXT")
    val address: String? = null,
    @field:Size(max = 255, message = "City must not exceed 255 characters")
    val city: String? = null,
    @field:Size(max = 50, message = "Phone must not exceed 50 characters")
    val phone: String? = null,
    @field:Size(max = 255, message = "Email must not exceed 255 characters")
    val email: String? = null,
    @Column(name = "owner_id", nullable = false)
    val ownerId: String,
    @Column(name = "is_active", columnDefinition = "BOOLEAN DEFAULT true")
    val isActive: Boolean = true,
    @Column(name = "client_ids", columnDefinition = "TEXT")
    val clientIds: String? = null, // Comma-separated string for H2 compatibility
    @Column(name = "created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),
    @Column(name = "updated_at")
    var updatedAt: LocalDateTime = LocalDateTime.now(),
    val description: String?,
    @Column(name = "additional_slots_count", columnDefinition = "INTEGER DEFAULT 0")
    val additionalSlotsCount: Int = 0,
    @field:Size(max = 500, message = "Google review link must not exceed 500 characters")
    @Column(name = "google_review_link", columnDefinition = "TEXT")
    val googleReviewLink: String? = null,
) {
    // Default constructor for JPA
    constructor() : this(
        "", "", null, null, null, null, "", true, null,
        LocalDateTime.now(), LocalDateTime.now(),"", 0, null,
    )

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Salon

        if (id != other.id) return false
        if (name != other.name) return false
        if (address != other.address) return false
        if (phone != other.phone) return false
        if (email != other.email) return false
        if (isActive != other.isActive) return false
        if (clientIds != other.clientIds) return false
        if (createdAt != other.createdAt) return false
        if (updatedAt != other.updatedAt) return false
        if (googleReviewLink != other.googleReviewLink) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + (address?.hashCode() ?: 0)
        result = 31 * result + (phone?.hashCode() ?: 0)
        result = 31 * result + (email?.hashCode() ?: 0)
        result = 31 * result + isActive.hashCode()
        result = 31 * result + (clientIds?.hashCode() ?: 0)
        result = 31 * result + createdAt.hashCode()
        result = 31 * result + updatedAt.hashCode()
        result = 31 * result + (googleReviewLink?.hashCode() ?: 0)
        return result
    }
}
