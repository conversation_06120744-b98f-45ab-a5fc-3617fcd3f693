package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.MassSmsCampaignEntity
import ro.animaliaprogramari.animalia.domain.model.MassSmsCampaign
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId

@Component
class MassSmsCampaignEntityMapper {

    fun toEntity(domain: MassSmsCampaign): MassSmsCampaignEntity {
        return MassSmsCampaignEntity(
            id = domain.id,
            salonId = domain.salonId.value,
            message = domain.message,
            templateId = domain.templateId,
            totalRecipients = domain.totalRecipients,
            successCount = domain.successCount,
            failureCount = domain.failureCount,
            estimatedCost = domain.estimatedCost,
            actualCost = domain.actualCost,
            status = domain.status,
            scheduledAt = domain.scheduledAt,
            sentAt = domain.sentAt,
            createdBy = domain.createdBy.value,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }

    fun toDomain(entity: MassSmsCampaignEntity): MassSmsCampaign {
        return MassSmsCampaign(
            id = entity.id,
            salonId = SalonId.of(entity.salonId),
            message = entity.message,
            templateId = entity.templateId,
            totalRecipients = entity.totalRecipients,
            successCount = entity.successCount,
            failureCount = entity.failureCount,
            estimatedCost = entity.estimatedCost,
            actualCost = entity.actualCost,
            status = entity.status,
            scheduledAt = entity.scheduledAt,
            sentAt = entity.sentAt,
            createdBy = UserId.of(entity.createdBy),
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    fun toDomainList(entities: List<MassSmsCampaignEntity>): List<MassSmsCampaign> {
        return entities.map { toDomain(it) }
    }

    fun toEntityList(domains: List<MassSmsCampaign>): List<MassSmsCampaignEntity> {
        return domains.map { toEntity(it) }
    }
}
