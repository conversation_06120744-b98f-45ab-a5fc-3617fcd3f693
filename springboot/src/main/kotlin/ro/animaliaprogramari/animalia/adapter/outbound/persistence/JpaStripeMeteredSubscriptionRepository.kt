package ro.animaliaprogramari.animalia.adapter.outbound.persistence

import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.StripeMeteredSubscriptionEntity
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringStripeMeteredSubscriptionRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StripeMeteredSubscriptionRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.billing.StripeMeteredSubscription
import java.time.LocalDateTime

@Component
class JpaStripeMeteredSubscriptionRepository(
    private val springRepo: SpringStripeMeteredSubscriptionRepository
) : StripeMeteredSubscriptionRepository {

    override fun findBySalonId(salonId: SalonId): StripeMeteredSubscription? {
        return springRepo.findBySalonId(salonId.value)?.toDomain()
    }

    override fun save(record: StripeMeteredSubscription): StripeMeteredSubscription {
        val entity = record.toEntity()
        val saved = springRepo.save(entity)
        return saved.toDomain()
    }

    private fun StripeMeteredSubscriptionEntity.toDomain(): StripeMeteredSubscription =
        StripeMeteredSubscription(
            id = this.id,
            salonId = SalonId.of(this.salonId),
            stripeCustomerId = this.stripeCustomerId,
            meterEventName = this.meterEventName,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt,
        )

    private fun StripeMeteredSubscription.toEntity(): StripeMeteredSubscriptionEntity =
        StripeMeteredSubscriptionEntity(
            id = this.id,
            salonId = this.salonId.value,
            stripeCustomerId = this.stripeCustomerId,
            meterEventName = this.meterEventName,
            createdAt = this.createdAt,
            updatedAt = LocalDateTime.now(),
        )
}

