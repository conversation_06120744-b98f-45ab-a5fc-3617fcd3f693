package ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.data.rest.core.annotation.RepositoryRestResource
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.AppointmentSettings
import java.util.*

/**
 * Repository for managing appointment settings
 * Excluded from Spring Data REST to avoid conflicts with custom controller
 */
@Repository
@RepositoryRestResource(exported = false)
interface AppointmentSettingsRepository : JpaRepository<AppointmentSettings, String> {

    /**
     * Find appointment settings by salon ID
     */
    @Query("SELECT a FROM AppointmentSettings a WHERE a.salonId = :salonId")
    fun findBySalonId(@Param("salonId") salonId: String): Optional<AppointmentSettings>

    /**
     * Check if appointment settings exist for a salon
     */
    @Query("SELECT COUNT(a) > 0 FROM AppointmentSettings a WHERE a.salonId = :salonId")
    fun existsBySalonId(@Param("salonId") salonId: String): Boolean

    /**
     * Delete appointment settings by salon ID
     */
    fun deleteBySalonId(salonId: String): Int
}
