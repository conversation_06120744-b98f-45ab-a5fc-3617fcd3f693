package ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsFollowUpTiming
import ro.animaliaprogramari.animalia.domain.model.SmsFollowUpTimingId
import ro.animaliaprogramari.animalia.domain.port.outbound.SmsFollowUpTimingRepository
import ro.animaliaprogramari.animalia.infrastructure.persistence.entity.SmsFollowUpTimingEntity
import ro.animaliaprogramari.animalia.infrastructure.persistence.repository.JpaSmsFollowUpTimingRepository

@Repository
class SmsFollowUpTimingRepositoryImpl(
    private val jpaRepository: JpaSmsFollowUpTimingRepository
) : SmsFollowUpTimingRepository {

    override fun save(timing: SmsFollowUpTiming): SmsFollowUpTiming {
        val entity = toEntity(timing)
        val saved = jpaRepository.save(entity)
        return toDomain(saved)
    }

    override fun findById(id: SmsFollowUpTimingId): SmsFollowUpTiming? {
        return jpaRepository.findById(id.value).map { toDomain(it) }.orElse(null)
    }

    override fun findBySalonId(salonId: SalonId): List<SmsFollowUpTiming> {
        return jpaRepository.findBySalonIdOrderByHoursAfterAsc(salonId.value).map { toDomain(it) }
    }

    override fun findAllEnabled(): List<SmsFollowUpTiming> {
        return jpaRepository.findByIsEnabledTrue().map { toDomain(it) }
    }

    override fun findBySalonIdAndEnabled(salonId: SalonId, enabled: Boolean): List<SmsFollowUpTiming> {
        return jpaRepository.findBySalonIdAndIsEnabled(salonId.value, enabled).map { toDomain(it) }
    }

    override fun deleteById(id: SmsFollowUpTimingId) {
        jpaRepository.deleteById(id.value)
    }

    override fun existsById(id: SmsFollowUpTimingId): Boolean {
        return jpaRepository.existsById(id.value)
    }

    override fun existsBySalonIdAndHoursAfter(salonId: SalonId, hoursAfter: Int): Boolean {
        return jpaRepository.existsBySalonIdAndHoursAfter(salonId.value, hoursAfter)
    }

    private fun toEntity(timing: SmsFollowUpTiming): SmsFollowUpTimingEntity {
        return SmsFollowUpTimingEntity(
            id = timing.id.value,
            salonId = timing.salonId.value,
            hoursAfter = timing.hoursAfter,
            isEnabled = timing.isEnabled,
            createdAt = timing.createdAt,
            updatedAt = timing.updatedAt
        )
    }

    private fun toDomain(entity: SmsFollowUpTimingEntity): SmsFollowUpTiming {
        return SmsFollowUpTiming(
            id = SmsFollowUpTimingId.of(entity.id),
            salonId = SalonId.of(entity.salonId),
            hoursAfter = entity.hoursAfter,
            isEnabled = entity.isEnabled,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }
}
