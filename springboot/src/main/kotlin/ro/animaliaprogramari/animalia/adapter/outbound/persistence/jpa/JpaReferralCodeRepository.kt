package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.application.port.outbound.ReferralCodeRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralCode
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralCodeId
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralCodeStatus

@Repository
class JpaReferralCodeRepository(
    private val springRepository: SpringReferralCodeRepository,
    private val mapper: ReferralCodeMapper
) : ReferralCodeRepository {

    override fun save(referralCode: ReferralCode): ReferralCode {
        val entity = mapper.toEntity(referralCode)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findById(id: ReferralCodeId): ReferralCode? {
        return springRepository.findById(id.value)
            .map { mapper.toDomain(it) }
            .orElse(null)
    }

    override fun findByCode(code: String): ReferralCode? {
        return springRepository.findByCode(code)
            ?.let { mapper.toDomain(it) }
    }

    override fun findByGeneratorSalonId(salonId: SalonId): List<ReferralCode> {
        return springRepository.findByGeneratorSalonIdOrderByCreatedAtDesc(salonId.value)
            .map { mapper.toDomain(it) }
    }

    override fun findByClaimerSalonId(salonId: SalonId): List<ReferralCode> {
        return springRepository.findByClaimerSalonIdOrderByClaimedAtDesc(salonId.value)
            .map { mapper.toDomain(it) }
    }

    override fun findByStatus(status: ReferralCodeStatus): List<ReferralCode> {
        val entityStatus = mapper.toEntityStatus(status)
        return springRepository.findByStatusOrderByCreatedAtDesc(entityStatus)
            .map { mapper.toDomain(it) }
    }

    override fun existsByCode(code: String): Boolean {
        return springRepository.existsByCode(code)
    }


}
