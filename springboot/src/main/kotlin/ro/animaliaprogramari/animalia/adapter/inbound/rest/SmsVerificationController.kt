package ro.animaliaprogramari.animalia.adapter.inbound.rest

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ApiResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ConfirmPhoneRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ConfirmPhoneResponse
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.ValidRomanianPhoneNumber
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.command.ConfirmPhoneCommand
import ro.animaliaprogramari.animalia.application.command.ResendPhoneVerificationCommand
import ro.animaliaprogramari.animalia.application.port.inbound.PhoneVerificationUseCase
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.exception.RateLimitExceededException
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse

@RestController
@RequestMapping("/auth")
@Tag(name = "SMS Verification", description = "Operations for verifying phone numbers")
class SmsVerificationController(
    private val phoneVerificationUseCase: PhoneVerificationUseCase
) {
    private val logger = LoggerFactory.getLogger(AuthenticationController::class.java)


    @PostMapping("/phone/send-verification")
    @Operation(
        summary = "Send phone verification code",
        description = "Send a 6-digit verification code to verify a phone number"
    )
    @SwaggerApiResponse(responseCode = "200", description = "Verification code sent successfully")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid phone number format")
    @SwaggerApiResponse(responseCode = "429", description = "Rate limit exceeded")
    fun sendPhoneVerification(
        @Valid @RequestBody request: SendPhoneVerificationRequest
    ): ResponseEntity<ApiResponse<Map<String, Any>>> {
        return try {
            // Normalize and validate Romanian phone number format
            val normalizedPhoneNumber = PhoneNumber.validateAndNormalize(request.phoneNumber)
                ?: return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(
                        message = "Formatul numărului de telefon nu este valid. Folosiți formatul românesc (+40XXXXXXXXX)",
                        code = "INVALID_PHONE_FORMAT"
                    ))

            val phoneNumber = PhoneNumber.of(normalizedPhoneNumber)

            val command = ResendPhoneVerificationCommand(phoneNumber)
            val result = phoneVerificationUseCase.sendVerificationCode(command)

            if (result.success) {
                val response: Map<String, Any> = mapOf(
                    "message" to result.message,
                    "phoneNumber" to result.phoneNumber,
                    "expiresIn" to (result.expiresIn ?: 300),
                    "canResendAfter" to (result.canResendAfter ?: 60),
                    "remainingAttempts" to (result.remainingAttempts ?: 0)
                )
                ResponseEntity.ok(ApiResponse.success(response))
            } else {
                when {
                    result.message.contains("limita") ->
                        ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                            .body(ApiResponse.error(result.message, "RATE_LIMIT_EXCEEDED"))
                    else ->
                        ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponse.error(result.message, "VERIFICATION_FAILED"))
                }
            }

        } catch (e: IllegalArgumentException) {
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(
                    message = "Formatul numărului de telefon nu este valid. Folosiți formatul românesc (+40XXXXXXXXX)",
                    code = "INVALID_PHONE_FORMAT"
                ))
        } catch (e: RateLimitExceededException) {
            ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                .body(ApiResponse.error(
                    message = e.message ?: "Ați depășit limita de SMS-uri",
                    code = "RATE_LIMIT_EXCEEDED"
                ))
        } catch (e: Exception) {
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(
                    message = "Eroare internă. Încercați din nou.",
                    code = "INTERNAL_ERROR"
                ))
        }
    }

    /**
     * PATCH /auth/profile/phone
     * Confirm phone number with verification code
     */
    @PatchMapping("/profile/phone")
    @Operation(summary = "Confirm phone number with verification code")
    @SwaggerApiResponse(responseCode = "200", description = "Phone number confirmed successfully")
    @SwaggerApiResponse(responseCode = "400", description = "Invalid verification code or phone number")
    @SwaggerApiResponse(responseCode = "401", description = AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR)
    fun confirmPhoneNumber(
        @Valid @RequestBody request: ConfirmPhoneRequest,
    ): ResponseEntity<ApiResponse<ConfirmPhoneResponse>> {
        logger.info("PATCH /auth/profile/phone - Phone confirmation request received")

        return try {
            val currentUser =
                SecurityUtils.getCurrentUser()
                    ?: return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error(AnimaliaConstants.USER_NOT_AUTHENTICATED_ERROR))

            // Normalize and validate Romanian phone number format
            val normalizedPhoneNumber = request.phoneNumber.replace(Regex("[\\s\\-\\(\\)\\.]"), "")
            val phoneNumber = PhoneNumber.of(normalizedPhoneNumber)
            if (!phoneNumber.isRomanianNumber()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(
                        "Formatul numărului de telefon nu este valid. Folosiți formatul românesc (+40XXXXXXXXX)",
                        "INVALID_PHONE_FORMAT"
                    ))
            }

            val command = ConfirmPhoneCommand(
                userId = currentUser.userId,
                phoneNumber = phoneNumber,
                confirmationCode = request.confirmationCode
            )

            val result = phoneVerificationUseCase.verifyPhoneNumber(command)

            val response = ConfirmPhoneResponse(
                success = result.success,
                message = result.message,
                phoneNumber = result.phoneNumber,
                verified = result.verified
            )

            if (result.success) {
                ResponseEntity.ok(ApiResponse.success(response))
            } else {
                ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(result.message, "VERIFICATION_FAILED"))
            }

        } catch (e: IllegalArgumentException) {
            logger.warn("Invalid phone number format: ${e.message}")
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error(
                    "Formatul numărului de telefon nu este valid. Folosiți formatul românesc (+40XXXXXXXXX)",
                    "INVALID_PHONE_FORMAT"
                ))
        } catch (e: Exception) {
            logger.error("Unexpected error during phone confirmation", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error(
                    "Eroare internă la confirmarea numărului de telefon. Încercați din nou.",
                    "INTERNAL_ERROR"
                ))
        }
    }

}



@Schema(description = "Request to send phone verification code")
data class SendPhoneVerificationRequest(
    @field:NotBlank(message = "Phone number is required")
    @field:ValidRomanianPhoneNumber
    @Schema(
        description = "Phone number in Romanian format. Accepts various formats: +40XXXXXXXXX, +40 XXX XXX XXX, 0XXXXXXXXX, etc.",
        example = "+40712345678"
    )
    val phoneNumber: String
)
