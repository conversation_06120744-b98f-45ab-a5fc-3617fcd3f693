package ro.animaliaprogramari.animalia.adapter.inbound.rest.mapper

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal

/**
 * Mapper between Appointment domain model and salon-specific DTOs
 */
@Component
class SalonAppointmentDtoMapper(
    private val clientRepository: ClientRepository,
    private val petRepository: PetRepository,
    private val salonServiceRepository: SalonServiceRepository,
    private val staffRepository: StaffRepository,
) {
    private val logger = LoggerFactory.getLogger(SalonAppointmentDtoMapper::class.java)

    /**
     * Convert domain model to response DTO
     */
    fun toResponse(appointment: Appointment): AppointmentResponse {
        // Load related entities with graceful handling of missing data
        val client = clientRepository.findById(appointment.clientId)
        if (client == null) {
            logger.warn("Client not found for appointment {}: {}", appointment.id.value, appointment.clientId.value)
        }

        val pet = petRepository.findById(appointment.petId)
        if (pet == null) {
            logger.warn("Pet not found for appointment {}: {}", appointment.id.value, appointment.petId.value)
        }

        val staff = staffRepository.findById(appointment.staffId)
        if (staff == null) {
            logger.warn(
                "Staff member not found for appointment {}: {}",
                appointment.id.value,
                appointment.staffId.value,
            )
        }

        return AppointmentResponse(
            id = appointment.id.value,
            salonId = appointment.salonId.value,
            client =
                client?.let {
                    ClientSummary(
                        id = it.id.value,
                        name = it.name,
                        phone = it.phone?.value,
                        email = it.email?.value,
                    )
                } ?: ClientSummary(
                    id = appointment.clientId.value,
                    name = "Unknown Client",
                    phone = null,
                    email = null,
                ),
            pet =
                pet?.let {
                    PetSummary(
                        id = it.id.value,
                        name = it.name,
                        breed = it.breed,
                        age = it.age,
                        weight = it.weight?.toDouble(),
                    )
                } ?: PetSummary(
                    id = appointment.petId.value,
                    name = "Unknown Pet",
                    breed = "Unknown",
                    age = null,
                    weight = null,
                ),
            staff =
                staff?.let {
                    StaffSummary(
                        id = it.id.value,
                        name = it.nickname ?: "Unknown Staff",
                        role = it.role.name,
                        specialties = emptyList(), // TODO: Add specialties from staff profile
                    )
                } ?: StaffSummary(
                    id = appointment.staffId.value,
                    name = "Unknown Staff",
                    role = "UNKNOWN",
                    specialties = emptyList(),
                ),
            appointmentDate = appointment.appointmentDate,
            startTime = appointment.startTime,
            endTime = appointment.endTime,
            services = appointment.serviceIds.also { serviceIds ->
                val uniqueServiceIds = serviceIds.toSet()
                if (uniqueServiceIds.size != serviceIds.size) {
                    logger.warn("⚠️ Duplicate service IDs detected in appointment {}: {}", appointment.id.value, serviceIds.map { it.value })
                }
            }.map { serviceId -> createServiceSummary(serviceId, appointment) },
            customServices = appointment.customServices?.mapKeys { it.key.value },
            status = appointment.status.name,
            notes = appointment.notes,
            photos = appointment.photos,
            subscriptionId = appointment.subscriptionId?.value,
            sequenceNumber = appointment.sequenceNumber,
            isRecurring = appointment.isRecurring,
            totalPrice = appointment.totalPrice.amount,
            totalDuration = appointment.totalMyDuration.minutes,
            completedAt = appointment.completedAt,
            actualDurationMinutes = appointment.actualDurationMinutes,
            createdAt = appointment.createdAt,
            updatedAt = appointment.updatedAt,
        )
    }

    /**
     * Helper method to create service summary from appointment service
     */
    private fun createServiceSummary(serviceId: ServiceId, appointment: Appointment): ServiceSummary {
        val service = salonServiceRepository.findById(serviceId)

        if (service == null) {
            logger.warn("Service not found for appointment {}: {}", appointment.id.value, serviceId.value)
        }

        // Check if there's a custom service for this service ID
        val customService = appointment.customServices?.get(serviceId)

        // Use custom service data if available, otherwise use original service data
        val serviceName = customService?.customName ?: service?.name ?: "Unknown Service"
        val servicePrice = customService?.let { BigDecimal.valueOf(it.customPrice) }
            ?: service?.basePrice?.amount
            ?: run {
                val serviceCount = appointment.serviceIds.size
                appointment.totalPrice.amount.divide(serviceCount.toBigDecimal(), 2, java.math.RoundingMode.HALF_UP)
            }

        val serviceDuration = service?.myDuration?.minutes
            ?: run {
                val serviceCount = appointment.serviceIds.size
                appointment.totalMyDuration.minutes / serviceCount
            }

        return ServiceSummary(
            id = serviceId.value,
            name = serviceName,
            price = servicePrice,
            duration = serviceDuration,
        )
    }
}
