// filepath: /Users/<USER>/animalia/springboot/src/main/kotlin/ro/animaliaprogramari/animalia/adapter/outbound/persistence/entity/WhatsAppTemplate.kt
package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import java.time.LocalDateTime

/**
 * JPA entity for WhatsApp templates stored in DB (optional).
 * This mirrors the domain model `WhatsAppTemplate` but is separate to avoid name clashes.
 */
@Entity
@Table(
    name = "whatsapp_templates",
    indexes = [
        Index(name = "idx_whatsapp_templates_type", columnList = "template_type")
    ]
)
data class WhatsAppTemplateEntity(
    @Id
    @Column(name = "id", nullable = false)
    val id: String,

    @Enumerated(EnumType.STRING)
    @Column(name = "template_type", nullable = false)
    val templateType: SmsMessageType,

    @field:NotBlank(message = "Display name is required")
    @Column(name = "display_name", nullable = false)
    val displayName: String,

    @Column(name = "language_flag", nullable = false)
    val languageFlag: String = "",

    @field:NotBlank(message = "Preview text is required")
    @Column(name = "preview_text", nullable = false, columnDefinition = "TEXT")
    val previewText: String,

    @field:NotBlank(message = "Content SID is required")
    @Column(name = "content_sid", nullable = false)
    val contentSid: String,

    @Column(name = "is_default", nullable = false)
    val isDefault: Boolean = false,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    // Default constructor for JPA
    constructor() : this(
        id = "",
        templateType = SmsMessageType.APPOINTMENT_CONFIRMATION,
        displayName = "",
        languageFlag = "",
        previewText = "",
        contentSid = "",
        isDefault = false,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )
}

