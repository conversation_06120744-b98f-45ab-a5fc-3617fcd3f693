package ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "mass_sms_campaigns")
data class MassSmsCampaignEntity(
    @Id
    @Column(name = "id", length = 36)
    val id: String,

    @Column(name = "salon_id", nullable = false, length = 36)
    val salonId: String,

    @Column(name = "message", nullable = false, columnDefinition = "TEXT")
    val message: String,

    @Column(name = "template_id", length = 36)
    val templateId: String?,

    @Column(name = "total_recipients", nullable = false)
    val totalRecipients: Int,

    @Column(name = "success_count", nullable = false)
    val successCount: Int,

    @Column(name = "failure_count", nullable = false)
    val failureCount: Int,

    @Column(name = "estimated_cost", nullable = false)
    val estimatedCost: Double,

    @Column(name = "actual_cost", nullable = false)
    val actualCost: Double,

    @Column(name = "status", nullable = false, length = 50)
    val status: String,

    @Column(name = "scheduled_at")
    val scheduledAt: LocalDateTime?,

    @Column(name = "sent_at")
    val sentAt: LocalDateTime?,

    @Column(name = "created_by", nullable = false, length = 36)
    val createdBy: String,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime,

    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime
) {
    constructor() : this(
        id = "",
        salonId = "",
        message = "",
        templateId = null,
        totalRecipients = 0,
        successCount = 0,
        failureCount = 0,
        estimatedCost = 0.0,
        actualCost = 0.0,
        status = "",
        scheduledAt = null,
        sentAt = null,
        createdBy = "",
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )
}
