package ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa

import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper.SmsReminderSettingsEntityMapper
import ro.animaliaprogramari.animalia.application.port.outbound.SmsReminderSettingsRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsReminderSettings

/**
 * JPA adapter implementing the SmsReminderSettingsRepository port
 */
@Repository
class JpaSmsReminderSettingsRepository(
    private val springRepository: SpringSmsReminderSettingsRepository,
    private val mapper: SmsReminderSettingsEntityMapper,
) : SmsReminderSettingsRepository {
    override fun save(settings: SmsReminderSettings): SmsReminderSettings {
        val entity = mapper.toEntity(settings)
        val savedEntity = springRepository.save(entity)
        return mapper.toDomain(savedEntity)
    }

    override fun findBySalonId(salonId: SalonId): SmsReminderSettings? {
        return springRepository.findBySalonId(salonId.value)
            ?.let { mapper.toDomain(it) }
    }

    override fun existsBySalonId(salonId: SalonId): Boolean {
        return springRepository.existsBySalonId(salonId.value)
    }
}
