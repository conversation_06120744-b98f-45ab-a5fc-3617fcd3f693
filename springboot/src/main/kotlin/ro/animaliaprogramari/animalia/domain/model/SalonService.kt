package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime

/**
 * Domain entity representing a grooming service
 * Pure domain model with no infrastructure dependencies
 * Each service is scoped to a specific salon
 */
data class SalonService(
    val id: ServiceId,
    val salonId: SalonId,
    val name: String,
    val description: String?,
    val basePrice: Money,
    val myDuration: MyDuration,
    val category: ServiceCategory,
    val displayOrder: Int = 0,
    val requirements: List<String> = emptyList(),
    val isActive: Boolean = true,
    // Variable pricing fields
    val sizePrices: Map<String, Money> = emptyMap(),
    val sizeDurations: Map<String, MyDuration> = emptyMap(),
    // Min-max pricing fields
    val minPrice: Money? = null,
    val maxPrice: Money? = null,
    val sizeMinPrices: Map<String, Money>? = null,
    val sizeMaxPrices: Map<String, Money>? = null,
    val color: String? = null, // Hex color code (e.g., #FF5722)
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    // Validation is now handled by ValidationService in the application layer
    // This keeps the domain model clean and validation logic centralized

    /**
     * Update service information
     */
    fun update(
        name: String? = null,
        description: String? = null,
        basePrice: Money? = null,
        myDuration: MyDuration? = null,
        category: ServiceCategory? = null,
        displayOrder: Int? = null,
        requirements: List<String>? = null,
        isActive: Boolean? = null,
        sizePrices: Map<String, Money>? = null,
        sizeDurations: Map<String, MyDuration>? = null,
        minPrice: Money? = null,
        maxPrice: Money? = null,
        sizeMinPrices: Map<String, Money>? = null,
        sizeMaxPrices: Map<String, Money>? = null,
        color: String? = null,
    ): SalonService {
        return copy(
            name = name ?: this.name,
            description = description ?: this.description,
            basePrice = basePrice ?: this.basePrice,
            myDuration = myDuration ?: this.myDuration,
            category = category ?: this.category,
            displayOrder = displayOrder ?: this.displayOrder,
            requirements = requirements ?: this.requirements,
            isActive = isActive ?: this.isActive,
            sizePrices = sizePrices ?: this.sizePrices,
            sizeDurations = sizeDurations ?: this.sizeDurations,
            minPrice = minPrice ?: this.minPrice,
            maxPrice = maxPrice ?: this.maxPrice,
            sizeMinPrices = sizeMinPrices ?: this.sizeMinPrices,
            sizeMaxPrices = sizeMaxPrices ?: this.sizeMaxPrices,
            color = color ?: this.color,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Deactivate the service
     */
    fun deactivate(): SalonService = copy(isActive = false, updatedAt = LocalDateTime.now())

    /**
     * Activate the service
     */
    fun activate(): SalonService = copy(isActive = true, updatedAt = LocalDateTime.now())

    /**
     * Get formatted price as string
     */
    fun getFormattedPrice(): String = "${basePrice.amount} RON"

    /**
     * Get formatted duration as string
     */
    fun getFormattedDuration(): String {
        val totalMinutes = myDuration.minutes
        val hours = totalMinutes / 60
        val minutes = totalMinutes % 60
        return when {
            hours > 0 && minutes > 0 -> "${hours}h ${minutes}min"
            hours > 0 -> "${hours}h"
            else -> "${minutes}min"
        }
    }

    companion object {
        /**
         * Create a new service for a salon
         */
        fun create(
            salonId: SalonId,
            name: String,
            description: String?,
            basePrice: Money,
            myDuration: MyDuration,
            category: ServiceCategory,
            displayOrder: Int = 0,
            requirements: List<String> = emptyList(),
            sizePrices: Map<String, Money> = emptyMap(),
            sizeDurations: Map<String, MyDuration> = emptyMap(),
            minPrice: Money? = null,
            maxPrice: Money? = null,
            sizeMinPrices: Map<String, Money>? = null,
            sizeMaxPrices: Map<String, Money>? = null,
            color: String? = null,
        ): SalonService {
            return SalonService(
                id = ServiceId.generate(),
                salonId = salonId,
                name = name,
                description = description,
                basePrice = basePrice,
                myDuration = myDuration,
                category = category,
                displayOrder = displayOrder,
                requirements = requirements,
                sizePrices = sizePrices,
                sizeDurations = sizeDurations,
                minPrice = minPrice,
                maxPrice = maxPrice,
                sizeMinPrices = sizeMinPrices,
                sizeMaxPrices = sizeMaxPrices,
                color = color,
            )
        }
    }

    /**
     * Check if this service uses variable pricing based on pet size
     */
    fun hasVariablePricing(): Boolean = sizePrices.isNotEmpty()

    /**
     * Check if this service uses min/max pricing
     */
    fun hasMinMaxPricing(): Boolean = minPrice != null && maxPrice != null

    /**
     * Check if this service uses size-based min/max pricing
     */
    fun hasSizeBasedMinMaxPricing(): Boolean = sizeMinPrices != null && sizeMaxPrices != null

}
