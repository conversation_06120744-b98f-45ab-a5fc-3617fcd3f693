package ro.animaliaprogramari.animalia.domain.model

import java.time.LocalDateTime
import java.util.*

/**
 * Value object for SalonWebPreferences ID
 */
@JvmInline
value class SalonWebPreferencesId(val value: String) {
    companion object {
        fun of(value: String): SalonWebPreferencesId = SalonWebPreferencesId(value)
        fun generate(): SalonWebPreferencesId = SalonWebPreferencesId(UUID.randomUUID().toString())
    }
}

/**
 * Domain entity representing salon website management preferences
 * Pure domain model with no infrastructure dependencies
 */
data class SalonWebPreferences(
    val id: SalonWebPreferencesId,
    val salonId: SalonId,
    
    // Basic Info
    val bookingWebsiteUrl: String,
    val businessName: String,
    val businessDescription: String,
    val businessAddress: String,
    val contactPhone: String,
    val contactEmail: String,
    val facebookLink: String,
    val instagramLink: String,
    val tiktokLink: String,
    
    // Website Photos
    val websitePhotos: List<String>,
    
    // Business Hours
    val businessHours: Map<String, Map<String, Any>>,
    
    // Extra Info
    val cancellationPolicy: CancellationPolicy,
    
    // Booking Acceptance
    val bookingAcceptance: BookingAcceptance,
    
    // Metadata
    val isActive: Boolean = true,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    /**
     * Activate web preferences
     */
    fun activate(): SalonWebPreferences {
        return copy(
            isActive = true,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Deactivate web preferences
     */
    fun deactivate(): SalonWebPreferences {
        return copy(
            isActive = false,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Update basic information
     */
    fun updateBasicInfo(
        businessName: String,
        businessDescription: String,
        businessAddress: String,
        contactPhone: String,
        contactEmail: String,
    ): SalonWebPreferences {
        return copy(
            businessName = businessName,
            businessDescription = businessDescription,
            businessAddress = businessAddress,
            contactPhone = contactPhone,
            contactEmail = contactEmail,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Update social media links
     */
    fun updateSocialMedia(
        facebookLink: String,
        instagramLink: String,
        tiktokLink: String,
    ): SalonWebPreferences {
        return copy(
            facebookLink = facebookLink,
            instagramLink = instagramLink,
            tiktokLink = tiktokLink,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Update website photos
     */
    fun updateWebsitePhotos(photos: List<String>): SalonWebPreferences {
        return copy(
            websitePhotos = photos,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Update business hours
     */
    fun updateBusinessHours(hours: Map<String, Map<String, Any>>): SalonWebPreferences {
        return copy(
            businessHours = hours,
            updatedAt = LocalDateTime.now(),
        )
    }

    /**
     * Update policies
     */
    fun updatePolicies(
        cancellationPolicy: CancellationPolicy,
        bookingAcceptance: BookingAcceptance,
    ): SalonWebPreferences {
        return copy(
            cancellationPolicy = cancellationPolicy,
            bookingAcceptance = bookingAcceptance,
            updatedAt = LocalDateTime.now(),
        )
    }
}

/**
 * Enum for cancellation policy options
 */
enum class CancellationPolicy {
    HOURS_24,
    HOURS_48,
    HOURS_72,
    NO_CHANGES
}

/**
 * Enum for booking acceptance options
 */
enum class BookingAcceptance {
    AUTOMATIC,
    MANUAL
}
