package ro.animaliaprogramari.animalia.domain.model

/**
 * Value object representing an international phone number
 */
@JvmInline
value class PhoneNumber(val value: String) {
    init {
        require(value.isNotBlank()) { "Phone number cannot be blank" }
        require(isValidPhoneNumber(value)) { "Invalid phone number format: $value" }
    }

    private fun isValidPhoneNumber(phone: String): <PERSON><PERSON><PERSON> {
        // Enhanced validation for international phone numbers
        val normalizedPhone = normalize(phone)

        // Must be in international format: +[country code][number]
        // Country code: 1-3 digits, followed by 4-15 digits for the phone number
        // This covers most international phone number formats
        return normalizedPhone.matches(Regex("^\\+[1-9][0-9]{0,2}[0-9]{4,14}$"))
    }

    /**
     * Convert to international format (+CountryCodeXXXXXXXXX)
     */
    fun toInternationalFormat(): String {
        val cleanPhone = value.replace(Regex("[\\s\\-\\(\\)\\.]"), "")

        return when {
            // Already in international format with +
            cleanPhone.startsWith("+") -> cleanPhone
            // Romanian format with 0040
            cleanPhone.startsWith("0040") -> "+${cleanPhone.substring(2)}"
            // Romanian local format starting with 0
            cleanPhone.startsWith("0") && cleanPhone.length == 10 -> "+40${cleanPhone.substring(1)}"
            // 9 digits without country code - assume Romanian
            cleanPhone.matches(Regex("\\d{9}")) -> "+40$cleanPhone"
            else -> value // Return original if can't convert
        }
    }

    /**
     * Check if this is a Romanian phone number
     */
    fun isRomanianNumber(): Boolean {
        val cleanPhone = value.replace(Regex("[\\s\\-\\(\\)\\.]"), "")
        return cleanPhone.startsWith("+40") || cleanPhone.startsWith("0040") ||
            (cleanPhone.startsWith("0") && cleanPhone.length >= 9)
    }

    companion object {
        fun of(value: String): PhoneNumber = PhoneNumber(value)

        /**
         * Create phone number from string, returning null if blank or invalid
         */
        fun ofNullable(value: String?): PhoneNumber? {
            return if (value.isNullOrBlank()) {
                null
            } else {
                try {
                    PhoneNumber(value)
                } catch (e: IllegalArgumentException) {
                    null
                }
            }
        }

        /**
         * Validate Romanian phone number format
         */
        fun isValidRomanianFormat(phone: String): Boolean {
            return try {
                val phoneNumber = PhoneNumber(phone)
                phoneNumber.isRomanianNumber()
            } catch (e: IllegalArgumentException) {
                false
            }
        }

        /**
         * Validate international phone number format
         * Accepts any valid international phone number
         */
        fun isValidInternationalFormat(phone: String): Boolean {
            return try {
                PhoneNumber(phone) // Constructor already validates international format
                true
            } catch (e: IllegalArgumentException) {
                false
            }
        }

        /**
         * Normalize phone number to international format
         * This is a public utility function for validation purposes
         */
        fun normalize(phone: String): String {
            // Remove all non-digit characters except + at the beginning
            val cleanPhone = phone.replace(Regex("[^+0-9]"), "")

            return when {
                // Already in international format with +
                cleanPhone.startsWith("+") -> cleanPhone
                // Romanian format with 0040
                cleanPhone.startsWith("0040") -> {
                    val digits = cleanPhone.substring(4)
                    if (digits.matches(Regex("\\d{9}"))) "+40$digits" else cleanPhone
                }
                // Romanian local format starting with 0
                cleanPhone.startsWith("0") && cleanPhone.length == 10 -> {
                    val digits = cleanPhone.substring(1)
                    if (digits.matches(Regex("\\d{9}")) ) "+40$digits" else cleanPhone
                }
                // 9 digits without country code - assume Romanian
                cleanPhone.matches(Regex("\\d{9}")) -> "+40$cleanPhone"
                else -> cleanPhone
            }
        }

        /**
         * Validate and normalize phone number, returning null if invalid
         */
        fun validateAndNormalize(phone: String?): String? {
            return if (phone.isNullOrBlank()) {
                null
            } else {
                try {
                    val phoneNumber = PhoneNumber(phone)
                    phoneNumber.toInternationalFormat()
                } catch (e: IllegalArgumentException) {
                    null
                }
            }
        }
    }

    override fun toString(): String = value
}
