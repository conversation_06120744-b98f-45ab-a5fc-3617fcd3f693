package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.time.Duration

/**
 * Rate limiter service to prevent sending duplicate messages.
 * Protects against bugs that might trigger multiple message sends to the same number.
 */
@Service
class MessageRateLimiterService {
    private val logger = LoggerFactory.getLogger(MessageRateLimiterService::class.java)

    // Store recent message sends: Key = "phoneNumber:messageType", Value = timestamp
    private val recentMessages = ConcurrentHashMap<String, Instant>()

    // Rate limit configuration
    private val rateLimitWindow = Duration.ofMinutes(0) // Don't send duplicate within 2 minutes
    private val maxEntriesBeforeCleanup = 10000

    /**
     * Check if a message can be sent to the phone number.
     * Returns true if message is allowed, false if rate limited.
     */
    fun allowMessage(phoneNumber: String, messageType: String): Boolean {
        cleanupOldEntries()

        val key = "$phoneNumber:$messageType"
        val now = Instant.now()
        val lastSent = recentMessages[key]

        if (lastSent != null) {
            val timeSinceLastSend = Duration.between(lastSent, now)

            if (timeSinceLastSend < rateLimitWindow) {
                logger.warn("⚠️ Rate limit triggered for $phoneNumber ($messageType). " +
                        "Last sent ${timeSinceLastSend.toSeconds()}s ago. Blocking duplicate message.")
                return false
            }
        }

        // Allow message and record timestamp
        recentMessages[key] = now
        return true
    }

    /**
     * Clean up old entries to prevent memory leaks.
     * Called automatically but can be invoked manually if needed.
     */
    fun cleanupOldEntries() {
        if (recentMessages.size > maxEntriesBeforeCleanup) {
            val cutoff = Instant.now().minus(rateLimitWindow)
            val keysToRemove = recentMessages.entries
                .filter { it.value.isBefore(cutoff) }
                .map { it.key }

            keysToRemove.forEach { recentMessages.remove(it) }

            if (keysToRemove.isNotEmpty()) {
                logger.info("🧹 Cleaned up ${keysToRemove.size} old rate limit entries")
            }
        }
    }

    /**
     * Reset rate limits for testing purposes.
     */
    fun reset() {
        recentMessages.clear()
        logger.info("🔄 Rate limiter reset")
    }

    /**
     * Get current rate limit statistics.
     */
    fun getStatistics(): Map<String, Any> {
        return mapOf(
            "totalTrackedMessages" to recentMessages.size,
            "rateLimitWindow" to rateLimitWindow.toMinutes(),
            "maxEntries" to maxEntriesBeforeCleanup
        )
    }
}

