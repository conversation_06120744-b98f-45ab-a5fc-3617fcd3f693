package ro.animaliaprogramari.animalia.domain.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.JpaSalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.ReferralCodeRepository
import ro.animaliaprogramari.animalia.application.port.outbound.BlockedSmsCreditsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonReferralCodeRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralCode
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralConstants
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralStats
import ro.animaliaprogramari.animalia.domain.model.referral.SalonReferralCode
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedSmsCredits
import kotlin.random.Random

/**
 * Domain service for referral code management
 */
@Service
class ReferralService(
    private val referralCodeRepository: ReferralCodeRepository,
    private val blockedSmsCreditsRepository: BlockedSmsCreditsRepository,
    private val salonReferralCodeRepository: SalonReferralCodeRepository,
    private val jpaSalonRepository: JpaSalonRepository
) {
    private val logger = LoggerFactory.getLogger(ReferralService::class.java)

    /**
     * Get or create the unique referral code for a salon
     */
    @Transactional
    fun getSalonReferralCode(salonId: SalonId): SalonReferralCode {
        // Check if salon already has a referral code
        val existingCode = salonReferralCodeRepository.findBySalonId(salonId)
        if (existingCode != null) {
            logger.info("Found existing referral code '{}' for salon: {}", existingCode.code, salonId.value)
            return existingCode
        }

        val salon = jpaSalonRepository.findById(salonId) ?: throw IllegalArgumentException("Salon not found: $salonId")

        // Generate a new unique code for the salon
        val code = generateUniqueCode(salon.name)
        val salonReferralCode = SalonReferralCode.createNew(
            salonId = salonId,
            code = code,
            smsCreditsAwarded = ReferralConstants.DEFAULT_SMS_CREDITS
        )

        val savedCode = salonReferralCodeRepository.save(salonReferralCode)
        logger.info("Created new referral code '{}' for salon: {}", code, salonId.value)

        return savedCode
    }

    /**
     * Claim a referral code and award SMS credits
     */
    @Transactional
    fun claimReferralCode(code: String, claimerSalonId: SalonId): ClaimResult {
        // First check if it's a salon's unique referral code
        val salonReferralCode = salonReferralCodeRepository.findByCode(code)
        if (salonReferralCode != null) {
            // Check if salon is trying to claim their own code
            if (salonReferralCode.salonId == claimerSalonId) {
                return ClaimResult.failure("You cannot claim your own referral code")
            }

            // Check if this salon has already claimed this specific salon's code
            // We check by looking at blocked SMS credits to see if they've already claimed from this salon
            val existingBlockedCredits = blockedSmsCreditsRepository.findBySalonId(claimerSalonId)
            val alreadyClaimedFromThisSalon = existingBlockedCredits.any { blockedCredit ->
                blockedCredit.source == ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsSource.REFERRAL_REWARD &&
                blockedCredit.sourceId.contains("salon-${salonReferralCode.salonId.value}")
            }

            if (alreadyClaimedFromThisSalon) {
                return ClaimResult.failure("Ai revendicat deja un cod de la acest salon")
            }

            // Calculate generator reward BEFORE starting the transaction modifications
            val existingReferralCount = countSuccessfulReferrals(salonReferralCode.salonId)
            val currentReferralNumber = existingReferralCount + 1 // This is the nth referral
            val generatorReward = (currentReferralNumber * 50 + 200).toInt()

            try {
                // Create blocked SMS credits for the claimer
                val claimerReferralId = "salon-${salonReferralCode.salonId.value}-claimer-${claimerSalonId.value}-${System.currentTimeMillis()}"
                val blockedCredits = BlockedSmsCredits.createFromReferral(
                    salonId = claimerSalonId,
                    creditsAmount = salonReferralCode.smsCreditsAwarded,
                    referralCodeId = claimerReferralId
                )
                blockedSmsCreditsRepository.save(blockedCredits)

                // Award progressive reward to the generator
                val generatorReferralId = "salon-${salonReferralCode.salonId.value}-generator-${claimerSalonId.value}-${System.currentTimeMillis()}"
                val generatorBlockedCredits = BlockedSmsCredits.createFromReferral(
                    salonId = salonReferralCode.salonId,
                    creditsAmount = generatorReward,
                    referralCodeId = generatorReferralId
                )
                blockedSmsCreditsRepository.save(generatorBlockedCredits)

                logger.info(
                    "Salon referral code '{}' claimed by salon: {}. Created {} blocked SMS credits for claimer and {} for generator (referral #{})",
                    code, claimerSalonId.value, salonReferralCode.smsCreditsAwarded, generatorReward, currentReferralNumber
                )

                // Create a dummy ReferralCode for the return value (for compatibility)
                val dummyReferralCode = ReferralCode.createNew(
                    code = code,
                    generatorSalonId = salonReferralCode.salonId,
                    smsCreditsAwarded = salonReferralCode.smsCreditsAwarded
                ).claim(claimerSalonId)

                return ClaimResult.success(dummyReferralCode)
            } catch (e: Exception) {
                logger.error("Failed to claim salon referral code '{}' for salon: {}", code, claimerSalonId.value, e)
                return ClaimResult.failure("Eroare la revendicarea codului: ${e.message}")
            }
        }

        // Fallback to old system for existing codes
        val referralCode = referralCodeRepository.findByCode(code)
            ?: return ClaimResult.failure("Cod de referal invalid")

        if (!referralCode.canBeClaimed()) {
            return when {
                referralCode.isClaimed() -> ClaimResult.failure("Codul a fost deja revendicat")
                referralCode.isExpired() -> ClaimResult.failure("Codul a expirat")
                else -> ClaimResult.failure("Codul nu poate fi revendicat")
            }
        }

        if (referralCode.generatorSalonId == claimerSalonId) {
            return ClaimResult.failure("You cannot claim your own referral code")
        }

        try {
            // Claim the code
            val claimedCode = referralCode.claim(claimerSalonId)
            val savedCode = referralCodeRepository.save(claimedCode)

            // Create blocked SMS credits instead of adding directly to quota
            val blockedCredits = BlockedSmsCredits.createFromReferral(
                salonId = claimerSalonId,
                creditsAmount = savedCode.smsCreditsAwarded,
                referralCodeId = savedCode.id.value
            )
            blockedSmsCreditsRepository.save(blockedCredits)

            logger.info(
                "Referral code '{}' claimed by salon: {}. Created {} blocked SMS credits",
                code, claimerSalonId.value, savedCode.smsCreditsAwarded
            )

            return ClaimResult.success(savedCode)
        } catch (e: Exception) {
            logger.error("Failed to claim referral code '{}' for salon: {}", code, claimerSalonId.value, e)
            return ClaimResult.failure("Eroare la revendicarea codului: ${e.message}")
        }
    }

    /**
     * Get referral codes generated by a salon
     */
    fun getGeneratedCodes(salonId: SalonId): List<ReferralCode> {
        return referralCodeRepository.findByGeneratorSalonId(salonId)
    }

    /**
     * Count how many people have been successfully referred by a salon
     * This includes both traditional referral codes and salon referral code claims
     */
    fun countSuccessfulReferrals(generatorSalonId: SalonId): Long {
        // Count claimed traditional referral codes where this salon is the generator
        val traditionalReferrals = referralCodeRepository.findByGeneratorSalonId(generatorSalonId)
            .count { it.isClaimed() }

        // For salon referral codes, we need to count how many times this salon's unique code was claimed
        // We can do this by counting blocked credits that were created for this salon as a generator
        // These will have the salon as the generator and REFERRAL_REWARD as source
        val salonReferralClaims = blockedSmsCreditsRepository.findBySalonId(generatorSalonId)
            .count { blockedCredit ->
                blockedCredit.source == ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsSource.REFERRAL_REWARD &&
                // Check if this is a generator reward (not a claimer reward)
                blockedCredit.sourceId.contains("-generator-")
            }

        return (traditionalReferrals + salonReferralClaims).toLong()
    }

    /**
     * Calculate the progressive reward for the next referral based on how many people have been referred
     */
    fun calculateNextReferralReward(generatorSalonId: SalonId): Int {
        // Count how many people have been successfully referred by this salon
        val referredCount = countSuccessfulReferrals(generatorSalonId)

        // Progressive reward: 200 SMS for 1st person, 250 for 2nd, 300 for 3rd, etc.
        return ((referredCount + 1) * ReferralConstants.PROGRESSIVE_REWARD_INCREMENT + ReferralConstants.PROGRESSIVE_REWARD_BASE).toInt()
    }

    /**
     * Get referral statistics for a salon
     */
    fun getReferralStats(salonId: SalonId): ReferralStats {
        val totalReferrals = countSuccessfulReferrals(salonId)
        val nextRewardAmount = calculateNextReferralReward(salonId)

        return ReferralStats(
            totalReferrals = totalReferrals,
            nextRewardAmount = nextRewardAmount
        )
    }

    /**
     * Check if a salon has ever claimed any referral code
     */
    fun hasClaimedAnyReferralCode(salonId: SalonId): Boolean {
        // Check if salon has any blocked SMS credits from referral rewards (as claimer)
        val blockedCredits = blockedSmsCreditsRepository.findBySalonId(salonId)
        return blockedCredits.any { blockedCredit ->
            blockedCredit.source == ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsSource.REFERRAL_REWARD &&
            // Check if this is a claimer reward (not a generator reward)
            // Claimer rewards have either "-claimer-" in the sourceId or are traditional referral codes
            (blockedCredit.sourceId.contains("-claimer-") ||
             !blockedCredit.sourceId.contains("-generator-"))
        }
    }

    /**
     * Check if a referral code is valid (exists and not claimed) without salon context
     */
    fun isValidReferralCode(code: String): Boolean {
        return try {
            // Check if code exists in salon referral codes and is not claimed
            val salonReferralCode = salonReferralCodeRepository.findByCode(code)
            if (salonReferralCode != null) {
                // For salon referral codes, we check if they can still be claimed
                // They are always available for claiming (no isClaimed property)
                return true
            }

            // Check if code exists in traditional referral codes and is not claimed
            val traditionalCode = referralCodeRepository.findByCode(code)
            traditionalCode != null && !traditionalCode.isClaimed()
        } catch (e: Exception) {
            logger.error("Error checking if referral code is valid: $code", e)
            false
        }
    }

    /**
     * Validate if a referral code exists and can be claimed
     */
    fun validateReferralCode(code: String, claimerSalonId: SalonId): ValidationResult {
        // First check if it's a salon's unique referral code
        val salonReferralCode = salonReferralCodeRepository.findByCode(code)
        if (salonReferralCode != null) {
            // Check if salon is trying to claim their own code
            if (salonReferralCode.salonId == claimerSalonId) {
                return ValidationResult.invalid("You cannot claim your own referral code")
            }

            // Check if this salon has already claimed this specific salon's code
            // We check by looking at blocked SMS credits to see if they've already claimed from this salon
            val existingBlockedCredits = blockedSmsCreditsRepository.findBySalonId(claimerSalonId)
            val alreadyClaimedFromThisSalon = existingBlockedCredits.any { blockedCredit ->
                blockedCredit.source == ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsSource.REFERRAL_REWARD &&
                blockedCredit.sourceId.contains("salon-${salonReferralCode.salonId.value}")
            }

            if (alreadyClaimedFromThisSalon) {
                return ValidationResult.invalid("Ai revendicat deja un cod de la acest salon")
            }

            // Create a temporary ReferralCode for validation response
            val tempReferralCode = ReferralCode.createNew(
                code = salonReferralCode.code,
                generatorSalonId = salonReferralCode.salonId,
                smsCreditsAwarded = salonReferralCode.smsCreditsAwarded
            )

            return ValidationResult.valid(tempReferralCode)
        }

        // Fallback to old system for existing codes
        val referralCode = referralCodeRepository.findByCode(code)
            ?: return ValidationResult.invalid("Cod de referal invalid")

        return when {
            !referralCode.canBeClaimed() -> {
                when {
                    referralCode.isClaimed() -> ValidationResult.invalid("Codul a fost deja revendicat")
                    referralCode.isExpired() -> ValidationResult.invalid("Codul a expirat")
                    else -> ValidationResult.invalid("Codul nu poate fi revendicat")
                }
            }
            referralCode.generatorSalonId == claimerSalonId -> {
                ValidationResult.invalid("You cannot claim your own referral code")
            }
            else -> ValidationResult.valid(referralCode)
        }
    }

    /**
     * Generate a unique 8-character alphanumeric code
     */
    private fun generateUniqueCode(name: String): String {
        var attempts = 0

        while (attempts < ReferralConstants.MAX_GENERATION_ATTEMPTS) {
            // Ensure the name portion is exactly 4 characters by padding or truncating
            val namePrefix = name.uppercase()
                .filter { it.isLetterOrDigit() } // Remove special characters
                .take(ReferralConstants.NAME_PREFIX_LENGTH) // Take up to 4 characters
                .padEnd(ReferralConstants.NAME_PREFIX_LENGTH, ReferralConstants.NAME_PADDING_CHARACTER) // Pad with '0' if shorter than 4 characters

            val randomSuffix = (1..ReferralConstants.RANDOM_SUFFIX_LENGTH)
                .map { ReferralConstants.ALLOWED_CODE_CHARACTERS[Random.nextInt(ReferralConstants.ALLOWED_CODE_CHARACTERS.length)] }
                .joinToString("")

            val code = namePrefix + randomSuffix

            // Check both repositories for uniqueness
            if (!referralCodeRepository.existsByCode(code) && !salonReferralCodeRepository.existsByCode(code)) {
                return code
            }
            attempts++
        }

        throw IllegalStateException("Failed to generate unique referral code after ${ReferralConstants.MAX_GENERATION_ATTEMPTS} attempts")
    }

    /**
     * Result of claiming a referral code
     */
    sealed class ClaimResult {
        data class Success(val referralCode: ReferralCode) : ClaimResult()
        data class Failure(val message: String) : ClaimResult()

        companion object {
            fun success(referralCode: ReferralCode) = Success(referralCode)
            fun failure(message: String) = Failure(message)
        }

    }

    /**
     * Result of validating a referral code
     */
    sealed class ValidationResult {
        data class Valid(val referralCode: ReferralCode) : ValidationResult()
        data class Invalid(val message: String) : ValidationResult()

        companion object {
            fun valid(referralCode: ReferralCode) = Valid(referralCode)
            fun invalid(message: String) = Invalid(message)
        }

        val isValid: Boolean get() = this is Valid
    }
}
