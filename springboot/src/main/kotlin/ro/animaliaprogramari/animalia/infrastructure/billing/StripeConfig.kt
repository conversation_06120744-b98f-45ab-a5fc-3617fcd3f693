package ro.animaliaprogramari.animalia.infrastructure.billing

import com.stripe.Stripe
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@EnableConfigurationProperties(StripeProperties::class)
class StripeConfig(
    stripeProperties: StripeProperties,
) {
    private val logger = LoggerFactory.getLogger(StripeConfig::class.java)

    init {
        if (stripeProperties.secretKey.isNotBlank()) {
            Stripe.apiKey = stripeProperties.secretKey
            logger.info("Stripe configured with provided secret key (test or live based on env)")
        } else {
            logger.warn("Stripe secret key is not configured. Billing features will be disabled.")
        }
    }
}

