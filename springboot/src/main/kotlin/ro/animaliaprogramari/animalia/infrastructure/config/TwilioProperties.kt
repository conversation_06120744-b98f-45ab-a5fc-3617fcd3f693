package ro.animaliaprogramari.animalia.infrastructure.config

import org.springframework.boot.context.properties.ConfigurationProperties

/**
 * Configuration properties for Twilio WhatsApp integration.
 */
@ConfigurationProperties(prefix = "twilio")
data class TwilioProperties(
    // TODO: Set these values in application.yml or environment variables
    val accountSid: String = "", // Your Twilio Account SID
    val authToken: String = "", // Your Twilio Auth Token
    val whatsappNumber: String = "", // Your Twilio WhatsApp number (e.g., +***********)
    val timeout: Long = 10000, // Timeout in milliseconds
    val retryAttempts: Int = 3, // Number of retry attempts
    val whatsapp: WhatsAppConfig = WhatsAppConfig()
)

/**
 * WhatsApp specific configuration.
 */
data class WhatsAppConfig(
    val enabled: Boolean = false, // Enable/disable WhatsApp functionality
    val baseUrl: String = "https://api.twilio.com" // Twilio API base URL
)
