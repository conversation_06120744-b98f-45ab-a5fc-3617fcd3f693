package ro.animaliaprogramari.animalia.infrastructure.persistence.repository

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import ro.animaliaprogramari.animalia.infrastructure.persistence.entity.SmsFollowUpTimingEntity

@Repository
interface JpaSmsFollowUpTimingRepository : JpaRepository<SmsFollowUpTimingEntity, String> {

    /**
     * Find all timings for a salon
     */
    fun findBySalonId(salonId: String): List<SmsFollowUpTimingEntity>

    /**
     * Find all enabled timings
     */
    fun findByIsEnabledTrue(): List<SmsFollowUpTimingEntity>

    /**
     * Find enabled/disabled timings for a salon
     */
    fun findBySalonIdAndIsEnabled(salonId: String, isEnabled: Boolean): List<SmsFollowUpTimingEntity>

    /**
     * Check if a timing with the same hours exists for a salon
     */
    @Query("SELECT COUNT(t) > 0 FROM SmsFollowUpTimingEntity t WHERE t.salonId = :salonId AND t.hoursAfter = :hoursAfter")
    fun existsBySalonIdAndHoursAfter(@Param("salonId") salonId: String, @Param("hoursAfter") hoursAfter: Int): Boolean

    /**
     * Find timings ordered by hours after
     */
    @Query("SELECT t FROM SmsFollowUpTimingEntity t WHERE t.salonId = :salonId ORDER BY t.hoursAfter ASC")
    fun findBySalonIdOrderByHoursAfterAsc(@Param("salonId") salonId: String): List<SmsFollowUpTimingEntity>
}
