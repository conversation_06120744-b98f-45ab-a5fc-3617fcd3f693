package ro.animaliaprogramari.animalia.infrastructure.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.web.reactive.function.client.WebClient
import java.util.concurrent.Executor

/**
 * Configuration for notification services
 */
@Configuration
@EnableConfigurationProperties(
    SmsoProperties::class,
    FcmProperties::class,
    NotificationProperties::class,
    PhoneValidationProperties::class,
    SmsNotificationProperties::class,
    TwilioProperties::class,
)
@EnableCaching
@EnableAsync
class NotificationConfiguration {
    /**
     * WebClient for SMSO API calls
     */
    @Bean("smsoWebClient")
    fun smsoWebClient(smsoProperties: SmsoProperties): WebClient {
        return WebClient.builder()
            .baseUrl(smsoProperties.url)
            .defaultHeader("X-Authorization", smsoProperties.key)
            .defaultHeader("Content-Type", "application/json")
            .defaultHeader("Accept", "application/json")
            .codecs { configurer ->
                configurer.defaultCodecs().maxInMemorySize(1024 * 1024) // 1MB
            }
            .build()
    }

    /**
     * WebClient for Twilio API calls (WhatsApp)
     */
    @Bean("twilioWebClient")
    fun twilioWebClient(twilioProperties: TwilioProperties): WebClient {
        return WebClient.builder()
            .baseUrl(twilioProperties.whatsapp.baseUrl)
            .defaultHeader("Content-Type", "application/x-www-form-urlencoded")
            .codecs { configurer ->
                configurer.defaultCodecs().maxInMemorySize(1024 * 1024) // 1MB
            }
            .build()
    }



    /**
     * Thread pool for async notification processing
     */
    @Bean("notificationTaskExecutor")
    fun notificationTaskExecutor(notificationProperties: NotificationProperties): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = notificationProperties.async.threadPoolSize
        executor.maxPoolSize = notificationProperties.async.threadPoolSize * 2
        executor.queueCapacity = 500
        executor.setThreadNamePrefix("notification-")
        executor.setWaitForTasksToCompleteOnShutdown(true)
        executor.setAwaitTerminationSeconds(30)
        executor.initialize()
        return executor
    }
}

/**
 * SMSO API configuration properties
 */
@ConfigurationProperties(prefix = "smso.api")
data class SmsoProperties(
    val key: String = "",
    val url: String = "https://api.smso.ro/v1",
    val timeout: Long = 30000,
    val retryAttempts: Int = 3,
)

/**
 * FCM configuration properties
 */
@ConfigurationProperties(prefix = "fcm")
data class FcmProperties(
    val enabled: Boolean = true,
    val serviceAccountKeyPath: String = "",
)

/**
 * Notification configuration properties
 */
@ConfigurationProperties(prefix = "notifications")
data class NotificationProperties(
    val sms: SmsNotificationProperties = SmsNotificationProperties(),
    val push: PushNotificationProperties = PushNotificationProperties(),
    val async: AsyncNotificationProperties = AsyncNotificationProperties(),
)

/**
 * SMS notification properties
 */
@ConfigurationProperties(prefix = "notifications.sms")
data class SmsNotificationProperties(
    val enabled: Boolean = true,
    val rateLimitPerHour: Int = 3,
    val rateLimitWindowMinutes: Int = 60,
)

/**
 * Push notification properties
 */
data class PushNotificationProperties(
    val enabled: Boolean = true,
)

/**
 * Async notification properties
 */
data class AsyncNotificationProperties(
    val enabled: Boolean = true,
    val threadPoolSize: Int = 5,
)

/**
 * Phone validation properties
 */
@ConfigurationProperties(prefix = "phone.validation")
data class PhoneValidationProperties(
    val countryCode: String = "+40",
    val minLength: Int = 10,
    val maxLength: Int = 10,
)
