package ro.animaliaprogramari.animalia.infrastructure.billing

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = "stripe.api")
class StripeProperties {
    var publicKey: String = ""
    var secretKey: String = ""
    var webhookSecret: String? = null
}

@Configuration
@ConfigurationProperties(prefix = "stripe.sms")
class StripeSmsProperties {
    var priceId: String = ""
}

