package ro.animaliaprogramari.animalia.infrastructure.persistence.mapper

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.infrastructure.persistence.entity.AppointmentSubscriptionEntity

/**
 * Mapper between AppointmentSubscription domain model and AppointmentSubscriptionEntity
 */
@Component
class AppointmentSubscriptionEntityMapper(
    private val objectMapper: ObjectMapper
) {

    /**
     * Convert domain model to entity
     */
    fun toEntity(domain: AppointmentSubscription): AppointmentSubscriptionEntity {
        return AppointmentSubscriptionEntity(
            id = domain.id.value,
            clientId = domain.clientId.value,
            salonId = domain.salonId.value,
            originalAppointmentId = domain.originalAppointmentId.value,
            staffId = domain.staffId.value,
            serviceIds = serializeServiceIds(domain.serviceIds),
            frequency = domain.frequency,
            period = domain.period.name,
            totalRepetitions = domain.totalRepetitions,
            remainingRepetitions = domain.remainingRepetitions,
            status = domain.status.name,
            startTime = domain.startTime,
            endTime = domain.endTime,
            notes = domain.notes,
            paymentModel = domain.paymentModel,
            discountPercentage = domain.discountPercentage,
            totalUpfrontAmount = domain.totalUpfrontAmount,
            appointmentPrice = domain.appointmentPrice,
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }

    /**
     * Convert entity to domain model
     */
    fun toDomain(entity: AppointmentSubscriptionEntity): AppointmentSubscription {
        return AppointmentSubscription(
            id = AppointmentSubscriptionId.of(entity.id),
            clientId = ClientId.of(entity.clientId),
            salonId = SalonId.of(entity.salonId),
            originalAppointmentId = AppointmentId.of(entity.originalAppointmentId),
            staffId = StaffId.of(entity.staffId),
            serviceIds = deserializeServiceIds(entity.serviceIds),
            frequency = entity.frequency,
            period = RecurrencePeriod.fromString(entity.period),
            totalRepetitions = entity.totalRepetitions,
            remainingRepetitions = entity.remainingRepetitions,
            status = AppointmentSubscriptionStatus.fromString(entity.status),
            startTime = entity.startTime,
            endTime = entity.endTime,
            notes = entity.notes,
            paymentModel = entity.paymentModel,
            discountPercentage = entity.discountPercentage,
            totalUpfrontAmount = entity.totalUpfrontAmount,
            appointmentPrice = entity.appointmentPrice,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt
        )
    }

    /**
     * Serialize service IDs to JSON string
     */
    private fun serializeServiceIds(serviceIds: List<ServiceId>): String {
        return try {
            val stringIds = serviceIds.map { it.value }
            objectMapper.writeValueAsString(stringIds)
        } catch (e: Exception) {
            throw IllegalArgumentException("Failed to serialize service IDs", e)
        }
    }

    /**
     * Deserialize service IDs from JSON string
     */
    private fun deserializeServiceIds(serviceIdsJson: String): List<ServiceId> {
        return try {
            val stringIds: List<String> = objectMapper.readValue(
                serviceIdsJson,
                object : TypeReference<List<String>>() {}
            )
            stringIds.map { ServiceId.of(it) }
        } catch (e: Exception) {
            throw IllegalArgumentException("Failed to deserialize service IDs from: $serviceIdsJson", e)
        }
    }
}
