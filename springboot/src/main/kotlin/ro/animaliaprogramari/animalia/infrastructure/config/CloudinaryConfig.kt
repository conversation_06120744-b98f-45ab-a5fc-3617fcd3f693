package ro.animaliaprogramari.animalia.infrastructure.config

import com.cloudinary.Cloudinary
import com.cloudinary.utils.ObjectUtils
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class CloudinaryConfig(
    @Value("\${cloudinary.cloud-name}")
    private val cloudName: String,
    @Value("\${cloudinary.api-key}")
    private val apiKey: String,
    @Value("\${cloudinary.api-secret}")
    private val apiSecret: String,
) {
    @Bean
    fun cloudinary(): Cloudinary {
        return Cloudinary(
            ObjectUtils.asMap(
                "cloud_name",
                cloudName,
                "api_key",
                apiKey,
                "api_secret",
                apiSecret,
            ),
        )
    }
}
