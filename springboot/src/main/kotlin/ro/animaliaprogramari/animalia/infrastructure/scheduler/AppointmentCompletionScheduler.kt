package ro.animaliaprogramari.animalia.infrastructure.scheduler

import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentCompletedEvent
import ro.animaliaprogramari.animalia.domain.model.AppointmentStatus
import ro.animaliaprogramari.animalia.domain.model.UserId
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

/**
 * Scheduler that automatically completes past appointments which were not cancelled.
 */
@Component
class AppointmentCompletionScheduler(
    private val appointmentRepository: AppointmentRepository,
    private val staffRepository: StaffRepository,
    private val eventPublisher: ApplicationEventPublisher,
) {
    private val logger = LoggerFactory.getLogger(AppointmentCompletionScheduler::class.java)

    /**
     * Runs hourly and marks past appointments as completed.
     */
    @Scheduled(cron = "0 15 * * * *")
    fun completePastAppointments() {
        logger.info("🔄 Starting automatic completion of past appointments...")

        val statusesToCheck =
            listOf(
                AppointmentStatus.SCHEDULED,
                AppointmentStatus.CONFIRMED,
                AppointmentStatus.IN_PROGRESS,
                AppointmentStatus.RESCHEDULED,
            )

        try {
            val appointments = appointmentRepository.findByStatuses(statusesToCheck)
            logger.info("📋 Found ${appointments.size} appointments with eligible statuses: ${statusesToCheck.map { it.name }}")

            val pastAppointments = appointments.filter { isAppointmentFinished(it.appointmentDate, it.endTime) }
            logger.info("⏰ Found ${pastAppointments.size} past appointments that need completion")

            var completedCount = 0
            var failedCount = 0

            pastAppointments.forEach { appointment ->
                try {
                    if (appointment.canBeCompleted()) {
                        val completed = appointment.complete("Auto-completed by scheduler")
                        val savedAppointment = appointmentRepository.save(completed)

                        // Publish completion event for consistency with manual completion
                        publishAppointmentCompletedEvent(savedAppointment)

                        completedCount++
                        logger.info("✅ Auto-completed appointment ${appointment.id.value} (${appointment.appointmentDate} ${appointment.startTime}-${appointment.endTime})")
                    } else {
                        logger.warn("⚠️ Skipped appointment ${appointment.id.value} - cannot be completed in status: ${appointment.status}")
                    }
                } catch (ex: Exception) {
                    failedCount++
                    logger.error("❌ Failed to auto-complete appointment ${appointment.id.value}: ${ex.message}", ex)
                }
            }

            logger.info("📊 Appointment completion summary: ${completedCount} completed, ${failedCount} failed")
        } catch (ex: Exception) {
            logger.error("❌ Error during appointment completion scheduler execution", ex)
        }
    }

    private fun isAppointmentFinished(
        date: LocalDate,
        endTime: LocalTime,
    ): Boolean {
        val endDateTime = LocalDateTime.of(date, endTime)
        return endDateTime.isBefore(LocalDateTime.now())
    }

    private fun publishAppointmentCompletedEvent(savedAppointment: ro.animaliaprogramari.animalia.domain.model.Appointment) {
        try {
            val userId = staffRepository.findById(savedAppointment.staffId)?.userId
            val completedAppointment = AppointmentCompletedEvent(
                eventId = UUID.randomUUID().toString(),
                occurredAt = LocalDateTime.now(),
                aggregateId = savedAppointment.id.value,
                appointmentId = savedAppointment.id,
                clientId = savedAppointment.clientId,
                petId = savedAppointment.petId,
                userId = userId ?: UserId.of(savedAppointment.staffId.value),
                totalPrice = savedAppointment.totalPrice,
                services = emptyList(), // Services not needed for auto-completion events
                salonId = savedAppointment.salonId,
            )
            eventPublisher.publishEvent(completedAppointment)
            logger.info("📡 Published AppointmentCompletedEvent for auto-completed appointment: ${savedAppointment.id.value}")
        } catch (ex: Exception) {
            logger.error("❌ Failed to publish AppointmentCompletedEvent for appointment ${savedAppointment.id.value}: ${ex.message}", ex)
        }
    }
}
