package ro.animaliaprogramari.animalia.infrastructure.scheduler

import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentReminderEvent
import ro.animaliaprogramari.animalia.domain.model.AppointmentStatus
import ro.animaliaprogramari.animalia.domain.model.SmsReminderTiming
import ro.animaliaprogramari.animalia.domain.model.UserId
import ro.animaliaprogramari.animalia.domain.port.outbound.SmsReminderTimingRepository
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

@Component
class CustomReminderScheduler(
    private val appointmentRepository: AppointmentRepository,
    private val smsReminderTimingRepository: SmsReminderTimingRepository,
    private val eventPublisher: ApplicationEventPublisher,
    private val staffRepository: StaffRepository
) {
    private val logger = LoggerFactory.getLogger(CustomReminderScheduler::class.java)

    // Track processed reminders to prevent duplicates within the same scheduler run
    private val processedReminders = mutableSetOf<String>()

    /**
     * Scenario: Programare la 14:00, configurare "2 ore înainte"
     * 12:00 - Scheduler verifică: 120 minute până la programare ✅
     * 12:05 - Scheduler verifică: 115 minute până la programare ✅
     * 12:15 - Scheduler verifică: 105 minute până la programare ❌ (prea târziu)
     *
     * Nu trimite SMS direct - publică un eveniment
     *
     *
     */
    @Scheduled(cron = "0 */15 * * * *") // Run every 15 minutes
    fun processCustomReminders() {
        logger.info("🔔 Starting custom reminder check...")

        // Clear processed reminders at the start of each scheduler run
        processedReminders.clear()

        val now = LocalDateTime.now()

        // Get all enabled custom timings
        val allTimings = smsReminderTimingRepository.findAllEnabled() // get timings for all salons

        logger.info("📊 Found ${allTimings.size} enabled reminder timings")
        allTimings.forEachIndexed { index, timing ->
            logger.info("📋 Timing $index: ${timing.hoursBefore}h before for salon ${timing.salonId}")
        }

        allTimings.forEach { timing ->
            processTimingReminders(timing, now)
        }

        logger.info("🏁 Finished processing all timings")
    }

    fun processTimingReminders(timing: SmsReminderTiming, now: LocalDateTime) {
        val targetDateTime = now.plusHours(timing.hoursBefore.toLong())
        val targetDate = targetDateTime.toLocalDate()

        logger.info("🔍 Processing timing: ${timing.hoursBefore}h before for salon ${timing.salonId}")
        logger.info("🕐 Current time: $now")
        logger.info("🎯 Target date time: $targetDateTime")
        logger.info("📅 Looking for appointments on: $targetDate")

        // Get appointments for the target date - use distinct() to avoid duplicates
        val scheduledAppts = appointmentRepository.findByDateAndStatus(targetDate, AppointmentStatus.SCHEDULED)
        val rescheduledAppts = appointmentRepository.findByDateAndStatus(targetDate, AppointmentStatus.RESCHEDULED)

        logger.info("📋 Found ${scheduledAppts.size} SCHEDULED appointments on $targetDate")
        logger.info("📋 Found ${rescheduledAppts.size} RESCHEDULED appointments on $targetDate")

        val allAppts = (scheduledAppts + rescheduledAppts).distinct()
        logger.info("📋 Total unique appointments before salon filter: ${allAppts.size}")

        val appointments = allAppts.filter { it.salonId == timing.salonId }
        logger.info("📋 Appointments for salon ${timing.salonId}: ${appointments.size}")

        val appointmentsNeedingReminders = appointments.filter { appt ->
            val appointmentDateTime = LocalDateTime.of(appt.appointmentDate, appt.startTime)
            val minutesUntilAppointment = ChronoUnit.MINUTES.between(now, appointmentDateTime)
            val targetMinutes = timing.hoursBefore * 60

            logger.info("🔍 Appointment ${appt.id.value} at $appointmentDateTime: ${minutesUntilAppointment} minutes away, target: $targetMinutes minutes")

            // Send reminder if within 15-minute window of target time
            val inWindow = minutesUntilAppointment in (targetMinutes - 15)..(targetMinutes)
            logger.info("⏰ Appointment ${appt.id.value} in reminder window (${targetMinutes-15} to $targetMinutes): $inWindow")

            inWindow
        }

        logger.info("📅 Found ${appointmentsNeedingReminders.size} appointments needing ${timing.hoursBefore}h reminders")

        appointmentsNeedingReminders.forEach { appt ->
            // Deduplication: Check if this reminder has already been processed
            val reminderKey = "${timing.hoursBefore}h-${appt.id.value}"
            if (processedReminders.contains(reminderKey)) {
                logger.info("🔁 Skipping already processed reminder for appointment ${appt.id.value} (${timing.hoursBefore}h)")
                return@forEach
            }

            logger.info("📨 Publishing event for ${timing.hoursBefore}h reminder for appointment ${appt.id.value}")
            eventPublisher.publishEvent(
                AppointmentReminderEvent(
                    appointmentId = appt.id,
                    clientId = appt.clientId,
                    appointmentDate = appt.appointmentDate,
                    startTime = appt.startTime,
                    salonId = appt.salonId,
                    eventId = "reminder-${timing.hoursBefore}h-${appt.id.value}",
                    occurredAt = LocalDateTime.now(),
                    aggregateId = appt.id.value,
                    userId = staffRepository.findById(appt.staffId)?.userId ?: UserId.of(appt.staffId.value),
                )
            )

            // Mark this reminder as processed
            processedReminders.add(reminderKey)
            logger.info("✅ Marked reminder ${reminderKey} as processed")
        }
    }
}
