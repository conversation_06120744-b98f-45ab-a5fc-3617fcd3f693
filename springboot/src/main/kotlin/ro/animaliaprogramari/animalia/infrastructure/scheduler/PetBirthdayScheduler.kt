package ro.animaliaprogramari.animalia.infrastructure.scheduler

import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SmsReminderSettingsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.service.FirebaseService
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate

@Component
class PetBirthdayScheduler(
    private val petRepository: PetRepository,
    private val clientRepository: ClientRepository,
    private val smsReminderSettingsRepository: SmsReminderSettingsRepository,
    private val staffRepository: StaffRepository,
    private val firebaseService: FirebaseService,
) {
    private val logger = LoggerFactory.getLogger(PetBirthdayScheduler::class.java)

    @Scheduled(cron = "0 0 8 * * *")
    fun sendBirthdayGreetings() {
        val today = LocalDate.now()
        logger.info("🎂 Checking for pet birthdays on $today")

        val petsWithBirthday = petRepository.findPetsWithBirthdayOn(today.monthValue, today.dayOfMonth)
        if (petsWithBirthday.isEmpty()) {
            logger.info("🎂 No pet birthdays found for $today")
            return
        }

        petsWithBirthday.forEach { pet ->
            val client = clientRepository.findById(pet.clientId)
            if (client == null) {
                logger.warn("Skipping birthday SMS for pet ${pet.id.value}: owner not found")
                return@forEach
            }

            val salonId = client.salonId
            if (salonId == null) {
                logger.warn("Skipping birthday SMS for pet ${pet.id.value}: client ${client.id.value} has no salon assigned")
                return@forEach
            }

            val settings = smsReminderSettingsRepository.findBySalonId(salonId)
                ?: SmsReminderSettings.createDefault(salonId)

            if (!settings.enabled || !settings.birthdayMessages) {
                logger.info(
                    "Skipping birthday SMS for pet ${pet.id.value}: birthday messages disabled for salon ${salonId.value}"
                )
                return@forEach
            }

            try {
                createNotificationForAllStaff(
                    salonId = salonId,
                    title = "🎂 Ziua de naștere a unui animaluț!",
                    message = "Astăzi este ziua de naștere a lui ${pet.name} (proprietar: ${client.name}). Poate doriți să îi trimiteți felicitări!",
                    eventType = "PET_BIRTHDAY",
                    notificationType = NotificationType.GENERAL
                )
                logger.info("✅ Birthday notification sent to salon staff for pet ${pet.id.value}")
            } catch (ex: Exception) {
                logger.error(
                    "❌ Failed to send birthday notification for pet ${pet.id.value} (client=${client.id.value}, salon=${salonId.value})",
                    ex,
                )
            }
        }
    }

    private fun createNotificationForAllStaff(
        salonId: SalonId,
        title: String,
        message: String,
        eventType: String,
        notificationType: NotificationType
    ) {
        try {
            val staffMembers = staffRepository.findActiveBySalonWithUserDetails(salonId)
            if (staffMembers.isEmpty()) {
                logger.warn("No active staff members found for salon $salonId")
                return
            }

            val userIds = staffMembers.map { it.userId }
            val dataMap = mapOf(
                "salonId" to salonId.value,
                "eventType" to eventType,
                "timestamp" to System.currentTimeMillis().toString()
            )

            firebaseService.sendNotificationToUsers(
                userIds = userIds,
                salonId = salonId,
                title = title,
                body = message,
                notificationType = notificationType,
                data = dataMap,
                appointmentId = null
            )
        } catch (e: Exception) {
            logger.error("Failed to send birthday notification to staff", e)
        }
    }
}
