package ro.animaliaprogramari.animalia.infrastructure.persistence.entity

import jakarta.persistence.*
import ro.animaliaprogramari.animalia.domain.model.PaymentModel
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * JPA entity for appointment subscriptions
 */
@Entity
@Table(name = "appointment_subscriptions")
data class AppointmentSubscriptionEntity(
    @Id
    @Column(name = "id", nullable = false)
    val id: String,

    @Column(name = "client_id", nullable = false)
    val clientId: String,

    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @Column(name = "original_appointment_id", nullable = false)
    val originalAppointmentId: String,

    @Column(name = "staff_id", nullable = false)
    val staffId: String,

    @Column(name = "service_ids", nullable = false, columnDefinition = "TEXT")
    val serviceIds: String, // JSON array of service IDs

    @Column(name = "frequency", nullable = false)
    val frequency: Int,

    @Column(name = "period", nullable = false)
    val period: String, // DAYS, WEEKS, MONTHS

    @Column(name = "total_repetitions", nullable = false)
    val totalRepetitions: Int,

    @Column(name = "remaining_repetitions", nullable = false)
    val remainingRepetitions: Int,

    @Column(name = "status", nullable = false)
    val status: String, // ACTIVE, PAUSED, CANCELLED, COMPLETED

    @Column(name = "start_time", nullable = false)
    val startTime: LocalTime,

    @Column(name = "end_time", nullable = false)
    val endTime: LocalTime,

    @Column(name = "notes", columnDefinition = "TEXT")
    val notes: String?,

    // Payment model fields
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_model", nullable = false)
    val paymentModel: PaymentModel = PaymentModel.PER_APPOINTMENT,

    @Column(name = "discount_percentage", precision = 5, scale = 2)
    val discountPercentage: BigDecimal? = null,

    @Column(name = "total_upfront_amount", precision = 10, scale = 2)
    val totalUpfrontAmount: BigDecimal? = null,

    @Column(name = "appointment_price", precision = 10, scale = 2, nullable = false)
    val appointmentPrice: BigDecimal = BigDecimal.ZERO,

    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime,

    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime
) {
    // JPA requires a no-arg constructor
    constructor() : this(
        id = "",
        clientId = "",
        salonId = "",
        originalAppointmentId = "",
        staffId = "",
        serviceIds = "",
        frequency = 1,
        period = "WEEKS",
        totalRepetitions = 1,
        remainingRepetitions = 1,
        status = "ACTIVE",
        startTime = LocalTime.now(),
        endTime = LocalTime.now(),
        notes = null,
        paymentModel = PaymentModel.PER_APPOINTMENT,
        discountPercentage = null,
        totalUpfrontAmount = null,
        appointmentPrice = BigDecimal.ZERO,
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )
}
