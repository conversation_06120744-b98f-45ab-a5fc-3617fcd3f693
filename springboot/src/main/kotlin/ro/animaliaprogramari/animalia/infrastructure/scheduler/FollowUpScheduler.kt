package ro.animaliaprogramari.animalia.infrastructure.scheduler

import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentFollowUpEvent
import ro.animaliaprogramari.animalia.domain.model.AppointmentStatus
import ro.animaliaprogramari.animalia.domain.model.SmsFollowUpTiming
import ro.animaliaprogramari.animalia.domain.model.UserId
import ro.animaliaprogramari.animalia.domain.port.outbound.SmsFollowUpTimingRepository
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

@Component
class FollowUpScheduler(
    private val smsFollowUpTimingRepository: SmsFollowUpTimingRepository,
    private val appointmentRepository: AppointmentRepository,
    private val staffRepository: StaffRepository,
    private val eventPublisher: ApplicationEventPublisher,
) {
    private val logger = LoggerFactory.getLogger(FollowUpScheduler::class.java)

    /**
     * Scenario: Programare completată la 14:00, configurare "2 ore după"
     * 16:00 - Scheduler verifică: 120 minute de la completare ✅
     * 16:05 - Scheduler verifică: 125 minute de la completare ✅
     * 16:10 - Scheduler verifică: 130 minute de la completare ❌ (prea târziu)
     *
     * Nu trimite SMS direct - publică un eveniment
     */
    @Scheduled(cron = "0 */5 * * * *") // Run every 5 minutes
    fun processFollowUpMessages() {
        logger.info("📬 Starting follow-up message check...")

        val now = LocalDateTime.now()

        // Get all enabled follow-up timings
        val allTimings = smsFollowUpTimingRepository.findAllEnabled()

        allTimings.forEach { timing ->
            processTimingFollowUps(timing, now)
        }
    }

    /**
     * Processes follow-up messages for appointments based on a specific timing configuration.
     *
     * This method finds completed appointments that are ready to receive follow-up messages
     * according to the specified timing rules. It calculates whether enough time has passed
     * since the appointment ended and if we're within the correct time window to send the
     * follow-up message.
     *
     * For example, if configured to send follow-ups "2 hours after completion":
     * - An appointment completed at 14:00 becomes eligible for follow-up at 16:00
     * - The system will send the follow-up if checked between 15:55-16:00 (5-minute window)
     * - If checked after 16:00, the follow-up opportunity is missed
     *
     * Special case for immediate follow-ups (0 hours):
     * - Messages are sent within the first 5 minutes after appointment completion
     *
     * The method doesn't send SMS messages directly. Instead, it publishes events that
     * other parts of the system can handle to actually send the messages.
     *
     * @param timing The follow-up timing configuration that specifies when to send messages
     * @param now The current date and time used for calculations
     */
    fun processTimingFollowUps(timing: SmsFollowUpTiming, now: LocalDateTime) {
        val targetDateTime = now.minusHours(timing.hoursAfter.toLong())
        val targetDate = targetDateTime.toLocalDate()

        // Get completed appointments from the target date
        val appointments = appointmentRepository.findByDateAndStatus(targetDate, AppointmentStatus.COMPLETED)
            .filter { it.salonId == timing.salonId }

        val appointmentsNeedingFollowUp = appointments.filter { appt ->
            // Use completedAt if available, otherwise fall back to endTime for backwards compatibility
            val completionDateTime = appt.completedAt ?: LocalDateTime.of(appt.appointmentDate, appt.endTime)
            val minutesSinceCompletion = ChronoUnit.MINUTES.between(completionDateTime, now)
            val targetMinutes = timing.hoursAfter * 60

            // Special handling for "immediate" follow-up (0 hours)
            val inWindow = if (timing.hoursAfter == 0) {
                // For immediate follow-up: send within first 5 minutes after completion
                minutesSinceCompletion in 0..5
            } else {
                // For delayed follow-up: send within 5-minute window around target time
                minutesSinceCompletion in (targetMinutes - 5)..(targetMinutes)
            }

            logger.debug("📊 Appointment ${appt.id.value}: ${minutesSinceCompletion}min since completion (using ${if (appt.completedAt != null) "completedAt" else "endTime"}), target: ${targetMinutes}min, inWindow: $inWindow")
            inWindow
        }

        logger.info("📅 Found ${appointmentsNeedingFollowUp.size} appointments needing ${timing.hoursAfter}h follow-up")

        appointmentsNeedingFollowUp.forEach { appt ->
            logger.info("📨 Publishing event for ${timing.hoursAfter}h follow-up for appointment ${appt.id.value}")
            eventPublisher.publishEvent(
                AppointmentFollowUpEvent(
                    appointmentId = appt.id,
                    clientId = appt.clientId,
                    appointmentDate = appt.appointmentDate,
                    startTime = appt.startTime,
                    salonId = appt.salonId,
                    hoursAfter = timing.hoursAfter,
                    eventId = "followup-${timing.hoursAfter}h-${appt.id.value}",
                    occurredAt = LocalDateTime.now(),
                    aggregateId = appt.id.value,
                    userId = staffRepository.findById(appt.staffId)?.userId ?: UserId.of(appt.staffId.value),
                )
            )
        }
    }
}
