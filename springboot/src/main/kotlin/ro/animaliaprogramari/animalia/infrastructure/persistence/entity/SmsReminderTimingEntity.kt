package ro.animaliaprogramari.animalia.infrastructure.persistence.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "sms_reminder_timings")
data class SmsReminderTimingEntity(
    @Id
    @Column(name = "id", nullable = false)
    val id: String,

    @Column(name = "salon_id", nullable = false)
    val salonId: String,

    @Column(name = "hours_before", nullable = false)
    val hoursBefore: Int,

    @Column(name = "is_enabled", nullable = false)
    val isEnabled: Boolean = true,


    @Column(name = "created_at", nullable = false)
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_at", nullable = false)
    val updatedAt: LocalDateTime = LocalDateTime.now()
)
