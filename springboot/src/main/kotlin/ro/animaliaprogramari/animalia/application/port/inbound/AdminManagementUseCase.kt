package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.SmsQuotaSummaryDto
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.SubscriptionSummaryDto
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.UserSalonDto
import ro.animaliaprogramari.animalia.application.command.ImpersonateUserCommand
import ro.animaliaprogramari.animalia.application.query.AdminGetAllUsersQuery
import ro.animaliaprogramari.animalia.application.query.AdminSearchUsersQuery
import ro.animaliaprogramari.animalia.application.query.SearchUsersQuery
import ro.animaliaprogramari.animalia.domain.model.JwtToken
import ro.animaliaprogramari.animalia.domain.model.User
import ro.animaliaprogramari.animalia.domain.model.UserId
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SubscriptionTier

/**
 * Enriched user data for admin management
 */
data class EnrichedUserData(
    val salons: List<UserSalonDto>,
    val smsQuota: SmsQuotaSummaryDto?,
    val activeSubscriptions: List<SubscriptionSummaryDto>,
    val hasPastSubscriptions: Boolean,
)

/**
 * Inbound port for admin management use cases
 */
interface AdminManagementUseCase {
    /**
     * Impersonate a user as admin and generate JWT token
     */
    fun impersonateUser(command: ImpersonateUserCommand): JwtToken

    /**
     * Get all users for admin management
     */
    fun getAllUsers(query: AdminGetAllUsersQuery): List<User>

    /**
     * Search users by criteria
     */
    fun searchUsers(query: AdminSearchUsersQuery): List<User>

    /**
     * Get user by ID
     */
    fun getUserById(userId: UserId): User?

    /**
     * Get enriched user data including salons, SMS quota, and subscriptions
     */
    fun getEnrichedUserData(userId: UserId): EnrichedUserData

    /**
     * Update SMS quota for a user's salon
     */
    fun updateUserSmsQuota(userId: UserId, salonId: SalonId, totalQuota: Int): SmsQuotaSummaryDto

    /**
     * Update subscription tier for a salon
     */
    fun updateSubscriptionTier(salonId: SalonId, tier: SubscriptionTier): SubscriptionSummaryDto
}
