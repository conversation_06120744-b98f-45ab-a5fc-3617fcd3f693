package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.notification.SmsQuota
import ro.animaliaprogramari.animalia.domain.model.notification.SmsQuotaId

/**
 * Outbound port for SMS quota persistence
 */
interface SmsQuotaRepository {
    /**
     * Save SMS quota
     */
    fun save(smsQuota: SmsQuota): SmsQuota

    /**
     * Find SMS quota by salon ID
     */
    fun findBySalonId(salonId: SalonId): SmsQuota?

    /**
     * Find SMS quota by ID
     */
    fun findById(id: SmsQuotaId): SmsQuota?

    /**
     * Check if SMS quota exists for a salon
     */
    fun existsBySalonId(salonId: SalonId): Boolean

    // findQuotasNeedingReset method removed - SMS credits never expire

    /**
     * Delete SMS quota by salon ID
     */
    fun deleteBySalonId(salonId: SalonId)
}
