package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.AuthenticatedUser
import ro.animaliaprogramari.animalia.domain.model.JwtToken
import ro.animaliaprogramari.animalia.domain.model.UserId

/**
 * Outbound port for JWT token generation and validation
 * This will be implemented by JWT adapter
 */
interface JwtTokenGenerator {
    /**
     * Generate JWT token for authenticated user
     */
    fun generateToken(user: AuthenticatedUser): JwtToken

    /**
     * Generate impersonation JWT token for admin user impersonating another user
     */
    fun generateImpersonationToken(user: AuthenticatedUser, adminUserId: UserId): JwtToken

    /**
     * Validate JWT token and extract user information
     */
    fun validateToken(token: String): JwtTokenValidationResult

    /**
     * Refresh JWT token
     */
    fun refreshToken(token: String): JwtToken?
}

/**
 * Result of JWT token validation
 */
data class JwtTokenValidationResult(
    val isValid: Boolean,
    val userId: String?,
    val firebaseUid: String?,
    val email: String?,
    val name: String?,
    val role: String?,
    val isActive: Boolean = false,
    val isImpersonated: Boolean = false,
    val impersonatedByAdminId: String? = null,
    val errorMessage: String? = null,
) {
    companion object {
        fun success(
            userId: String,
            firebaseUid: String,
            email: String,
            name: String,
            role: String,
            isActive: Boolean,
            isImpersonated: Boolean = false,
            impersonatedByAdminId: String? = null,
        ): JwtTokenValidationResult {
            return JwtTokenValidationResult(
                isValid = true,
                userId = userId,
                firebaseUid = firebaseUid,
                email = email,
                name = name,
                role = role,
                isActive = isActive,
                isImpersonated = isImpersonated,
                impersonatedByAdminId = impersonatedByAdminId,
            )
        }

        fun failure(errorMessage: String): JwtTokenValidationResult {
            return JwtTokenValidationResult(
                isValid = false,
                userId = null,
                firebaseUid = null,
                email = null,
                name = null,
                role = null,
                errorMessage = errorMessage,
            )
        }
    }
}
