package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

/**
 * Query to get notification statistics
 */
data class GetNotificationStatsQuery(
    val userId: UserId,
    val salonId: SalonId,
    val startDate: LocalDateTime? = null,
    val endDate: LocalDateTime? = null
)

/**
 * Query to get notifications for a user
 */
data class GetUserNotificationsQuery(
    val userId: UserId,
    val salonId: SalonId,
    val page: Int = 0,
    val pageSize: Int = 20,
    val unreadOnly: Boolean = false,
    val type: String? = null
) {
    init {
        require(pageSize > 0 && pageSize <= 100) { "Page size must be between 1 and 100" }
        require(page >= 0) { "Page must be non-negative" }
    }

    val offset: Int get() = page * pageSize
    val limit: Int get() = pageSize
}

/**
 * Query to get FCM tokens for a user
 */
data class GetUserFcmTokensQuery(
    val userId: UserId,
    val salonId: SalonId,
    val activeOnly: Boolean = true
)

