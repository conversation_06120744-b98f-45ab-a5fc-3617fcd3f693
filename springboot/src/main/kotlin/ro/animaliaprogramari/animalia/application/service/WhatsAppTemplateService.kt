package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.WhatsAppTemplatePreference
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository.WhatsAppTemplatePreferenceRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringWhatsAppTemplateRepository
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.WhatsAppTemplateEntity
import ro.animaliaprogramari.animalia.domain.model.UpdateWhatsAppTemplatePreferenceRequest
import ro.animaliaprogramari.animalia.domain.model.WhatsAppTemplate
import ro.animaliaprogramari.animalia.domain.model.WhatsAppTemplatePreferenceDomain
import java.time.LocalDateTime
import java.util.*

/**
 * Service for managing WhatsApp template preferences
 */
@Service
@Transactional
class WhatsAppTemplateService(
    private val preferenceRepository: WhatsAppTemplatePreferenceRepository,
    private val whatsAppTemplateRepository: SpringWhatsAppTemplateRepository
) {
    private val logger = LoggerFactory.getLogger(WhatsAppTemplateService::class.java)

    companion object {
        // Predefined WhatsApp templates with ContentSids
        private val AVAILABLE_TEMPLATES = mapOf(
            SmsMessageType.APPOINTMENT_CONFIRMATION to listOf(
                WhatsAppTemplate(
                    id = "confirmation_friendly_ro",
                    templateType = "APPOINTMENT_CONFIRMATION",
                    displayName = "Prietenos",
                    languageFlag = "🇷🇴",
                    previewText = "Salut {OWNER_NAME}! Programarea pentru {PET_NAME} e confirmată pe {APPOINTMENT_DATE} la {APPOINTMENT_TIME}. Te așteptăm! 🐾",
                    contentSid = "HX54463693fc53410d1a2a79c4b65988f6",
                    isDefault = true
                ),
                WhatsAppTemplate(
                    id = "confirmation_formal_ro",
                    templateType = "APPOINTMENT_CONFIRMATION",
                    displayName = "Formal",
                    languageFlag = "🇷🇴",
                    previewText = "Bună ziua {OWNER_NAME}! Programarea pentru {PET_NAME} a fost confirmată pentru {APPOINTMENT_DATE} la {APPOINTMENT_TIME}.",
                    contentSid = "HX54463693fc53410d1a2a79c4b65988f7"
                )
            ),
            SmsMessageType.REMINDER to listOf(
                WhatsAppTemplate(
                    id = "reminder_friendly_ro",
                    templateType = "REMINDER",
                    displayName = "Prietenos",
                    languageFlag = "🇷🇴",
                    previewText = "Salut {OWNER_NAME}! Te așteptăm cu {PET_NAME} la {APPOINTMENT_TIME}. Ne bucurăm să vă vedem! 🐾",
                    contentSid = "HXfc6882b06959b046693509ea67f440af",
                    isDefault = true
                ),
                WhatsAppTemplate(
                    id = "reminder_urgent_ro",
                    templateType = "REMINDER",
                    displayName = "Urgent",
                    languageFlag = "🇷🇴",
                    previewText = "⏰ Reminder {OWNER_NAME}! {PET_NAME} are programare la {APPOINTMENT_TIME}. Te așteptăm!",
                    contentSid = "HXfc6882b06959b046693509ea67f440b0"
                )
            ),
            SmsMessageType.APPOINTMENT_CANCELLATION to listOf(
                WhatsAppTemplate(
                    id = "cancellation_simple_ro",
                    templateType = "APPOINTMENT_CANCELLATION",
                    displayName = "Simplu",
                    languageFlag = "🇷🇴",
                    previewText = "Salut {OWNER_NAME}! Programarea pentru {PET_NAME} din {APPOINTMENT_DATE} a fost anulată. Pentru reprogramare: {SALON_PHONE}",
                    contentSid = "HX33e703a3fb2a3e141a9e42cccf878a8f",
                    isDefault = true
                ),
                WhatsAppTemplate(
                    id = "cancellation_apologetic_ro",
                    templateType = "APPOINTMENT_CANCELLATION",
                    displayName = "Cu scuze",
                    languageFlag = "🇷🇴",
                    previewText = "Ne pare rău {OWNER_NAME}! Programarea pentru {PET_NAME} din {APPOINTMENT_DATE} s-a anulat. Reprogramăm? {SALON_PHONE}",
                    contentSid = "HX33e703a3fb2a3e141a9e42cccf878a90"
                )
            ),
            SmsMessageType.APPOINTMENT_RESCHEDULE to listOf(
                WhatsAppTemplate(
                    id = "reschedule_simple_ro",
                    templateType = "APPOINTMENT_RESCHEDULE",
                    displayName = "Simplu",
                    languageFlag = "🇷🇴",
                    previewText = "Salut {OWNER_NAME}! Programarea pentru {PET_NAME} s-a mutat pe {APPOINTMENT_DATE} la {APPOINTMENT_TIME}. Te așteptăm! 🐾",
                    contentSid = "HXca11c0ae15e5560949da41a98eb3e692",
                    isDefault = true
                ),
                WhatsAppTemplate(
                    id = "reschedule_formal_ro",
                    templateType = "APPOINTMENT_RESCHEDULE",
                    displayName = "Formal",
                    languageFlag = "🇷🇴",
                    previewText = "Bună ziua {OWNER_NAME}! Programarea pentru {PET_NAME} a fost reprogramată pe {APPOINTMENT_DATE} la {APPOINTMENT_TIME}.",
                    contentSid = "HXca11c0ae15e5560949da41a98eb3e693"
                )
            ),
            SmsMessageType.APPOINTMENT_COMPLETION to listOf(
                WhatsAppTemplate(
                    id = "completion_thankyou_ro",
                    templateType = "COMPLETION",
                    displayName = "Mulțumire",
                    languageFlag = "🇷🇴",
                    previewText = "Mulțumim {OWNER_NAME}! {PET_NAME} a fost grozav astăzi. Sperăm să ne revedem curând! 🐾",
                    contentSid = "HX715668bc529346ed239c09a3fb62881c",
                    isDefault = true
                ),
                WhatsAppTemplate(
                    id = "completion_review_ro",
                    templateType = "COMPLETION",
                    displayName = "Cu recenzie",
                    languageFlag = "🇷🇴",
                    previewText = "Mulțumim {OWNER_NAME}! Dacă {PET_NAME} s-a simțit bine, ne-ar bucura o recenzie. Info: {SALON_PHONE}",
                    contentSid = "HX715668bc529346ed239c09a3fb62881d"
                )
            ),
            SmsMessageType.FOLLOW_UP to listOf(
                WhatsAppTemplate(
                    id = "followup_care_ro",
                    templateType = "FOLLOW_UP",
                    displayName = "Grija",
                    languageFlag = "🇷🇴",
                    previewText = "Salut {OWNER_NAME}! Cum se simte {PET_NAME} după vizită? Mulțumim că ne-ai ales! Pentru întrebări: {SALON_PHONE}",
                    contentSid = "HX0ee6de0187083f11060d1febdac5ceb8",
                    isDefault = true
                ),
                WhatsAppTemplate(
                    id = "followup_review_ro",
                    templateType = "FOLLOW_UP",
                    displayName = "Recenzie",
                    languageFlag = "🇷🇴",
                    previewText = "Mulțumim pentru vizită {OWNER_NAME}! Dacă {PET_NAME} s-a simțit bine, ne-ar bucura o recenzie pe Google. 🌟",
                    contentSid = "HX0ee6de0187083f11060d1febdac5ceb9"
                )
            )
        )
    }

    /**
     * Get all available WhatsApp templates
     */
    fun getAvailableTemplates(): List<WhatsAppTemplate> {
        // Try DB first
        val dbTemplates = whatsAppTemplateRepository.findAll()
        if (dbTemplates.isNotEmpty()) {
            return dbTemplates.map { entityToDomain(it) }
        }

        // Fallback to static list
        return AVAILABLE_TEMPLATES.values.flatten()
    }

    /**
     * Get available templates for a specific message type
     */
    fun getAvailableTemplatesForType(messageType: SmsMessageType): List<WhatsAppTemplate> {
        // DB first
        val dbList = whatsAppTemplateRepository.findByTemplateType(messageType)
        if (dbList.isNotEmpty()) {
            return dbList.map { entityToDomain(it) }
        }

        return AVAILABLE_TEMPLATES[messageType] ?: emptyList()
    }

    /**
     * Get salon's template preferences
     */
    @Transactional(readOnly = true)
    fun getSalonTemplatePreferences(salonId: String): List<WhatsAppTemplatePreferenceDomain> {
        logger.info("Getting WhatsApp template preferences for salon: $salonId")

        val preferences = preferenceRepository.findBySalonId(salonId)
        return preferences.map { preference ->
            WhatsAppTemplatePreferenceDomain(
                id = preference.id,
                salonId = preference.salonId,
                templateType = preference.messageType.name,
                whatsappTemplateId = preference.whatsappTemplateId,
                contentSid = preference.contentSid
            )
        }
    }

    /**
     * Update salon's template preference for a specific message type
     */
    fun updateTemplatePreference(
        salonId: String,
        request: UpdateWhatsAppTemplatePreferenceRequest
    ): WhatsAppTemplatePreferenceDomain {
        logger.info("Updating WhatsApp template preference for salon: $salonId, type: ${request.templateType}")

        val messageType = try {
            SmsMessageType.valueOf(request.templateType)
        } catch (_: IllegalArgumentException) {
            throw IllegalArgumentException("Invalid message type: ${request.templateType}")
        }

        // Find the template to get contentSid
        val template = findTemplateById(request.whatsappTemplateId)
            ?: throw IllegalArgumentException("Template not found: ${request.whatsappTemplateId}")

        // Validate template type matches
        require(template.templateType == request.templateType) { "Template type mismatch" }

        // Find existing preference or create new one
        val existingPreference = preferenceRepository.findBySalonIdAndMessageType(salonId, messageType)

        val preference = existingPreference
            .map { existing ->
                preferenceRepository.save(
                    existing.copy(
                        whatsappTemplateId = request.whatsappTemplateId,
                        contentSid = template.contentSid,
                        updatedAt = LocalDateTime.now()
                    )
                )
            }
            .orElseGet {
                preferenceRepository.save(
                    WhatsAppTemplatePreference(
                        id = UUID.randomUUID().toString(),
                        salonId = salonId,
                        messageType = messageType,
                        whatsappTemplateId = request.whatsappTemplateId,
                        contentSid = template.contentSid
                    )
                )
            }

        logger.info("Successfully updated WhatsApp template preference for salon: $salonId")

        return WhatsAppTemplatePreferenceDomain(
            id = preference.id,
            salonId = preference.salonId,
            templateType = preference.messageType.name,
            whatsappTemplateId = preference.whatsappTemplateId,
            contentSid = preference.contentSid
        )
    }

    /**
     * Get preferred template for a salon and message type (for logging purposes)
     */
    @Transactional(readOnly = true)
    fun getPreferredTemplateForSalon(salonId: String, messageType: SmsMessageType): WhatsAppTemplate? {
        logger.debug("Getting preferred template for salon: {}, type: {}", salonId, messageType)

        // Try to get salon's preference
        val preferenceOpt = preferenceRepository.findBySalonIdAndMessageType(salonId, messageType)

        if (preferenceOpt.isPresent) {
            val preference = preferenceOpt.get()
            // Find the template by its whatsappTemplateId
            val template = findTemplateById(preference.whatsappTemplateId)
            if (template != null) {
                logger.debug("Found salon's preferred template: {}", template.displayName)
                return template
            }
        }

        // Return default template for this message type
        val defaultTemplate = getAvailableTemplatesForType(messageType).find { it.isDefault }
        logger.debug("Using default template: {}", defaultTemplate?.displayName ?: "None")
        return defaultTemplate
    }

    /**
     * Get content SID for salon's preferred template for a message type
     */
    @Transactional(readOnly = true)
    fun getPreferredContentSid(salonId: String, messageType: SmsMessageType): String {
        logger.debug("Getting preferred content SID for salon: {}, type: {}", salonId, messageType)

        val contentSidOpt = preferenceRepository.findContentSidBySalonIdAndMessageType(salonId, messageType)

        val sid = contentSidOpt.orElse(null)
        if (sid != null) {
            logger.debug("Found salon preference, using content SID: {}", sid)
            return sid
        }

        // Use default template for this message type
        val defaultTemplate = getAvailableTemplatesForType(messageType).find { it.isDefault }
        val defaultSid = defaultTemplate?.contentSid ?: getDefaultContentSid(messageType)
        logger.debug("No salon preference found, using default content SID: {}", defaultSid)
        return defaultSid
    }

    private fun findTemplateById(templateId: String): WhatsAppTemplate? {
        // Check DB first
        val dbOpt = whatsAppTemplateRepository.findById(templateId)
        val fromDb = dbOpt.map { entityToDomain(it) }.orElse(null)
        if (fromDb != null) return fromDb

        return AVAILABLE_TEMPLATES.values.flatten().find { it.id == templateId }
    }

    private fun entityToDomain(entity: WhatsAppTemplateEntity): WhatsAppTemplate {
        return WhatsAppTemplate(
            id = entity.id,
            templateType = entity.templateType.name,
            displayName = entity.displayName,
            languageFlag = entity.languageFlag,
            previewText = entity.previewText,
            contentSid = entity.contentSid,
            isDefault = entity.isDefault
        )
    }

    private fun getDefaultContentSid(messageType: SmsMessageType): String {
        return when (messageType) {
            SmsMessageType.APPOINTMENT_CONFIRMATION -> "HX54463693fc53410d1a2a79c4b65988f6"
            SmsMessageType.REMINDER -> "HXfc6882b06959b046693509ea67f440af"
            SmsMessageType.APPOINTMENT_CANCELLATION -> "HX33e703a3fb2a3e141a9e42cccf878a8f"
            SmsMessageType.APPOINTMENT_RESCHEDULE -> "HXca11c0ae15e5560949da41a98eb3e692"
            SmsMessageType.APPOINTMENT_COMPLETION -> "HX715668bc529346ed239c09a3fb62881c"
            SmsMessageType.FOLLOW_UP -> "HX0ee6de0187083f11060d1febdac5ceb8"
            else -> "HXb5b62575e6e4ff6129ad7c8efe1f983e"
        }
    }
}
