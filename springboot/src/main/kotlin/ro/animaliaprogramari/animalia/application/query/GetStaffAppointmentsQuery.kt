package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.AppointmentStatus
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.StaffId
import ro.animaliaprogramari.animalia.domain.model.UserId
import java.time.LocalDate

data class GetStaffAppointmentsQuery(
    val salonId: SalonId,
    val staffId: StaffId,
    val date: LocalDate? = null,
    val startDate: LocalDate? = null,
    val endDate: LocalDate? = null,
    val status: AppointmentStatus? = null,
    val requesterId: UserId,
)
