package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Outbound port for salon invitation persistence
 * This will be implemented by JPA adapter
 */
interface SalonInvitationRepository {
    /**
     * Save invitation
     */
    fun save(invitation: SalonInvitation): SalonInvitation

    /**
     * Find invitation by ID
     */
    fun findById(id: InvitationId): SalonInvitation?

    /**
     * Find pending invitations for a user by phone number
     */
    fun findPendingByInvitedUserPhone(phoneNumber: String): List<SalonInvitation>

    /**
     * Find invitations sent by a salon
     */
    fun findBySalonId(salonId: SalonId): List<SalonInvitation>

    /**
     * Find invitations sent by a specific user
     */
    fun findByInviterUserId(inviterUserId: UserId): List<SalonInvitation>

    /**
     * Find pending invitations for a salon
     */
    fun findPendingBySalonId(salonId: SalonId): List<SalonInvitation>

    /**
     * Check if there's already a pending invitation for the same phone and salon
     */
    fun existsPendingInvitation(
        salonId: SalonId,
        phoneNumber: String,
    ): Boolean

    /**
     * Find expired invitations that need to be marked as expired
     */
    fun findExpiredInvitations(): List<SalonInvitation>

    /**
     * Delete invitation by ID
     */
    fun deleteById(id: InvitationId): Boolean

    /**
     * Count invitations by status for a salon
     */
    fun countByStatusAndSalonId(
        status: InvitationStatus,
        salonId: SalonId,
    ): Long
}
