package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.data.rest.webmvc.ResourceNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.AppointmentSettings
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository.AppointmentSettingsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import java.time.LocalDateTime

/**
 * Service for managing appointment settings
 */
@Service
@Transactional
class AppointmentSettingsService(
    private val appointmentSettingsRepository: AppointmentSettingsRepository,
    private val salonRepository: SalonRepository
) {
    private val logger = LoggerFactory.getLogger(AppointmentSettingsService::class.java)

    /**
     * Get appointment settings for a salon
     * Creates default settings if none exist
     */
    @Transactional(readOnly = true)
    fun getAppointmentSettings(salonId: String): AppointmentSettings {
        logger.info("Getting appointment settings for salon: {}", salonId)

        // Verify salon exists
        if (!salonRepository.existsById(SalonId.of(salonId))) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        return appointmentSettingsRepository.findBySalonId(salonId)
            .orElseGet {
                logger.info("Creating default appointment settings for salon: {}", salonId)
                createDefaultSettings(salonId)
            }
    }

    /**
     * Update appointment settings for a salon
     */
    fun updateAppointmentSettings(
        salonId: String,
        autoFinalizeEnabled: Boolean,
        overdueNotificationsEnabled: Boolean,
        smsCompletionEnabled: Boolean
    ): AppointmentSettings {
        logger.info("Updating appointment settings for salon: {}", salonId)

        // Verify salon exists
        if (!salonRepository.existsById(SalonId.of(salonId))) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        // Business rule: if auto-finalize is enabled, overdue notifications should be disabled
        val actualOverdueNotificationsEnabled = if (autoFinalizeEnabled) false else overdueNotificationsEnabled

        val settings = appointmentSettingsRepository.findBySalonId(salonId)
            .orElseGet { createDefaultSettings(salonId) }

        // Update settings
        settings.autoFinalizeEnabled = autoFinalizeEnabled
        settings.overdueNotificationsEnabled = actualOverdueNotificationsEnabled
        settings.smsCompletionEnabled = smsCompletionEnabled
        settings.updatedAt = LocalDateTime.now()

        val savedSettings = appointmentSettingsRepository.save(settings)

        logger.info("Updated appointment settings for salon: {} - autoFinalize: {}, overdueNotifications: {}, smsCompletion: {}",
            salonId, autoFinalizeEnabled, actualOverdueNotificationsEnabled, smsCompletionEnabled)

        return savedSettings
    }

    /**
     * Create default appointment settings for a salon
     */
    private fun createDefaultSettings(salonId: String): AppointmentSettings {
        val defaultSettings = AppointmentSettings(
            salonId = salonId,
            autoFinalizeEnabled = false,
            overdueNotificationsEnabled = true,
            smsCompletionEnabled = true,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        return appointmentSettingsRepository.save(defaultSettings)
    }

}
