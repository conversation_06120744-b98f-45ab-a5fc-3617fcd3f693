package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import ro.animaliaprogramari.animalia.application.command.AuthenticateWithFirebaseCommand
import ro.animaliaprogramari.animalia.application.command.ImpersonateUserCommand
import ro.animaliaprogramari.animalia.application.command.RefreshTokenCommand
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationResult
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.FirebaseTokenValidator
import ro.animaliaprogramari.animalia.application.port.outbound.JwtTokenGenerator
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.application.query.GetUserByFirebaseUidQuery
import ro.animaliaprogramari.animalia.application.query.GetUserByIdQuery
import ro.animaliaprogramari.animalia.domain.model.AuthenticatedUser
import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.User
import ro.animaliaprogramari.animalia.domain.model.UserId
import ro.animaliaprogramari.animalia.domain.model.UserRole
import ro.animaliaprogramari.animalia.domain.service.AuthenticationService

/**
 * Implementation of authentication use cases
 * Orchestrates authentication operations using domain services and outbound ports
 */
open class AuthenticationUseCaseImpl(
    private val firebaseTokenValidator: FirebaseTokenValidator,
    private val jwtTokenGenerator: JwtTokenGenerator,
    private val userRepository: UserRepository,
    private val staffRepository: StaffRepository,
    private val authenticationService: AuthenticationService,
) : AuthenticationUseCase {
    private val logger = LoggerFactory.getLogger(AuthenticationUseCaseImpl::class.java)

    override fun authenticateWithFirebase(command: AuthenticateWithFirebaseCommand): AuthenticationResult {
        try {

            // 1. Validate Firebase token
            val firebaseResult = firebaseTokenValidator.validateToken(command.firebaseToken)
            if (!firebaseResult.isValid) {
                logger.error("Step 1 FAILED: Firebase token validation failed: ${firebaseResult.errorMessage}")
                return AuthenticationResult.failure(
                    firebaseResult.errorMessage ?: "Invalid Firebase token",
                )
            }


            // 2. Extract user data from Firebase token
            val firebaseUid = firebaseResult.firebaseUid!!
            val email = Email.ofNullable(firebaseResult.email)
            val name = firebaseResult.name!!
            val phoneNumber = firebaseResult.phoneNumber ?: ""

            // 3. Find or create user
            val existingUser = userRepository.findByFirebaseUid(firebaseUid)

            val user =
                authenticationService.createOrUpdateUserFromFirebase(
                    firebaseUid = firebaseUid,
                    email = email,
                    phoneNumber = phoneNumber,
                    name = name,
                    existingUser = existingUser,
                )

            // 4. Validate user for authentication
            val validationResult = authenticationService.validateUserForAuthentication(user)
            if (!validationResult.isValid()) {
                return AuthenticationResult.failure(
                    validationResult.getErrorMessage() ?: "User validation failed",
                )
            }

            // 5. Save user (create or update)
            logger.info("Saving user to database")
            val savedUser = userRepository.save(user)
            logger.info("User saved successfully with ID: ${savedUser.id.value}")

            // 6. Load staff associations
            val staffAssociations = staffRepository.findActiveByUserId(savedUser.id)

            // 7. Create authenticated user context
            val authenticatedUser = AuthenticatedUser.from(savedUser, staffAssociations)

            // 8. Generate JWT token
            logger.info("Generating JWT token for user")
            val jwtToken = jwtTokenGenerator.generateToken(authenticatedUser)
            logger.info("JWT token generated successfully")

            return AuthenticationResult.success(authenticatedUser, jwtToken)
        } catch (e: Exception) {
            logger.error("Authentication failed with exception", e)
            return AuthenticationResult.failure("Authentication failed: ${e.message}")
        }
    }

    override fun refreshToken(command: RefreshTokenCommand): AuthenticationResult {
        try {
            val newToken =
                jwtTokenGenerator.refreshToken(command.refreshToken)
                    ?: return AuthenticationResult.failure("Invalid refresh token")

            // Validate the new token to get user context
            val validationResult = jwtTokenGenerator.validateToken(newToken.token)
            if (!validationResult.isValid) {
                return AuthenticationResult.failure("Token refresh failed")
            }

            // Get user from database
            val userId = UserId.of(validationResult.userId!!)
            val user =
                userRepository.findById(userId)
                    ?: return AuthenticationResult.failure("User not found")

            val staffAssociations = staffRepository.findActiveByUserId(user.id)
            val authenticatedUser = AuthenticatedUser.from(user, staffAssociations)
            return AuthenticationResult.success(authenticatedUser, newToken)
        } catch (e: Exception) {
            return AuthenticationResult.failure("Token refresh failed: ${e.message}")
        }
    }

    override fun getCurrentUser(query: GetUserByIdQuery): AuthenticatedUser? {
        val user = userRepository.findById(query.userId)
        return user?.takeIf { it.isActive }?.let {
            val staffAssociations = staffRepository.findActiveByUserId(it.id)
            AuthenticatedUser.from(it, staffAssociations)
        }
    }

    override fun getUserByFirebaseUid(query: GetUserByFirebaseUidQuery): User? {
        return userRepository.findByFirebaseUid(query.firebaseUid)
    }

    override fun validateToken(token: String): AuthenticatedUser? {
        try {
            val validationResult = jwtTokenGenerator.validateToken(token)

            if (!validationResult.isValid) {
                logger.error("JWT validation failed: ${validationResult.errorMessage}")
                return null
            }

            val userId = UserId.of(validationResult.userId!!)
            val user = userRepository.findById(userId)

            if (user == null) {
                logger.error("User not found in database for ID: ${userId.value}")
                return null
            }

            return user.takeIf { it.isActive }?.let {
                val staffAssociations = staffRepository.findActiveByUserId(it.id)

                val authenticatedUser = AuthenticatedUser.from(it, staffAssociations)
                authenticatedUser
            } ?: run {
                logger.error("User is not active: ${user.id.value}")
                null
            }
        } catch (e: Exception) {
            logger.error("=== JWT TOKEN VALIDATION ERROR ===")
            logger.error("Exception type: ${e.javaClass.simpleName}")
            logger.error("Exception message: ${e.message}")
            logger.error("Stack trace: ", e)
            return null
        }
    }

    override fun impersonateUser(command: ImpersonateUserCommand): AuthenticationResult {
        try {
            // 1. Verify admin user exists and has admin role
            val adminUser = userRepository.findById(command.adminUserId)
                ?: return AuthenticationResult.failure("Admin user not found")

            if (adminUser.role != UserRole.ADMIN) {
                return AuthenticationResult.failure("Only administrators can impersonate users")
            }

            if (!adminUser.isActive) {
                return AuthenticationResult.failure("Admin user is not active")
            }

            // 2. Verify target user exists and is active
            val targetUser = userRepository.findById(command.targetUserId)
                ?: return AuthenticationResult.failure("Target user not found")

            if (!targetUser.isActive) {
                return AuthenticationResult.failure("Target user is not active")
            }

            // 3. Load target user's staff associations
            val staffAssociations = staffRepository.findActiveByUserId(targetUser.id)

            // 4. Create authenticated user context for target user
            val authenticatedUser = AuthenticatedUser.from(targetUser, staffAssociations)

            // 5. Generate impersonation JWT token
            val jwtToken = jwtTokenGenerator.generateImpersonationToken(authenticatedUser, command.adminUserId)

            logger.info("Admin ${adminUser.id.value} successfully impersonating user ${targetUser.id.value}")

            return AuthenticationResult.success(authenticatedUser, jwtToken)
        } catch (e: Exception) {
            logger.error("Impersonation failed with exception", e)
            return AuthenticationResult.failure("Impersonation failed: ${e.message}")
        }
    }
}
