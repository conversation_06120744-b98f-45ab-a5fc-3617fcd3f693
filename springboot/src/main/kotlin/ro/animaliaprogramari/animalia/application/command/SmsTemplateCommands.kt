package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsTemplateType
import ro.animaliaprogramari.animalia.domain.model.UserId

/**
 * Command to create a new SMS template
 */
data class CreateSmsTemplateCommand(
    val salonId: SalonId,
    val templateType: SmsTemplateType,
    val templateContent: String,
    val creatorUserId: UserId,
)

/**
 * Command to update an existing SMS template
 */
data class UpdateSmsTemplateCommand(
    val templateId: String,
    val salonId: SalonId,
    val templateContent: String,
    val isActive: Boolean = true,
    val updaterUserId: UserId,
)

/**
 * Command to create SMS reminder timing
 */
data class CreateSmsReminderTimingCommand(
    val salonId: SalonId,
    val hoursBefore: Int,
    val isEnabled: Boolean = true,
    val creatorUserId: UserId,
)

/**
 * Command to update SMS reminder timing
 */
data class UpdateSmsReminderTimingCommand(
    val timingId: String,
    val salonId: SalonId,
    val hoursBefore: Int,
    val isEnabled: Boolean,
    val updaterUserId: UserId,
)


data class DeleteSmsReminderTimingCommand(
    val timingId: String,
    val salonId: SalonId,
    val deleterUserId: UserId,
)
