package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsTemplateType

/**
 * Query to get all SMS templates for a salon
 */
data class GetSmsTemplatesQuery(
    val salonId: SalonId,
    val activeOnly: Boolean = false,
)

/**
 * Query to get a specific SMS template
 */
data class GetSmsTemplateQuery(
    val salonId: SalonId,
    val templateType: SmsTemplateType,
)

