package ro.animaliaprogramari.animalia.application.usecase

import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsFollowUpTiming
import ro.animaliaprogramari.animalia.domain.model.SmsFollowUpTimingId
import ro.animaliaprogramari.animalia.domain.port.outbound.SmsFollowUpTimingRepository

@Service
class SmsFollowUpTimingUseCase(
    private val smsFollowUpTimingRepository: SmsFollowUpTimingRepository
) {

    fun createTiming(command: CreateSmsFollowUpTimingCommand): SmsFollowUpTiming {
        val timing = SmsFollowUpTiming.create(
            salonId = command.salonId,
            hoursAfter = command.hoursAfter
        ).setEnabled(command.isEnabled)

        return smsFollowUpTimingRepository.save(timing)
    }

    fun updateTiming(command: UpdateSmsFollowUpTimingCommand): SmsFollowUpTiming {
        val existingTiming = smsFollowUpTimingRepository.findById(command.timingId)
            ?: throw IllegalArgumentException("Follow-up timing not found: ${command.timingId.value}")

        val updatedTiming = existingTiming
            .updateHours(command.hoursAfter)
            .setEnabled(command.isEnabled)

        return smsFollowUpTimingRepository.save(updatedTiming)
    }

    fun deleteTiming(timingId: SmsFollowUpTimingId) {
        if (!smsFollowUpTimingRepository.existsById(timingId)) {
            throw IllegalArgumentException("Follow-up timing not found: ${timingId.value}")
        }
        smsFollowUpTimingRepository.deleteById(timingId)
    }

    fun getTimingsBySalon(salonId: SalonId): List<SmsFollowUpTiming> {
        return smsFollowUpTimingRepository.findBySalonId(salonId)
    }

    fun getEnabledTimingsBySalon(salonId: SalonId): List<SmsFollowUpTiming> {
        return smsFollowUpTimingRepository.findBySalonIdAndEnabled(salonId, true)
    }

    fun getAllEnabledTimings(): List<SmsFollowUpTiming> {
        return smsFollowUpTimingRepository.findAllEnabled()
    }
}

data class CreateSmsFollowUpTimingCommand(
    val salonId: SalonId,
    val hoursAfter: Int,
    val isEnabled: Boolean = true
)

data class UpdateSmsFollowUpTimingCommand(
    val timingId: SmsFollowUpTimingId,
    val hoursAfter: Int,
    val isEnabled: Boolean
)
