package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralCode
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralCodeId
import ro.animaliaprogramari.animalia.domain.model.referral.ReferralCodeStatus

/**
 * Outbound port for referral code persistence
 */
interface ReferralCodeRepository {
    /**
     * Save a referral code
     */
    fun save(referralCode: ReferralCode): ReferralCode

    /**
     * Find a referral code by ID
     */
    fun findById(id: ReferralCodeId): ReferralCode?

    /**
     * Find a referral code by code string
     */
    fun findByCode(code: String): ReferralCode?

    /**
     * Find all referral codes generated by a salon
     */
    fun findByGeneratorSalonId(salonId: SalonId): List<ReferralCode>

    /**
     * Find all referral codes claimed by a salon
     */
    fun findByClaimerSalonId(salonId: SalonId): List<ReferralCode>

    /**
     * Find all referral codes with a specific status
     */
    fun findByStatus(status: ReferralCodeStatus): List<ReferralCode>

    /**
     * Check if a code already exists
     */
    fun existsByCode(code: String): Boolean


}
