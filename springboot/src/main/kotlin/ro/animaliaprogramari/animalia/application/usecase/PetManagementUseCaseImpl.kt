package ro.animaliaprogramari.animalia.application.usecase

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.PetManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.DomainEventPublisher
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.event.PetAddedEvent
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.Pet
import java.time.LocalDateTime
import java.util.UUID

/**
 * Implementation of pet management use cases
 */
class PetManagementUseCaseImpl(
    private val petRepository: PetRepository,
    private val clientRepository: ClientRepository,
    private val domainEventPublisher: DomainEventPublisher,
) : PetManagementUseCase {
    override fun addPet(command: AddPetCommand): Pet {
        // Verify client exists
        clientRepository.findById(command.clientId)
            ?: throw EntityNotFoundException("Client", command.clientId.value)

        val pet =
            Pet.create(
                clientId = command.clientId,
                name = command.name,
                breed = command.breed,
                birthDate = command.birthDate,
                age = command.age,
                weight = command.weight,
                color = command.color,
                gender = command.gender,
                notes = command.notes,
                medicalConditions = command.medicalConditions,
                petSize = command.size,
                species = command.species,
                photoUrl = command.photoUrl,
            )

        val savedPet = petRepository.save(pet)

        // Publish domain event
        val event =
            PetAddedEvent(
                eventId = UUID.randomUUID().toString(),
                occurredAt = LocalDateTime.now(),
                aggregateId = savedPet.id.value,
                petId = savedPet.id,
                clientId = savedPet.clientId,
                petName = savedPet.name,
                breed = savedPet.breed,
            )

        domainEventPublisher.publish(event)

        return savedPet
    }

    override fun updatePet(command: UpdatePetCommand): Pet {
        val existingPet =
            petRepository.findById(command.petId)
                ?: throw EntityNotFoundException("Pet", command.petId.value)

        val updatedPet =
            existingPet.update(
                name = command.name,
                breed = command.breed,
                species = command.species,
                size = command.size,
                birthDate = command.birthDate,
                age = command.age,
                weight = command.weight,
                color = command.color,
                gender = command.gender,
                notes = command.notes,
                medicalConditions = command.medicalConditions,
                photoUrl = command.photoUrl,
            )

        return petRepository.save(updatedPet)
    }

    override fun deactivatePet(command: DeactivatePetCommand): Pet {
        val pet =
            petRepository.findById(command.petId)
                ?: throw EntityNotFoundException("Pet", command.petId.value)

        val deactivatedPet = pet.deactivate()
        return petRepository.save(deactivatedPet)
    }

    override fun activatePet(command: ActivatePetCommand): Pet {
        val pet =
            petRepository.findById(command.petId)
                ?: throw EntityNotFoundException("Pet", command.petId.value)

        val activatedPet = pet.activate()
        return petRepository.save(activatedPet)
    }

    override fun getPetById(query: GetPetByIdQuery): Pet? {
        return petRepository.findById(query.petId)
    }

    override fun getPetsByClient(query: GetPetsByClientQuery): List<Pet> {
        return petRepository.findByClientId(query.clientId, query.activeOnly)
    }

    override fun searchPets(query: SearchPetsQuery): List<Pet> {
        return petRepository.findAll(
            search = query.searchTerm,
            clientId = query.clientId,
            isActive = query.isActive,
            limit = query.limit,
            offset = query.offset,
        )
    }

    override fun getAllPets(query: GetAllPetsQuery): List<Pet> {
        return petRepository.findAll(
            search = null,
            clientId = query.clientId,
            isActive = query.isActive,
            limit = query.limit,
            offset = query.offset,
        )
    }
}
