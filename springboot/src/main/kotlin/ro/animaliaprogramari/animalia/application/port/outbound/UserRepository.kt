package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Outbound port for user persistence
 * This will be implemented by JPA adapter
 */
interface UserRepository {
    /**
     * Save user
     */
    fun save(user: User): User

    /**
     * Find user by ID
     */
    fun findById(id: UserId): User?

    /**
     * Find user by Firebase UID
     */
    fun findByFirebaseUid(firebaseUid: String): User?

    /**
     * Find user by email
     */
    fun findByEmail(email: Email): User?

    /**
     * Find user by phone number
     */
    fun findByPhoneNumber(phoneNumber: PhoneNumber): User?

    /**
     * Check if user exists by Firebase UID
     */
    fun existsByFirebaseUid(firebaseUid: String): Boolean

    /**
     * Check if user exists by email
     */
    fun existsByEmail(email: Email): Boolean

    /**
     * Find all active users
     */
    fun findActiveUsers(): List<User>

    /**
     * Find users with pagination and filtering
     */
    fun findAll(
        search: String? = null,
        role: UserRole? = null,
        isActive: Boolean? = null,
        limit: Int? = null,
        offset: Int? = null,
    ): List<User>

    /**
     * Delete user by ID
     */
    fun deleteById(id: UserId): Boolean
}
