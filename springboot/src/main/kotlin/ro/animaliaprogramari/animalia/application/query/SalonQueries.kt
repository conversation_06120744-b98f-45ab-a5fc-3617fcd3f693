package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Query to get salon by ID
 */
data class GetSalonByIdQuery(
    val salonId: SalonId,
)

/**
 * Query to get all salons with optional filtering
 */
data class GetAllSalonsQuery(
    val search: String? = null,
    val isActive: Boolean? = null,
    val limit: Int? = null,
    val offset: Int? = null,
)

/**
 * Query to get salons by user association
 */
data class GetSalonsByUserQuery(
    val userId: UserId,
    val isActive: Boolean? = null,
)

/**
 * Query to get salon details with client count
 */
data class GetSalonDetailsQuery(
    val salonId: SalonId,
    val requestingUserId: UserId,
)

/**
 * Query to get salons with client
 */
data class GetSalonsWithClientQuery(
    val clientId: ClientId,
)
