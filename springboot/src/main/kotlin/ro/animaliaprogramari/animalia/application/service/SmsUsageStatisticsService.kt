package ro.animaliaprogramari.animalia.application.service

import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.port.outbound.StripeSmsUsageRecordRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime
import java.time.YearMonth
import java.time.temporal.TemporalAdjusters

@Service
class SmsUsageStatisticsService(
    private val stripeSmsUsageRecordRepository: ro.animaliaprogramari.animalia.application.port.outbound.StripeSmsUsageRecordRepository
) {

    companion object {
        // SMS cost per unit in EUR (configurable)
        private val SMS_COST_PER_UNIT = BigDecimal("0.05") // 5 cents per SMS
    }

    /**
     * Get comprehensive SMS usage statistics for a salon
     */
    fun getUsageStatistics(salonId: SalonId): SmsUsageStatistics {
        val now = LocalDateTime.now()

        // Current month boundaries
        val currentMonthStart = now.with(TemporalAdjusters.firstDayOfMonth()).withHour(0).withMinute(0).withSecond(0).withNano(0)
        val currentMonthEnd = now.with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59).withNano(999999999)

        // Last month boundaries
        val lastMonth = YearMonth.from(now).minusMonths(1)
        val lastMonthStart = lastMonth.atDay(1).atStartOfDay()
        val lastMonthEnd = lastMonth.atEndOfMonth().atTime(23, 59, 59, 999999999)

        // Get SMS units (not just message counts)
        val currentMonthSms = calculateSmsUnitsForPeriod(salonId, currentMonthStart, currentMonthEnd)
        val lastMonthSms = calculateSmsUnitsForPeriod(salonId, lastMonthStart, lastMonthEnd)
        val totalSms = calculateTotalSmsUnits(salonId)

        // Calculate costs
        val currentMonthCost = calculateCost(currentMonthSms)
        val lastMonthCost = calculateCost(lastMonthSms)
        val totalCost = calculateCost(totalSms)

        // Estimate remaining month cost based on daily average
        val daysInCurrentMonth = currentMonthStart.toLocalDate().lengthOfMonth()
        val daysPassed = now.dayOfMonth
        val daysRemaining = daysInCurrentMonth - daysPassed

        val dailyAverage = if (daysPassed > 0) currentMonthSms.toDouble() / daysPassed else 0.0
        val estimatedRemainingMonthSms = (dailyAverage * daysRemaining).toLong()
        val estimatedTotalMonthSms = currentMonthSms + estimatedRemainingMonthSms
        val estimatedMonthCost = calculateCost(estimatedTotalMonthSms)

        return SmsUsageStatistics(
            salonId = salonId.value,
            currentMonthSms = currentMonthSms,
            lastMonthSms = lastMonthSms,
            totalSms = totalSms,
            currentMonthCost = formatCost(currentMonthCost),
            lastMonthCost = formatCost(lastMonthCost),
            totalCost = formatCost(totalCost),
            estimatedMonthCost = formatCost(estimatedMonthCost),
            costPerSms = formatCost(SMS_COST_PER_UNIT),
            currency = "EUR"
        )
    }

    /**
     * Get detailed monthly breakdown for the last N months
     */
    fun getMonthlyBreakdown(salonId: SalonId, months: Int = 12): List<MonthlyUsage> {
        val now = LocalDateTime.now()
        val breakdown = mutableListOf<MonthlyUsage>()

        for (i in 0 until months) {
            val month = YearMonth.from(now).minusMonths(i.toLong())
            val monthStart = month.atDay(1).atStartOfDay()
            val monthEnd = month.atEndOfMonth().atTime(23, 59, 59, 999999999)

            val smsCount = calculateSmsUnitsForPeriod(salonId, monthStart, monthEnd)

            val cost = calculateCost(smsCount)

            breakdown.add(
                MonthlyUsage(
                    year = month.year,
                    month = month.monthValue,
                    monthName = month.month.name,
                    smsCount = smsCount,
                    cost = formatCost(cost),
                    currency = "EUR"
                )
            )
        }

        return breakdown.reversed() // Return chronological order (oldest first)
    }

    /**
     * Calculate cost for given SMS count
     */
    private fun calculateCost(smsCount: Long): BigDecimal {
        return SMS_COST_PER_UNIT.multiply(BigDecimal(smsCount))
    }

    /**
     * Format cost to 2 decimal places
     */
    private fun formatCost(cost: BigDecimal): String {
        return cost.setScale(2, RoundingMode.HALF_UP).toString()
    }

    /**
     * Calculate SMS units for a specific time period from Stripe usage records
     */
    private fun calculateSmsUnitsForPeriod(salonId: SalonId, startDate: LocalDateTime, endDate: LocalDateTime): Long {
        val usageRecords = stripeSmsUsageRecordRepository.findBySalonIdAndReportedAtBetween(salonId, startDate, endDate)
        return usageRecords.sumOf { it.smsUnits.toLong() }
    }

    /**
     * Calculate total SMS units for a salon from Stripe usage records
     */
    private fun calculateTotalSmsUnits(salonId: SalonId): Long {
        val usageRecords = stripeSmsUsageRecordRepository.findBySalonId(salonId)
        return usageRecords.sumOf { it.smsUnits.toLong() }
    }
}

/**
 * SMS usage statistics data class
 */
data class SmsUsageStatistics(
    val salonId: String,
    val currentMonthSms: Long,
    val lastMonthSms: Long,
    val totalSms: Long,
    val currentMonthCost: String,
    val lastMonthCost: String,
    val totalCost: String,
    val estimatedMonthCost: String,
    val costPerSms: String,
    val currency: String
)

/**
 * Monthly usage breakdown data class
 */
data class MonthlyUsage(
    val year: Int,
    val month: Int,
    val monthName: String,
    val smsCount: Long,
    val cost: String,
    val currency: String
)
