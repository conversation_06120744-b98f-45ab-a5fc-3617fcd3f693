package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.sms.*
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.application.port.outbound.MassSmsRepository
import ro.animaliaprogramari.animalia.application.port.outbound.MassSmsTemplateRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.SmsQuotaService
import ro.animaliaprogramari.animalia.domain.service.SmsLengthCalculator
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsStatus
import ro.animaliaprogramari.animalia.domain.service.MessagingStrategyService
import java.time.LocalDateTime
import java.util.*

@Service
@Transactional
class MassSmsService(
    private val clientRepository: ClientRepository,
    private val petRepository: PetRepository,
    private val massSmsRepository: MassSmsRepository,
    private val massSmsTemplateRepository: MassSmsTemplateRepository,
    private val messageService: MessagingStrategyService,
    private val smsQuotaService: SmsQuotaService,
    private val smsLengthCalculator: SmsLengthCalculator,
    private val smsLogService: SmsLogService,
    private val stripeSmsBillingService: StripeSmsBillingService
) {
    private val logger = LoggerFactory.getLogger(MassSmsService::class.java)

    companion object {
        private const val BATCH_SIZE = 50 // Send SMS in batches to avoid rate limiting
        private const val MAX_RETRIES = 3
        private const val RETRY_DELAY_MS = 1000L
    }

    fun sendMassSms(
        salonId: SalonId,
        clientIds: List<String>,
        message: String,
        templateId: String? = null,
        scheduledAt: LocalDateTime? = null,
        userId: UserId
    ): MassSmsResponse {
        logger.info("Starting mass SMS campaign for salon: $salonId, clients: ${clientIds.size}")

        // Create campaign record
        val campaignId = UUID.randomUUID().toString()
        val campaign = MassSmsCampaign.create(
            id = campaignId,
            salonId = salonId,
            message = message,
            templateId = templateId,
            scheduledAt = scheduledAt,
            createdBy = userId
        )

        // Get valid clients with phone numbers
        val allClients = clientRepository.findBySalonId(salonId, isActive = true)
        val clients = allClients.filter { client ->
            clientIds.contains(client.id.value) &&
            client.phone != null &&
            client.phone.value.isNotBlank()
        }

        if (clients.isEmpty()) {
            logger.warn("No valid clients found for mass SMS campaign: $campaignId")
            return MassSmsResponse(
                campaignId = campaignId,
                totalRecipients = 0,
                successCount = 0,
                failureCount = 0,
                estimatedCost = 0.0,
                actualCost = 0.0,
                status = "FAILED",
                scheduledAt = scheduledAt,
                sentAt = null,
                failures = listOf(MassSmsFailure(
                    clientId = "",
                    clientName = "",
                    phoneNumber = "",
                    reason = "Nu s-au găsit clienți valizi cu numere de telefon",
                    errorCode = "NO_VALID_CLIENTS"
                ))
            )
        }

        // Calculate cost
        val smsSegments = smsLengthCalculator.calculateSmsUnits(message)
        val totalSmsUnits = clients.size * smsSegments
        val estimatedCost = calculateCost(totalSmsUnits)

        // Check SMS quota
        if (!smsQuotaService.canSendSms(salonId, totalSmsUnits)) {
            logger.warn("Insufficient SMS quota for campaign: $campaignId")
            return MassSmsResponse(
                campaignId = campaignId,
                totalRecipients = clients.size,
                successCount = 0,
                failureCount = clients.size,
                estimatedCost = estimatedCost,
                actualCost = 0.0,
                status = "FAILED",
                scheduledAt = scheduledAt,
                sentAt = null,
                failures = listOf(MassSmsFailure(
                    clientId = "",
                    clientName = "",
                    phoneNumber = "",
                    reason = "Cotă SMS insuficientă",
                    errorCode = "INSUFFICIENT_QUOTA"
                ))
            )
        }

        // Update campaign with client count
        val updatedCampaign = campaign.copy(
            totalRecipients = clients.size,
            estimatedCost = estimatedCost,
            status = if (scheduledAt != null) "SCHEDULED" else "SENDING"
        )
        massSmsRepository.save(updatedCampaign)

        // If scheduled, save and return
        if (scheduledAt != null && scheduledAt.isAfter(LocalDateTime.now())) {
            logger.info("Mass SMS campaign scheduled for: $scheduledAt")
            return MassSmsResponse(
                campaignId = campaignId,
                totalRecipients = clients.size,
                successCount = 0,
                failureCount = 0,
                estimatedCost = estimatedCost,
                actualCost = 0.0,
                status = "SCHEDULED",
                scheduledAt = scheduledAt,
                sentAt = null
            )
        }

        // Send immediately
        return sendSmsToClients(updatedCampaign, clients, message, smsSegments)
    }

    private fun sendSmsToClients(
        campaign: MassSmsCampaign,
        clients: List<Client>,
        message: String,
        smsSegments: Int
    ): MassSmsResponse {
        val failures = mutableListOf<MassSmsFailure>()
        var successCount = 0
        var actualCost = 0.0

        // Send in batches
        clients.chunked(BATCH_SIZE).forEach { batch ->
            batch.forEach { client ->
                try {
                    val phoneNumber = client.phone
                    if (phoneNumber == null) {
                        failures.add(MassSmsFailure(
                            clientId = client.id.value,
                            clientName = client.name,
                            phoneNumber = "",
                            reason = "Număr de telefon lipsă",
                            errorCode = "NO_PHONE"
                        ))
                        return@forEach
                    }

                    // Send SMS with error handling
                    try {
                        messageService.sendMessage(phoneNumber.value, message, campaign.salonId)

                        // If we get here, SMS was sent successfully
                        successCount++
                        actualCost += calculateCost(smsSegments)

                        // Log successful SMS
                        smsLogService.logSms(
                            salonId = campaign.salonId.value,
                            phoneNumber = client.phone!!.value,
                            messageContent = message,
                            messageType = SmsMessageType.MASS_SMS,
                            clientId = client.id.value,
                            clientName = client.name,
                            status = SmsStatus.SENT
                        )
                    } catch (e: Exception) {
                        logger.error("Failed to send SMS to client ${client.id.value}", e)
                        failures.add(MassSmsFailure(
                            clientId = client.id.value,
                            clientName = client.name,
                            phoneNumber = phoneNumber.value,
                            reason = e.message ?: "Eroare necunoscută",
                            errorCode = "SEND_ERROR"
                        ))

                        // Log failed SMS
                        smsLogService.logSms(
                            salonId = campaign.salonId.value,
                            phoneNumber = client.phone!!.value,
                            messageContent = message,
                            messageType = SmsMessageType.MASS_SMS,
                            clientId = client.id.value,
                            clientName = client.name,
                            status = SmsStatus.FAILED
                        )
                    }
                } catch (e: Exception) {
                    logger.error("Error sending SMS to client ${client.id}", e)
                    failures.add(MassSmsFailure(
                        clientId = client.id.value,
                        clientName = client.name,
                        phoneNumber = client.phone?.value ?: "",
                        reason = "Eroare tehnică: ${e.message}",
                        errorCode = "TECHNICAL_ERROR"
                    ))
                }
            }

            // Small delay between batches to avoid rate limiting
            if (batch.size == BATCH_SIZE) {
                Thread.sleep(100)
            }
        }

        // Update campaign with results
        val finalCampaign = campaign.copy(
            successCount = successCount,
            failureCount = failures.size,
            actualCost = actualCost,
            status = if (failures.isEmpty()) "COMPLETED" else "PARTIALLY_COMPLETED",
            sentAt = LocalDateTime.now()
        )
        massSmsRepository.save(finalCampaign)

        // Deduct SMS quota for successful sends
        if (successCount > 0) {
            smsQuotaService.recordMultipleSmsUnits(campaign.salonId, successCount * smsSegments)
        }

        // Process billing for successful SMS
        if (actualCost > 0) {
            try {
                stripeSmsBillingService.reportSmsUsage(campaign.salonId, successCount * smsSegments)
            } catch (e: Exception) {
                logger.error("Error processing SMS billing for campaign: ${campaign.id}", e)
            }
        }

        logger.info("Mass SMS campaign completed: ${campaign.id}. Success: $successCount, Failed: ${failures.size}")

        return MassSmsResponse(
            campaignId = campaign.id,
            totalRecipients = clients.size,
            successCount = successCount,
            failureCount = failures.size,
            estimatedCost = campaign.estimatedCost,
            actualCost = actualCost,
            status = finalCampaign.status,
            scheduledAt = campaign.scheduledAt,
            sentAt = finalCampaign.sentAt,
            failures = failures
        )
    }



    private fun calculateCost(smsUnits: Int): Double {
        // Base cost per SMS unit (can be configured)
        val costPerUnit = 0.05 // 5 bani per SMS unit
        return smsUnits * costPerUnit
    }

    fun previewMassSms(
        salonId: SalonId,
        clientIds: List<String>,
        message: String
    ): MassSmsPreviewResponse {
        logger.info("Previewing mass SMS for salon: $salonId, clients: ${clientIds.size}")

        // Get valid clients
        val allClients = clientRepository.findBySalonId(salonId, isActive = true)
        val clients = allClients.filter { client ->
            clientIds.contains(client.id.value) &&
            client.phone != null &&
            client.phone.value.isNotBlank()
        }

        // Get pets for each client
        val clientPets = if (clients.isNotEmpty()) {
            petRepository.findByClientIds(clients.map { client -> client.id })
                .groupBy { pet -> pet.clientId }
        } else {
            emptyMap()
        }

        // Calculate SMS segments and cost
        val smsSegments = smsLengthCalculator.calculateSmsUnits(message)
        val totalSmsUnits = clients.size * smsSegments
        val estimatedCost = calculateCost(totalSmsUnits)

        // Build recipient list
        val recipients = clients.map { client ->
            val pets = clientPets[client.id] ?: emptyList()
            MassSmsRecipient(
                clientId = client.id.value,
                clientName = client.name,
                phoneNumber = client.phone?.value ?: "",
                petNames = pets.map { it.name },
                lastVisit = null, // TODO: Get from appointments
                canReceiveSms = client.phone != null && client.isActive
            )
        }

        val costBreakdown = MassSmsCostBreakdown(
            basePrice = 0.05,
            smsSegments = smsSegments,
            pricePerSegment = 0.05,
            totalCost = estimatedCost
        )

        return MassSmsPreviewResponse(
            totalRecipients = clients.size,
            estimatedCost = estimatedCost,
            smsSegments = smsSegments,
            characterCount = message.length,
            recipients = recipients,
            costBreakdown = costBreakdown
        )
    }

    fun getMassSmsTemplates(salonId: SalonId): List<MassSmsTemplateResponse> {
        logger.info("Getting mass SMS templates for salon: $salonId")

        val templates = massSmsTemplateRepository.findBySalonIdOrDefault(salonId)

        return templates.map { template ->
            MassSmsTemplateResponse(
                id = template.id,
                name = template.name,
                content = template.content,
                category = template.category,
                isDefault = template.isDefault,
                usageCount = template.usageCount,
                createdAt = template.createdAt,
                updatedAt = template.updatedAt
            )
        }
    }

    fun saveMassSmsTemplate(
        salonId: SalonId,
        request: SaveMassSmsTemplateRequest,
        userId: UserId
    ): MassSmsTemplateResponse {
        logger.info("Saving mass SMS template for salon: $salonId")

        val template = MassSmsTemplate.create(
            salonId = salonId,
            name = request.name,
            content = request.content,
            category = request.category,
            createdBy = userId
        )

        val savedTemplate = massSmsTemplateRepository.save(template)

        return MassSmsTemplateResponse(
            id = savedTemplate.id,
            name = savedTemplate.name,
            content = savedTemplate.content,
            category = savedTemplate.category,
            isDefault = savedTemplate.isDefault,
            usageCount = savedTemplate.usageCount,
            createdAt = savedTemplate.createdAt,
            updatedAt = savedTemplate.updatedAt
        )
    }

    fun getMassSmsHistory(
        salonId: SalonId,
        page: Int,
        size: Int
    ): MassSmsHistoryResponse {
        logger.info("Getting mass SMS history for salon: $salonId")

        val pageable = PageRequest.of(page, size)
        val campaignsPage = massSmsRepository.findBySalonIdOrderByCreatedAtDesc(salonId, pageable)

        val historyItems = campaignsPage.content.map { campaign ->
            MassSmsHistoryItem(
                campaignId = campaign.id,
                message = campaign.message,
                totalRecipients = campaign.totalRecipients,
                successCount = campaign.successCount,
                failureCount = campaign.failureCount,
                cost = campaign.actualCost,
                status = campaign.status,
                createdBy = campaign.createdBy.value,
                createdAt = campaign.createdAt,
                sentAt = campaign.sentAt,
                templateName = campaign.templateId // TODO: Resolve template name
            )
        }

        return MassSmsHistoryResponse(
            campaigns = historyItems,
            totalPages = campaignsPage.totalPages,
            totalElements = campaignsPage.totalElements,
            currentPage = page,
            pageSize = size
        )
    }

    data class SmsResult(
        val success: Boolean,
        val errorMessage: String? = null,
        val errorCode: String? = null
    )
}
