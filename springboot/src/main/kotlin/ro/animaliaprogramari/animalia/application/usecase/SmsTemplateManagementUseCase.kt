package ro.animaliaprogramari.animalia.application.usecase

import ro.animaliaprogramari.animalia.application.command.CreateSmsTemplateCommand
import ro.animaliaprogramari.animalia.application.command.UpdateSmsTemplateCommand
import ro.animaliaprogramari.animalia.application.query.GetSmsTemplateQuery
import ro.animaliaprogramari.animalia.application.query.GetSmsTemplatesQuery
import ro.animaliaprogramari.animalia.domain.model.SmsTemplate

/**
 * Use case interface for SMS template management
 */
interface SmsTemplateManagementUseCase {
    
    /**
     * Get all SMS templates for a salon
     */
    fun getSmsTemplates(query: GetSmsTemplatesQuery): List<SmsTemplate>

    /**
     * Get a specific SMS template
     */
    fun getSmsTemplate(query: GetSmsTemplateQuery): SmsTemplate?

    /**
     * Create a new SMS template
     */
    fun createSmsTemplate(command: CreateSmsTemplateCommand): SmsTemplate

    /**
     * Update an existing SMS template
     */
    fun updateSmsTemplate(command: UpdateSmsTemplateCommand): SmsTemplate

    /**
     * Delete an SMS template
     */
    fun deleteSmsTemplate(templateId: String, salonId: String)

    /**
     * Reset templates to defaults for a salon
     */
    fun resetToDefaults(salonId: String): List<SmsTemplate>

    /**
     * Preview a template with sample data
     */
    fun previewTemplate(templateContent: String, sampleData: Map<String, String>): String
}
