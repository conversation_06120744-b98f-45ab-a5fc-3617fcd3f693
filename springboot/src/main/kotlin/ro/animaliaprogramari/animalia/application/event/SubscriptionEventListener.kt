package ro.animaliaprogramari.animalia.application.event

import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.event.DomainEvent
import ro.animaliaprogramari.animalia.domain.service.BlockedSmsCreditsService
import ro.animaliaprogramari.animalia.domain.model.SalonId
import java.time.LocalDateTime

/**
 * Event listener for subscription changes that might trigger SMS credits unlock
 */
@Component
class SubscriptionEventListener(
    private val blockedSmsCreditsService: BlockedSmsCreditsService
) {
    private val logger = LoggerFactory.getLogger(SubscriptionEventListener::class.java)

    /**
     * Handle subscription activation events
     */
    @Async
    @EventListener
    fun handleSubscriptionActivated(event: SubscriptionActivatedEvent) {
        try {
            logger.info("Handling subscription activation for salon: ${event.salonId}")

            val result = blockedSmsCreditsService.checkAndUnlockCredits(SalonId.of(event.salonId))

            if (result.isSuccess && result is BlockedSmsCreditsService.UnlockResult.Success) {
                logger.info(
                    "Unlocked ${result.totalCreditsUnlocked} SMS credits for salon: ${event.salonId} " +
                    "due to subscription activation"
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to process SMS credits unlock for subscription activation", e)
        }
    }

    /**
     * Handle trial end events
     */
    @Async
    @EventListener
    fun handleTrialEnded(event: TrialEndedEvent) {
        try {
            logger.info("Handling trial end for salon: ${event.salonId}")

            val result = blockedSmsCreditsService.checkAndUnlockCredits(SalonId.of(event.salonId))

            if (result.isSuccess && result is BlockedSmsCreditsService.UnlockResult.Success) {
                logger.info(
                    "Unlocked ${result.totalCreditsUnlocked} SMS credits for salon: ${event.salonId} " +
                    "due to trial end"
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to process SMS credits unlock for trial end", e)
        }
    }

    /**
     * Handle subscription tier change events
     */
    @Async
    @EventListener
    fun handleSubscriptionTierChanged(event: SubscriptionTierChangedEvent) {
        try {
            logger.info("Handling subscription tier change for salon: ${event.salonId}")

            val result = blockedSmsCreditsService.checkAndUnlockCredits(SalonId.of(event.salonId))

            if (result.isSuccess && result is BlockedSmsCreditsService.UnlockResult.Success) {
                logger.info(
                    "Unlocked ${result.totalCreditsUnlocked} SMS credits for salon: ${event.salonId} " +
                    "due to subscription tier change"
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to process SMS credits unlock for subscription tier change", e)
        }
    }
}

/**
 * Event fired when a subscription is activated
 */
data class SubscriptionActivatedEvent(
    override val eventId: String,
    override val occurredAt: LocalDateTime,
    override val aggregateId: String,
    val salonId: String,
    val subscriptionTier: String,
    val isTrialActive: Boolean
) : DomainEvent

/**
 * Event fired when a trial period ends
 */
data class TrialEndedEvent(
    override val eventId: String,
    override val occurredAt: LocalDateTime,
    override val aggregateId: String,
    val salonId: String,
    val subscriptionTier: String
) : DomainEvent

/**
 * Event fired when subscription tier changes
 */
data class SubscriptionTierChangedEvent(
    override val eventId: String,
    override val occurredAt: LocalDateTime,
    override val aggregateId: String,
    val salonId: String,
    val oldTier: String,
    val newTier: String,
    val isTrialActive: Boolean
) : DomainEvent
