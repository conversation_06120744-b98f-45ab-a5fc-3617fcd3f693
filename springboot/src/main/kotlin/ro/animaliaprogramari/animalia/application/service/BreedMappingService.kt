package ro.animaliaprogramari.animalia.application.service

import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository.BreedRepository

/**
 * Service for mapping pet breeds to sizes and categories
 * Now uses database instead of hardcoded constants
 */
@Service
class BreedMappingService(
    private val breedRepository: BreedRepository
) {

    /**
     * Get all known breeds from database
     */
    fun getAllBreeds(): Set<String> {
        return try {
            breedRepository.findByIsActiveTrue().map { it.name }.toSet()
        } catch (e: Exception) {
            // Fallback to empty set if database is not available
            emptySet()
        }
    }

    /**
     * Get all dog breeds from database
     */
    fun getDogBreeds(): Set<String> {
        return try {
            breedRepository.findAllDogBreeds().map { it.name }.toSet()
        } catch (e: Exception) {
            // Fallback to empty set if database is not available
            emptySet()
        }
    }

    /**
     * Get all cat breeds from database
     */
    fun getCatBreeds(): Set<String> {
        return try {
            breedRepository.findAllCatBreeds().map { it.name }.toSet()
        } catch (e: Exception) {
            // Fallback to empty set if database is not available
            emptySet()
        }
    }

    /**
     * Get all other animal breeds from database
     */
    fun getOtherBreeds(): Set<String> {
        return try {
            breedRepository.findAllOtherBreeds().map { it.name }.toSet()
        } catch (e: Exception) {
            // Fallback to empty set if database is not available
            emptySet()
        }
    }

    /**
     * Get the size category for a breed from database
     */
    fun getBreedSize(breed: String?): String {
        if (breed.isNullOrBlank()) return "M" // Default to Medium

        return try {
            val breeds = breedRepository.findByNameIgnoreCaseAndIsActiveTrue(breed)
            breeds.firstOrNull()?.size ?: "M"
        } catch (e: Exception) {
            "M" // Default to Medium if database is not available
        }
    }

    /**
     * Get the display name for a size
     */
    fun getSizeDisplayName(size: String): String {
        return when (size) {
            "S" -> "Mic"
            "M" -> "Mediu"
            "L" -> "Mare"
            else -> "Mediu"
        }
    }

    /**
     * Check if a breed is recognized in database
     */
    fun isKnownBreed(breed: String?): Boolean {
        if (breed.isNullOrBlank()) return false

        return try {
            breedRepository.findByNameIgnoreCaseAndIsActiveTrue(breed).isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Normalize breed name for better matching using database
     */
    fun normalizeBreedName(breed: String?): String? {
        if (breed.isNullOrBlank()) return null

        return try {
            // Try case-insensitive match in database
            val breeds = breedRepository.findByNameIgnoreCaseAndIsActiveTrue(breed)
            breeds.firstOrNull()?.name
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Search breeds by name containing text
     */
    fun searchBreeds(searchText: String): List<String> {
        if (searchText.isBlank()) return emptyList()

        return try {
            breedRepository.searchByNameContaining(searchText).map { it.name }
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * Search breeds by name for specific species
     */
    fun searchBreedsBySpecies(searchText: String, species: String): List<String> {
        if (searchText.isBlank()) return emptyList()

        return try {
            breedRepository.searchByNameContainingAndSpecies(searchText, species).map { it.name }
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * Get all available species
     */
    fun getAllSpecies(): List<String> {
        return try {
            breedRepository.findAllDistinctSpecies()
        } catch (e: Exception) {
            listOf("dog", "cat", "other") // Fallback
        }
    }
}
