package ro.animaliaprogramari.animalia.application.usecase

import jakarta.transaction.Transactional
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.command.CreateSmsTemplateCommand
import ro.animaliaprogramari.animalia.application.command.UpdateSmsTemplateCommand
import ro.animaliaprogramari.animalia.application.query.GetSmsTemplateQuery
import ro.animaliaprogramari.animalia.application.query.GetSmsTemplatesQuery
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsTemplate
import ro.animaliaprogramari.animalia.domain.model.SmsTemplateId
import ro.animaliaprogramari.animalia.domain.model.SmsTemplateType
import ro.animaliaprogramari.animalia.domain.port.outbound.SmsTemplateRepository

/**
 * Implementation of SMS template management use case
 */
@Service
@Transactional
class SmsTemplateManagementUseCaseImpl(
    private val smsTemplateRepository: SmsTemplateRepository,
) : SmsTemplateManagementUseCase {

    private val logger = LoggerFactory.getLogger(SmsTemplateManagementUseCaseImpl::class.java)

    override fun getSmsTemplates(query: GetSmsTemplatesQuery): List<SmsTemplate> {
        logger.info("Getting SMS templates for salon: ${query.salonId.value}, activeOnly: ${query.activeOnly}")

        val templates = if (query.activeOnly) {
            smsTemplateRepository.findActiveBySalonId(query.salonId)
        } else {
            smsTemplateRepository.findBySalonId(query.salonId)
        }

        // If no templates exist for this salon, create default ones
        if (templates.isEmpty()) {
            logger.info("No SMS templates found for salon: ${query.salonId.value}, creating defaults")
            return createDefaultTemplatesForSalon(query.salonId)
        }

        return templates
    }

    override fun getSmsTemplate(query: GetSmsTemplateQuery): SmsTemplate? {
        logger.info("Getting SMS template for salon: ${query.salonId.value}, type: ${query.templateType}")

        val template = smsTemplateRepository.findBySalonIdAndType(query.salonId, query.templateType)

        // If template doesn't exist, create a default one
        if (template == null) {
            logger.info("SMS template not found for salon: ${query.salonId.value}, type: ${query.templateType}, creating default")
            val defaultContent = SmsTemplate.getDefaultContent(query.templateType)
            val newTemplate = SmsTemplate.create(query.salonId, query.templateType, defaultContent)
            return smsTemplateRepository.save(newTemplate)
        }

        return template
    }

    override fun createSmsTemplate(command: CreateSmsTemplateCommand): SmsTemplate {
        logger.info("Creating SMS template for salon: ${command.salonId.value}, type: ${command.templateType}")

        // Check if template already exists
        val existingTemplate = smsTemplateRepository.findBySalonIdAndType(command.salonId, command.templateType)
        if (existingTemplate != null) {
            throw IllegalArgumentException("Template of type ${command.templateType} already exists for salon ${command.salonId.value}")
        }

        val template = SmsTemplate.create(
            salonId = command.salonId,
            templateType = command.templateType,
            templateContent = command.templateContent
        )

        return smsTemplateRepository.save(template)
    }

    override fun updateSmsTemplate(command: UpdateSmsTemplateCommand): SmsTemplate {
        logger.info("Updating SMS template: ${command.templateId}")

        val templateId = SmsTemplateId.of(command.templateId)
        val existingTemplate = smsTemplateRepository.findById(templateId)
            ?: throw IllegalArgumentException("Template not found: ${command.templateId}")

        // Verify salon ownership
        if (existingTemplate.salonId != command.salonId) {
            throw IllegalArgumentException("Template does not belong to salon: ${command.salonId.value}")
        }

        val updatedTemplate = existingTemplate
            .updateContent(command.templateContent)
            .setActive(command.isActive)

        return smsTemplateRepository.save(updatedTemplate)
    }

    override fun deleteSmsTemplate(templateId: String, salonId: String) {
        logger.info("Deleting SMS template: $templateId for salon: $salonId")

        val templateIdObj = SmsTemplateId.of(templateId)
        val existingTemplate = smsTemplateRepository.findById(templateIdObj)
            ?: throw IllegalArgumentException("Template not found: $templateId")

        // Verify salon ownership
        if (existingTemplate.salonId.value != salonId) {
            throw IllegalArgumentException("Template does not belong to salon: $salonId")
        }

        smsTemplateRepository.deleteById(templateIdObj)
    }

    override fun resetToDefaults(salonId: String): List<SmsTemplate> {
        logger.info("Resetting SMS templates to defaults for salon: $salonId")

        val salonIdObj = SalonId.of(salonId)
        val templates = mutableListOf<SmsTemplate>()

        // Create default templates for all types
        SmsTemplateType.entries.forEach { templateType ->
            val existingTemplate = smsTemplateRepository.findBySalonIdAndType(salonIdObj, templateType)

            val template = if (existingTemplate != null) {
                // Update existing template with default content
                existingTemplate.updateContent(SmsTemplate.getDefaultContent(templateType))
            } else {
                // Create new template with default content
                SmsTemplate.create(
                    salonId = salonIdObj,
                    templateType = templateType,
                    templateContent = SmsTemplate.getDefaultContent(templateType)
                )
            }

            templates.add(smsTemplateRepository.save(template))
        }

        return templates
    }

    override fun previewTemplate(templateContent: String, sampleData: Map<String, String>): String {
        logger.info("Previewing template with sample data")

        var preview = templateContent
        sampleData.forEach { (key, value) ->
            preview = preview.replace("{$key}", value)
        }

        return preview
    }

    /**
     * Create default SMS templates for a salon
     */
    private fun createDefaultTemplatesForSalon(salonId: SalonId): List<SmsTemplate> {
        logger.info("Creating default SMS templates for salon: ${salonId.value}")

        val defaultTemplates = SmsTemplateType.entries.map { templateType ->
            val defaultContent = SmsTemplate.getDefaultContent(templateType)
            SmsTemplate.create(salonId, templateType, defaultContent)
        }

        // Save all default templates
        val savedTemplates = defaultTemplates.map { template ->
            smsTemplateRepository.save(template)
        }

        logger.info("Created ${savedTemplates.size} default SMS templates for salon: ${salonId.value}")
        return savedTemplates
    }
}
