package ro.animaliaprogramari.animalia.application.service

import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.RecurringAppointmentPayment
import ro.animaliaprogramari.animalia.domain.model.PaymentModel
import ro.animaliaprogramari.animalia.infrastructure.persistence.entity.AppointmentSubscriptionEntity
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime
import java.util.*

/**
 * Service for handling recurring appointment payment calculations and processing
 */
@Service
class RecurringAppointmentPaymentService {

    /**
     * Calculate payment details for a recurring appointment subscription
     */
    fun calculatePaymentDetails(
        appointmentPrice: BigDecimal,
        numberOfAppointments: Int,
        paymentModel: PaymentModel,
        discountPercentage: BigDecimal? = null
    ): PaymentCalculationResult {

        val originalTotalAmount = appointmentPrice.multiply(BigDecimal(numberOfAppointments))

        return when (paymentModel) {
            PaymentModel.PER_APPOINTMENT -> {
                PaymentCalculationResult(
                    paymentModel = paymentModel,
                    appointmentPrice = appointmentPrice,
                    numberOfAppointments = numberOfAppointments,
                    originalTotalAmount = originalTotalAmount,
                    discountPercentage = null,
                    discountAmount = BigDecimal.ZERO,
                    finalPaymentAmount = appointmentPrice, // Per appointment
                    totalUpfrontAmount = null,
                    effectivePricePerAppointment = appointmentPrice
                )
            }

            PaymentModel.UPFRONT_WITH_DISCOUNT -> {
                val validDiscountPercentage = (discountPercentage ?: BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP)
                validateDiscountPercentage(validDiscountPercentage)

                val discountAmount = originalTotalAmount
                    .multiply(validDiscountPercentage)
                    .divide(BigDecimal(100), 2, RoundingMode.HALF_UP)

                val finalTotalAmount = originalTotalAmount.subtract(discountAmount)
                val effectivePricePerAppointment = finalTotalAmount
                    .divide(BigDecimal(numberOfAppointments), 2, RoundingMode.HALF_UP)

                PaymentCalculationResult(
                    paymentModel = paymentModel,
                    appointmentPrice = appointmentPrice,
                    numberOfAppointments = numberOfAppointments,
                    originalTotalAmount = originalTotalAmount,
                    discountPercentage = validDiscountPercentage,
                    discountAmount = discountAmount,
                    finalPaymentAmount = finalTotalAmount,
                    totalUpfrontAmount = finalTotalAmount,
                    effectivePricePerAppointment = effectivePricePerAppointment
                )
            }
        }
    }

    /**
     * Create a payment record for a recurring appointment subscription
     */
    fun createPaymentRecord(
        subscriptionId: String,
        salonId: String,
        clientId: String,
        calculationResult: PaymentCalculationResult,
        paymentMethod: String? = null,
        notes: String? = null
    ): RecurringAppointmentPayment {

        return RecurringAppointmentPayment(
            id = UUID.randomUUID().toString(),
            subscriptionId = subscriptionId,
            salonId = salonId,
            clientId = clientId,
            paymentModel = calculationResult.paymentModel,
            paymentAmount = calculationResult.finalPaymentAmount,
            originalTotalAmount = calculationResult.originalTotalAmount,
            discountPercentage = calculationResult.discountPercentage,
            discountAmount = calculationResult.discountAmount,
            numberOfAppointments = calculationResult.numberOfAppointments,
            appointmentPrice = calculationResult.appointmentPrice,
            paymentStatus = "PENDING",
            paymentMethod = paymentMethod,
            paymentReference = null,
            notes = notes,
            paidAt = null,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Update subscription entity with payment model details
     */
    fun updateSubscriptionWithPaymentDetails(
        subscription: AppointmentSubscriptionEntity,
        calculationResult: PaymentCalculationResult
    ): AppointmentSubscriptionEntity {

        return subscription.copy(
            paymentModel = calculationResult.paymentModel,
            discountPercentage = calculationResult.discountPercentage,
            totalUpfrontAmount = calculationResult.totalUpfrontAmount,
            appointmentPrice = calculationResult.appointmentPrice,
            updatedAt = LocalDateTime.now()
        )
    }

    /**
     * Validate discount percentage is within acceptable range
     */
    private fun validateDiscountPercentage(discountPercentage: BigDecimal) {
        if (discountPercentage < BigDecimal.ZERO || discountPercentage > BigDecimal(100)) {
            throw IllegalArgumentException("Discount percentage must be between 0 and 100, got: $discountPercentage")
        }
    }

    /**
     * Check if a subscription requires upfront payment
     */
    fun requiresUpfrontPayment(subscription: AppointmentSubscriptionEntity): Boolean {
        return subscription.paymentModel == PaymentModel.UPFRONT_WITH_DISCOUNT
    }

    /**
     * Calculate revenue recognition for upfront payments
     * Distributes the upfront payment across appointment dates for accounting
     */
    fun calculateRevenueRecognition(
        upfrontAmount: BigDecimal,
        appointmentDates: List<LocalDateTime>
    ): List<RevenueRecognitionEntry> {

        if (appointmentDates.isEmpty()) {
            return emptyList()
        }

        val amountPerAppointment = upfrontAmount
            .divide(BigDecimal(appointmentDates.size), 2, RoundingMode.HALF_UP)

        return appointmentDates.map { date ->
            RevenueRecognitionEntry(
                recognitionDate = date,
                amount = amountPerAppointment,
                description = "Revenue recognition for prepaid appointment"
            )
        }
    }
}

/**
 * Result of payment calculation
 */
data class PaymentCalculationResult(
    val paymentModel: PaymentModel,
    val appointmentPrice: BigDecimal,
    val numberOfAppointments: Int,
    val originalTotalAmount: BigDecimal,
    val discountPercentage: BigDecimal?,
    val discountAmount: BigDecimal,
    val finalPaymentAmount: BigDecimal,
    val totalUpfrontAmount: BigDecimal?,
    val effectivePricePerAppointment: BigDecimal
)

/**
 * Revenue recognition entry for accounting purposes
 */
data class RevenueRecognitionEntry(
    val recognitionDate: LocalDateTime,
    val amount: BigDecimal,
    val description: String
)
