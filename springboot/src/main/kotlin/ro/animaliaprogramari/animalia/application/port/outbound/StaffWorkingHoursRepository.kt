package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Repository port for staff working hours persistence operations
 * This is an outbound port that defines the persistence contract for staff working hours
 */
interface StaffWorkingHoursRepository {
    /**
     * Find working hours settings by staff ID and salon ID
     * Returns null if no working hours are configured for this staff member
     */
    fun findByStaffIdAndSalonId(
        staffId: StaffId,
        salonId: SalonId,
    ): StaffWorkingHoursSettings?

    /**
     * Save working hours settings
     */
    fun save(workingHours: StaffWorkingHoursSettings): StaffWorkingHoursSettings

    /**
     * Delete working hours settings by staff ID and salon ID
     */
    fun deleteByStaffIdAndSalonId(
        staffId: StaffId,
        salonId: SalonId,
    )

    /**
     * Check if working hours settings exist for staff
     */
    fun existsByStaffIdAndSalonId(
        staffId: StaffId,
        salonId: SalonId,
    ): Boolean

    /**
     * Find all staff working hours for a salon
     */
    fun findBySalonId(salonId: SalonId): List<StaffWorkingHoursSettings>

    /**
     * Find working hours settings by multiple staff IDs and salon ID
     * Returns list of working hours for the specified staff members
     */
    fun findByStaffIdsAndSalonId(
        staffIds: List<StaffId>,
        salonId: SalonId,
    ): List<StaffWorkingHoursSettings>
}
