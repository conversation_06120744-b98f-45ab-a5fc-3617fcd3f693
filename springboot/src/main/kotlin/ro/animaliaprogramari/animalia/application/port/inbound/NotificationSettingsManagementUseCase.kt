package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.UpdateNotificationSettingsCommand
import ro.animaliaprogramari.animalia.application.query.GetNotificationSettingsQuery
import ro.animaliaprogramari.animalia.domain.model.NotificationSettings

/**
 * Inbound port for notification settings management operations
 * This interface defines the business operations available for notification settings management
 */
interface NotificationSettingsManagementUseCase {
    /**
     * Get notification settings for a salon
     * Creates default settings if none exist
     */
    fun getNotificationSettings(query: GetNotificationSettingsQuery): NotificationSettings

    /**
     * Update notification settings for a salon
     */
    fun updateNotificationSettings(command: UpdateNotificationSettingsCommand): NotificationSettings
}
