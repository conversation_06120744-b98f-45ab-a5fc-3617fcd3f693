package ro.animaliaprogramari.animalia.application.service

import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime

/**
 * Service for calculating real client statistics
 */
@Service
class ClientStatisticsService(
    private val appointmentRepository: AppointmentRepository,
    private val petRepository: PetRepository,
) {

    /**
     * Calculate real statistics for a client in a salon
     */
    fun calculateClientStatistics(
        clientId: ClientId,
        salonId: SalonId,
    ): ClientStatistics {
        // Get all appointments for this client in this salon
        val allAppointments = appointmentRepository.findBySalonIdWithFilters(
            salonId = salonId,
            clientId = clientId
        )

        // Filter appointments by status
        val completedAppointments = allAppointments.filter { it.status == AppointmentStatus.COMPLETED }
        val cancelledAppointments = allAppointments.filter { it.status == AppointmentStatus.CANCELLED }
        val noShowAppointments = allAppointments.filter { it.status == AppointmentStatus.NO_SHOW }
        val upcomingAppointments = allAppointments.filter { 
            it.status in listOf(AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED) &&
            it.appointmentDate.atTime(it.startTime).isAfter(LocalDateTime.now())
        }

        // Calculate revenue from completed appointments
        val totalRevenue = completedAppointments.sumOf { it.totalPrice.amount }

        // Calculate average appointment value
        val averageAppointmentValue = if (completedAppointments.isNotEmpty()) {
            totalRevenue.divide(BigDecimal(completedAppointments.size), 2, RoundingMode.HALF_UP)
        } else {
            BigDecimal.ZERO
        }

        // Find last visit date
        val lastVisitDate = completedAppointments
            .maxByOrNull { it.appointmentDate.atTime(it.startTime) }
            ?.let { it.appointmentDate.atTime(it.startTime) }

        // Get total pets for this client
        val totalPets = try {
            petRepository.findByClientId(clientId).size
        } catch (e: Exception) {
            0
        }

        // Calculate loyalty score based on various factors
        val loyaltyScore = calculateLoyaltyScore(
            completedAppointments = completedAppointments.size,
            totalRevenue = totalRevenue,
            cancelledAppointments = cancelledAppointments.size,
            noShowAppointments = noShowAppointments.size
        )

        // Get favorite services (most frequently used services)
        val favoriteServices = calculateFavoriteServices(completedAppointments)

        return ClientStatistics(
            clientId = clientId,
            salonId = salonId,
            totalAppointments = allAppointments.size,
            completedAppointments = completedAppointments.size,
            cancelledAppointments = cancelledAppointments.size,
            noShowAppointments = noShowAppointments.size,
            upcomingAppointments = upcomingAppointments.size,
            lastVisitDate = lastVisitDate,
            totalRevenue = totalRevenue,
            averageAppointmentValue = averageAppointmentValue,
            totalPets = totalPets,
            loyaltyScore = loyaltyScore,
            favoriteServices = favoriteServices,
            averageRating = null, // TODO: Implement when reviews are available
            totalReviews = 0, // TODO: Implement when reviews are available
            activeSubscriptions = 0, // TODO: Implement when subscriptions are available
        )
    }

    /**
     * Calculate loyalty score based on client behavior
     * Score is from 0.0 to 100.0
     */
    private fun calculateLoyaltyScore(
        completedAppointments: Int,
        totalRevenue: BigDecimal,
        cancelledAppointments: Int,
        noShowAppointments: Int
    ): BigDecimal {
        var score = BigDecimal.ZERO

        // Base score from completed appointments (up to 40 points)
        val appointmentScore = minOf(completedAppointments * 2, 40)
        score = score.add(BigDecimal(appointmentScore))

        // Revenue score (up to 30 points)
        val revenueScore = when {
            totalRevenue >= BigDecimal("1000") -> 30
            totalRevenue >= BigDecimal("500") -> 20
            totalRevenue >= BigDecimal("200") -> 10
            totalRevenue > BigDecimal.ZERO -> 5
            else -> 0
        }
        score = score.add(BigDecimal(revenueScore))

        // Reliability score (up to 30 points)
        val totalAppointments = completedAppointments + cancelledAppointments + noShowAppointments
        val reliabilityScore = if (totalAppointments > 0) {
            val reliabilityRate = completedAppointments.toDouble() / totalAppointments
            (reliabilityRate * 30).toInt()
        } else {
            0
        }
        score = score.add(BigDecimal(reliabilityScore))

        // Ensure score is between 0 and 100
        return score.min(BigDecimal("100.0")).max(BigDecimal.ZERO)
    }

    /**
     * Calculate favorite services based on appointment history
     */
    private fun calculateFavoriteServices(completedAppointments: List<Appointment>): List<String> {
        // Count service occurrences
        val serviceCount = mutableMapOf<String, Int>()
        
        completedAppointments.forEach { appointment ->
            appointment.serviceIds.forEach { serviceId ->
                // For now, use service ID as service name
                // TODO: Map service IDs to actual service names when service repository is available
                val serviceName = serviceId.value
                serviceCount[serviceName] = serviceCount.getOrDefault(serviceName, 0) + 1
            }
        }

        // Return top 3 most used services
        return serviceCount.entries
            .sortedByDescending { it.value }
            .take(3)
            .map { it.key }
    }
}
