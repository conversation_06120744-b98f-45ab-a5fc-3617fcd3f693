package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.referral.SalonReferralCode
import ro.animaliaprogramari.animalia.domain.model.referral.SalonReferralCodeId

/**
 * Outbound port for salon referral code persistence
 */
interface SalonReferralCodeRepository {
    /**
     * Save a salon referral code
     */
    fun save(salonReferralCode: SalonReferralCode): SalonReferralCode

    /**
     * Find a salon referral code by ID
     */
    fun findById(id: SalonReferralCodeId): SalonReferralCode?

    /**
     * Find a salon referral code by salon ID
     */
    fun findBySalonId(salonId: SalonId): SalonReferralCode?

    /**
     * Find a salon referral code by code string
     */
    fun findByCode(code: String): SalonReferralCode?

    /**
     * Check if a code already exists
     */
    fun existsByCode(code: String): Boolean

    /**
     * Check if a salon already has a referral code
     */
    fun existsBySalonId(salonId: SalonId): <PERSON><PERSON><PERSON>


}
