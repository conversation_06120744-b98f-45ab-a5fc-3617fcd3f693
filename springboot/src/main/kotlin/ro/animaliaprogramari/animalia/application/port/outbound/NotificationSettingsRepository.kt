package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.NotificationSettings
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId

/**
 * Outbound port for notification settings persistence
 * This interface defines the contract for notification settings data access
 */
interface NotificationSettingsRepository {
    /**
     * Save notification settings
     */
    fun save(settings: NotificationSettings): NotificationSettings

    /**
     * Find notification settings by user ID and salon ID
     */
    fun findByUserIdAndSalonId(userId: UserId, salonId: SalonId): NotificationSettings?

    /**
     * Check if notification settings exist for a user in a salon
     */
    fun existsByUserIdAndSalonId(userId: UserId, salonId: SalonId): Boolean

    /**
     * Delete notification settings by user ID and salon ID
     */
    fun deleteByUserIdAndSalonId(userId: UserId, salonId: SalonId)

    /**
     * Find all notification settings for a salon (for admin purposes)
     */
    fun findBySalonId(salonId: SalonId): List<NotificationSettings>
}
