package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.FeatureFlag
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository.FeatureFlagRepository

/**
 * Service for managing feature flags
 * Provides caching for better performance
 */
@Service
@Transactional(readOnly = true)
class FeatureFlagService(
    private val featureFlagRepository: FeatureFlagRepository
) {
    private val logger = LoggerFactory.getLogger(FeatureFlagService::class.java)

    /**
     * Get all feature flags as a map
     */
    @Cacheable("featureFlags")
    fun getAllFeatureFlags(): Map<String, Boolean> {
        return try {
            val flags = featureFlagRepository.findAllAsMap()
            flags.associate {
                it[0] as String to it[1] as <PERSON>ole<PERSON>
            }.also {
                logger.info("Retrieved {} feature flags from database", it.size)
            }
        } catch (e: Exception) {
            logger.error("Error retrieving feature flags from database", e)
            getDefaultFeatureFlags()
        }
    }

    /**
     * Get feature flag value by name
     */
    @Cacheable("featureFlags", key = "#flagName")
    fun isFeatureEnabled(flagName: String): Boolean {
        return try {
            featureFlagRepository.findValueByFlagName(flagName)
                .orElse(getDefaultValue(flagName))
                .also {
                    logger.info("Feature flag '{}' is {}", flagName, if (it) "enabled" else "disabled")
                }
        } catch (e: Exception) {
            logger.error("Error retrieving feature flag '{}' from database", flagName, e)
            getDefaultValue(flagName)
        }
    }

    /**
     * Update feature flag value
     */
    @Transactional
    @CacheEvict("featureFlags", allEntries = true)
    fun updateFeatureFlag(flagName: String, flagValue: Boolean): Boolean {
        return try {
            val existingFlag = featureFlagRepository.findByFlagName(flagName)

            if (existingFlag.isPresent) {
                val flag = existingFlag.get()
                flag.updateValue(flagValue)
                featureFlagRepository.save(flag)
                logger.info("Updated feature flag '{}' to {}", flagName, flagValue)
                true
            } else {
                // Create new flag if it doesn't exist
                val newFlag = FeatureFlag(
                    flagName = flagName,
                    flagValue = flagValue,
                    description = "Dynamically created feature flag"
                )
                featureFlagRepository.save(newFlag)
                logger.info("Created new feature flag '{}' with value {}", flagName, flagValue)
                true
            }
        } catch (e: Exception) {
            logger.error("Error updating feature flag '{}' to {}", flagName, flagValue, e)
            false
        }
    }

    /**
     * Clear feature flags cache
     */
    @CacheEvict("featureFlags", allEntries = true)
    fun clearCache() {
        logger.info("Feature flags cache cleared")
    }

    /**
     * Get default feature flags (fallback when database is unavailable)
     */
    private fun getDefaultFeatureFlags(): Map<String, Boolean> {
        return mapOf(
            "monthly_view_enabled" to false,
            "theme_selection_enabled" to true,
            "translations_enabled" to false,
            "appointment_confirmations" to true,
            "reminder" to true,
            "sms_notifications" to false,
            "push_notifications" to true,
            "google_places_autocomplete" to true,
            "phone_verification_required" to false,
            "multi_salon_support" to true,
            "showcase_enabled" to false,
            "whatsapp_enabled" to false
        )
    }

    /**
     * Get default value for a specific flag
     */
    private fun getDefaultValue(flagName: String): Boolean {
        return getDefaultFeatureFlags()[flagName] ?: false
    }
}
