package ro.animaliaprogramari.animalia.application.service

import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsLog
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsStatus
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository.SmsLogRepository
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.*

@Service
class SmsLogService(
    private val smsLogRepository: SmsLogRepository
) {

    /**
     * Log an SMS message
     */
    fun logSms(
        salonId: String,
        phoneNumber: String,
        messageContent: String,
        messageType: SmsMessageType,
        appointmentId: String? = null,
        clientId: String? = null,
        clientName: String? = null,
        petName: String? = null,
        appointmentDate: LocalDate? = null,
        appointmentTime: LocalTime? = null,
        status: SmsStatus = SmsStatus.SENT
    ): SmsLog {
        val smsLog = SmsLog(
            id = UUID.randomUUID().toString(),
            salonId = salonId,
            appointmentId = appointmentId,
            clientId = clientId,
            phoneNumber = phoneNumber,
            messageContent = messageContent,
            messageType = messageType,
            clientName = clientName,
            petName = petName,
            appointmentDate = appointmentDate,
            appointmentTime = appointmentTime,
            status = status,
            sentAt = LocalDateTime.now(),
            createdAt = LocalDateTime.now()
        )

        return smsLogRepository.save(smsLog)
    }

    /**
     * Get SMS logs for a salon with pagination
     */
    fun getSmsLogs(salonId: String, page: Int = 0, size: Int = 10): Page<SmsLog> {
        val pageable: Pageable = PageRequest.of(page, size)
        return smsLogRepository.findBySalonIdOrderBySentAtDesc(salonId, pageable)
    }

    /**
     * Get SMS logs by message type
     */
    fun getSmsLogsByType(
        salonId: String,
        messageType: SmsMessageType,
        page: Int = 0,
        size: Int = 10
    ): Page<SmsLog> {
        val pageable: Pageable = PageRequest.of(page, size)
        return smsLogRepository.findBySalonIdAndMessageTypeOrderBySentAtDesc(salonId, messageType, pageable)
    }

    /**
     * Get SMS logs by status
     */
    fun getSmsLogsByStatus(
        salonId: String,
        status: SmsStatus,
        page: Int = 0,
        size: Int = 10
    ): Page<SmsLog> {
        val pageable: Pageable = PageRequest.of(page, size)
        return smsLogRepository.findBySalonIdAndStatusOrderBySentAtDesc(salonId, status, pageable)
    }

    /**
     * Get SMS logs within date range
     */
    fun getSmsLogsInDateRange(
        salonId: String,
        startDate: LocalDateTime,
        endDate: LocalDateTime,
        page: Int = 0,
        size: Int = 10
    ): Page<SmsLog> {
        val pageable: Pageable = PageRequest.of(page, size)
        return smsLogRepository.findBySalonIdAndSentAtBetween(salonId, startDate, endDate, pageable)
    }

    /**
     * Get SMS logs for a specific appointment
     */
    fun getSmsLogsForAppointment(appointmentId: String): List<SmsLog> {
        return smsLogRepository.findByAppointmentIdOrderBySentAtDesc(appointmentId)
    }

    /**
     * Get SMS logs for a specific client
     */
    fun getSmsLogsForClient(clientId: String, page: Int = 0, size: Int = 10): Page<SmsLog> {
        val pageable: Pageable = PageRequest.of(page, size)
        return smsLogRepository.findByClientIdOrderBySentAtDesc(clientId, pageable)
    }

    /**
     * Get SMS statistics for a salon
     */
    fun getSmsStatistics(salonId: String): SmsStatistics {
        val totalSms = smsLogRepository.countBySalonId(salonId)
        val sentSms = smsLogRepository.countBySalonIdAndStatus(salonId, SmsStatus.SENT)
        val failedSms = smsLogRepository.countBySalonIdAndStatus(salonId, SmsStatus.FAILED)
        val pendingSms = smsLogRepository.countBySalonIdAndStatus(salonId, SmsStatus.PENDING)

        val confirmationSms = smsLogRepository.countBySalonIdAndMessageType(salonId, SmsMessageType.APPOINTMENT_CONFIRMATION)
        val reminderSms = smsLogRepository.countBySalonIdAndMessageType(salonId, SmsMessageType.REMINDER)
        val followUpSms = smsLogRepository.countBySalonIdAndMessageType(salonId, SmsMessageType.FOLLOW_UP)
        val APPOINTMENTCompletionSms = smsLogRepository.countBySalonIdAndMessageType(salonId, SmsMessageType.APPOINTMENT_COMPLETION)

        return SmsStatistics(
            totalSms = totalSms,
            sentSms = sentSms,
            failedSms = failedSms,
            pendingSms = pendingSms,
            confirmationSms = confirmationSms,
            reminderSms = reminderSms,
            followUpSms = followUpSms,
            completionSms = APPOINTMENTCompletionSms
        )
    }
}

data class SmsStatistics(
    val totalSms: Long,
    val sentSms: Long,
    val failedSms: Long,
    val pendingSms: Long,
    val confirmationSms: Long,
    val reminderSms: Long,
    val followUpSms: Long,
    val completionSms: Long
)
