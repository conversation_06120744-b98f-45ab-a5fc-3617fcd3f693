package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsMessageType
import ro.animaliaprogramari.animalia.domain.service.MessagingService

/**
 * Outbound port for sending messages via different channels (SMS, WhatsApp, etc.)
 * This interface serves as the abstraction for all messaging providers.
 */
interface SmsSender {
    /**
     * Send a message to the specified phone number.
     * @param to Phone number in E.164 format (e.g., +40712345678)
     * @param message The message content to send
     * @param messageType Type of message being sent (e.g., APPOINTMENT_CONFIRMATION, REMINDER)
     * @return MessageSendResult containing information about what was sent
     */
    fun sendMessage(
        to: String,
        message: String,
        messageType: SmsMessageType = SmsMessageType.CUSTOM,
        messageContext: MessagingService.MessageContext? = null,
    ): MessageSendResult

    /**
     * Get the type of messaging channel this sender uses.
     * @return The messaging channel type (SMS or WhatsApp)
     */
    fun getChannelType(): MessagingChannelType
}

/**
 * Result of sending a message, containing information about what was actually sent
 */
data class MessageSendResult(
    val channelType: MessagingChannelType,
    val templateName: String? = null,
    val templateId: String? = null,
    val success: Boolean = true
)

/**
 * Enum representing different messaging channels.
 */
enum class MessagingChannelType {
    SMS,
    WHATSAPP
}
