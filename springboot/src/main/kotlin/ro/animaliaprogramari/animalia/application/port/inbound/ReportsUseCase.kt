package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Use case interface for generating business reports
 */
interface ReportsUseCase {
    
    /**
     * Generate pet distribution report showing size and breed breakdown
     */
    fun getPetReport(query: GetPetReportQuery): PetReportData
    
    /**
     * Generate service performance report showing requests and revenue
     */
    fun getServiceReport(query: GetServiceReportQuery): ServiceReportData
    
    /**
     * Generate client analytics report showing top clients and metrics
     */
    fun getClientReport(query: GetClientReportQuery): ClientReportData
    
    /**
     * Generate staff performance report for individual staff member
     */
    fun getStaffPerformanceReport(query: GetStaffPerformanceReportQuery): StaffPerformanceReportData
    
    /**
     * Generate revenue analytics report showing trends and breakdown
     */
    fun getRevenueReport(query: GetRevenueReportQuery): RevenueReportData
}
