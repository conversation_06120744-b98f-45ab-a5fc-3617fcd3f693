package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek

/**
 * Command for updating staff working hours settings
 */
data class UpdateStaffWorkingHoursCommand(
    val staffId: StaffId,
    val salonId: SalonId,
    val updaterUserId: UserId,
    val weeklySchedule: Map<DayOfWeek, DaySchedule>,
    val holidays: List<StaffHoliday>,
    val customClosures: List<StaffCustomClosure>,
    val inheritFromBusiness: Boolean = true, // New field for inheritance control
)

