package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Query to get salon subscription
 */
data class GetSalonSubscriptionQuery(
    val salonId: SalonId,
    val userId: UserId,
)

/**
 * Query to get user subscription (applies to all their salons)
 */
data class GetUserSubscriptionQuery(
    val userId: UserId,
)

/**
 * Query to get salon subscription history
 */
data class GetSalonSubscriptionHistoryQuery(
    val salonId: SalonId,
    val userId: UserId,
)

/**
 * Query to check feature access
 */
data class CheckFeatureAccessQuery(
    val salonId: SalonId,
    val userId: UserId,
    val feature: String,
)

/**
 * Query to get subscription limits
 */
data class GetSubscriptionLimitsQuery(
    val salonId: SalonId,
    val userId: UserId,
)

/**
 * Query to get subscription analytics
 */
data class GetSubscriptionAnalyticsQuery(
    val userId: UserId,
    val salonId: SalonId? = null,
    val startDate: java.time.LocalDateTime? = null,
    val endDate: java.time.LocalDateTime? = null,
)

/**
 * Query to check if action is allowed based on subscription limits
 */
data class CheckLimitQuery(
    val salonId: SalonId,
    val userId: UserId,
    val limitType: String, // 'staff', 'clients', 'sms'
)

/**
 * Query to get user's highest subscription tier across all their salons
 */
data class GetUserHighestTierQuery(
    val userId: UserId
)

/**
 * Query to check if user can create salons based on their highest subscription tier
 */
data class CanUserCreateSalonsQuery(
    val userId: UserId
)
