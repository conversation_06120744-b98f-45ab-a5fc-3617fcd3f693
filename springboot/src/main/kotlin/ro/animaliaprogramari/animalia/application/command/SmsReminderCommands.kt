package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.application.port.outbound.MessagingChannelType
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.UserId

/**
 * Command to update SMS reminder settings for a salon
 */
data class UpdateSmsReminderSettingsCommand(
    val salonId: SalonId,
    val enabled: <PERSON><PERSON>an,
    val appointmentConfirmations: <PERSON><PERSON><PERSON>,
    val rescheduleNotifications: <PERSON>olean,
    val cancellationNotifications: <PERSON><PERSON>an,
    val completionMessages: <PERSON><PERSON>an,
    val followUpMessages: <PERSON><PERSON>an,
    val birthdayMessages: <PERSON><PERSON>an,
    val messagingChannel: MessagingChannelType,
    val updaterUserId: UserId,
)
