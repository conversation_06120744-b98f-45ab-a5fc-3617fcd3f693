package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate

data class GetSalonAppointmentsQuery(
    val salonId: SalonId,
    val date: LocalDate? = null,
    val startDate: LocalDate? = null,
    val endDate: LocalDate? = null,
    val status: AppointmentStatus? = null,
    val clientId: ClientId? = null,
    val staffId: StaffId? = null,
    val requesterId: UserId,
)
