package ro.animaliaprogramari.animalia.application.usecase

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal

/**
 * Use case interface for managing appointment subscriptions
 */
interface AppointmentSubscriptionUseCase {

    /**
     * Create a new appointment subscription from a recurring appointment
     */
    fun createSubscription(
        appointment: Appointment,
        frequency: Int,
        period: RecurrencePeriod,
        totalRepetitions: Int,
        paymentModel: PaymentModel = PaymentModel.PER_APPOINTMENT,
        discountPercentage: BigDecimal? = null
    ): AppointmentSubscription

    /**
     * Update an existing appointment subscription
     */
    fun updateSubscription(command: UpdateAppointmentSubscriptionCommand): AppointmentSubscription

    /**
     * Update the remaining repetitions count for a subscription
     */
    fun updateRemainingRepetitions(subscriptionId: AppointmentSubscriptionId, remainingRepetitions: Int): AppointmentSubscription

    /**
     * Cancel an appointment subscription and optionally cancel future appointments
     */
    fun cancelSubscription(command: CancelAppointmentSubscriptionCommand): AppointmentSubscription

    /**
     * Pause an appointment subscription
     */
    fun pauseSubscription(command: PauseAppointmentSubscriptionCommand): AppointmentSubscription

    /**
     * Resume a paused appointment subscription
     */
    fun resumeSubscription(command: ResumeAppointmentSubscriptionCommand): AppointmentSubscription

    /**
     * Get all active subscriptions for a client
     */
    fun getClientSubscriptions(clientId: ClientId, salonId: SalonId): List<AppointmentSubscription>

    /**
     * Get subscription by ID
     */
    fun getSubscription(subscriptionId: AppointmentSubscriptionId, salonId: SalonId): AppointmentSubscription?

    /**
     * Process subscription to create next appointment
     */
    fun processSubscriptionForNextAppointment(
        subscriptionId: AppointmentSubscriptionId,
        lastAppointmentDate: java.time.LocalDate
    ): Appointment?

    /**
     * Get appointments that will be affected by subscription modification
     */
    fun getAffectedAppointments(
        subscriptionId: AppointmentSubscriptionId,
        modificationType: SubscriptionModificationType
    ): List<Appointment>

    /**
     * Get all appointments for a subscription
     */
    fun getSubscriptionAppointments(
        subscriptionId: AppointmentSubscriptionId,
        salonId: SalonId
    ): List<Appointment>

    /**
     * Get subscription by appointment ID
     */
    fun getSubscriptionByAppointmentId(
        appointmentId: AppointmentId,
        salonId: SalonId
    ): AppointmentSubscription?
}

/**
 * Types of subscription modifications
 */
enum class SubscriptionModificationType {
    TIME_CHANGE,
    REPETITION_REDUCTION,
    SERVICE_CHANGE,
    CANCELLATION
}
