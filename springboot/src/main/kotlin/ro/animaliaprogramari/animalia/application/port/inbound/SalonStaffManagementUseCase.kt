package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Use case interface for salon staff management operations
 * This is a port in hexagonal architecture for managing staff within salons
 */
interface SalonStaffManagementUseCase {
    /**
     * Get all staff for a salon with optional filtering
     */
    fun getSalonStaff(
        salonId: SalonId,
        activeOnly: Boolean = true,
        search: String? = null,
    ): List<SalonStaffInfo>

    /**
     * Get pending invitations for a salon
     */
    fun getSalonPendingInvitations(
        salonId: SalonId,
        search: String? = null,
    ): List<Pair<SalonInvitation, String>>

    /**
     * Add an existing user as staff to the salon
     */
    fun addUserToSalon(
        salonId: SalonId,
        userId: UserId,
        role: StaffRole,
        permissions: StaffPermissions = StaffPermissions.defaultGroomerAccess(),
    ): Staff

    /**
     * Create staff directly without user account
     */
    fun createStaffDirectly(
        salonId: SalonId,
        nickname: String,
        role: StaffRole,
        permissions: StaffPermissions = StaffPermissions.defaultGroomerAccess(),
        notes: String? = null,
    ): Staff

    /**
     * Invite someone by phone number to join the salon as staff
     */
    fun inviteStaffByPhone(
        salonId: SalonId,
        inviterUserId: UserId,
        phoneNumber: String,
        role: StaffRole,
        permissions: StaffPermissions = StaffPermissions.defaultGroomerAccess(),
        message: String? = null,
    ): SalonInvitation

    /**
     * Update staff role and permissions within a salon
     */
    fun updateStaffRole(
        salonId: SalonId,
        staffId: StaffId,
        role: StaffRole,
        permissions: StaffPermissions,
        nickName: String?,
    ): Staff

    /**
     * Toggle staff status (active/inactive) within a salon
     */
    fun toggleStaffStatus(
        salonId: SalonId,
        userId: UserId,
    ): Staff

    /**
     * Toggle staff status (active/inactive) within a salon by staff ID
     */
    fun toggleStaffStatusByStaffId(
        salonId: SalonId,
        staffId: StaffId,
    ): Staff

    /**
     * Remove staff from salon (soft delete)
     */
    fun removeStaffFromSalon(
        staffId: StaffId,
    )

    /**
     * Get staff details within a salon context
     */
    fun getSalonStaffDetails(
        salonId: SalonId,
        userId: UserId,
    ): SalonStaffInfo?

    /**
     * Update pending invitation details (role, permissions, etc.)
     */
    fun updatePendingInvitation(
        salonId: SalonId,
        invitationId: InvitationId,
        role: StaffRole,
        permissions: StaffPermissions,
    ): SalonInvitation

    /**
     * Cancel pending invitation from salon staff management context
     */
    fun cancelPendingInvitation(
        salonId: SalonId,
        invitationId: InvitationId,
        cancellingUserId: UserId,
    ): Boolean
}
