package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CustomServiceData
import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

/**
 * Command to schedule a new appointment
 */
data class ScheduleAppointmentCommand(
    val salonId: SalonId,
    val clientId: ClientId ,
    val clientName: String?,
    val clientPhone: PhoneNumber?,
    val petId: PetId?, // Made optional - will be generated if null
    val petName: String?,
    val petSpecies: String?,
    val petBreed: String?,
    val petSize: String?,
    val staffId: StaffId, // Changed from userId to staffId
    val appointmentDate: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val serviceIds: List<ServiceId>,
    val customServices: Map<ServiceId, CustomServiceData>? = null,
    val notes: String? = null,
    // Enhanced recurrence options
    val recurrenceFrequency: Int? = null, // 1-99
    val recurrencePeriod: RecurrencePeriod? = null, // DAYS, WEEKS, MONTHS
    val totalRepetitions: Int? = null, // 1-24
    // Payment model options for recurring appointments
    val paymentModel: PaymentModel? = null, // PER_APPOINTMENT, UPFRONT_WITH_DISCOUNT
    val discountPercentage: BigDecimal? = null, // 0-100 for upfront payments
    val isNewClient: Boolean = false,
    val isNewPet: Boolean = false,
) {
    init {
        // Validate recurrence parameters if provided
        if (recurrenceFrequency != null || recurrencePeriod != null || totalRepetitions != null) {
            require(recurrenceFrequency != null && recurrencePeriod != null && totalRepetitions != null) {
                "All recurrence parameters must be provided together"
            }
            require(recurrenceFrequency in 1..99) { "Recurrence frequency must be between 1 and 99" }
            require(totalRepetitions in 1..24) { "Total repetitions must be between 1 and 24" }
        }
    }

    /**
     * Check if this appointment should create a subscription
     */
    fun hasRecurrence(): Boolean {
        return recurrenceFrequency != null && recurrencePeriod != null && totalRepetitions != null
    }
}

/**
 * Command to update an appointment subscription
 */
data class UpdateAppointmentSubscriptionCommand(
    val subscriptionId: AppointmentSubscriptionId,
    val salonId: SalonId,
    val newStartTime: LocalTime? = null,
    val newEndTime: LocalTime? = null,
    val newTotalRepetitions: Int? = null,
    val newServiceIds: List<ServiceId>? = null,
    val newNotes: String? = null,
) {
    init {
        if (newTotalRepetitions != null) {
            require(newTotalRepetitions in 1..24) { "Total repetitions must be between 1 and 24" }
        }
        if (newStartTime != null && newEndTime != null) {
            require(newStartTime.isBefore(newEndTime)) { "Start time must be before end time" }
        }
    }
}

/**
 * Command to cancel an appointment subscription
 */
data class CancelAppointmentSubscriptionCommand(
    val subscriptionId: AppointmentSubscriptionId,
    val salonId: SalonId,
    val cancelFutureAppointments: Boolean = true,
)

/**
 * Command to pause an appointment subscription
 */
data class PauseAppointmentSubscriptionCommand(
    val subscriptionId: AppointmentSubscriptionId,
    val salonId: SalonId,
)

/**
 * Command to resume an appointment subscription
 */
data class ResumeAppointmentSubscriptionCommand(
    val subscriptionId: AppointmentSubscriptionId,
    val salonId: SalonId,
)

/**
 * Command to update an existing appointment
 */
data class UpdateAppointmentCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val clientId: ClientId? = null,
    val petId: PetId? = null,
    val staffId: StaffId? = null,
    val appointmentDate: LocalDate? = null,
    val startTime: LocalTime? = null,
    val endTime: LocalTime? = null,
    val serviceIds: List<ServiceId>? = null,
    val notes: String? = null,
    val updaterUserId: UserId,
)

/**
 * Command to update appointment photos
 */
data class UpdateAppointmentPhotosCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val photos: List<String>,
    val updaterUserId: UserId,
)

/**
 * Command to cancel an appointment
 */
data class CancelAppointmentCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val reason: String? = null,
    val cancellerUserId: UserId,
)

/**
 * Command to complete an appointment
 */
data class CompleteAppointmentCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val notes: String? = null,
    val actualServices: List<ServiceId>? = null,
    val completedAt: LocalDateTime? = null,
    val completerUserId: UserId,
)

/**
 * Command to reschedule an appointment
 */
data class RescheduleAppointmentCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val newDate: LocalDate? = null,
    val newStartTime: LocalTime,
    val newEndTime: LocalTime,
    val newStaffId: StaffId? = null,
    val reason: String? = null,
    val reschedulerUserId: UserId,
)

/**
 * Command to mark appointment as no-show
 */
data class MarkNoShowCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val noShowNotes: String? = null,
    val notifyClient: Boolean = true,
    val markerUserId: UserId,
)

/**
 * Command to mark appointment as in-progress
 */
data class MarkInProgressCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val markerUserId: UserId,
)

/**
 * Command to create multiple appointments in batch
 */
data class CreateBulkAppointmentsCommand(
    val salonId: SalonId,
    val appointments: List<ScheduleAppointmentCommand>,
    val creatorUserId: UserId,
)

/**
 * Command to delete an appointment
 */
data class DeleteAppointmentCommand(
    val appointmentId: AppointmentId,
    val salonId: SalonId,
    val deleterUserId: UserId,
)
