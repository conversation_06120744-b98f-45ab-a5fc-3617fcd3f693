package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.data.rest.webmvc.ResourceNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonWebPreferencesRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import java.util.Optional

/**
 * Service for managing salon website preferences
 * Follows hexagonal architecture pattern with domain models and repository ports
 */
@Service
class SalonWebPreferencesService(
    private val salonWebPreferencesRepository: SalonWebPreferencesRepository,
    private val salonRepository: SalonRepository,
) {
    private val logger = LoggerFactory.getLogger(SalonWebPreferencesService::class.java)
    private val objectMapper = ObjectMapper()

    // Legacy methods for backward compatibility with existing controller

    /**
     * Get web preferences for a salon (legacy method)
     * Returns Optional for backward compatibility
     */
    @Transactional(readOnly = true)
    fun getWebPreferences(salonId: String): Optional<ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences> {
        logger.info("Getting web preferences for salon (legacy): {}", salonId)

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            return Optional.empty()
        }

        val domainPreferences = salonWebPreferencesRepository.findBySalonId(salonIdDomain)
            ?: return Optional.empty()

        // Convert domain model to JPA entity for backward compatibility
        val entity = convertDomainToEntity(domainPreferences)
        return Optional.of(entity)
    }

    /**
     * Save web preferences for a salon (legacy method)
     */
    @Transactional
    fun saveWebPreferences(salonId: String, preferences: ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences?): ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences {
        logger.info("Saving web preferences for salon (legacy): {}", salonId)

        if (preferences == null) {
            throw IllegalArgumentException("Preferences cannot be null")
        }

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        // Convert JPA entity to domain model
        val domainPreferences = convertEntityToDomain(preferences, salonIdDomain)

        // Save using domain repository
        val savedDomain = salonWebPreferencesRepository.save(domainPreferences)

        // Convert back to entity for return
        return convertDomainToEntity(savedDomain)
    }

    /**
     * Check if web preferences exist for a salon (legacy method)
     */
    @Transactional(readOnly = true)
    fun hasWebPreferences(salonId: String): Boolean {
        logger.info("Checking web preferences existence for salon: {}", salonId)

        val salonIdDomain = SalonId.of(salonId)
        return salonWebPreferencesRepository.existsBySalonId(salonIdDomain)
    }

    /**
     * Delete web preferences for a salon (legacy method)
     */
    @Transactional
    fun deleteWebPreferences(salonId: String) {
        logger.info("Deleting web preferences for salon: {}", salonId)

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        salonWebPreferencesRepository.deleteBySalonId(salonIdDomain)
    }

    // Helper methods for conversion between domain and entity models

    /**
     * Convert domain model to JPA entity for backward compatibility
     */
    private fun convertDomainToEntity(domain: ro.animaliaprogramari.animalia.domain.model.SalonWebPreferences): ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences {
        val entity = ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences()
        entity.id = domain.id.value
        entity.salonId = domain.salonId.value
        entity.bookingWebsiteUrl = domain.bookingWebsiteUrl
        entity.businessName = domain.businessName
        entity.businessDescription = domain.businessDescription
        entity.businessAddress = domain.businessAddress
        entity.contactPhone = domain.contactPhone
        entity.contactEmail = domain.contactEmail
        entity.facebookLink = domain.facebookLink
        entity.instagramLink = domain.instagramLink
        entity.tiktokLink = domain.tiktokLink
        entity.isActive = domain.isActive
        entity.createdAt = domain.createdAt
        entity.updatedAt = domain.updatedAt

        // Convert enums
        entity.cancellationPolicy = when (domain.cancellationPolicy) {
            ro.animaliaprogramari.animalia.domain.model.CancellationPolicy.HOURS_24 -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_24
            ro.animaliaprogramari.animalia.domain.model.CancellationPolicy.HOURS_48 -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_48
            ro.animaliaprogramari.animalia.domain.model.CancellationPolicy.HOURS_72 -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_72
            ro.animaliaprogramari.animalia.domain.model.CancellationPolicy.NO_CHANGES -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.NO_CHANGES
        }

        entity.bookingAcceptance = when (domain.bookingAcceptance) {
            ro.animaliaprogramari.animalia.domain.model.BookingAcceptance.AUTOMATIC -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.BookingAcceptance.AUTOMATIC
            ro.animaliaprogramari.animalia.domain.model.BookingAcceptance.MANUAL -> ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.BookingAcceptance.MANUAL
        }

        // Convert collections to JSON
        if (domain.websitePhotos.isNotEmpty()) {
            entity.websitePhotos = objectMapper.writeValueAsString(domain.websitePhotos)
        }

        if (domain.businessHours.isNotEmpty()) {
            entity.businessHours = objectMapper.writeValueAsString(domain.businessHours)
        }

        return entity
    }

    /**
     * Convert JPA entity to domain model
     */
    private fun convertEntityToDomain(entity: ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences, salonId: SalonId): ro.animaliaprogramari.animalia.domain.model.SalonWebPreferences {
        // Parse JSON fields
        val photosJson = entity.websitePhotos
        val websitePhotos = if (photosJson.isNullOrBlank()) {
            emptyList<String>()
        } else {
            try {
                objectMapper.readValue<List<String>>(photosJson)
            } catch (e: Exception) {
                emptyList()
            }
        }

        val hoursJson = entity.businessHours
        val businessHours = if (hoursJson.isNullOrBlank()) {
            emptyMap<String, Map<String, Any>>()
        } else {
            try {
                objectMapper.readValue<Map<String, Map<String, Any>>>(hoursJson)
            } catch (e: Exception) {
                emptyMap()
            }
        }

        val entityId = entity.id
        return ro.animaliaprogramari.animalia.domain.model.SalonWebPreferences(
            id = if (entityId.isNullOrBlank()) SalonWebPreferencesId.generate() else SalonWebPreferencesId.of(entityId),
            salonId = salonId,
            bookingWebsiteUrl = entity.bookingWebsiteUrl ?: "",
            businessName = entity.businessName ?: "",
            businessDescription = entity.businessDescription ?: "",
            businessAddress = entity.businessAddress ?: "",
            contactPhone = entity.contactPhone ?: "",
            contactEmail = entity.contactEmail ?: "",
            facebookLink = entity.facebookLink ?: "",
            instagramLink = entity.instagramLink ?: "",
            tiktokLink = entity.tiktokLink ?: "",
            websitePhotos = websitePhotos,
            businessHours = businessHours,
            cancellationPolicy = when (entity.cancellationPolicy) {
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_24 -> ro.animaliaprogramari.animalia.domain.model.CancellationPolicy.HOURS_24
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_48 -> ro.animaliaprogramari.animalia.domain.model.CancellationPolicy.HOURS_48
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.HOURS_72 -> ro.animaliaprogramari.animalia.domain.model.CancellationPolicy.HOURS_72
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.CancellationPolicy.NO_CHANGES -> ro.animaliaprogramari.animalia.domain.model.CancellationPolicy.NO_CHANGES
                null -> ro.animaliaprogramari.animalia.domain.model.CancellationPolicy.HOURS_24
            },
            bookingAcceptance = when (entity.bookingAcceptance) {
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.BookingAcceptance.AUTOMATIC -> ro.animaliaprogramari.animalia.domain.model.BookingAcceptance.AUTOMATIC
                ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SalonWebPreferences.BookingAcceptance.MANUAL -> ro.animaliaprogramari.animalia.domain.model.BookingAcceptance.MANUAL
                null -> ro.animaliaprogramari.animalia.domain.model.BookingAcceptance.AUTOMATIC
            },
            isActive = entity.isActive ?: true,
            createdAt = entity.createdAt ?: LocalDateTime.now(),
            updatedAt = entity.updatedAt ?: LocalDateTime.now()
        )
    }

    /**
     * Create default web preferences for a salon
     */
    private fun createDefaultPreferences(salonId: SalonId): ro.animaliaprogramari.animalia.domain.model.SalonWebPreferences {
        val defaultPreferences = ro.animaliaprogramari.animalia.domain.model.SalonWebPreferences(
            id = SalonWebPreferencesId.generate(),
            salonId = salonId,
            bookingWebsiteUrl = "",
            businessName = "",
            businessDescription = "",
            businessAddress = "",
            contactPhone = "",
            contactEmail = "",
            facebookLink = "",
            instagramLink = "",
            tiktokLink = "",
            websitePhotos = emptyList(),
            businessHours = emptyMap(),
            cancellationPolicy = ro.animaliaprogramari.animalia.domain.model.CancellationPolicy.HOURS_24,
            bookingAcceptance = ro.animaliaprogramari.animalia.domain.model.BookingAcceptance.AUTOMATIC,
            isActive = true,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        return salonWebPreferencesRepository.save(defaultPreferences)
    }
}
