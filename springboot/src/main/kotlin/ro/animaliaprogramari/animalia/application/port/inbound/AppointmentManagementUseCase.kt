package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.command.ScheduleAppointmentCommand
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.application.query.GetAppointmentByIdQuery
import ro.animaliaprogramari.animalia.application.query.GetClientAppointmentsQuery
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime

/**
 * Inbound port for appointment management operations
 * Defines the contract for salon-specific appointment management
 */
interface AppointmentManagementUseCase {
    /**
     * Schedule a new appointment in a salon
     */
    fun scheduleAppointment(command: ScheduleAppointmentCommand): Appointment

    /**
     * Get appointment by ID
     */
    fun getAppointmentById(query: GetAppointmentByIdQuery): Appointment?

    /**
     * Get all appointments for a salon with optional filtering
     */
    fun getSalonAppointments(query: GetSalonAppointmentsQuery): List<Appointment>

    /**
     * Get appointments for a specific staff member
     */
    fun getStaffAppointments(query: GetStaffAppointmentsQuery): List<Appointment>

    /**
     * Get appointments for a specific client in a salon
     */
    fun getClientAppointments(query: GetClientAppointmentsQuery): List<Appointment>

    /**
     * Update an existing appointment
     */
    fun updateAppointment(command: UpdateAppointmentCommand): Appointment

    /**
     * Update appointment photos
     */
    fun updateAppointmentPhotos(command: UpdateAppointmentPhotosCommand): Appointment

    /**
     * Cancel an appointment
     */
    fun cancelAppointment(command: CancelAppointmentCommand): Appointment

    /**
     * Complete an appointment
     */
    fun completeAppointment(command: CompleteAppointmentCommand): Appointment

    /**
     * Reschedule an appointment
     */
    fun rescheduleAppointment(command: RescheduleAppointmentCommand): Appointment

    /**
     * Mark appointment as no-show
     */
    fun markNoShow(command: MarkNoShowCommand): Appointment

    /**
     * Mark appointment as in-progress
     */
    fun markInProgress(command: MarkInProgressCommand): Appointment

    /**
     * Check time slot availability
     */
    fun checkAvailability(query: CheckAvailabilityQuery): AvailabilityResult

    /**
     * Get conflicting appointments for a time slot
     */
    fun getConflictingAppointments(query: GetConflictingAppointmentsQuery): List<Appointment>

    /**
     * Get available time slots for booking
     */
    fun getAvailableSlots(query: GetAvailableSlotsQuery): List<TimeSlot>

    /**
     * Get appointment summary/statistics
     */
    fun getAppointmentSummary(query: GetAppointmentSummaryQuery): AppointmentSummary

    /**
     * Create multiple appointments in batch
     */
    fun createBulkAppointments(command: CreateBulkAppointmentsCommand): List<Appointment>

    /**
     * Delete an appointment (soft delete)
     */
    fun deleteAppointment(command: DeleteAppointmentCommand): Boolean

    /**
     * Get appointments by date range (legacy support)
     */
    fun getAppointmentsByDateRange(query: GetAppointmentsByDateRangeQuery): List<Appointment>

    /**
     * Get appointments by groomer (legacy support)
     */
    fun getAppointmentsByGroomer(query: GetAppointmentsByGroomerQuery): List<Appointment>

    /**
     * Check groomer availability (legacy support)
     */
    fun checkGroomerAvailability(query: CheckGroomerAvailabilityQuery): AvailabilityResult
}

/**
 * Time slot for appointment booking
 */
data class TimeSlot(
    val startTime: LocalTime,
    val endTime: LocalTime,
    val isAvailable: Boolean,
    val staffId: StaffId? = null,
)

/**
 * Result of availability check
 */
data class AvailabilityResult(
    val isAvailable: Boolean,
    val reason: String? = null,
    val conflictingAppointments: List<AppointmentId> = emptyList(),
    val suggestedTimes: List<LocalTime> = emptyList(),
)

/**
 * Appointment summary/statistics
 */
data class AppointmentSummary(
    val totalAppointments: Int,
    val completedAppointments: Int,
    val cancelledAppointments: Int,
    val noShowAppointments: Int,
    val totalRevenue: Money,
    val averageAppointmentDuration: Int, // in minutes
    val busyDays: List<LocalDate>,
    val topServices: List<Pair<ServiceId, Int>>, // service ID and count
    val staffUtilization: Map<UserId, Double>, // staff ID and utilization percentage
)
