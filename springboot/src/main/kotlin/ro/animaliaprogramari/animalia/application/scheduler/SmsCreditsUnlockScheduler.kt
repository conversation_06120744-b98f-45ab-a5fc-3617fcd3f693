package ro.animaliaprogramari.animalia.application.scheduler

import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import ro.animaliaprogramari.animalia.domain.service.BlockedSmsCreditsService

/**
 * Scheduler for automatically unlocking SMS credits when conditions are met
 */
@Component
@ConditionalOnProperty(
    name = ["animalia.scheduling.sms-credits-unlock.enabled"],
    havingValue = "true",
    matchIfMissing = true
)
class SmsCreditsUnlockScheduler(
    private val blockedSmsCreditsService: BlockedSmsCreditsService
) {
    private val logger = LoggerFactory.getLogger(SmsCreditsUnlockScheduler::class.java)

    /**
     * Check and unlock SMS credits every hour
     */
    @Scheduled(fixedRate = 3600000) // 1 hour = 3,600,000 milliseconds
    fun unlockEligibleSmsCredits() {
        try {
            logger.info("Starting scheduled SMS credits unlock check...")
            
            val result = blockedSmsCreditsService.processAllUnlockableCredits()
            
            if (result.totalUnlocked > 0) {
                logger.info(
                    "Scheduled unlock completed successfully. " +
                    "Processed: ${result.totalProcessed}, " +
                    "Unlocked: ${result.totalUnlocked} credits, " +
                    "Total SMS credits: ${result.totalCreditsUnlocked}"
                )
            } else {
                logger.debug("No SMS credits were unlocked in this scheduled run")
            }
        } catch (e: Exception) {
            logger.error("Failed to process scheduled SMS credits unlock", e)
        }
    }

    /**
     * Check and unlock SMS credits daily at 9 AM
     * This is a backup check in case the hourly check misses something
     */
    @Scheduled(cron = "0 0 9 * * *") // Daily at 9:00 AM
    fun dailySmsCreditsUnlockCheck() {
        try {
            logger.info("Starting daily SMS credits unlock check...")
            
            val result = blockedSmsCreditsService.processAllUnlockableCredits()
            
            logger.info(
                "Daily unlock check completed. " +
                "Processed: ${result.totalProcessed}, " +
                "Unlocked: ${result.totalUnlocked} credits, " +
                "Total SMS credits: ${result.totalCreditsUnlocked}"
            )
        } catch (e: Exception) {
            logger.error("Failed to process daily SMS credits unlock check", e)
        }
    }
}
