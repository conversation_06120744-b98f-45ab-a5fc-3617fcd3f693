package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.sms.*
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

@Service
class ClientFilteringService(
    private val clientRepository: ClientRepository,
    private val petRepository: PetRepository,
    private val appointmentRepository: AppointmentRepository
) {
    private val logger = LoggerFactory.getLogger(ClientFilteringService::class.java)

    fun filterClients(salonId: SalonId, request: ClientFilterRequest): ClientFilterResponse {
        logger.info("Filtering clients for salon: $salonId with criteria: $request")

        // Get all active clients for the salon
        val allClients = clientRepository.findBySalonId(salonId, isActive = true)
        val totalClientsBeforeFilter = allClients.size

        // Apply filters
        var filteredClients = allClients

        // Filter by phone number requirement
        if (request.hasPhone) {
            filteredClients = filteredClients.filter { it.phone != null && it.phone.value.isNotBlank() }
        }

        // Filter by SMS capability
        if (request.canReceiveSms) {
            filteredClients = filteredClients.filter {
                it.phone != null && it.phone.value.isNotBlank() && it.isActive
            }
        }

        // Filter by search query (name, phone, email)
        if (!request.searchQuery.isNullOrBlank()) {
            val query = request.searchQuery.lowercase()
            filteredClients = filteredClients.filter { client ->
                client.name.lowercase().contains(query) ||
                (client.phone?.value?.contains(query) == true) ||
                (client.email?.value?.lowercase()?.contains(query) == true)
            }
        }

        // Get pets for remaining clients
        val clientIds = filteredClients.map { it.id }
        val clientPets = if (clientIds.isNotEmpty()) {
            petRepository.findByClientIds(clientIds).groupBy { it.clientId }
        } else {
            emptyMap()
        }

        // Filter by pet criteria
        if (!request.petSizes.isNullOrEmpty()) {
            filteredClients = filteredClients.filter { client ->
                val pets = clientPets[client.id] ?: emptyList()
                pets.any { pet ->
                    request.petSizes.contains(pet.size?.lowercase())
                }
            }
        }

        if (!request.petBreeds.isNullOrEmpty()) {
            filteredClients = filteredClients.filter { client ->
                val pets = clientPets[client.id] ?: emptyList()
                pets.any { pet ->
                    request.petBreeds.any { breed ->
                        pet.breed?.lowercase()?.contains(breed.lowercase()) == true
                    }
                }
            }
        }

        if (!request.petSpecies.isNullOrEmpty()) {
            filteredClients = filteredClients.filter { client ->
                val pets = clientPets[client.id] ?: emptyList()
                pets.any { pet ->
                    request.petSpecies.contains(pet.species?.lowercase())
                }
            }
        }

        // Get appointment data for frequency and last visit filtering
        val appointmentData = if (filteredClients.isNotEmpty()) {
            getClientAppointmentData(salonId, filteredClients.map { it.id })
        } else {
            emptyMap()
        }

        // Filter by client frequency
        if (!request.clientFrequency.isNullOrBlank()) {
            filteredClients = filteredClients.filter { client ->
                val data = appointmentData[client.id]
                when (request.clientFrequency.lowercase()) {
                    "frequent" -> (data?.totalVisits ?: 0) >= 5
                    "occasional" -> (data?.totalVisits ?: 0) in 2..4
                    "new" -> (data?.totalVisits ?: 0) <= 1
                    else -> true
                }
            }
        }

        // Filter by last visit date range
        if (request.lastVisitFrom != null || request.lastVisitTo != null) {
            filteredClients = filteredClients.filter { client ->
                val lastVisit = appointmentData[client.id]?.lastVisit
                when {
                    lastVisit == null -> false
                    request.lastVisitFrom != null && lastVisit.isBefore(request.lastVisitFrom) -> false
                    request.lastVisitTo != null && lastVisit.isAfter(request.lastVisitTo) -> false
                    else -> true
                }
            }
        }

        // Filter by customer loyalty (appointments in time period)
        if (request.loyaltyMinAppointments != null && request.loyaltyTimePeriod != null) {
            val loyaltyData = getClientLoyaltyData(salonId, filteredClients.map { it.id }, request.loyaltyTimePeriod)
            filteredClients = filteredClients.filter { client ->
                val appointmentCount = loyaltyData[client.id] ?: 0
                if (request.loyaltyMinAppointments == 0) {
                    appointmentCount == 0
                } else {
                    appointmentCount >= request.loyaltyMinAppointments
                }
            }
        }

        // Build response
        val filteredClientResponses = filteredClients.map { client ->
            val pets = clientPets[client.id] ?: emptyList()
            val data = appointmentData[client.id]

            FilteredClient(
                clientId = client.id.value,
                clientName = client.name,
                phoneNumber = client.phone?.value ?: "",
                email = client.email?.value,
                pets = pets.map { pet ->
                    FilteredPet(
                        petId = pet.id.value,
                        petName = pet.name,
                        breed = pet.breed,
                        size = pet.size,
                        species = pet.species
                    )
                },
                lastVisit = data?.lastVisit,
                totalVisits = data?.totalVisits ?: 0,
                totalSpent = data?.totalSpent ?: 0.0,
                canReceiveSms = client.phone != null && client.phone.value.isNotBlank() && client.isActive,
                tags = emptyList() // TODO: Implement client tags
            )
        }

        val filterSummary = ClientFilterSummary(
            appliedFilters = buildAppliedFiltersMap(request),
            totalClientsBeforeFilter = totalClientsBeforeFilter,
            totalClientsAfterFilter = filteredClients.size,
            filterEffectiveness = if (totalClientsBeforeFilter > 0) {
                (filteredClients.size.toDouble() / totalClientsBeforeFilter * 100)
            } else 0.0
        )

        logger.info("Client filtering completed. Before: $totalClientsBeforeFilter, After: ${filteredClients.size}")

        return ClientFilterResponse(
            totalClients = filteredClients.size,
            filteredClients = filteredClientResponses,
            filterSummary = filterSummary
        )
    }

    private fun getClientAppointmentData(
        salonId: SalonId,
        clientIds: List<ClientId>
    ): Map<ClientId, ClientAppointmentData> {
        if (clientIds.isEmpty()) return emptyMap()

        val appointments = appointmentRepository.findBySalonIdAndClientIds(salonId, clientIds)

        return appointments.groupBy { it.clientId }.mapValues { (_, clientAppointments) ->
            val completedAppointments = clientAppointments.filter {
                it.status == AppointmentStatus.COMPLETED
            }

            ClientAppointmentData(
                totalVisits = completedAppointments.size,
                lastVisit = completedAppointments.maxByOrNull { appointment -> appointment.appointmentDate.atTime(appointment.startTime) }?.let { appointment -> appointment.appointmentDate.atTime(appointment.startTime) },
                totalSpent = completedAppointments.sumOf { appointment ->
                    appointment.totalPrice.amount.toDouble()
                }
            )
        }
    }

    private fun buildAppliedFiltersMap(request: ClientFilterRequest): Map<String, Any> {
        val filters = mutableMapOf<String, Any>()

        if (!request.petSizes.isNullOrEmpty()) {
            filters["petSizes"] = request.petSizes
        }
        if (!request.petBreeds.isNullOrEmpty()) {
            filters["petBreeds"] = request.petBreeds
        }
        if (!request.petSpecies.isNullOrEmpty()) {
            filters["petSpecies"] = request.petSpecies
        }
        if (!request.clientFrequency.isNullOrBlank()) {
            filters["clientFrequency"] = request.clientFrequency
        }
        if (request.lastVisitFrom != null) {
            filters["lastVisitFrom"] = request.lastVisitFrom
        }
        if (request.lastVisitTo != null) {
            filters["lastVisitTo"] = request.lastVisitTo
        }
        if (!request.searchQuery.isNullOrBlank()) {
            filters["searchQuery"] = request.searchQuery
        }
        if (request.loyaltyMinAppointments != null) {
            filters["loyaltyMinAppointments"] = request.loyaltyMinAppointments
        }
        if (!request.loyaltyTimePeriod.isNullOrBlank()) {
            filters["loyaltyTimePeriod"] = request.loyaltyTimePeriod
        }
        filters["hasPhone"] = request.hasPhone
        filters["canReceiveSms"] = request.canReceiveSms

        return filters
    }

    private fun getClientLoyaltyData(
        salonId: SalonId,
        clientIds: List<ClientId>,
        timePeriod: String
    ): Map<ClientId, Int> {
        if (clientIds.isEmpty()) return emptyMap()

        val cutoffDate = calculateCutoffDate(timePeriod)
        val appointments = appointmentRepository.findBySalonIdAndClientIdsAfterDate(salonId, clientIds, cutoffDate)

        return appointments.groupBy { it.clientId }.mapValues { (_, clientAppointments) ->
            clientAppointments.filter {
                it.status == AppointmentStatus.COMPLETED
            }.size
        }
    }

    private fun calculateCutoffDate(timePeriod: String): LocalDateTime {
        val now = LocalDateTime.now()
        return when (timePeriod) {
            "1_week" -> now.minusWeeks(1)
            "1_month" -> now.minusMonths(1)
            "3_months" -> now.minusMonths(3)
            "6_months" -> now.minusMonths(6)
            "1_year" -> now.minusYears(1)
            else -> now.minusMonths(6) // Default to 6 months
        }
    }

    private data class ClientAppointmentData(
        val totalVisits: Int,
        val lastVisit: LocalDateTime?,
        val totalSpent: Double
    )
}
