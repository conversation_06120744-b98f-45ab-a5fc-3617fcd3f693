package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Inbound port for subscription use cases
 */
interface SubscriptionUseCase {
    /**
     * Create a free tier subscription for new users
     */
    fun createFreeSubscription(command: CreateFreeSubscriptionCommand): SalonSubscription


    /**
     * Verify and activate a subscription purchase
     */
    fun verifyPurchase(command: VerifyPurchaseCommand): SalonSubscription

    /**
     * Sync subscription status with RevenueCat
     */
    fun renewall(command: SyncSubscriptionCommand): SalonSubscription?

    fun changeProduct(command: ChangeProductCommand): SalonSubscription?

    /**
     * Create subscription from webhook data (for initial purchases)
     */
    fun createSubscription(command: CreateSubscriptionFromWebhookCommand): SalonSubscription?

    /**
     * Get current subscription for a salon
     */
    fun getCurrentSubscription(query: GetSalonSubscriptionQuery): SalonSubscription?

    /**
     * Get current subscription for a user (applies to all their salons)
     */
    fun getUserSubscription(query: GetUserSubscriptionQuery): SalonSubscription?

    /**
     * Get subscription history for a salon
     */
    fun getSubscriptionHistory(query: GetSalonSubscriptionHistoryQuery): List<SalonSubscription>

    /**
     * Check if salon can access a feature
     */
    fun canAccessFeature(query: CheckFeatureAccessQuery): Boolean

    /**
     * Get subscription limits for a salon
     */
    fun getSubscriptionLimits(query: GetSubscriptionLimitsQuery): SubscriptionLimits

    /**
     * Cancel subscription
     */
    fun cancelSubscription(command: CancelSubscriptionCommand): SalonSubscription

    /**
     * Restore subscription from RevenueCat
     */
    fun restoreSubscription(command: RestoreSubscriptionCommand): SalonSubscription?

    /**
     * Track usage for analytics
     */
    fun trackUsage(command: TrackUsageCommand)

    /**
     * Check if action is allowed based on subscription limits
     */
    fun checkLimit(query: CheckLimitQuery): Boolean

    /**
     * Get user's highest subscription tier across all their salons
     */
    fun getUserHighestSubscriptionTier(query: GetUserHighestTierQuery): SubscriptionTier?

    /**
     * Check if user can create salons based on their highest subscription tier
     */
    fun canUserCreateSalons(query: CanUserCreateSalonsQuery): Boolean

    /**
     * Update subscription metadata
     */
    fun updateSubscriptionMetadata(command: UpdateSubscriptionMetadataCommand): SalonSubscription

    /**
     * Get subscription analytics
     */
    fun getSubscriptionAnalytics(query: GetSubscriptionAnalyticsQuery): SubscriptionAnalytics

    fun expire(command: ExpireSubscriptionCommand) : SalonSubscription?

    fun cancel(command: ExpireSubscriptionCommand) : SalonSubscription?

    fun uncancel(command: ExpireSubscriptionCommand) : SalonSubscription?
}

/**
 * Subscription limits data class
 */
data class SubscriptionLimits(
    val maxStaff: Int,
    val maxClients: Int,
    val smsQuota: Int,
    val currentStaffCount: Int,
    val currentClientCount: Int,
    val currentSmsUsage: Int,
) {
    fun canAddStaff(): Boolean = maxStaff == -1 || currentStaffCount < maxStaff
    fun canAddClients(): Boolean = maxClients == -1 || currentClientCount < maxClients
    fun canSendSms(): Boolean = currentSmsUsage < smsQuota
    fun getStaffUsagePercentage(): Double = if (maxStaff == -1) 0.0 else (currentStaffCount.toDouble() / maxStaff) * 100
    fun getClientUsagePercentage(): Double = if (maxClients == -1) 0.0 else (currentClientCount.toDouble() / maxClients) * 100
    fun getSmsUsagePercentage(): Double = (currentSmsUsage.toDouble() / smsQuota) * 100
}

/**
 * Subscription analytics data class
 */
data class SubscriptionAnalytics(
    val totalSubscriptions: Long,
    val activeSubscriptions: Long,
    val trialSubscriptions: Long,
    val expiredSubscriptions: Long,
    val subscriptionsByTier: Map<SubscriptionTier, Long>,
    val monthlyRevenue: Double,
    val annualRevenue: Double,
    val averageSubscriptionDuration: Double,
    val churnRate: Double,
)


