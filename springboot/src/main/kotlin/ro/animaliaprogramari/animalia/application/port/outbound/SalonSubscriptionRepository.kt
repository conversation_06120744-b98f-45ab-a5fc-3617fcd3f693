package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Outbound port for salon subscription repository
 */
interface SalonSubscriptionRepository {
    /**
     * Save a subscription
     */
    fun save(subscription: SalonSubscription): SalonSubscription

    /**
     * Find subscription by ID
     */
    fun findById(id: SubscriptionId): SalonSubscription?

    /**
     * Find active subscription for a salon
     */
    fun findActiveBySalonId(salonId: SalonId): SalonSubscription?

    /**
     * Find all subscriptions for a salon
     */
    fun findBySalonId(salonId: SalonId): List<SalonSubscription>

    /**
     * Find all subscriptions for a user
     */
    fun findByUserId(userId: UserId): List<SalonSubscription>

    /**
     * Find active subscriptions for a user with database-level filtering
     */
    fun findActiveByUserId(userId: UserId): List<SalonSubscription>

    /**
     * Find subscription by RevenueCat customer ID
     */
    fun findByRevenueCatCustomerId(customerId: String): SalonSubscription?

    /**
     * Find all active subscriptions
     */
    fun findAllActive(): List<SalonSubscription>

    /**
     * Find all expired subscriptions
     */
    fun findAllExpired(): List<SalonSubscription>

    /**
     * Find all trial subscriptions
     */
    fun findAllTrials(): List<SalonSubscription>

    /**
     * Delete subscription
     */
    fun delete(id: SubscriptionId)

    /**
     * Check if salon has any active subscription
     */
    fun hasActiveSubscription(salonId: SalonId): Boolean

    /**
     * Get subscription count by tier
     */
    fun countByTier(tier: SubscriptionTier): Long

    /**
     * Get subscription count by status
     */
    fun countByStatus(status: SubscriptionStatus): Long
}
