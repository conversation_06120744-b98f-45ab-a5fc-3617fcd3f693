package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Inbound port for salon management operations
 * This interface defines the business operations available for salon management
 */
interface SalonManagementUseCase {
    /**
     * Create a new salon
     */
    fun createSalon(command: CreateSalonCommand): Salon

    /**
     * Update salon information
     */
    fun updateSalon(command: UpdateSalonCommand): Salon

    /**
     * Activate a salon
     */
    fun activateSalon(command: ActivateSalonCommand): Salon

    /**
     * Deactivate a salon
     */
    fun deactivateSalon(command: DeactivateSalonCommand): Salon

    /**
     * Delete a salon permanently
     */
    fun deleteSalon(command: DeleteSalonCommand): Boolean

    /**
     * Add a client to a salon
     */
    fun addClientToSalon(command: AddClientToSalonCommand): Salon

    /**
     * Remove a client from a salon
     */
    fun removeClientFromSalon(command: RemoveClientFromSalonCommand): Salon

    /**
     * Get salon by ID
     */
    fun getSalonById(query: GetSalonByIdQuery): Salon?

    /**
     * Get all salons with optional filtering
     */
    fun getAllSalons(query: GetAllSalonsQuery): List<Salon>

    /**
     * Get salons by user association
     */
    fun getSalonsByUser(query: GetSalonsByUserQuery): List<Salon>

    /**
     * Get salon details with additional information
     */
    fun getSalonDetails(query: GetSalonDetailsQuery): Map<String, Any>

    /**
     * Get salons that have a specific client
     */
    fun getSalonsWithClient(query: GetSalonsWithClientQuery): List<Salon>

    /**
     * Increment additional slots counter for a salon
     */
    fun incrementAdditionalSlotsCount(salonId: SalonId): Salon
}
