package ro.animaliaprogramari.animalia.application.port.outbound

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.NotificationHistory
import ro.animaliaprogramari.animalia.domain.model.SalonId
import java.time.LocalDateTime

/**
 * Repository port for notification history operations
 * This is an outbound port in hexagonal architecture
 */
interface NotificationHistoryRepository {

    /**
     * Save a notification
     */
    fun save(notification: NotificationHistory): NotificationHistory

    /**
     * Find notification by ID
     */
    fun findById(id: Long): NotificationHistory?

    /**
     * Find notifications by salon with filters and pagination
     */
    fun findBySalonIdWithFilters(
        salonId: SalonId,
        type: String? = null,
        readStatus: Boolean? = null,
        startDate: LocalDateTime? = null,
        endDate: LocalDateTime? = null,
        pageable: Pageable
    ): Page<NotificationHistory>

    /**
     * Find recent unread notifications for a salon
     */
    fun findRecentUnreadBySalonId(
        salonId: SalonId,
        pageable: Pageable
    ): Page<NotificationHistory>

    /**
     * Count unread notifications by salon
     */
    fun countBySalonIdAndReadStatus(
        salonId: SalonId,
        readStatus: Boolean = false
    ): Long

    /**
     * Find notifications by appointment ID
     */
    fun findByAppointmentIdOrderByTimestampDesc(
        appointmentId: String
    ): List<NotificationHistory>

    /**
     * Find notifications by client ID
     */
    fun findByClientIdOrderByTimestampDesc(
        clientId: String
    ): List<NotificationHistory>

    /**
     * Mark notifications as read in batch
     */
    fun markAsReadBatch(
        notificationIds: List<Long>,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    /**
     * Mark all notifications as read for a salon
     */
    fun markAllAsReadBySalonId(
        salonId: SalonId,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    /**
     * Delete old notifications
     */
    fun deleteOldNotifications(cutoffDate: LocalDateTime): Int

    /**
     * Get notification statistics by salon
     */
    fun getNotificationStatistics(
        salonId: SalonId,
        startDate: LocalDateTime
    ): List<Array<Any>>

    /**
     * Find notifications by type and date range
     */
    fun findByTypeAndDateRange(
        salonId: SalonId,
        type: String,
        startDate: LocalDateTime,
        endDate: LocalDateTime
    ): List<NotificationHistory>

    /**
     * Check if notification exists for appointment
     */
    fun existsByAppointmentIdAndType(
        appointmentId: String,
        type: String
    ): Boolean

    /**
     * Find latest notification by appointment and type
     */
    fun findFirstByAppointmentIdAndTypeOrderByTimestampDesc(
        appointmentId: String,
        type: String
    ): NotificationHistory?

    /**
     * Get dashboard summary
     */
    fun getDashboardSummary(
        salonId: SalonId,
        startDate: LocalDateTime
    ): List<Map<String, Any>>
}
