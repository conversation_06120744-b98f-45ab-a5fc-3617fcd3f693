package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.ConfirmPhoneCommand
import ro.animaliaprogramari.animalia.application.command.ResendPhoneVerificationCommand
import ro.animaliaprogramari.animalia.domain.model.PhoneVerificationCode

/**
 * Use case for phone verification operations
 */
interface PhoneVerificationUseCase {
    /**
     * Send verification code to a phone number
     */
    fun sendVerificationCode(command: ResendPhoneVerificationCommand): PhoneVerificationCodeResult

    /**
     * Verify phone number with confirmation code
     */
    fun verifyPhoneNumber(command: ConfirmPhoneCommand): PhoneVerificationResult
}

/**
 * Result of phone verification code operation
 */
data class PhoneVerificationCodeResult(
    val success: Boolean,
    val message: String,
    val phoneNumber: String,
    val expiresIn: Int? = null, // seconds
    val canResendAfter: Int? = null, // seconds
    val remainingAttempts: Int? = null
)

/**
 * Result of phone verification operation
 */
data class PhoneVerificationResult(
    val success: <PERSON><PERSON><PERSON>,
    val message: String,
    val phoneNumber: String? = null,
    val verified: Boolean = false
)
