package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.SalonManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.SubscriptionUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal

/**
 * Implementation of salon management use case
 * Handles all salon-related business logic
 */
@Service
@Transactional
class SalonManagementUseCaseImpl(
    private val salonRepository: SalonRepository,
    private val userRepository: UserRepository,
    private val staffRepository: StaffRepository,
    private val petRepository: PetRepository,
    private val clientRepository: ClientRepository,
    private val workingHoursRepository: WorkingHoursRepository,
    private val notificationSettingsRepository: NotificationSettingsRepository,
    private val salonServiceRepository: SalonServiceRepository,
    private val smsReminderSettingsRepository: SmsReminderSettingsRepository,
    private val subscriptionUseCase: SubscriptionUseCase,
) : SalonManagementUseCase {
    private val logger = LoggerFactory.getLogger(SalonManagementUseCaseImpl::class.java)

    override fun createSalon(command: CreateSalonCommand): Salon {
        logger.info("Creating salon: ${command.name}")

        // Validate creator exists
        val creator = userRepository.findById(command.creatorUserId)
            ?: throw EntityNotFoundException("Creator user not found: ${command.creatorUserId.value}")

        // Update user information if provided
        if (command.ownerName != null || command.phone != null) {
            val updatedUser = creator.updateInfo(
                name = command.ownerName ?: creator.name,
                phoneNumber = command.phone?.value ?: creator.phoneNumber
            )
            userRepository.save(updatedUser)
            logger.info("Updated user info for salon creator: ${command.creatorUserId.value}")
        }

        // Create salon (note: description field is not yet supported in domain model)
        val salon =
            Salon(
                id = SalonId.generate(),
                name = command.name,
                address = command.address,
                city = command.city,
                phone = command.phone,
                email = command.email,
                ownerId = command.creatorUserId,
                description = command.description,
                isActive = true,
            )

        val savedSalon = salonRepository.save(salon)
        logger.info("Salon created: ${savedSalon.id.value}")

        // Create default working hours for the salon
        val defaultWorkingHours = WorkingHoursSettings.createDefault(savedSalon.id)
        workingHoursRepository.save(defaultWorkingHours)
        logger.info("Default working hours created for salon: ${savedSalon.id.value}")

        // Create chief groomer staff for the creator
        val staff =
            Staff.createChiefGroomer(
                userId = command.creatorUserId,
                salonId = savedSalon.id,
            )
        staffRepository.save(staff)
        logger.info("Chief groomer association created for user: ${command.creatorUserId.value}")

        // Check if this is the user's first salon and create free subscription if needed
        val userSalons = staffRepository.findByUserId(command.creatorUserId)
            .filter { it.role == StaffRole.CHIEF_GROOMER && it.isActive }

        if (userSalons.size == 1) { // This is their first salon (just created)
            logger.info("Creating first salon for user ${command.creatorUserId.value}, checking for existing subscription")

            // Check if user already has a subscription
            val existingSubscription = subscriptionUseCase.getUserSubscription(
                GetUserSubscriptionQuery(command.creatorUserId)
            )

            if (existingSubscription == null) {
                logger.info("No existing subscription found, creating free subscription for user ${command.creatorUserId.value}")
                try {
                    subscriptionUseCase.createFreeSubscription(
                        CreateFreeSubscriptionCommand(command.creatorUserId)
                    )
                    logger.info("Free subscription created successfully for user ${command.creatorUserId.value}")
                } catch (e: Exception) {
                    logger.error("Failed to create free subscription for user ${command.creatorUserId.value}", e)
                    // Don't fail salon creation if subscription creation fails
                }
            } else {
                logger.info("User ${command.creatorUserId.value} already has a subscription: ${existingSubscription.tier.name}")
            }
        } else {
            logger.info("User ${command.creatorUserId.value} already has ${userSalons.size} salons, not creating free subscription")
        }

        val defaultService = SalonService.create(
            salonId = savedSalon.id,
            name = "Serviciu Standard",
            description = "Serviciu standard de toaletare pentru animale",
            basePrice = Money.of(200.0),
            myDuration = MyDuration.ofHours(1),
            category = ServiceCategory.GROOMING,
            displayOrder = 0
        )
        salonServiceRepository.save(defaultService)
        logger.info("Default standard service created for salon: ${savedSalon.id.value}")

        val defaultClient = Client.create(
            salonId = savedSalon.id,
            name = command.ownerName.toString(),
            phone = PhoneNumber.of(command.phone?.value ?: creator.phoneNumber ?: "+40731446895"),
            email = Email.of("<EMAIL>"),
            address = "Str. Salonului 123, București",
            notes = "Client standard",
            userIds = setOf(command.creatorUserId),
        )
        clientRepository.save(defaultClient)
        logger.info("Default client created for salon: ${savedSalon.id.value}")

        val defaultPet = Pet.create(
            clientId = defaultClient.id,
            name = "Archie",
            species = "Câine",
            breed = "Standard Poodle",
            birthDate = null,
            age = 3,
            weight = BigDecimal(15.0),
            color = "Red",
            gender = Gender.MALE,
            medicalConditions = "Urechi sensibile",
            notes = "Cuminte si foarte prietenos",
        )
        petRepository.save(defaultPet)
        logger.info("Default pet created for salon: ${savedSalon.id.value}")

        // Initialize SMS reminder settings with defaults
        val defaultSmsSettings = SmsReminderSettings.createDefault(savedSalon.id)
        smsReminderSettingsRepository.save(defaultSmsSettings)
        logger.info("Default SMS reminder settings created for salon: ${savedSalon.id.value}")

        return savedSalon
    }

    override fun updateSalon(command: UpdateSalonCommand): Salon {
        logger.info("Updating salon: ${command.salonId.value}")

        // Find salon
        val salon =
            salonRepository.findById(command.salonId)
                ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Validate updater has permission
        val updaterStaff =
            staffRepository.findByUserIdAndSalonId(
                command.updaterUserId,
                command.salonId,
            )
        if (updaterStaff == null || updaterStaff.role != StaffRole.CHIEF_GROOMER) {
            throw BusinessRuleViolationException("User does not have permission to update this salon")
        }

        // Update salon
        val updatedSalon =
            salon.copy(
                name = command.name ?: salon.name,
                address = command.address ?: salon.address,
                phone = command.phone ?: salon.phone,
                email = command.email ?: salon.email,
                googleReviewLink = command.googleReviewLink ?: salon.googleReviewLink,
                updatedAt = java.time.LocalDateTime.now(),
            )

        return salonRepository.save(updatedSalon)
    }

    override fun activateSalon(command: ActivateSalonCommand): Salon {
        logger.info("Activating salon: ${command.salonId.value}")

        val salon =
            salonRepository.findById(command.salonId)
                ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        val activatedSalon =
            salon.copy(
                isActive = true,
                updatedAt = java.time.LocalDateTime.now(),
            )

        return salonRepository.save(activatedSalon)
    }

    override fun deactivateSalon(command: DeactivateSalonCommand): Salon {
        logger.info("Deactivating salon: ${command.salonId.value}")

        val salon =
            salonRepository.findById(command.salonId)
                ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        val deactivatedSalon =
            salon.copy(
                isActive = false,
                updatedAt = java.time.LocalDateTime.now(),
            )

        return salonRepository.save(deactivatedSalon)
    }

    override fun deleteSalon(command: DeleteSalonCommand): Boolean {
        logger.info("Deleting salon: ${command.salonId.value}")

        // Validate salon exists
        salonRepository.findById(command.salonId)
            ?: throw EntityNotFoundException("Salon not found: ${'$'}{command.salonId.value}")

        // Validate user is chief groomer for this salon
        val staff = staffRepository.findByUserIdAndSalonId(command.deletedBy, command.salonId)
        if (staff == null || staff.role != StaffRole.CHIEF_GROOMER) {
            throw BusinessRuleViolationException("User does not have permission to delete this salon")
        }

        // Delete or deactivate all staff associations for this salon
        val salonStaff = staffRepository.findBySalonId(command.salonId)
        salonStaff.forEach { staffMember ->
            // Deactivate staff instead of hard delete to preserve history
            val deactivatedStaff = staffMember.deactivate()
            staffRepository.save(deactivatedStaff)
            logger.info("Deactivated staff: ${staffMember.id.value} for salon: ${command.salonId.value}")
        }

        // Delete related data
        workingHoursRepository.deleteBySalonId(command.salonId)

        // Delete all notification settings for this salon (for all users)
        val notificationSettings = notificationSettingsRepository.findBySalonId(command.salonId)
        notificationSettings.forEach { settings ->
            notificationSettingsRepository.deleteByUserIdAndSalonId(settings.userId, settings.salonId)
        }

        return salonRepository.deleteById(command.salonId)
    }

    override fun addClientToSalon(command: AddClientToSalonCommand): Salon {
        logger.info("Adding client ${command.clientId.value} to salon ${command.salonId.value}")

        // Validate salon exists
        val salon =
            salonRepository.findById(command.salonId)
                ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Validate client exists and update with salon association
        val client = clientRepository.findById(command.clientId)
            ?: throw EntityNotFoundException("Client not found: ${command.clientId.value}")

        // Validate groomer has permission
        val groomerStaff =
            staffRepository.findByUserIdAndSalonId(
                command.groomerUserId,
                command.salonId,
            )
        if (groomerStaff == null || groomerStaff.permissions.clientDataAccess == ClientDataAccess.NONE) {
            throw BusinessRuleViolationException("Groomer does not have permission to add clients to this salon")
        }

        // Update client with salon association (using FK relationship)
        val updatedClient = client.copy(salonId = command.salonId, updatedAt = java.time.LocalDateTime.now())
        clientRepository.save(updatedClient)

        return salon // Return salon unchanged since we're using FK relationship
    }

    override fun removeClientFromSalon(command: RemoveClientFromSalonCommand): Salon {
        logger.info("Removing client ${command.clientId.value} from salon ${command.salonId.value}")

        // Validate salon exists
        val salon =
            salonRepository.findById(command.salonId)
                ?: throw EntityNotFoundException("Salon not found: ${command.salonId.value}")

        // Validate client exists and belongs to this salon
        val client = clientRepository.findById(command.clientId)
            ?: throw EntityNotFoundException("Client not found: ${command.clientId.value}")

        if (client.salonId != command.salonId) {
            throw BusinessRuleViolationException("Client does not belong to this salon")
        }

        // Validate groomer has permission
        val groomerStaff =
            staffRepository.findByUserIdAndSalonId(
                command.groomerUserId,
                command.salonId,
            )
        if (groomerStaff == null || groomerStaff.role != StaffRole.CHIEF_GROOMER) {
            throw BusinessRuleViolationException("Only chief groomers can remove clients from salon")
        }

        // Remove client from salon (set salonId to null)
        val updatedClient = client.copy(salonId = null, updatedAt = java.time.LocalDateTime.now())
        clientRepository.save(updatedClient)

        return salon // Return salon unchanged since we're using FK relationship
    }

    override fun getSalonById(query: GetSalonByIdQuery): Salon? {
        return salonRepository.findById(query.salonId)
    }

    override fun getAllSalons(query: GetAllSalonsQuery): List<Salon> {
        return salonRepository.findAll(
            search = query.search,
            isActive = query.isActive,
            limit = query.limit,
            offset = query.offset,
        )
    }

    override fun getSalonsByUser(query: GetSalonsByUserQuery): List<Salon> {
        val staffList = staffRepository.findByUserId(query.userId)
        val salonIds =
            staffList
                .filter { query.isActive == null || it.isActive == query.isActive }
                .map { it.salonId }

        return salonIds.mapNotNull { salonRepository.findById(it) }
    }

    override fun getSalonDetails(query: GetSalonDetailsQuery): Map<String, Any> {
        val salon =
            salonRepository.findById(query.salonId)
                ?: throw EntityNotFoundException("Salon not found: ${query.salonId.value}")

        // Get salon staff
        val staffList = staffRepository.findActiveBySalonWithUserDetails(query.salonId)

        return mapOf(
            "salon" to salon,
            "clientCount" to clientRepository.findBySalonId(salon.id).size,
            "groomerCount" to staffList.size,
            "chiefGroomers" to staffList.filter { it.role == StaffRole.CHIEF_GROOMER }.size,
            "regularGroomers" to staffList.filter { it.role == StaffRole.GROOMER }.size,
        )
    }

    override fun getSalonsWithClient(query: GetSalonsWithClientQuery): List<Salon> {
        return salonRepository.findSalonsWithClient(query.clientId)
    }

    override fun incrementAdditionalSlotsCount(salonId: SalonId): Salon {
        logger.info("Incrementing additional slots count for salon: ${salonId.value}")

        val salon = salonRepository.findById(salonId)
            ?: throw EntityNotFoundException("Salon not found: ${salonId.value}")

        val updatedSalon = salon.incrementAdditionalSlotsCount()
        return salonRepository.save(updatedSalon)
    }
}
