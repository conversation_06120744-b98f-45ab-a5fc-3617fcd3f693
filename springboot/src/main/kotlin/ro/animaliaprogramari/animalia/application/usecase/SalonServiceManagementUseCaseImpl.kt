package ro.animaliaprogramari.animalia.application.usecase

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.port.inbound.SalonServiceManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.SalonServiceRepository
import ro.animaliaprogramari.animalia.config.AnimaliaConstants
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Implementation of SalonServiceManagementUseCase
 * This is the application service layer in hexagonal architecture
 */
@Service
@Transactional
class SalonServiceManagementUseCaseImpl(
    private val salonServiceRepository: SalonServiceRepository,
    private val pricingValidationService: ro.animaliaprogramari.animalia.domain.service.ServicePricingValidationService,
) : SalonServiceManagementUseCase {
    override fun createService(
        salonId: SalonId,
        name: String,
        description: String?,
        price: Money,
        myDuration: MyDuration,
        category: ServiceCategory,
        displayOrder: Int,
        requirements: List<String>,
        sizePrices: Map<String, Money>,
        sizeDurations: Map<String, MyDuration>,
        minPrice: Money?,
        maxPrice: Money?,
        sizeMinPrices: Map<String, Money>?,
        sizeMaxPrices: Map<String, Money>?,
        color: String?,
    ): SalonService {
        // Check if service name is unique within salon
        if (!isServiceNameUnique(salonId, name)) {
            throw IllegalArgumentException("Un serviciu cu acest nume există deja în salon")
        }

        // Validate pricing configuration
        val validationResult =
            pricingValidationService.validatePricingConfiguration(
                basePrice = price,
                baseMyDuration = myDuration,
                sizePrices = sizePrices,
                sizeDurations = sizeDurations,
                minPrice = minPrice,
                maxPrice = maxPrice,
                sizeMinPrices = sizeMinPrices,
                sizeMaxPrices = sizeMaxPrices,
            )

        if (!validationResult.isValid) {
            throw IllegalArgumentException(
                "Configurația de prețuri nu este validă: ${validationResult.getErrorMessage()}",
            )
        }

        val service =
            SalonService.create(
                salonId = salonId,
                name = name,
                description = description,
                basePrice = price,
                myDuration = myDuration,
                category = category,
                displayOrder = displayOrder,
                requirements = requirements,
                sizePrices = sizePrices,
                sizeDurations = sizeDurations,
                minPrice = minPrice,
                maxPrice = maxPrice,
                sizeMinPrices = sizeMinPrices,
                sizeMaxPrices = sizeMaxPrices,
                color = color,
            )

        return salonServiceRepository.save(service)
    }

    override fun updateService(
        serviceId: ServiceId,
        salonId: SalonId,
        name: String?,
        description: String?,
        price: Money?,
        myDuration: MyDuration?,
        category: ServiceCategory?,
        displayOrder: Int?,
        requirements: List<String>?,
        sizePrices: Map<String, Money>?,
        sizeDurations: Map<String, MyDuration>?,
        minPrice: Money?,
        maxPrice: Money?,
        sizeMinPrices: Map<String, Money>?,
        sizeMaxPrices: Map<String, Money>?,
        color: String?,
    ): SalonService {
        val existingService =
            salonServiceRepository.findById(serviceId)
                ?: throw IllegalArgumentException(AnimaliaConstants.SERVICIUL_NU_A_FOST_GASIT)

        // Verify service belongs to the salon
        if (existingService.salonId != salonId) {
            throw IllegalArgumentException("Serviciul nu aparține acestui salon")
        }

        // Check if new name is unique (if name is being changed)
        if (name != null && name != existingService.name) {
            if (!isServiceNameUnique(salonId, name, serviceId)) {
                throw IllegalArgumentException("Un serviciu cu acest nume există deja în salon")
            }
        }

        // Validate pricing configuration if any pricing fields are being updated
        if (price != null || minPrice != null || maxPrice != null ||
            sizePrices != null || sizeDurations != null ||
            sizeMinPrices != null || sizeMaxPrices != null
        ) {
            val validationResult =
                pricingValidationService.validatePricingConfiguration(
                    basePrice = price ?: existingService.basePrice,
                    baseMyDuration = myDuration ?: existingService.myDuration,
                    sizePrices = sizePrices ?: existingService.sizePrices,
                    sizeDurations = sizeDurations ?: existingService.sizeDurations,
                    minPrice = minPrice ?: existingService.minPrice,
                    maxPrice = maxPrice ?: existingService.maxPrice,
                    sizeMinPrices = sizeMinPrices ?: existingService.sizeMinPrices,
                    sizeMaxPrices = sizeMaxPrices ?: existingService.sizeMaxPrices,
                )

            if (!validationResult.isValid) {
                throw IllegalArgumentException(
                    "Configurația de prețuri nu este validă: ${validationResult.getErrorMessage()}",
                )
            }
        }

        val updatedService =
            existingService.update(
                name = name,
                description = description,
                basePrice = price,
                myDuration = myDuration,
                category = category,
                displayOrder = displayOrder,
                requirements = requirements,
                sizePrices = sizePrices,
                sizeDurations = sizeDurations,
                minPrice = minPrice,
                maxPrice = maxPrice,
                sizeMinPrices = sizeMinPrices,
                sizeMaxPrices = sizeMaxPrices,
                color = color,
            )

        return salonServiceRepository.save(updatedService)
    }

    @Transactional(readOnly = true)
    override fun getServiceById(
        serviceId: ServiceId,
        salonId: SalonId,
    ): SalonService? {
        val service = salonServiceRepository.findById(serviceId)
        return if (service?.salonId == salonId) service else null
    }

    @Transactional(readOnly = true)
    override fun getServicesBySalon(
        salonId: SalonId,
        activeOnly: Boolean,
        search: String?,
        category: ServiceCategory?,
    ): List<SalonService> {
        return salonServiceRepository.findBySalonId(
            salonId = salonId,
            search = search,
            category = category,
            isActive = if (activeOnly) true else null,
        )
    }

    override fun deactivateService(
        serviceId: ServiceId,
        salonId: SalonId,
    ): SalonService {
        val service =
            salonServiceRepository.findById(serviceId)
                ?: throw IllegalArgumentException(AnimaliaConstants.SERVICIUL_NU_A_FOST_GASIT)

        // Verify service belongs to the salon
        if (service.salonId != salonId) {
            throw IllegalArgumentException("Serviciul nu aparține acestui salon")
        }

        val deactivatedService = service.deactivate()
        return salonServiceRepository.save(deactivatedService)
    }

    override fun activateService(
        serviceId: ServiceId,
        salonId: SalonId,
    ): SalonService {
        val service =
            salonServiceRepository.findById(serviceId)
                ?: throw IllegalArgumentException(AnimaliaConstants.SERVICIUL_NU_A_FOST_GASIT)

        // Verify service belongs to the salon
        if (service.salonId != salonId) {
            throw IllegalArgumentException("Serviciul nu aparține acestui salon")
        }

        val activatedService = service.activate()
        return salonServiceRepository.save(activatedService)
    }

    override fun deleteService(
        serviceId: ServiceId,
        salonId: SalonId,
    ) {
        val service =
            salonServiceRepository.findById(serviceId)
                ?: throw IllegalArgumentException(AnimaliaConstants.SERVICIUL_NU_A_FOST_GASIT)

        // Verify service belongs to the salon
        if (service.salonId != salonId) {
            throw IllegalArgumentException("Serviciul nu aparține acestui salon")
        }

        // TODO: Check if service is used in any appointments before allowing deletion
        // For now, we'll just delete it
        salonServiceRepository.deleteById(serviceId)
    }

    @Transactional(readOnly = true)
    override fun isServiceNameUnique(
        salonId: SalonId,
        name: String,
        excludeServiceId: ServiceId?,
    ): Boolean {
        val existingServices = salonServiceRepository.findBySalonId(salonId)
        return existingServices.none { service ->
            service.name.equals(name, ignoreCase = true) &&
                (excludeServiceId == null || service.id != excludeServiceId)
        }
    }

    override fun toggleServiceStatus(
        serviceId: ServiceId,
        salonId: SalonId,
    ): SalonService {
        val service =
            salonServiceRepository.findById(serviceId)
                ?: throw IllegalArgumentException(AnimaliaConstants.SERVICIUL_NU_A_FOST_GASIT)

        // Verify service belongs to the salon
        if (service.salonId != salonId) {
            throw IllegalArgumentException("Serviciul nu aparține acestui salon")
        }

        val toggledService =
            if (service.isActive) {
                service.deactivate()
            } else {
                service.activate()
            }

        return salonServiceRepository.save(toggledService)
    }

    override fun duplicateService(
        serviceId: ServiceId,
        salonId: SalonId,
    ): SalonService {
        val originalService =
            salonServiceRepository.findById(serviceId)
                ?: throw IllegalArgumentException(AnimaliaConstants.SERVICIUL_NU_A_FOST_GASIT)

        // Verify service belongs to the salon
        if (originalService.salonId != salonId) {
            throw IllegalArgumentException("Serviciul nu aparține acestui salon")
        }

        // Generate unique name for the duplicate
        val baseName = originalService.name
        var duplicateName = "$baseName - Copie"
        var counter = 1

        while (!isServiceNameUnique(salonId, duplicateName)) {
            counter++
            duplicateName = "$baseName - Copie $counter"
        }

        // Create duplicate service
        val duplicateService =
            SalonService.create(
                salonId = salonId,
                name = duplicateName,
                description = originalService.description,
                basePrice = originalService.basePrice,
                myDuration = originalService.myDuration,
                category = originalService.category,
                displayOrder = originalService.displayOrder,
                requirements = originalService.requirements,
                // Preserve pricing configuration
                sizePrices = originalService.sizePrices,
                sizeDurations = originalService.sizeDurations,
                minPrice = originalService.minPrice,
                maxPrice = originalService.maxPrice,
                sizeMinPrices = originalService.sizeMinPrices,
                sizeMaxPrices = originalService.sizeMaxPrices,
                color = originalService.color,
            )

        return salonServiceRepository.save(duplicateService)
    }
}
