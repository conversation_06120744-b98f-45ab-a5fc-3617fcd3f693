package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime

/**
 * Command to create a new block time
 */
data class CreateBlockTimeCommand(
    val salonId: SalonId,
    val startTime: ZonedDateTime,
    val endTime: ZonedDateTime,
    val reason: BlockReason,
    val customReason: String? = null,
    val staffIds: Set<StaffId>,
    val isRecurring: Boolean = false,
    val recurrencePattern: RecurrencePattern? = null,
    val notes: String? = null,
    val createdBy: UserId,
    val allowConflicts: Boolean = false, // Allow creation despite conflicts
) {
    init {
        require(staffIds.isNotEmpty()) { "At least one staff member must be specified" }
        require(endTime.isAfter(startTime)) { "End time must be after start time" }
        if (isRecurring) {
            require(recurrencePattern != null) { "Recurrence pattern is required for recurring blocks" }
        }
        if (reason == BlockReason.ALTELE) {
            require(!customReason.isNullOrBlank()) { "Custom reason is required when reason is 'Altele'" }
        }
    }
}

/**
 * Command to create block times for multiple consecutive days
 */
data class CreateMultiDayBlockTimeCommand(
    val salonId: SalonId,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val reason: BlockReason,
    val customReason: String? = null,
    val staffIds: Set<StaffId>,
    val notes: String? = null,
    val createdBy: UserId,
    val allowConflicts: Boolean = true, // Multi-day blocks typically allow conflicts
) {
    init {
        require(staffIds.isNotEmpty()) { "At least one staff member must be specified" }
        require(!endDate.isBefore(startDate)) { "End date must be on or after start date" }
        require(endTime.isAfter(startTime)) { "End time must be after start time" }
        if (reason == BlockReason.ALTELE) {
            require(!customReason.isNullOrBlank()) { "Custom reason is required when reason is 'Altele'" }
        }
    }
}

/**
 * Command to update an existing block time
 */
data class UpdateBlockTimeCommand(
    val salonId: SalonId,
    val blockId: BlockTimeId,
    val startTime: ZonedDateTime? = null,
    val endTime: ZonedDateTime? = null,
    val reason: BlockReason? = null,
    val customReason: String? = null,
    val staffIds: Set<StaffId>? = null,
    val notes: String? = null,
    val updatedBy: UserId,
) {
    init {
        if (startTime != null && endTime != null) {
            require(endTime.isAfter(startTime)) { "End time must be after start time" }
        }
        if (staffIds != null) {
            require(staffIds.isNotEmpty()) { "At least one staff member must be specified" }
        }
        if (reason == BlockReason.ALTELE) {
            require(!customReason.isNullOrBlank()) { "Custom reason is required when reason is 'Altele'" }
        }
    }
}

/**
 * Command to delete (cancel) a block time
 */
data class DeleteBlockTimeCommand(
    val salonId: SalonId,
    val blockId: BlockTimeId,
    val reason: String? = null,
    val notifyStaff: Boolean = true,
    val deletedBy: UserId,
)

/**
 * Command for bulk block time operations
 */
data class BulkBlockTimeOperationsCommand(
    val salonId: SalonId,
    val operation: BulkOperation,
    val blocks: List<BulkBlockTimeItem>,
    val performedBy: UserId,
) {
    init {
        require(blocks.isNotEmpty()) { "At least one block must be specified" }
        require(blocks.size <= 100) { "Cannot process more than 100 blocks at once" }
    }
}

/**
 * Individual item for bulk operations
 */
data class BulkBlockTimeItem(
    val blockId: BlockTimeId? = null, // For update/delete operations
    val startTime: ZonedDateTime? = null,
    val endTime: ZonedDateTime? = null,
    val reason: BlockReason? = null,
    val customReason: String? = null,
    val staffIds: Set<StaffId>? = null,
    val notes: String? = null,
) {
    fun toCreateCommand(
        salonId: SalonId,
        createdBy: UserId,
        allowConflicts: Boolean = false,
    ): CreateBlockTimeCommand? {
        return if (startTime != null && endTime != null && reason != null && staffIds != null) {
            CreateBlockTimeCommand(
                salonId = salonId,
                startTime = startTime,
                endTime = endTime,
                reason = reason,
                customReason = customReason,
                staffIds = staffIds,
                notes = notes,
                createdBy = createdBy,
                allowConflicts = allowConflicts,
            )
        } else {
            null
        }
    }

    fun toUpdateCommand(
        salonId: SalonId,
        updatedBy: UserId,
    ): UpdateBlockTimeCommand? {
        return if (blockId != null) {
            UpdateBlockTimeCommand(
                salonId = salonId,
                blockId = blockId,
                startTime = startTime,
                endTime = endTime,
                reason = reason,
                customReason = customReason,
                staffIds = staffIds,
                notes = notes,
                updatedBy = updatedBy,
            )
        } else {
            null
        }
    }

    fun toDeleteCommand(
        salonId: SalonId,
        deletedBy: UserId,
    ): DeleteBlockTimeCommand? {
        return if (blockId != null) {
            DeleteBlockTimeCommand(
                salonId = salonId,
                blockId = blockId,
                deletedBy = deletedBy,
            )
        } else {
            null
        }
    }
}

/**
 * Types of bulk operations
 */
enum class BulkOperation(val displayName: String) {
    CREATE("Create"),
    UPDATE("Update"),
    DELETE("Delete"),
    ;

    companion object {
        fun fromString(value: String): BulkOperation {
            return values().find { it.name.equals(value, ignoreCase = true) }
                ?: throw IllegalArgumentException("Invalid bulk operation: $value")
        }
    }
}
