package ro.animaliaprogramari.animalia.application.usecase

import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.command.CreateSmsReminderTimingCommand
import ro.animaliaprogramari.animalia.application.command.DeleteSmsReminderTimingCommand
import ro.animaliaprogramari.animalia.application.command.UpdateSmsReminderTimingCommand
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsReminderTiming
import ro.animaliaprogramari.animalia.domain.model.SmsReminderTimingId
import ro.animaliaprogramari.animalia.domain.port.outbound.SmsReminderTimingRepository

@Service
class SmsReminderTimingUseCase(
    private val smsReminderTimingRepository: SmsReminderTimingRepository
) {

    fun createTiming(command: CreateSmsReminderTimingCommand): SmsReminderTiming {
        val timing = SmsReminderTiming.create(
            salonId = command.salonId,
            hoursBefore = command.hoursBefore
        ).setEnabled(command.isEnabled)

        return smsReminderTimingRepository.save(timing)
    }

    fun updateTiming(command: UpdateSmsReminderTimingCommand): SmsReminderTiming {
        val timing = smsReminderTimingRepository.findById(SmsReminderTimingId.of(command.timingId))
            ?: throw IllegalArgumentException("Timing not found")

        if (timing.salonId != command.salonId) {
            throw IllegalArgumentException("Timing does not belong to this salon")
        }

        val updatedTiming = timing
            .updateHours(command.hoursBefore)
            .setEnabled(command.isEnabled)

        return smsReminderTimingRepository.save(updatedTiming)
    }

    fun getTimingsBySalon(salonId: SalonId): List<SmsReminderTiming> {
        return smsReminderTimingRepository.findBySalonId(salonId)
    }

    fun deleteTiming(command: DeleteSmsReminderTimingCommand)= smsReminderTimingRepository.deleteById(SmsReminderTimingId.of(command.timingId))
}
