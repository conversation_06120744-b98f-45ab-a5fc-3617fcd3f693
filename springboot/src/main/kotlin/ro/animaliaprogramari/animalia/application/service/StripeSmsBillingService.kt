package ro.animaliaprogramari.animalia.application.service

import com.stripe.exception.StripeException
import com.stripe.StripeClient
import com.stripe.param.v2.billing.MeterEventCreateParams
import com.stripe.param.CustomerCreateParams
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.billing.StripeSmsUsageRecord
import ro.animaliaprogramari.animalia.infrastructure.billing.StripeProperties
import java.time.Instant

/**
 * Service responsible for reporting metered SMS usage to Stripe.
 *
 * This is independent from your main subscription system.
 * We persist Stripe metered subscription identifiers in SalonSettings via StripeMeteredBillingSettingsService.
 */
@Service
class StripeSmsBillingService(
    private val meteredSettingsService: StripeMeteredBillingSettingsService,
    private val stripeProperties: StripeProperties,
    private val stripeSmsUsageRecordRepository: ro.animaliaprogramari.animalia.application.port.outbound.StripeSmsUsageRecordRepository,
) {
    private val logger = LoggerFactory.getLogger(StripeSmsBillingService::class.java)

    // Create StripeClient lazily to ensure API key is properly set
    private val stripeClient: StripeClient by lazy {
        if (stripeProperties.secretKey.isBlank()) {
            throw IllegalStateException("Stripe secret key is not configured")
        }

        // Validate that the API key supports v2 APIs
        if (!stripeProperties.secretKey.startsWith("sk_test_") && !stripeProperties.secretKey.startsWith("sk_live_")) {
            throw IllegalStateException("Invalid Stripe API key format. v2 APIs require standard secret keys (sk_test_* or sk_live_*)")
        }

        logger.info("Initializing Stripe v2 client with API key: ${stripeProperties.secretKey.take(12)}...")
        StripeClient.builder().setApiKey(stripeProperties.secretKey).build()
    }

    data class StripeUsageContext(
        val customerId: String?,
        val meterEventName: String?, // New field for meter event name
    )

    fun getStripeUsageContext(salonId: SalonId): StripeUsageContext? {
        val s = meteredSettingsService.getSettings(salonId)
        if (s.meterEventName.isNullOrBlank()) {
            logger.info("No Stripe metered subscription item configured for salon ${salonId.value}")
            return null
        }
        return StripeUsageContext(
            customerId = s.customerId,
            meterEventName = s.meterEventName, // You'll need to add this to your settings
        )
    }

    /**
     * Report N SMS units as metered usage to Stripe for the given salon.
     * Safe to call even when Stripe is not configured or IDs are missing.
     */
    fun reportSmsUsage(salonId: SalonId, smsUnits: Int, timestampSeconds: Long = (System.currentTimeMillis() / 1000)) {
        if (smsUnits <= 0) return

        val ctx = getStripeUsageContext(salonId) ?: return
        val meterEventName = ctx.meterEventName
        if (meterEventName.isNullOrBlank()) {
            logger.warn("Missing stripe meterEventName for salon ${salonId.value}; cannot report usage")
            return
        }

        try {
            // Use the new v2 billing meter events API
            val meterEventName = "sent_sms" // Default event name
            val customerId = ctx.customerId

            if (customerId.isNullOrBlank()) {
                logger.warn("Missing stripe customer_id for salon ${salonId.value}; cannot report meter event")
                return
            }

            // Validate that customerId is a proper Stripe customer ID
            if (!customerId.startsWith("cus_")) {
                logger.error("Invalid Stripe customer ID format for salon ${salonId.value}: '$customerId'. Expected format: 'cus_xxxxxxxxxx'")
                return
            }

            val params = MeterEventCreateParams.builder()
                .setEventName("sent_sms")
                .setTimestamp(Instant.ofEpochSecond(timestampSeconds))
                .putPayload("stripe_customer_id", customerId)
                .putPayload("value", smsUnits.toString())
                .build()

            stripeClient.v2().billing().meterEvents().create(params)

            // Save usage record to database for reporting
            val usageRecord = StripeSmsUsageRecord.create(
                salonId = salonId,
                smsUnits = smsUnits,
                stripeCustomerId = customerId,
                meterEventName = "sent_sms",
                stripeTimestamp = timestampSeconds
            )
            stripeSmsUsageRecordRepository.save(usageRecord)

            logger.info("Reported $smsUnits SMS usage units to Stripe for salon ${salonId.value} using meter event '$meterEventName'")
        } catch (e: StripeException) {
            when {
                e.message?.contains("not supported for V2 authentication") == true -> {
                    logger.error("Stripe API key does not support v2 APIs. Please generate a new standard secret key from Stripe Dashboard → Developers → API keys", e)
                }
                e.message?.contains("unauthorized") == true -> {
                    logger.error("Stripe API key is invalid or unauthorized for v2 APIs. Please check your API key configuration", e)
                }
                else -> {
                    logger.error("Failed to report SMS usage to Stripe for salon ${salonId.value}", e)
                }
            }
        } catch (e: Exception) {
            logger.error("Unexpected error while reporting SMS usage to Stripe for salon ${salonId.value}", e)
        }
    }

    /**
     * Helper method to create a Stripe customer for a salon.
     * This should be called when setting up billing for a salon.
     */
    fun createStripeCustomer(salonId: SalonId, email: String? = null, name: String? = null): String? {
        return try {
            val params = CustomerCreateParams.builder()
                .apply {
                    if (email != null) setEmail(email)
                    if (name != null) setName(name)
                    // Use salon ID as metadata for reference
                    putMetadata("salon_id", salonId.value)
                }
                .build()

            val customer = stripeClient.customers().create(params)
            logger.info("Created Stripe customer ${customer.id} for salon ${salonId.value}")
            customer.id
        } catch (e: StripeException) {
            logger.error("Failed to create Stripe customer for salon ${salonId.value}", e)
            null
        }
    }
}

