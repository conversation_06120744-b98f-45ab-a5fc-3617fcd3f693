package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.command.ConfirmPhoneCommand
import ro.animaliaprogramari.animalia.application.command.ResendPhoneVerificationCommand
import ro.animaliaprogramari.animalia.application.port.inbound.PhoneVerificationCodeResult
import ro.animaliaprogramari.animalia.application.port.inbound.PhoneVerificationResult
import ro.animaliaprogramari.animalia.application.port.inbound.PhoneVerificationUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.PhoneVerificationCodeRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SmsRateLimitRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SmsSender
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.domain.exception.RateLimitExceededException
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.PhoneVerificationCode
import ro.animaliaprogramari.animalia.domain.model.notification.SmsRateLimit
import ro.animaliaprogramari.animalia.infrastructure.config.SmsNotificationProperties

/**
 * Implementation of phone verification use cases
 */
@Service
class PhoneVerificationUseCaseImpl(
    private val phoneVerificationCodeRepository: PhoneVerificationCodeRepository,
    private val smsRateLimitRepository: SmsRateLimitRepository,
    @Qualifier("smsoSmsSender") private val smsSender: SmsSender,
    private val smsNotificationProperties: SmsNotificationProperties,
    private val userRepository: UserRepository
) : PhoneVerificationUseCase {

    private val logger = LoggerFactory.getLogger(PhoneVerificationUseCaseImpl::class.java)

    override fun sendVerificationCode(command: ResendPhoneVerificationCommand): PhoneVerificationCodeResult {
        return try {
            logger.info("Sending verification code to: ${command.phoneNumber.value}")

            // 1. Check rate limiting
            val rateLimit = checkAndUpdateRateLimit(command.phoneNumber)
            if (!rateLimit.canSendSms()) {
                val remainingTime = rateLimit.getTimeUntilNextWindow()
                logger.warn("Rate limit exceeded for: ${command.phoneNumber.value}")
                throw RateLimitExceededException(
                    "Ați depășit limita de SMS-uri. Încercați din nou în ${remainingTime} minute."
                )
            }

            // 2. Check if this is a test phone number
            if (isTestPhoneNumber(command.phoneNumber.value)) {
                logger.info("Test phone number detected, skipping SMS sending: ${command.phoneNumber.value}")

                // For test numbers, we don't need to save verification codes or send SMS
                // The hardcoded code "123456" will be accepted during verification
                return PhoneVerificationCodeResult(
                    success = true,
                    message = "Codul de verificare a fost trimis cu succes",
                    phoneNumber = command.phoneNumber.value,
                    expiresIn = 300, // 5 minutes
                    canResendAfter = 60, // 1 minute
                    remainingAttempts = 10 // Dummy value for test numbers
                )
            }

            // 3. Invalidate existing verification codes for this phone number (if any)
            phoneVerificationCodeRepository.invalidateAllForPhoneNumber(command.phoneNumber)

            // 4. Generate new verification code
            val verificationCode = PhoneVerificationCode.create(
                phoneNumber = command.phoneNumber,
                expiryMinutes = 5
            )

            // 5. Save verification code
            val savedCode = phoneVerificationCodeRepository.save(verificationCode)

            // 6. Send SMS
            val message = createVerificationSmsMessage(savedCode.code)
            smsSender.sendMessage(command.phoneNumber.toInternationalFormat(), message)

            // 7. Update rate limit
            val updatedRateLimit = rateLimit.recordSmsSent()
            smsRateLimitRepository.save(updatedRateLimit)

            logger.info("Verification code sent successfully to: ${command.phoneNumber.value}")

            PhoneVerificationCodeResult(
                success = true,
                message = "Codul de verificare a fost trimis cu succes",
                phoneNumber = command.phoneNumber.value,
                expiresIn = 300, // 5 minutes
                canResendAfter = 60, // 1 minute
                remainingAttempts = updatedRateLimit.getRemainingCount()
            )

        } catch (e: RateLimitExceededException) {
            logger.warn("Rate limit exceeded: ${e.message}")
            PhoneVerificationCodeResult(
                success = false,
                message = e.message ?: "Ați depășit limita de SMS-uri",
                phoneNumber = command.phoneNumber.value
            )
        } catch (e: Exception) {
            logger.error("Failed to resend verification code", e)
            PhoneVerificationCodeResult(
                success = false,
                message = "Eroare la trimiterea codului de verificare. Încercați din nou.",
                phoneNumber = command.phoneNumber.value
            )
        }
    }

    override fun verifyPhoneNumber(command: ConfirmPhoneCommand): PhoneVerificationResult {
        return try {
            logger.info("Verifying phone number: ${command.phoneNumber.value} for user: ${command.userId.value}")

            // Check if this is a test phone number with hardcoded verification code
            if (isTestPhoneNumber(command.phoneNumber.value) && command.confirmationCode == "123456") {
                logger.info("Test phone number verified with hardcoded code: ${command.phoneNumber.value}")

                // For test numbers, skip database verification and proceed directly to user update
                val user = userRepository.findById(command.userId)
                if (user == null) {
                    logger.error("User not found: ${command.userId.value}")
                    return PhoneVerificationResult(
                        success = false,
                        message = "Utilizatorul nu a fost găsit.",
                        phoneNumber = command.phoneNumber.value
                    )
                }

                val updatedUser = user.updateInfo(phoneNumber = command.phoneNumber.value)
                userRepository.save(updatedUser)

                logger.info("Phone number verified and updated successfully for user: ${command.userId.value}")

                return PhoneVerificationResult(
                    success = true,
                    message = "Numărul de telefon a fost verificat și actualizat cu succes.",
                    phoneNumber = command.phoneNumber.value,
                    verified = true
                )
            }

            // 1. Find the verification code (for real phone numbers)
            val verificationCode = phoneVerificationCodeRepository.findValidCodeForPhoneNumber(command.phoneNumber)
            if (verificationCode == null) {
                logger.warn("No valid verification code found for phone: ${command.phoneNumber.value}")
                return PhoneVerificationResult(
                    success = false,
                    message = "Codul de verificare nu a fost găsit sau a expirat. Solicitați un cod nou.",
                    phoneNumber = command.phoneNumber.value
                )
            }

            // 2. Check if code matches
            if (verificationCode.code != command.confirmationCode) {
                logger.warn("Invalid verification code for phone: ${command.phoneNumber.value}")
                return PhoneVerificationResult(
                    success = false,
                    message = "Codul de verificare este incorect. Verificați și încercați din nou.",
                    phoneNumber = command.phoneNumber.value
                )
            }

            // 3. Check if code is expired
            if (verificationCode.isExpired()) {
                logger.warn("Verification code expired for phone: ${command.phoneNumber.value}")
                return PhoneVerificationResult(
                    success = false,
                    message = "Codul de verificare a expirat. Solicitați un cod nou.",
                    phoneNumber = command.phoneNumber.value
                )
            }

            // 4. Mark code as used
            phoneVerificationCodeRepository.markAsUsed(verificationCode)

            // 5. Update user's phone number
            val user = userRepository.findById(command.userId)
            if (user == null) {
                logger.error("User not found: ${command.userId.value}")
                return PhoneVerificationResult(
                    success = false,
                    message = "Utilizatorul nu a fost găsit.",
                    phoneNumber = command.phoneNumber.value
                )
            }

            val updatedUser = user.updateInfo(phoneNumber = command.phoneNumber.value)
            userRepository.save(updatedUser)

            logger.info("Phone number verified and updated successfully for user: ${command.userId.value}")

            PhoneVerificationResult(
                success = true,
                message = "Numărul de telefon a fost verificat și actualizat cu succes.",
                phoneNumber = command.phoneNumber.value,
                verified = true
            )

        } catch (e: Exception) {
            logger.error("Failed to verify phone number", e)
            PhoneVerificationResult(
                success = false,
                message = "Eroare la verificarea numărului de telefon. Încercați din nou.",
                phoneNumber = command.phoneNumber.value
            )
        }
    }

    private fun checkAndUpdateRateLimit(phoneNumber: PhoneNumber): SmsRateLimit {
        val existingRateLimit = smsRateLimitRepository.findByPhoneNumber(phoneNumber)

        return existingRateLimit ?: SmsRateLimit.create(
            phoneNumber = phoneNumber,
            maxSmsPerWindow = smsNotificationProperties.rateLimitPerHour,
            windowDurationMinutes = smsNotificationProperties.rateLimitWindowMinutes
        )
    }

    private fun createVerificationSmsMessage(code: String): String {
        return "Codul dvs. de verificare Animalia este: $code. Codul expiră în 5 minute. Nu împărtășiți acest cod cu nimeni."
    }

    /**
     * Check if the phone number is a test phone number
     * Test phone numbers are used for development and testing purposes
     */
    private fun isTestPhoneNumber(phoneNumber: String): Boolean {
        val testNumbers = setOf(
            "+40799999999",
            "+40799999998",
            "+40799999997",
            "+40799999996",
            "+40799999995",
            "+40788888888",
            "+40731446856",
            "+40731446891",
            "+40731446898",
            "+40799999999",
            "+40731444444",
            "+40731446897",
            "+40731446896",
        )
        return testNumbers.contains(phoneNumber)
    }
}

