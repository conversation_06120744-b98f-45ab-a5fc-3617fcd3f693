package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.port.inbound.ReportsUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.math.abs

/**
 * Service implementation for generating business reports
 */
@Service
class ReportsService(
    private val appointmentRepository: AppointmentRepository,
    private val clientRepository: ClientRepository,
    private val petRepository: PetRepository,
    private val salonServiceRepository: SalonServiceRepository,
    private val staffRepository: StaffRepository,
    private val breedMappingService: BreedMappingService,
) : ReportsUseCase {

    private val logger = LoggerFactory.getLogger(ReportsService::class.java)

    override fun getPetReport(query: GetPetReportQuery): PetReportData {
        logger.info("Generating pet report for salon: ${query.salonId.value}, period: ${query.startDate} to ${query.endDate}")

        // Get all appointments in the date range to find pets that were serviced
        val appointments = appointmentRepository.findBySalonIdWithFilters(
            salonId = query.salonId,
            startDate = query.startDate,
            endDate = query.endDate,
            status = AppointmentStatus.COMPLETED
        )

        // Get unique pets from appointments
        val petIds = appointments.map { it.petId }.distinct()
        val pets = petIds.mapNotNull { petId ->
            try {
                petRepository.findById(petId)
            } catch (e: Exception) {
                logger.warn("Could not find pet with ID: ${petId.value}")
                null
            }
        }

        // Generate size breakdown using breed-based sizing
        val sizeBreakdown = pets.groupBy { pet ->
            val breedSize = breedMappingService.getBreedSize(pet.breed)
            breedMappingService.getSizeDisplayName(breedSize)
        }.map { (sizeDisplayName, petsInSize) ->
            BarChartItem(
                label = sizeDisplayName,
                value = petsInSize.size.toDouble(),
                metadata = mapOf(
                    "description" to when (sizeDisplayName) {
                        "Mic" -> "Câini sub 10kg, pisici"
                        "Mediu" -> "Câini 10-25kg"
                        "Mare" -> "Câini peste 25kg"
                        else -> "Mărime necunoscută"
                    }
                )
            )
        }.sortedByDescending { it.value }

        // Generate breed breakdown using actual breed names
        val breedBreakdown = pets.groupBy { pet ->
            val normalizedBreed = breedMappingService.normalizeBreedName(pet.breed)
            normalizedBreed ?: if (pet.breed.isNullOrBlank()) "Necunoscut" else pet.breed
        }.map { (breedName, petsInBreed) ->
            BarChartItem(
                label = breedName,
                value = petsInBreed.size.toDouble()
            )
        }.sortedByDescending { it.value }

        return PetReportData(
            title = "Raport Animale pe Mărime și Rasă",
            startDate = query.startDate,
            endDate = query.endDate,
            sizeBreakdown = sizeBreakdown,
            breedBreakdown = breedBreakdown,
            totalPets = pets.size
        )
    }

    override fun getServiceReport(query: GetServiceReportQuery): ServiceReportData {
        logger.info("Generating service report for salon: ${query.salonId.value}, period: ${query.startDate} to ${query.endDate}")

        // Get completed appointments in the date range
        val appointments = appointmentRepository.findBySalonIdWithFilters(
            salonId = query.salonId,
            startDate = query.startDate,
            endDate = query.endDate,
            status = AppointmentStatus.COMPLETED
        )

        // Get salon services for mapping
        val salonServices = try {
            salonServiceRepository.findActiveBySalonId(query.salonId)
        } catch (e: Exception) {
            logger.warn("Could not load salon services for salon: ${query.salonId.value}")
            emptyList()
        }

        val serviceMap = salonServices.associateBy { it.id }

        // Count service requests
        val serviceRequestCounts = mutableMapOf<String, Int>()
        val serviceRevenueTotals = mutableMapOf<String, Double>()

        appointments.forEach { appointment ->
            appointment.serviceIds.forEach { serviceId ->
                val service = serviceMap[serviceId]
                val serviceName = service?.name ?: "Serviciu necunoscut"
                val servicePrice = service?.basePrice?.amount?.toDouble() ?: 0.0

                serviceRequestCounts[serviceName] = serviceRequestCounts.getOrDefault(serviceName, 0) + 1
                serviceRevenueTotals[serviceName] = serviceRevenueTotals.getOrDefault(serviceName, 0.0) + servicePrice
            }
        }

        // Convert to chart items
        val serviceRequests = serviceRequestCounts.map { (serviceName, count) ->
            BarChartItem(
                label = serviceName,
                value = count.toDouble()
            )
        }.sortedByDescending { it.value }

        val serviceRevenue = serviceRevenueTotals.map { (serviceName, revenue) ->
            BarChartItem(
                label = serviceName,
                value = revenue
            )
        }.sortedByDescending { it.value }

        return ServiceReportData(
            title = "Raport Servicii Solicitate",
            startDate = query.startDate,
            endDate = query.endDate,
            serviceRequests = serviceRequests,
            serviceRevenue = serviceRevenue,
            totalServices = serviceRequestCounts.values.sum(),
            totalRevenue = serviceRevenueTotals.values.sum()
        )
    }

    override fun getClientReport(query: GetClientReportQuery): ClientReportData {
        logger.info("Generating client report for salon: ${query.salonId.value}, period: ${query.startDate} to ${query.endDate}")

        // Get completed appointments in the date range
        val appointments = appointmentRepository.findBySalonIdWithFilters(
            salonId = query.salonId,
            startDate = query.startDate,
            endDate = query.endDate,
            status = AppointmentStatus.COMPLETED
        )

        // Calculate client spending
        val clientSpending = mutableMapOf<ClientId, Double>()
        appointments.forEach { appointment ->
            val currentSpending = clientSpending.getOrDefault(appointment.clientId, 0.0)
            clientSpending[appointment.clientId] = currentSpending + appointment.totalPrice.amount.toDouble()
        }

        // Get client details and create top clients list
        val topClients = clientSpending.entries
            .sortedByDescending { it.value }
            .take(8)
            .mapNotNull { (clientId, spending) ->
                try {
                    val client = clientRepository.findById(clientId)
                    if (client != null) {
                        BarChartItem(
                            label = client.name,
                            value = spending
                        )
                    } else {
                        logger.warn("Could not find client with ID: ${clientId.value}")
                        null
                    }
                } catch (e: Exception) {
                    logger.warn("Could not find client with ID: ${clientId.value}")
                    null
                }
            }

        // Calculate client metrics
        val allClients = try {
            clientRepository.findBySalonId(query.salonId)
        } catch (e: Exception) {
            logger.warn("Could not load clients for salon: ${query.salonId.value}")
            emptyList()
        }

        val totalClients = allClients.size
        val averageSpending = if (clientSpending.isNotEmpty()) {
            clientSpending.values.average()
        } else 0.0

        // Calculate new vs returning clients (simplified logic)
        val clientsWithAppointments = clientSpending.keys
        val newClients = clientsWithAppointments.count { clientId ->
            // Consider a client "new" if they have only appointments in this period
            val allClientAppointments = appointmentRepository.findBySalonIdWithFilters(
                salonId = query.salonId,
                clientId = clientId
            )
            allClientAppointments.all { it.appointmentDate >= query.startDate }
        }

        val returningClients = clientsWithAppointments.size - newClients

        return ClientReportData(
            title = "Top Clienți",
            startDate = query.startDate,
            endDate = query.endDate,
            topClients = topClients,
            totalClients = totalClients,
            averageSpending = averageSpending,
            newClients = newClients,
            returningClients = returningClients
        )
    }

    override fun getStaffPerformanceReport(query: GetStaffPerformanceReportQuery): StaffPerformanceReportData {
        logger.info("Generating staff performance report for salon: ${query.salonId.value}, staff: ${query.staffId.value}, period: ${query.startDate} to ${query.endDate}")

        // Get staff details
        val staff = try {
            staffRepository.findById(query.staffId)
                ?: throw IllegalArgumentException("Staff member not found: ${query.staffId.value}")
        } catch (e: Exception) {
            throw IllegalArgumentException("Staff member not found: ${query.staffId.value}")
        }

        // Get staff appointments in the date range
        val allAppointments = appointmentRepository.findBySalonIdWithFilters(
            salonId = query.salonId,
            startDate = query.startDate,
            endDate = query.endDate,
            staffId = query.staffId
        )

        val completedAppointments = allAppointments.filter { it.status == AppointmentStatus.COMPLETED }
        val cancelledAppointments = allAppointments.filter { it.status == AppointmentStatus.CANCELLED }

        // Calculate metrics
        val totalRevenue = completedAppointments.sumOf { it.totalPrice.amount.toDouble() }
        val averageRating = 4.5 // Simplified - would need rating system
        val utilizationRate = 0.75 // Simplified calculation

        // Service breakdown
        val salonServices = try {
            salonServiceRepository.findActiveBySalonId(query.salonId)
        } catch (e: Exception) {
            emptyList()
        }
        val serviceMap = salonServices.associateBy { it.id }

        val serviceCounts = mutableMapOf<String, Int>()
        completedAppointments.forEach { appointment ->
            appointment.serviceIds.forEach { serviceId ->
                val serviceName = serviceMap[serviceId]?.name ?: "Serviciu necunoscut"
                serviceCounts[serviceName] = serviceCounts.getOrDefault(serviceName, 0) + 1
            }
        }

        val serviceBreakdown = serviceCounts.map { (serviceName, count) ->
            BarChartItem(
                label = serviceName,
                value = count.toDouble()
            )
        }.sortedByDescending { it.value }

        // Revenue over time (daily)
        val revenueOverTime = generateDailyRevenue(completedAppointments, query.startDate, query.endDate)

        return StaffPerformanceReportData(
            title = "Performanță ${staff.nickname ?: staff.userId.value}",
            startDate = query.startDate,
            endDate = query.endDate,
            staffId = query.staffId.value,
            staffName = staff.nickname ?: staff.userId.value,
            totalAppointments = allAppointments.size,
            totalRevenue = totalRevenue,
            averageRating = averageRating,
            completedAppointments = completedAppointments.size,
            cancelledAppointments = cancelledAppointments.size,
            utilizationRate = utilizationRate,
            serviceBreakdown = serviceBreakdown,
            revenueOverTime = revenueOverTime
        )
    }

    override fun getRevenueReport(query: GetRevenueReportQuery): RevenueReportData {
        logger.info("Generating revenue report for salon: ${query.salonId.value}, period: ${query.startDate} to ${query.endDate}")

        // Get completed appointments in the date range
        val appointments = appointmentRepository.findBySalonIdWithFilters(
            salonId = query.salonId,
            startDate = query.startDate,
            endDate = query.endDate,
            status = AppointmentStatus.COMPLETED
        )

        // Calculate total revenue
        val totalRevenue = appointments.sumOf { it.totalPrice.amount.toDouble() }
        val daysDifference = ChronoUnit.DAYS.between(query.startDate, query.endDate) + 1
        val averageDailyRevenue = totalRevenue / daysDifference

        // Calculate growth rate (simplified - compare with previous period)
        val previousPeriodStart = query.startDate.minusDays(daysDifference)
        val previousPeriodEnd = query.startDate.minusDays(1)

        val previousAppointments = appointmentRepository.findBySalonIdWithFilters(
            salonId = query.salonId,
            startDate = previousPeriodStart,
            endDate = previousPeriodEnd,
            status = AppointmentStatus.COMPLETED
        )

        val previousRevenue = previousAppointments.sumOf { it.totalPrice.amount.toDouble() }
        val growthRate = if (previousRevenue > 0) {
            (totalRevenue - previousRevenue) / previousRevenue
        } else 0.0

        // Daily revenue breakdown
        val dailyRevenue = generateDailyRevenue(appointments, query.startDate, query.endDate)

        // Revenue by service
        val salonServices = try {
            salonServiceRepository.findActiveBySalonId(query.salonId)
        } catch (e: Exception) {
            emptyList()
        }
        val serviceMap = salonServices.associateBy { it.id }

        val serviceRevenueTotals = mutableMapOf<String, Double>()
        appointments.forEach { appointment ->
            appointment.serviceIds.forEach { serviceId ->
                val service = serviceMap[serviceId]
                val serviceName = service?.name ?: "Serviciu necunoscut"
                val servicePrice = service?.basePrice?.amount?.toDouble() ?: 0.0
                serviceRevenueTotals[serviceName] = serviceRevenueTotals.getOrDefault(serviceName, 0.0) + servicePrice
            }
        }

        val revenueByService = serviceRevenueTotals.map { (serviceName, revenue) ->
            BarChartItem(
                label = serviceName,
                value = revenue
            )
        }.sortedByDescending { it.value }

        return RevenueReportData(
            title = "Raport Venituri",
            startDate = query.startDate,
            endDate = query.endDate,
            dailyRevenue = dailyRevenue,
            totalRevenue = totalRevenue,
            averageDailyRevenue = averageDailyRevenue,
            growthRate = growthRate,
            revenueByService = revenueByService
        )
    }

    private fun generateDailyRevenue(appointments: List<Appointment>, startDate: LocalDate, endDate: LocalDate): List<LineChartPoint> {
        val dailyTotals = mutableMapOf<LocalDate, Double>()

        // Initialize all dates with 0
        var currentDate = startDate
        while (!currentDate.isAfter(endDate)) {
            dailyTotals[currentDate] = 0.0
            currentDate = currentDate.plusDays(1)
        }

        // Add actual revenue
        appointments.forEach { appointment ->
            val date = appointment.appointmentDate
            if (!date.isBefore(startDate) && !date.isAfter(endDate)) {
                dailyTotals[date] = dailyTotals.getOrDefault(date, 0.0) + appointment.totalPrice.amount.toDouble()
            }
        }

        return dailyTotals.entries
            .sortedBy { it.key }
            .map { (date, revenue) ->
                LineChartPoint(
                    date = date,
                    value = revenue
                )
            }
    }
}
