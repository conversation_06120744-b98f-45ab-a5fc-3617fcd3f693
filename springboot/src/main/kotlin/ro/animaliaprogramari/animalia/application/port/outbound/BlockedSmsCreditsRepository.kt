package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedSmsCredits
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsStatus
import ro.animaliaprogramari.animalia.domain.model.notification.BlockedCreditsSource

/**
 * Repository interface for blocked SMS credits
 */
interface BlockedSmsCreditsRepository {
    
    /**
     * Save blocked SMS credits
     */
    fun save(blockedCredits: BlockedSmsCredits): BlockedSmsCredits
    
    /**
     * Find blocked SMS credits by salon ID
     */
    fun findBySalonId(salonId: SalonId): List<BlockedSmsCredits>
    
    /**
     * Find blocked SMS credits by salon ID and status
     */
    fun findBySalonIdAndStatus(salonId: SalonId, status: BlockedCreditsStatus): List<BlockedSmsCredits>
    
    /**
     * Find blocked SMS credits by source and source ID
     */
    fun findBySourceAndSourceId(source: BlockedCreditsSource, sourceId: String): BlockedSmsCredits?
    
    /**
     * Find all blocked credits that can potentially be unlocked
     * (status = BLOCKED)
     */
    fun findAllBlocked(): List<BlockedSmsCredits>
    
    /**
     * Find blocked credits for a salon that are currently blocked
     */
    fun findBlockedBySalonId(salonId: SalonId): List<BlockedSmsCredits>
    
    /**
     * Get total blocked credits amount for a salon
     */
    fun getTotalBlockedCredits(salonId: SalonId): Int
    
    /**
     * Delete blocked SMS credits (for cleanup)
     */
    fun delete(blockedCredits: BlockedSmsCredits)
}
