package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.UpdateWorkingHoursCommand
import ro.animaliaprogramari.animalia.application.query.GetWorkingHoursQuery
import ro.animaliaprogramari.animalia.domain.model.WorkingHoursSettings

/**
 * Use case port for working hours management operations
 * This is an inbound port that defines the business operations for working hours
 */
interface WorkingHoursManagementUseCase {
    /**
     * Get working hours settings for a salon
     */
    fun getWorkingHours(query: GetWorkingHoursQuery): WorkingHoursSettings

    /**
     * Update working hours settings for a salon
     */
    fun updateWorkingHours(command: UpdateWorkingHoursCommand): WorkingHoursSettings
}
