package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SalonService
import ro.animaliaprogramari.animalia.domain.model.ServiceCategory
import ro.animaliaprogramari.animalia.domain.model.ServiceId

/**
 * Outbound port for grooming service persistence
 */
interface SalonServiceRepository {
    /**
     * Save a grooming service
     */
    fun save(service: SalonService): SalonService

    /**
     * Find a service by ID
     */
    fun findById(id: ServiceId): SalonService?

    /**
     * Find services by IDs
     */
    fun findByIds(ids: List<ServiceId>): List<SalonService>

    /**
     * Find all services for a salon with optional filtering
     */
    fun findBySalonId(
        salonId: SalonId,
        search: String? = null,
        category: ServiceCategory? = null,
        isActive: Boolean? = null,
        limit: Int? = null,
        offset: Int? = null,
    ): List<SalonService>

    /**
     * Find active services for a salon
     */
    fun findActiveBySalonId(salonId: SalonId): List<SalonService>

    /**
     * Find all services with optional filtering
     */
    fun findAll(
        search: String? = null,
        category: ServiceCategory? = null,
        isActive: Boolean? = null,
        limit: Int? = null,
        offset: Int? = null,
    ): List<SalonService>

    /**
     * Find services by category
     */
    fun findByCategory(category: ServiceCategory): List<SalonService>

    /**
     * Find active services
     */
    fun findActiveServices(): List<SalonService>

    /**
     * Check if a service exists by ID
     */
    fun existsById(id: ServiceId): Boolean

    /**
     * Delete a service by ID
     */
    fun deleteById(id: ServiceId): Boolean

    /**
     * Check if service exists by ID and salon ID
     */
    fun existsByIdAndSalonId(
        serviceId: ServiceId,
        salonId: SalonId,
    ): Boolean

    /**
     * Count total services with optional filtering
     */
    fun count(
        search: String? = null,
        category: ServiceCategory? = null,
        isActive: Boolean? = null,
    ): Long

    /**
     * Count services for a salon
     */
    fun countBySalonId(
        salonId: SalonId,
        search: String? = null,
        category: ServiceCategory? = null,
        isActive: Boolean? = null,
    ): Long
}
