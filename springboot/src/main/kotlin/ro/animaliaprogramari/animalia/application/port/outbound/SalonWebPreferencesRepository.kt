package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Repository port for salon web preferences persistence operations
 * This is an outbound port that defines the persistence contract for salon web preferences
 */
interface SalonWebPreferencesRepository {
    /**
     * Save salon web preferences
     */
    fun save(preferences: SalonWebPreferences): SalonWebPreferences

    /**
     * Find web preferences by ID
     */
    fun findById(id: SalonWebPreferencesId): SalonWebPreferences?

    /**
     * Find web preferences by salon ID
     */
    fun findBySalonId(salonId: SalonId): SalonWebPreferences?

    /**
     * Find active web preferences by salon ID
     */
    fun findActiveBySalonId(salonId: SalonId): SalonWebPreferences?

    /**
     * Check if web preferences exist for a salon
     */
    fun existsBySalonId(salonId: SalonId): Boolean

    /**
     * Check if web preferences exist by ID
     */
    fun existsById(id: SalonWebPreferencesId): Boolean

    /**
     * Delete web preferences by salon ID
     */
    fun deleteBySalonId(salonId: SalonId)

    /**
     * Delete web preferences by ID
     */
    fun deleteById(id: SalonWebPreferencesId)

    /**
     * Find all web preferences with optional filtering
     */
    fun findAll(
        isActive: Boolean? = null,
        limit: Int? = null,
        offset: Int? = null,
    ): List<SalonWebPreferences>

    /**
     * Count total web preferences with optional filtering
     */
    fun count(isActive: Boolean? = null): Long
}
