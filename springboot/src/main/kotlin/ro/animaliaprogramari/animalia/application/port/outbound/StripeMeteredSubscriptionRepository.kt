package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.billing.StripeMeteredSubscription

/**
 * Port for persisting Stripe metered subscription linkage per salon
 */
interface StripeMeteredSubscriptionRepository {
    fun findBySalonId(salonId: SalonId): StripeMeteredSubscription?
    fun save(record: StripeMeteredSubscription): StripeMeteredSubscription
}

