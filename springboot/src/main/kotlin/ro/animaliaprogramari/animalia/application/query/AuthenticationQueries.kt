package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Query to get user by ID
 */
data class GetUserByIdQuery(
    val userId: UserId,
)

/**
 * Query to get user by Firebase UID
 */
data class GetUserByFirebaseUidQuery(
    val firebaseUid: String,
)

/**
 * Query to get user by email
 */
data class GetUserByEmailQuery(
    val email: Email,
)

/**
 * Query to search users
 */
data class SearchUsersQuery(
    val searchTerm: String? = null,
    val role: UserRole? = null,
    val isActive: Boolean? = null,
    val limit: Int? = null,
    val offset: Int? = null,
)

/**
 * Query to get all users with pagination
 */
data class GetAllUsersQuery(
    val role: UserRole? = null,
    val isActive: Boolean? = null,
    val limit: Int? = null,
    val offset: Int? = null,
)
