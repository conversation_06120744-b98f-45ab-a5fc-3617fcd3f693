package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.notification.SmsRateLimit
import ro.animaliaprogramari.animalia.domain.model.notification.SmsRateLimitId

/**
 * Outbound port for SMS rate limit persistence
 */
interface SmsRateLimitRepository {
    /**
     * Save SMS rate limit
     */
    fun save(smsRateLimit: SmsRateLimit): SmsRateLimit

    /**
     * Find rate limit by phone number
     */
    fun findByPhoneNumber(phoneNumber: PhoneNumber): SmsRateLimit?

    /**
     * Find rate limit by ID
     */
    fun findById(id: SmsRateLimitId): SmsRateLimit?

    /**
     * Delete expired rate limit records (for cleanup)
     */
    fun deleteExpiredRecords()
}
