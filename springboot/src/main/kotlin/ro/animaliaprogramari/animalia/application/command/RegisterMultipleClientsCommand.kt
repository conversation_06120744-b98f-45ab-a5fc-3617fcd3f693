package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.SalonId

data class RegisterMultipleClientsCommand(
    val salonId: SalonId,
    val clients: List<ClientData>
) {
    data class ClientData(
        val name: String,
        val phone: PhoneNumber,
        val email: Email? = null,
        val address: String? = null,
        val notes: String? = null
    )
}