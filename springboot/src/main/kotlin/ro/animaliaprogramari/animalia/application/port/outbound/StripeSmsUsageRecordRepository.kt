package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.billing.StripeSmsUsageRecord
import ro.animaliaprogramari.animalia.domain.model.billing.StripeSmsUsageRecordId
import java.time.LocalDateTime

interface StripeSmsUsageRecordRepository {
    fun save(record: StripeSmsUsageRecord): StripeSmsUsageRecord
    fun findById(id: StripeSmsUsageRecordId): StripeSmsUsageRecord?
    fun findBySalonId(salonId: SalonId): List<StripeSmsUsageRecord>
    fun findBySalonIdAndReportedAtBetween(salonId: SalonId, startDate: LocalDateTime, endDate: LocalDateTime): List<StripeSmsUsageRecord>
}