package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Query to get invitation by ID
 */
data class GetInvitationByIdQuery(
    val invitationId: InvitationId,
)

/**
 * Query to get pending invitations for a user by phone number
 */
data class GetPendingInvitationsByPhoneQuery(
    val phoneNumber: String,
)

/**
 * Query to get invitations sent by a salon
 */
data class GetInvitationsBySalonQuery(
    val salonId: SalonId,
    val status: InvitationStatus? = null,
)

/**
 * Query to get invitations sent by a user
 */
data class GetInvitationsByInviterQuery(
    val inviterUserId: UserId,
)

/**
 * Query to get pending invitations for a salon
 */
data class GetPendingInvitationsBySalonQuery(
    val salonId: SalonId,
)

/**
 * Query to check if user has pending invitations
 */
data class HasPendingInvitationsQuery(
    val phoneNumber: String,
)
