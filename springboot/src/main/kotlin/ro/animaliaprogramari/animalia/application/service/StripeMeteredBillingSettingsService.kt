package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.port.outbound.StripeMeteredSubscriptionRepository
import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.billing.StripeMeteredSubscription

/**
 * Stores per-salon Stripe metered SMS subscription identifiers in a dedicated table.
 */
@Service
class StripeMeteredBillingSettingsService(
    private val stripeMeteredSubRepo: StripeMeteredSubscriptionRepository,
) {
    private val logger = LoggerFactory.getLogger(StripeMeteredBillingSettingsService::class.java)

    data class MeteredSettings(
        val customerId: String? = null,
        val meterEventName: String? = null,
    )

    @Transactional(readOnly = true)
    fun getSettings(salonId: SalonId): MeteredSettings {
        val r = stripeMeteredSubRepo.findBySalonId(salonId)
        return MeteredSettings(
            customerId = r?.stripeCustomerId,
            meterEventName = r?.meterEventName,
        )
    }

    @Transactional
    fun updateSettings(
        salonId: SalonId,
        customerId: String? = null,
        meterEventName: String? = null,
    ) {
        val existing = stripeMeteredSubRepo.findBySalonId(salonId)
        val toSave = existing?.copy(
            stripeCustomerId = customerId ?: existing.stripeCustomerId,
            meterEventName = meterEventName ?: existing.meterEventName,
        )
            ?: StripeMeteredSubscription.createNew(
                salonId = salonId,
                stripeCustomerId = customerId,
                meterEventName = meterEventName,
            )
        stripeMeteredSubRepo.save(toSave)
        logger.info("Updated Stripe metered billing settings for salon ${salonId.value}")
    }

    @Transactional
    fun softDelete(
        salonId: SalonId,
    ) {
        val existing = stripeMeteredSubRepo.findBySalonId(salonId)
            ?: throw IllegalArgumentException("No Stripe metered billing settings found for salon ${salonId.value}")
        val toSave = existing.copy(
            stripeCustomerId = null,
        )
        stripeMeteredSubRepo.save(toSave)
        logger.info("Updated Stripe metered billing settings for salon ${salonId.value}")
    }
}

