package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.SalonId
import ro.animaliaprogramari.animalia.domain.model.SmsReminderSettings

/**
 * Outbound port for SMS reminder settings persistence
 */
interface SmsReminderSettingsRepository {
    /**
     * Save SMS reminder settings
     */
    fun save(settings: SmsReminderSettings): SmsReminderSettings

    /**
     * Find SMS reminder settings by salon ID
     */
    fun findBySalonId(salonId: SalonId): SmsReminderSettings?

    /**
     * Check if SMS reminder settings exist for a salon
     */
    fun existsBySalonId(salonId: SalonId): <PERSON><PERSON>an
}
