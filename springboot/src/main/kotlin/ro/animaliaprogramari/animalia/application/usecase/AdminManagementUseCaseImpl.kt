package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.application.command.ImpersonateUserCommand
import ro.animaliaprogramari.animalia.application.port.inbound.AdminManagementUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.EnrichedUserData
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.query.AdminGetAllUsersQuery
import ro.animaliaprogramari.animalia.application.query.AdminSearchUsersQuery
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.exception.UnauthorizedException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.SmsQuotaService

/**
 * Implementation of admin management use cases
 */
@Service
class AdminManagementUseCaseImpl(
    private val userRepository: UserRepository,
    private val staffRepository: StaffRepository,
    private val salonRepository: SalonRepository,
    private val salonSubscriptionRepository: SalonSubscriptionRepository,
    private val smsQuotaService: SmsQuotaService,
    private val jwtTokenGenerator: JwtTokenGenerator,
) : AdminManagementUseCase {
    private val logger = LoggerFactory.getLogger(AdminManagementUseCaseImpl::class.java)

    @Transactional
    override fun impersonateUser(command: ImpersonateUserCommand): JwtToken {
        logger.info("Admin impersonation requested by ${command.adminUserId.value} for user ${command.targetUserId.value}")

        val adminUser = validateAdminUser(command.adminUserId)
        val targetUser = validateTargetUser(command.targetUserId)

        // Load staff associations for the target user
        val staffAssociations = staffRepository.findActiveByUserId(targetUser.id)

        // Create authenticated user context for the target user
        val authenticatedUser = AuthenticatedUser.from(targetUser, staffAssociations)

        // Generate impersonation token
        val token = jwtTokenGenerator.generateImpersonationToken(authenticatedUser, command.adminUserId)

        logger.info("Admin impersonation successful: admin=${command.adminUserId.value}, target=${command.targetUserId.value}")
        return token
    }

    private fun validateAdminUser(adminUserId: UserId): User {
        val adminUser = userRepository.findById(adminUserId)
            ?: throw EntityNotFoundException("Admin user", adminUserId.value)

        if (adminUser.role != UserRole.ADMIN) {
            logger.error("Non-admin user ${adminUserId.value} attempted impersonation")
            throw UnauthorizedException("Only admin users can impersonate other users")
        }

        if (!adminUser.isActive) {
            throw UnauthorizedException("Admin user account is inactive")
        }

        return adminUser
    }

    private fun validateTargetUser(targetUserId: UserId): User {
        val targetUser = userRepository.findById(targetUserId)
            ?: throw EntityNotFoundException("Target user", targetUserId.value)

        if (!targetUser.isActive) {
            throw IllegalStateException("Cannot impersonate inactive user ${targetUserId.value}")
        }

        return targetUser
    }

    override fun getAllUsers(query: AdminGetAllUsersQuery): List<User> {
        return userRepository.findAll(
            isActive = query.isActive,
            limit = query.limit,
            offset = query.offset
        )
    }

    override fun searchUsers(query: AdminSearchUsersQuery): List<User> {
        return userRepository.findAll(
            search = query.searchTerm,
            isActive = query.isActive,
            limit = query.limit,
            offset = query.offset
        )
    }

    override fun getUserById(userId: UserId): User? {
        return userRepository.findById(userId)
    }

    override fun getEnrichedUserData(userId: UserId): EnrichedUserData {
        // Get user's staff associations
        val staffAssociations = staffRepository.findByUserId(userId)

        // Get salon information
        val salons = staffAssociations.mapNotNull { staff ->
            salonRepository.findById(staff.salonId)?.let { salon ->
                UserSalonDto(
                    salonId = salon.id.value,
                    salonName = salon.name,
                    staffRole = staff.role.name,
                    isActive = staff.isActive
                )
            }
        }

        // Get aggregated SMS quota across all salons
        val smsQuota = calculateAggregatedSmsQuota(staffAssociations.map { it.salonId })

        // Get subscription information
        val allSubscriptions = staffAssociations.flatMap { staff ->
            salonSubscriptionRepository.findBySalonId(staff.salonId)
        }

        val activeSubscriptions = allSubscriptions
            .filter { subscription -> subscription.isActive() }  // Fix: use explicit parameter name
            .map { sub ->
                SubscriptionSummaryDto(
                    id = sub.id.value,
                    salonId = sub.salonId.value,
                    tier = sub.tier.name,
                    status = sub.status.name,
                    startDate = sub.startDate.toString(),
                    endDate = sub.endDate?.toString()
                )
            }

        val hasPastSubscriptions = allSubscriptions.any { subscription -> !subscription.isActive() }  // Fix: use explicit parameter name

        return EnrichedUserData(
            salons = salons,
            smsQuota = smsQuota,
            activeSubscriptions = activeSubscriptions,
            hasPastSubscriptions = hasPastSubscriptions
        )
    }

    private fun calculateAggregatedSmsQuota(salonIds: List<SalonId>): SmsQuotaSummaryDto? {
        if (salonIds.isEmpty()) return null

        var totalQuota = 0
        var totalUsed = 0

        salonIds.forEach { salonId ->
            try {
                val status = smsQuotaService.getQuotaStatus(salonId)
                totalQuota += status.totalQuota
                totalUsed += status.usedQuota
            } catch (e: Exception) {
                logger.warn("Failed to get SMS quota for salon ${salonId.value}", e)
            }
        }

        val remaining = maxOf(0, totalQuota - totalUsed)
        val percentage = if (totalQuota > 0) {
            (totalUsed.toDouble() / totalQuota.toDouble()) * 100.0
        } else 0.0

        return SmsQuotaSummaryDto(
            totalQuota = totalQuota,
            usedQuota = totalUsed,
            remainingQuota = remaining,
            usagePercentage = percentage
        )
    }

    @Transactional
    override fun updateUserSmsQuota(userId: UserId, salonId: SalonId, totalQuota: Int): SmsQuotaSummaryDto {
        // Verify user has access to this salon
        val staff = staffRepository.findByUserIdAndSalonId(userId, salonId)
            ?: throw EntityNotFoundException("Staff association", "userId=$userId, salonId=$salonId")

        // Update the quota using existing infrastructure
        smsQuotaService.updateTotalQuota(salonId, totalQuota)

        // Get updated status
        val status = smsQuotaService.getQuotaStatus(salonId)

        return SmsQuotaSummaryDto(
            totalQuota = status.totalQuota,
            usedQuota = status.usedQuota,
            remainingQuota = status.remainingQuota,
            usagePercentage = status.usagePercentage
        )
    }

    @Transactional
    override fun updateSubscriptionTier(salonId: SalonId, tier: SubscriptionTier): SubscriptionSummaryDto {
        logger.info("Updating subscription tier for salon ${salonId.value} to $tier")

        // Find active subscription for the salon
        val subscription = salonSubscriptionRepository.findActiveBySalonId(salonId)
            ?: throw EntityNotFoundException("Active subscription", "salonId=${salonId.value}")

        // Update the tier - simple copy with new tier
        val updatedSubscription = subscription.copy(
            tier = tier,
            updatedAt = java.time.LocalDateTime.now()
        )

        // Save updated subscription
        val saved = salonSubscriptionRepository.save(updatedSubscription)

        logger.info("Subscription tier updated successfully for salon ${salonId.value}")

        return SubscriptionSummaryDto(
            id = saved.id.value,
            salonId = saved.salonId.value,
            tier = saved.tier.name,
            status = saved.status.name,
            startDate = saved.startDate.toString(),
            endDate = saved.endDate?.toString()
        )
    }
}
