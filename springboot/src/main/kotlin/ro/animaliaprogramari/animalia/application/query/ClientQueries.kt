package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.ClientId
import ro.animaliaprogramari.animalia.domain.model.Email
import ro.animaliaprogramari.animalia.domain.model.UserId

/**
 * Query to get client by ID
 */
data class GetClientByIdQuery(
    val clientId: ClientId,
)

/**
 * Query to get client by email
 */
data class GetClientByEmailQuery(
    val email: Email,
)

/**
 * Query to search clients
 */
data class SearchClientsQuery(
    val searchTerm: String,
    val isActive: Boolean? = null,
    val limit: Int? = null,
    val offset: Int? = null,
)

/**
 * Query to get all clients
 */
data class GetAllClientsQuery(
    val isActive: Boolean? = null,
    val userId: UserId? = null,
    val limit: Int? = null,
    val offset: Int? = null,
)

/**
 * Query to get clients by groomer
 */
data class GetClientsByGroomerQuery(
    val userId: UserId,
    val isActive: Boolean? = null,
    val limit: Int? = null,
    val offset: Int? = null,
)
