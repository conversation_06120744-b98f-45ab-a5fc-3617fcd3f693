package ro.animaliaprogramari.animalia.application.query

/**
 * Query to get all users with pagination
 */
data class AdminGetAllUsersQuery(
    val isActive: Boolean? = null,
    val limit: Int = 50,
    val offset: Int = 0,
)

/**
 * Query to search users by name, email, or phone
 */
data class AdminSearchUsersQuery(
    val searchTerm: String,
    val isActive: Boolean? = null,
    val limit: Int = 20,
    val offset: Int = 0,
)
