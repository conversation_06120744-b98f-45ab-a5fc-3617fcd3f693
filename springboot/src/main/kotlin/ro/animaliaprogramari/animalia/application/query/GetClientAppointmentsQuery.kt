package ro.animaliaprogramari.animalia.application.query

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate

/**
 * Query to get all appointments for a specific client in a salon
 */
data class GetClientAppointmentsQuery(
    val salonId: SalonId,
    val clientId: ClientId,
    val date: LocalDate? = null,
    val startDate: LocalDate? = null,
    val endDate: LocalDate? = null,
    val status: AppointmentStatus? = null,
    val petId: PetId? = null,
    val requesterId: UserId,
)
