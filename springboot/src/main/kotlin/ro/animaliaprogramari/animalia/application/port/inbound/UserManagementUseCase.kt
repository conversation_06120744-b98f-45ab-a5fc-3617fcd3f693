package ro.animaliaprogramari.animalia.application.port.inbound

import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.model.User

/**
 * Inbound port for user management use cases
 */
interface UserManagementUseCase {
    /**
     * Create a new user
     */
    fun createUser(command: CreateUserCommand): User

    /**
     * Update user information
     */
    fun updateUser(command: UpdateUserCommand): User

    /**
     * Activate user
     */
    fun activateUser(command: ActivateUserCommand): User

    /**
     * Deactivate user
     */
    fun deactivateUser(command: DeactivateUserCommand): User

    /**
     * Update user role
     */
    fun updateUserRole(command: UpdateUserRoleCommand): User

    /**
     * Delete user account permanently
     * This will remove the user and all associated data
     */
    fun deleteUserAccount(command: DeleteUserAccountCommand): Boolean

    /**
     * Get user by ID
     */
    fun getUserById(query: GetUserByIdQuery): User?

    /**
     * Get user by Firebase UID
     */
    fun getUserByFirebaseUid(query: GetUserByFirebaseUidQuery): User?

    /**
     * Get user by email
     */
    fun getUserByEmail(query: GetUserByEmailQuery): User?

    /**
     * Search users
     */
    fun searchUsers(query: SearchUsersQuery): List<User>

    /**
     * Get all users with pagination
     */
    fun getAllUsers(query: GetAllUsersQuery): List<User>
}
