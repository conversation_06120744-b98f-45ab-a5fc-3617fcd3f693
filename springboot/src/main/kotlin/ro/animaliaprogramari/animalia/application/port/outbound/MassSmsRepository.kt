package ro.animaliaprogramari.animalia.application.port.outbound

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import ro.animaliaprogramari.animalia.domain.model.MassSmsCampaign
import ro.animaliaprogramari.animalia.domain.model.SalonId

/**
 * Repository interface for mass SMS campaigns
 */
interface MassSmsRepository {

    /**
     * Save a mass SMS campaign
     */
    fun save(campaign: MassSmsCampaign): MassSmsCampaign

    /**
     * Find campaigns by salon ID ordered by creation date descending
     */
    fun findBySalonIdOrderByCreatedAtDesc(salonId: SalonId, pageable: Pageable): Page<MassSmsCampaign>
}
