package ro.animaliaprogramari.animalia.application.usecase

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.SubscriptionUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.SubscriptionLimits
import ro.animaliaprogramari.animalia.application.port.inbound.SubscriptionAnalytics
import ro.animaliaprogramari.animalia.application.port.outbound.SalonSubscriptionRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.SmsQuotaService
import java.time.LocalDateTime
import java.time.Instant
import java.time.ZoneId

private const val initialFreeTrialDurationDays = 60

/**
 * Implementation of subscription use cases
 */
@Service
class SubscriptionUseCaseImpl(
    private val subscriptionRepository: SalonSubscriptionRepository,
    private val salonRepository: SalonRepository,
    private val staffRepository: StaffRepository,
    private val clientRepository: ClientRepository,
    private val smsQuotaService: SmsQuotaService,
) : SubscriptionUseCase {
    private val logger = LoggerFactory.getLogger(SubscriptionUseCaseImpl::class.java)

    /**
     * This is specifically for web payments only
     */
    override fun createSubscription(command: CreateSubscriptionFromWebhookCommand): SalonSubscription? {
        logger.info("Creating subscription from webhook for user: ${command.userId.value}")

        try {
            // Get user's salon associations to determine salon ID
            val staffAssociations = staffRepository.findByUserId(command.userId)
            if (staffAssociations.isEmpty()) {
                logger.warn("No salon associations found for user: ${command.userId.value}")
                return null
            }
            val salonId = staffAssociations
                .firstOrNull { it.role == StaffRole.CHIEF_GROOMER && it.isActive }?.salonId
                ?: staffAssociations.first().salonId
            logger.info("Using salon ID: ${salonId.value} for webhook subscription creation")

            val customerInfo = command.customerInfo
            val revenueCatCustomerId = customerInfo.originalAppUserId
            val productId = customerInfo.productId
            val purchasedAt = customerInfo.purchasedAt
            val expiresAt = customerInfo.expiresAt
            val isTrialPeriod = customerInfo.isTrialPeriod
            // Map product ID to subscription tier
            val tier = mapProductIdToTier(productId)
            // Parse dates with better error handling
            val startDate = validateStartDate(purchasedAt)
            val endDate = validateEndDate(expiresAt, startDate, isTrialPeriod)

            // Check if subscription already exists for this customer using RC's id
            var existingSubscription = findHighestActiveSub(revenueCatCustomerId, command.userId)

            if (existingSubscription != null) {
                logger.info("Subscription already exists for customer ID: $revenueCatCustomerId, updating instead")
                val previousTier = existingSubscription.tier

                // Update existing subscription
                val updatedSubscriptionObject = createUpdateSubsriptionObj(
                    existingSubscription,
                    tier,
                    startDate,
                    endDate,
                    isTrialPeriod,
                    revenueCatCustomerId
                )
                val savedSub = subscriptionRepository.save(updatedSubscriptionObject)

                // Manage SMS credits based on tier change
                manageSmsCredits(isTrialPeriod, savedSub, isNewSubscription = false, previousTier = previousTier)
                return savedSub
            }

            // Create new subscription - use trial creation if it's a trial period
            val subscription = if (isTrialPeriod) {
                logger.info("Creating trial subscription for customer: $revenueCatCustomerId")
                createTrialObject(command, salonId, tier, revenueCatCustomerId, startDate)
            } else {
                logger.info("Creating regular subscription for customer: $revenueCatCustomerId")
                createSubsriptionObject(command, salonId, tier, startDate, endDate, revenueCatCustomerId)
            }
            val savedSubscription = subscriptionRepository.save(subscription)
            addSms(isTrialPeriod, savedSubscription)
            logger.info("Webhook subscription created: ${savedSubscription.id.value}")
            return savedSubscription
        } catch (e: Exception) {
            logger.error("Failed to create subscription from webhook for user: ${command.userId.value}", e)
            return null
        }
    }



    /**
     * Alwatys add credits for renewall
     * Renwall if
     * - same tier as existing subscription
     * - or no existing subscription (new user)
     * - or existing subscription is expired/canceled
     * - or existing subscription is trial and new is not trial
     * - or existing subscription is lower tier than new
     * - or existing subscription is higher tier than new
     */
    override fun renewall(command: SyncSubscriptionCommand): SalonSubscription? {
        logger.info("Syncing subscription for user: ${command.userId.value}")
        // Find existing subscription by RevenueCat customer ID
        val customerInfo = command.customerInfo
        val revenueCatCustomerId = customerInfo.originalAppUserId
        val newTier = customerInfo.entitlements.premiumAccess?.subscriptionTier
        var existingSubscription = findHighestActiveSub(revenueCatCustomerId, command.userId) ?: return null

        if (existingSubscription.tier != newTier) {
            logger.info("Tier change detected from ${existingSubscription.tier.name} to ${newTier?.name}")
            return null; // Let changeProduct handle tier changes
        }
        logger.info("No tier change needed, just activating subscription")
        val createUpdateSubsriptionObj = createUpdateSubsriptionObj(
            existingSubscription,
            newTier ?: SubscriptionTier.FREE,
            parseIsoDate(customerInfo.purchasedAt),
            parseIsoDate(customerInfo.expiresAt),
            customerInfo.isTrialPeriod,
            revenueCatCustomerId
        )
        val savedSubscription = subscriptionRepository.save(createUpdateSubsriptionObj)

        // For renewals, manage SMS credits properly (no accumulation for same tier)
        val previousTier = existingSubscription?.tier
        manageSmsCredits(
            isTrialPeriod = customerInfo.isTrialPeriod,
            savedSubscription = savedSubscription,
            isNewSubscription = existingSubscription == null,
            previousTier = previousTier
        )
        return savedSubscription
    }

    /**
     * PRODUCT_CHANGE events and webhooks
     * The expiration_at_ms will always be for the product the customer is changing from (old product).
     * The PRODUCT_CHANGE webhook should be considered informative, and does not mean that the product change has gone into effect. When the product change goes into effect you will receive a RENEWAL event on Apple and Stripe or a INITIAL_PURCHASE event on Google Play.
     */
    override fun changeProduct(command: ChangeProductCommand): SalonSubscription? {
        logger.info("Changing product for user: ${command.userId.value}")
        return null
    }

    /**
     * Used for the free tier subscription when user creates their first salon
     */
    override fun createFreeSubscription(command: CreateFreeSubscriptionCommand): SalonSubscription {
        logger.info("Creating free subscription for user: ${command.userId.value}")

        // Check if user already has any subscription
        val existingSubscriptions = subscriptionRepository.findByUserId(command.userId)
            .filter { it.isActive() }

        if (existingSubscriptions.isNotEmpty()) {
            throw BusinessRuleViolationException("User already has an active subscription")
        }

        // For free tier, we create a user-based subscription that applies to all their salons
        // Use a special salon ID to indicate this is a user-level subscription
        val chiefGroomer =
            (staffRepository.findByUserId(command.userId).first { it.role == StaffRole.CHIEF_GROOMER && it.isActive }
                ?: throw BusinessRuleViolationException("User must be a chief groomer of at least one salon to create a free subscription"))


        // Create free tier subscription
        val freeSubscription = SalonSubscription.createFree(
            userId = command.userId,
            salonId = chiefGroomer.salonId
        )

        val savedSubscription = subscriptionRepository.save(freeSubscription)
        logger.info("Free subscription created successfully: ${savedSubscription.id.value}")

        // Initialize SMS quota for free tier (0 SMS)
        try {
            // Note: We don't have a specific salon yet, so we'll set this up when they create their first salon
            logger.info("Free tier subscription created with 0 SMS quota")
        } catch (e: Exception) {
            logger.error("Failed to initialize SMS quota for free subscription", e)
        }

        return savedSubscription
    }


    override fun verifyPurchase(command: VerifyPurchaseCommand): SalonSubscription {
        logger.info("Verifying purchase for salon: ${command.salonId.value}")
        logger.info("Package ID received: ${command.packageId}")

        // Validate salon exists and user is the owner
        val staffAssociation = staffRepository.findByUserIdAndSalonId(command.userId, command.salonId)
        if (staffAssociation?.role != StaffRole.CHIEF_GROOMER || !staffAssociation.isActive) {
            throw BusinessRuleViolationException("Only salon owners can purchase subscriptions")
        }

        // Validate customer info contains required fields
        val revenueCatCustomerId = command.customerInfo["originalAppUserId"] as? String
        if (revenueCatCustomerId.isNullOrBlank()) {
            throw BusinessRuleViolationException("Invalid customer info: missing originalAppUserId")
        }

        // Try to find subscription created by webhook - retry a few times if not found
        var subscription: SalonSubscription? = null
        var attempts = 0
        val maxAttempts = 3

        val targetTier = mapProductIdToTier(command.packageId) // Validate package ID
        while (subscription?.tier != targetTier && attempts < maxAttempts) {
            attempts++
            logger.info("Attempt $attempts to find subscription for user: ${command.userId.value}")

            subscription = findHighestActiveSub(revenueCatCustomerId, command.userId)

            if (subscription == null && attempts < maxAttempts) {
                logger.info("Subscription not found, waiting 1 second before retry...")
                Thread.sleep(1000) // Wait 1 second before retry
            }
        }

        if (subscription == null) {
            throw BusinessRuleViolationException("No active subscription found. Subscription should be created via RevenueCat webhook first.")
        }

        logger.info("Purchase verified successfully for subscription: ${subscription.id.value}, tier: ${subscription.tier.name}")
        return subscription
    }

    private fun findHighestActiveSub(
        revenueCatCustomerId: String,
        id: UserId
    ): SalonSubscription? = subscriptionRepository.findByRevenueCatCustomerId(revenueCatCustomerId)
            ?: subscriptionRepository.findByUserId(id).filter { it.status == SubscriptionStatus.ACTIVE }
                .maxByOrNull { it.tier.ordinal }

    /**
     * Manage SMS credits based on subscription changes
     * Implements proper credit management according to business rules:
     * - New subscriptions: Set exact quota for the tier
     * - Upgrades: Set exact quota for new tier (no accumulation)
     * - Downgrades: Set exact quota for new tier (immediate reduction)
     * - Renewals: Keep existing quota (no changes)
     */
    private fun manageSmsCredits(
        isTrialPeriod: Boolean,
        savedSubscription: SalonSubscription,
        isNewSubscription: Boolean = true,
        previousTier: SubscriptionTier? = null
    ) {
        try {
            val targetQuota = if (isTrialPeriod) {
                // Trial subscriptions get 10% of the standard tier quota
                (savedSubscription.tier.smsQuota * 0.1).toInt()
            } else {
                savedSubscription.tier.smsQuota
            }

            if (isNewSubscription) {
                // New subscription: Set exact quota
                smsQuotaService.setExactQuota(savedSubscription.salonId, targetQuota)
                logger.info("Set initial $targetQuota SMS credits for new subscription for salon ${savedSubscription.salonId.value}")
            } else if (previousTier != null && previousTier != savedSubscription.tier) {
                // Tier change: Set exact quota for new tier (no accumulation)
                smsQuotaService.setExactQuota(savedSubscription.salonId, targetQuota)
                logger.info("Updated SMS credits from ${previousTier.smsQuota} to $targetQuota for tier change (${previousTier.name} → ${savedSubscription.tier.name}) for salon ${savedSubscription.salonId.value}")
            } else {
                // Renewal of same tier: No SMS credit changes needed
                logger.info("Renewal of same tier ${savedSubscription.tier.name} - no SMS credit changes for salon ${savedSubscription.salonId.value}")
            }
        } catch (e: Exception) {
            logger.error("Failed to manage SMS credits for subscription: ${savedSubscription.id.value}", e)
        }
    }

    /**
     * Legacy method for backward compatibility - now uses proper credit management
     */
    private fun addSms(
        isTrialPeriod: Boolean,
        savedSubscription: SalonSubscription
    ) {
        manageSmsCredits(isTrialPeriod, savedSubscription, isNewSubscription = true)
    }

    private fun createUpdateSubsriptionObj(
        existingSubscription: SalonSubscription,
        tier: SubscriptionTier,
        startDate: LocalDateTime,
        endDate: LocalDateTime?,
        isTrialPeriod: Boolean,
        revenueCatCustomerId: String?
    ): SalonSubscription {
        val updatedSubscription = existingSubscription.copy(
            tier = tier,
            startDate = startDate,
            endDate = endDate,
            status = SubscriptionStatus.ACTIVE,
            isTrialActive = isTrialPeriod,
            trialEndDate = if (isTrialPeriod) endDate else null,
            revenueCatCustomerId = revenueCatCustomerId,
            updatedAt = LocalDateTime.now()
        )
        return updatedSubscription
    }

    private fun validateStartDate(purchasedAt: String): LocalDateTime {
        val startDate = purchasedAt.let {
            logger.info("Parsing purchasedAt date: $it")
            parseIsoDate(it)
        }
        return startDate
    }

    private fun createSubsriptionObject(
        command: CreateSubscriptionFromWebhookCommand,
        salonId: SalonId,
        tier: SubscriptionTier,
        startDate: LocalDateTime,
        endDate: LocalDateTime?,
        customerId: String
    ): SalonSubscription = SalonSubscription.create(
        userId = command.userId,
        salonId = salonId,
        tier = tier,
        startDate = startDate,
        endDate = endDate,
        revenueCatCustomerId = customerId,
        revenueCatEntitlementId = "premium_access"
    )

    private fun createTrialObject(
        command: CreateSubscriptionFromWebhookCommand,
        salonId: SalonId,
        tier: SubscriptionTier,
        customerId: String,
        startDate: LocalDateTime
    ): SalonSubscription = SalonSubscription.createTrial(
        userId = command.userId,
        salonId = salonId,
        tier = tier,
        trialDurationDays = initialFreeTrialDurationDays, // Default trial duration of 60 days
    ).copy(
        revenueCatCustomerId = customerId,
        revenueCatEntitlementId = "premium_access",
        startDate = startDate,
        trialEndDate = startDate.plusDays(initialFreeTrialDurationDays.toLong())
    )

    override fun expire(command: ExpireSubscriptionCommand) : SalonSubscription? =
        findHighestActiveSub(command.revenueCatCustomerId, command.userId)?.let { existingSubscription ->
            val expiredSubscription = existingSubscription.expire()
            val savedSubscription = subscriptionRepository.save(expiredSubscription)
            logger.info("Subscription expired successfully: ${savedSubscription.id.value}")
            return savedSubscription
        }


    override fun cancel(command: ExpireSubscriptionCommand): SalonSubscription? =
        findHighestActiveSub(command.revenueCatCustomerId, command.userId)?.let { existingSubscription ->
            val canceledSubsription = existingSubscription.cancel()
            val savedSubscription = subscriptionRepository.save(canceledSubsription)
            logger.info("Subscription expired successfully: ${savedSubscription.id.value}")
            return savedSubscription
    }

    private fun validateEndDate(
        expiresAt: String,
        startDate: LocalDateTime,
        isTrialPeriod: Boolean
    ): LocalDateTime? {
        val endDate = expiresAt.let {
            logger.info("Parsing expiresAt date: $it")
            val parsed = parseIsoDate(it)
            // Validate that end date is after start date
            if (parsed.isAfter(startDate)) {
                parsed
            } else {
                logger.warn("Parsed end date ($parsed) is not after start date ($startDate), using default duration")
                startDate.plusMonths(1)
            }
        } ?: startDate.plusMonths(1)

        logger.info("Subscription dates - Start: $startDate, End: $endDate, IsTrialPeriod: $isTrialPeriod")
        return endDate
    }

    override fun uncancel(command: ExpireSubscriptionCommand): SalonSubscription? =
        findHighestActiveSub(command.revenueCatCustomerId, command.userId)?.let { existingSubscription ->
            val uncancelledSubsription = existingSubscription.activate()
            val savedSubscription = subscriptionRepository.save(uncancelledSubsription)
            logger.info("Subscription expired successfully: ${savedSubscription.id.value}")
            return savedSubscription
    }

    private fun getSalons(userId: UserId): List<SalonId> {
        // Get user's primary salon (first active salon association)
        val owner = staffRepository.findActiveByUserId(userId)
        if (owner.isEmpty()) {
            logger.warn("User ${userId.value} has no active salon associations")
        }
        // Use the first salon where user is chief groomer, or first salon if none
        return owner.map { it.salonId }
            .ifEmpty {
                logger.warn("User ${userId.value} has no active salons")
                return emptyList()
            }
    }


    override fun getCurrentSubscription(query: GetSalonSubscriptionQuery): SalonSubscription? {
        // Verify user has access to salon
        val staffAssociation = staffRepository.findByUserIdAndSalonId(query.userId, query.salonId)
        if (staffAssociation?.role != StaffRole.CHIEF_GROOMER || !staffAssociation.isActive) {
            throw BusinessRuleViolationException("Only salon owners can view subscription details")
        }

        // Use database-level filtering for better consistency
        // Return user's active subscription (user-based, not salon-based)
        // This allows the same subscription to apply to all salons owned by the user
        val activeSubscriptions = subscriptionRepository.findActiveByUserId(query.userId)
        logger.info("Found ${activeSubscriptions.size} active subscriptions for user: ${query.userId.value}")

        val subscription = activeSubscriptions.maxByOrNull { it.tier.ordinal }

        // If no subscription found, retry once with a small delay to handle race conditions
        if (subscription == null) {
            logger.warn("No active subscription found for user: ${query.userId.value}, retrying...")
            Thread.sleep(100) // Small delay to handle race conditions
            val retrySubscriptions = subscriptionRepository.findActiveByUserId(query.userId)
            logger.info("Retry found ${retrySubscriptions.size} active subscriptions for user: ${query.userId.value}")
            return retrySubscriptions.maxByOrNull { it.tier.ordinal }
        }

        return subscription
    }

    override fun getUserSubscription(query: GetUserSubscriptionQuery): SalonSubscription? {
        // Use database-level filtering for better consistency
        val activeSubscriptions = subscriptionRepository.findActiveByUserId(query.userId)
        logger.info("Found ${activeSubscriptions.size} active subscriptions for user: ${query.userId.value}")

        val subscription = activeSubscriptions.maxByOrNull { it.tier.ordinal }

        // If no subscription found, retry once with a small delay to handle race conditions
        if (subscription == null) {
            logger.warn("No active subscription found for user: ${query.userId.value}, retrying...")
            Thread.sleep(100) // Small delay to handle race conditions
            val retrySubscriptions = subscriptionRepository.findActiveByUserId(query.userId)
            logger.info("Retry found ${retrySubscriptions.size} active subscriptions for user: ${query.userId.value}")
            return retrySubscriptions.maxByOrNull { it.tier.ordinal }
        }

        return subscription
    }

    override fun getSubscriptionHistory(query: GetSalonSubscriptionHistoryQuery): List<SalonSubscription> {
        // Verify user has access to salon
        val staffAssociation = staffRepository.findByUserIdAndSalonId(query.userId, query.salonId)
        if (staffAssociation?.role != StaffRole.CHIEF_GROOMER || !staffAssociation.isActive) {
            throw BusinessRuleViolationException("Only salon owners can view subscription history")
        }

        return subscriptionRepository.findBySalonId(query.salonId)
    }

    override fun canAccessFeature(query: CheckFeatureAccessQuery): Boolean {
        val subscription = subscriptionRepository.findActiveBySalonId(query.salonId)
        return subscription?.canAccessFeature(query.feature) ?: false
    }

    override fun getSubscriptionLimits(query: GetSubscriptionLimitsQuery): SubscriptionLimits {
        // Verify user has access to salon
        val staffAssociation = staffRepository.findByUserIdAndSalonId(query.userId, query.salonId)
        if (staffAssociation == null || !staffAssociation.isActive) {
            throw BusinessRuleViolationException("Nu aveți permisiunea să accesați această resursă")
        }

        // Get user's active subscription (user-based, not salon-based)
        // Use database-level filtering for better consistency
        val activeSubscriptions = subscriptionRepository.findActiveByUserId(query.userId)
        logger.info("Found ${activeSubscriptions.size} active subscriptions for user: ${query.userId.value}")

        val subscription = activeSubscriptions.maxByOrNull { it.tier.ordinal }

        // If no subscription found, retry once with a small delay to handle race conditions
        if (subscription == null) {
            logger.warn("No active subscription found for user: ${query.userId.value}, retrying...")
            Thread.sleep(100) // Small delay to handle race conditions
            val retrySubscriptions = subscriptionRepository.findActiveByUserId(query.userId)
            logger.info("Retry found ${retrySubscriptions.size} active subscriptions for user: ${query.userId.value}")
            val retrySubscription = retrySubscriptions.maxByOrNull { it.tier.ordinal }

            if (retrySubscription != null) {
                return buildSubscriptionLimits(retrySubscription, query)
            }
        } else {
            return buildSubscriptionLimits(subscription, query)
        }

        // No subscription found after retry
        return SubscriptionLimits(
            maxStaff = 0,
            maxClients = 0,
            smsQuota = 0,
            currentStaffCount = 0,
            currentClientCount = 0,
            currentSmsUsage = 0
        )
    }

    private fun buildSubscriptionLimits(subscription: SalonSubscription, query: GetSubscriptionLimitsQuery): SubscriptionLimits {
        // Get current usage
        val currentStaffCount = staffRepository.countActiveBySalon(query.salonId).toInt()
        val currentClientCount = clientRepository.findBySalonId(query.salonId, isActive = true).size

        // Get actual SMS quota data
        val smsQuota = smsQuotaService.getOrCreateSmsQuota(query.salonId)
        val currentSmsUsage = smsQuota.usedQuota
        val totalSmsQuota = smsQuota.totalQuota

        return SubscriptionLimits(
            maxStaff = subscription.tier.maxStaff,
            maxClients = subscription.tier.maxClients,
            smsQuota = totalSmsQuota, // Use actual total quota instead of tier quota
            currentStaffCount = currentStaffCount,
            currentClientCount = currentClientCount,
            currentSmsUsage = currentSmsUsage
        )
    }

    override fun cancelSubscription(command: CancelSubscriptionCommand): SalonSubscription {
        val subscription = subscriptionRepository.findById(command.subscriptionId)
            ?: throw EntityNotFoundException("Subscription not found: ${command.subscriptionId.value}")

        // Verify user owns the subscription
        if (subscription.userId != command.userId) {
            throw BusinessRuleViolationException("User does not own this subscription")
        }

        val cancelledSubscription = subscription.cancel()
        return subscriptionRepository.save(cancelledSubscription)
    }

    override fun restoreSubscription(command: RestoreSubscriptionCommand): SalonSubscription? {
        // This would typically involve checking with RevenueCat for restored purchases
        // For now, we'll implement a basic version that syncs with existing data
        return renewall(
            SyncSubscriptionCommand(
                userId = command.userId,
                salonId = command.salonId,
                customerInfo = command.customerInfo
            )
        )
    }

    override fun updateSubscriptionMetadata(command: UpdateSubscriptionMetadataCommand): SalonSubscription {
        val subscription = subscriptionRepository.findById(command.subscriptionId)
            ?: throw EntityNotFoundException("Subscription not found: ${command.subscriptionId.value}")

        // Verify user owns the subscription
        if (subscription.userId != command.userId) {
            throw BusinessRuleViolationException("User does not own this subscription")
        }

        val updatedSubscription = subscription.copy(
            metadata = subscription.metadata + command.metadata,
            updatedAt = LocalDateTime.now()
        )

        return subscriptionRepository.save(updatedSubscription)
    }

    override fun getSubscriptionAnalytics(query: GetSubscriptionAnalyticsQuery): SubscriptionAnalytics {
        // Basic analytics implementation
        val totalSubscriptions = subscriptionRepository.countByStatus(SubscriptionStatus.ACTIVE) +
            subscriptionRepository.countByStatus(SubscriptionStatus.TRIAL) +
            subscriptionRepository.countByStatus(SubscriptionStatus.EXPIRED)

        val activeSubscriptions = subscriptionRepository.countByStatus(SubscriptionStatus.ACTIVE)
        val trialSubscriptions = subscriptionRepository.countByStatus(SubscriptionStatus.TRIAL)
        val expiredSubscriptions = subscriptionRepository.countByStatus(SubscriptionStatus.EXPIRED)

        val subscriptionsByTier = mapOf(
            SubscriptionTier.FREELANCER to subscriptionRepository.countByTier(SubscriptionTier.FREELANCER),
            SubscriptionTier.TEAM to subscriptionRepository.countByTier(SubscriptionTier.TEAM),
            SubscriptionTier.ENTERPRISE to subscriptionRepository.countByTier(SubscriptionTier.ENTERPRISE)
        )

        return SubscriptionAnalytics(
            totalSubscriptions = totalSubscriptions,
            activeSubscriptions = activeSubscriptions,
            trialSubscriptions = trialSubscriptions,
            expiredSubscriptions = expiredSubscriptions,
            subscriptionsByTier = subscriptionsByTier,
            monthlyRevenue = 0.0,
            annualRevenue = 0.0,
            averageSubscriptionDuration = 0.0,
            churnRate = 0.0
        )
    }

    override fun trackUsage(command: TrackUsageCommand) {
        logger.info("Tracking usage for salon: ${command.salonId.value}, type: ${command.usageType}")

        // Validate user has access to salon
        val staffAssociation = staffRepository.findByUserIdAndSalonId(command.userId, command.salonId)
        if (staffAssociation?.role != StaffRole.CHIEF_GROOMER || !staffAssociation.isActive) {
            throw BusinessRuleViolationException("Only salon owners can track usage")
        }

        // Here you could implement usage analytics tracking
        // For now, we'll just log the usage
        logger.info("Usage tracked: ${command.usageType} at ${command.timestamp} for salon ${command.salonId.value}")
    }

    override fun checkLimit(query: CheckLimitQuery): Boolean {
        logger.info("Checking limit for salon: ${query.salonId.value}, type: ${query.limitType}")

        // Get user's active subscription (user-based, not salon-based)
        val activeSubscriptions = subscriptionRepository.findActiveByUserId(query.userId)
        logger.info("Found ${activeSubscriptions.size} active subscriptions for user: ${query.userId.value}")

        val subscription = activeSubscriptions.maxByOrNull { it.tier.ordinal }

        if (subscription == null) {
            logger.warn("No active subscription found for user: ${query.userId.value}, retrying...")
            Thread.sleep(100) // Small delay to handle race conditions
            val retrySubscriptions = subscriptionRepository.findActiveByUserId(query.userId)
            logger.info("Retry found ${retrySubscriptions.size} active subscriptions for user: ${query.userId.value}")
            val retrySubscription = retrySubscriptions.maxByOrNull { it.tier.ordinal }

            if (retrySubscription == null) {
                return false
            }
            return checkLimitForSubscription(retrySubscription, query)
        }

        return checkLimitForSubscription(subscription, query)
    }

    private fun checkLimitForSubscription(subscription: SalonSubscription, query: CheckLimitQuery): Boolean {

        return when (query.limitType) {
            "staff" -> {
                val currentStaffCount = staffRepository.countActiveBySalon(query.salonId).toInt()
                subscription.tier.maxStaff == -1 || currentStaffCount < subscription.tier.maxStaff
            }

            "clients" -> {
                val currentClientCount = clientRepository.findBySalonId(query.salonId, isActive = true).size
                subscription.tier.maxClients == -1 || currentClientCount < subscription.tier.maxClients
            }

            "sms" -> {
                // Get actual SMS quota data
                val smsQuota = smsQuotaService.getOrCreateSmsQuota(query.salonId)
                val currentSmsUsage = smsQuota.usedQuota
                val totalSmsQuota = smsQuota.totalQuota
                currentSmsUsage < totalSmsQuota
            }

            else -> true
        }
    }

    override fun getUserHighestSubscriptionTier(query: GetUserHighestTierQuery): SubscriptionTier? {
        logger.info("Getting highest subscription tier for user: ${query.userId.value}")

        // Get user's active subscriptions (user-based, not salon-based)
        val activeSubscriptions = subscriptionRepository.findActiveByUserId(query.userId)
        logger.info("Found ${activeSubscriptions.size} active subscriptions for user: ${query.userId.value}")

        if (activeSubscriptions.isEmpty()) {
            logger.warn("No active subscription found for user: ${query.userId.value}, retrying...")
            Thread.sleep(100) // Small delay to handle race conditions
            val retrySubscriptions = subscriptionRepository.findActiveByUserId(query.userId)
            logger.info("Retry found ${retrySubscriptions.size} active subscriptions for user: ${query.userId.value}")

            if (retrySubscriptions.isEmpty()) {
                logger.info("User has no active subscriptions")
                return null
            }

            val highestTier = retrySubscriptions.maxByOrNull { it.tier.ordinal }?.tier
            logger.info("User's highest subscription tier (after retry): ${highestTier?.name}")
            return highestTier
        }

        // Find the highest tier
        val highestTier = activeSubscriptions.maxByOrNull { it.tier.ordinal }?.tier
        logger.info("User's highest subscription tier: ${highestTier?.name}")
        return highestTier
    }

    override fun canUserCreateSalons(query: CanUserCreateSalonsQuery): Boolean {
        logger.info("Checking if user can create salons: ${query.userId.value}")

        // Check how many salons the user currently owns (as chief groomer)
        val userSalons = staffRepository.findByUserId(query.userId)
            .filter { it.role == StaffRole.CHIEF_GROOMER && it.isActive }

        logger.info("User ${query.userId.value} currently owns ${userSalons.size} salons")

        // Users can always create their first salon (will get free tier subscription)
        if (userSalons.isEmpty()) {
            logger.info("User can create their first salon")
            return true
        }

        // For additional salons, only Enterprise users are allowed
        val highestTier = getUserHighestSubscriptionTier(GetUserHighestTierQuery(query.userId))
        val canCreate = highestTier == SubscriptionTier.ENTERPRISE

        logger.info("User can create additional salons: $canCreate (highest tier: ${highestTier?.name ?: "None"})")
        return canCreate
    }

    private fun parseIsoDate(dateString: String): LocalDateTime {
        return try {
            logger.info("Attempting to parse date string: '$dateString'")

            // Try parsing ISO 8601 format with timezone first
            val instant = Instant.parse(dateString)
            val result = LocalDateTime.ofInstant(instant, ZoneId.systemDefault())
            logger.info("Successfully parsed date: '$dateString' -> $result")
            result
        } catch (e: Exception) {
            logger.error("Failed to parse date: '$dateString' - ${e.message}")

            // Try alternative parsing methods
            try {
                // Try parsing as LocalDateTime directly (without timezone)
                val result = LocalDateTime.parse(dateString)
                logger.info("Successfully parsed date using LocalDateTime.parse: '$dateString' -> $result")
                result
            } catch (e2: Exception) {
                logger.error("All date parsing attempts failed for: '$dateString'. Using current time as fallback. Original error: ${e.message}, Secondary error: ${e2.message}")
                LocalDateTime.now()
            }
        }
    }

    /**
     * Map Stripe price ID or RevenueCat product ID to subscription tier
     */
    private fun mapProductIdToTier(productId: String): SubscriptionTier {
        logger.info("Mapping product ID to tier: $productId")

        // Stripe Price ID and Product ID mapping
        val stripeProductMapping = mapOf(
            // Stripe Price IDs (for web payments)
            // Freelancer/Bronze Tier
            "price_1RrlIPAjJhyMx6eU3eTyrioS" to SubscriptionTier.FREELANCER, // Freelancer Monthly
            "price_1RrlIPAjJhyMx6eUzvAkveU7" to SubscriptionTier.FREELANCER, // Freelancer Annual

            // Team/Silver Tier
            "price_1RrlILAjJhyMx6eUp4RCLINL" to SubscriptionTier.TEAM,       // Team Monthly
            "price_1RrlILAjJhyMx6eU44GJL52w" to SubscriptionTier.TEAM,       // Team Annual

            // Enterprise/Gold Tier
            "price_1RrlIGAjJhyMx6eUqSLH1qfm" to SubscriptionTier.ENTERPRISE, // Enterprise Monthly
            "price_1RrlIGAjJhyMx6eUoBftLpm2" to SubscriptionTier.ENTERPRISE, // Enterprise Annual

            // Stripe Product IDs (from RevenueCat webhooks)
            "prod_SnM01Tng90IuSw" to SubscriptionTier.FREELANCER,  // Freelancer product
            "prod_SnM0Tm0GwESAaW" to SubscriptionTier.TEAM,        // Team product
            "prod_SnM0PU26iHuwHZ" to SubscriptionTier.ENTERPRISE,  // Enterprise product
        )

        // Check Stripe price ID mapping first
        stripeProductMapping[productId]?.let { tier ->
            logger.info("Mapped Stripe price ID $productId to tier: ${tier.name}")
            return tier
        }

        // Fallback to keyword-based mapping for RevenueCat product IDs
        val tier = when {
            productId.contains("freelancer", ignoreCase = true) ||
            productId.contains("bronze", ignoreCase = true) -> SubscriptionTier.FREELANCER

            productId.contains("team", ignoreCase = true) ||
            productId.contains("silver", ignoreCase = true) -> SubscriptionTier.TEAM

            productId.contains("enterprise", ignoreCase = true) ||
            productId.contains("gold", ignoreCase = true) -> SubscriptionTier.ENTERPRISE

            else -> {
                logger.warn("Unknown product ID: $productId, defaulting to FREELANCER")
                SubscriptionTier.FREELANCER
            }
        }

        logger.info("Mapped product ID $productId to tier: ${tier.name}")
        return tier
    }


}
