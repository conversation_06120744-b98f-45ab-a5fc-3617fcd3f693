package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.ZonedDateTime

/**
 * Outbound port for block time persistence operations
 * This interface defines the contract for block time data access
 */
interface BlockTimeRepository {
    /**
     * Save a new block time
     */
    fun save(blockTime: BlockTime): BlockTime

    /**
     * Update an existing block time
     */
    fun update(blockTime: BlockTime): BlockTime

    /**
     * Find block time by ID
     */
    fun findById(blockId: BlockTimeId): BlockTime?

    /**
     * Find block time by ID and salon ID
     */
    fun findByIdAndSalonId(
        blockId: BlockTimeId,
        salonId: SalonId,
    ): BlockTime?

    /**
     * Find all block times for a salon with filtering
     */
    fun findBySalonId(
        salonId: SalonId,
        startDate: LocalDate? = null,
        endDate: LocalDate? = null,
        staffId: StaffId? = null,
        reason: BlockReason? = null,
        status: BlockTimeStatus? = null,
        page: Int = 1,
        limit: Int = 50,
    ): List<BlockTime>

    /**
     * Count block times for a salon with filtering
     */
    fun countBySalonId(
        salonId: SalonId,
        startDate: LocalDate? = null,
        endDate: LocalDate? = null,
        staffId: StaffId? = null,
        reason: BlockReason? = null,
        status: BlockTimeStatus? = null,
    ): Long

    /**
     * Find block times that overlap with a time period for specific staff
     */
    fun findOverlappingBlocks(
        salonId: SalonId,
        startTime: ZonedDateTime,
        endTime: ZonedDateTime,
        staffIds: Set<StaffId>,
        excludeBlockId: BlockTimeId? = null,
    ): List<BlockTime>

    /**
     * Find active block times for specific staff members
     */
    fun findActiveBlocksForStaff(
        salonId: SalonId,
        staffIds: Set<UserId>,
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<BlockTime>

    /**
     * Find block times by date range for statistics
     */
    fun findByDateRange(
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate,
        staffId: StaffId? = null,
    ): List<BlockTime>

    /**
     * Delete (soft delete) a block time
     */
    fun delete(blockId: BlockTimeId): Boolean

    /**
     * Find expired block times that need status update
     */
    fun findExpiredBlocks(): List<BlockTime>

    /**
     * Bulk save block times
     */
    fun saveAll(blockTimes: List<BlockTime>): List<BlockTime>

    /**
     * Get block time statistics aggregated data
     */
    fun getStatisticsData(
        salonId: SalonId,
        startDate: LocalDate,
        endDate: LocalDate,
        staffId: StaffId? = null,
    ): BlockTimeStatisticsData

    /**
     * Check if a block time exists
     */
    fun existsById(blockId: BlockTimeId): Boolean

    /**
     * Check if a block time exists for salon
     */
    fun existsByIdAndSalonId(
        blockId: BlockTimeId,
        salonId: SalonId,
    ): Boolean
}

/**
 * Aggregated statistics data from repository
 */
data class BlockTimeStatisticsData(
    val totalBlocks: Long,
    val totalMinutes: Long,
    val reasonCounts: Map<BlockReason, Long>,
    val staffCounts: Map<StaffId, Long>,
    val dailyCounts: Map<LocalDate, Long>,
    val hourlyCounts: Map<Int, Long>,
)
