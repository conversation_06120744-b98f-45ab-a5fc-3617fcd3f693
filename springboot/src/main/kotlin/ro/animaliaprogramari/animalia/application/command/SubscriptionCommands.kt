package ro.animaliaprogramari.animalia.application.command

import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CustomerInfo
import ro.animaliaprogramari.animalia.domain.model.*

/**
 * Command to create a free tier subscription for new users
 */
data class CreateFreeSubscriptionCommand(
    val userId: UserId,
)

/**
 * Command to verify a subscription purchase
 */
data class VerifyPurchaseCommand(
    val userId: UserId,
    val salonId: SalonId,
    val packageId: String,
    val revenueCatCustomerId: String,
    val revenueCatEntitlementId: String,
    val customerInfo: Map<String, Any>,
)

/**
 * Command to sync subscription with RevenueCat
 */
data class SyncSubscriptionCommand(
    val userId: UserId,
    val salonId: SalonId?, // Optional - will be determined from user's salon associations if null
    val customerInfo: CustomerInfo,
)

data class ChangeProductCommand(
    val userId: UserId,
    val revenueCatCustomerId: String,
    val customerInfo: CustomerInfo,
)

data class ExpireSubscriptionCommand(
    val revenueCatCustomerId: String,
    val userId: UserId,
)

/**
 * Command to create subscription from webhook data
 */
data class CreateSubscriptionFromWebhookCommand(
    val userId: UserId,
    val customerInfo: CustomerInfo,
)

/**
 * Command to cancel a subscription
 */
data class CancelSubscriptionCommand(
    val subscriptionId: SubscriptionId,
    val userId: UserId,
    val reason: String? = null,
)

/**
 * Command to restore subscription from RevenueCat
 */
data class RestoreSubscriptionCommand(
    val userId: UserId,
    val salonId: SalonId,
    val customerInfo: CustomerInfo,
)

/**
 * Command to update subscription metadata
 */
data class UpdateSubscriptionMetadataCommand(
    val subscriptionId: SubscriptionId,
    val userId: UserId,
    val metadata: Map<String, String>,
)

/**
 * Command to track usage for analytics
 */
data class TrackUsageCommand(
    val salonId: SalonId,
    val userId: UserId,
    val usageType: String, // 'staff_added', 'client_added', 'sms_sent'
    val timestamp: String,
)
