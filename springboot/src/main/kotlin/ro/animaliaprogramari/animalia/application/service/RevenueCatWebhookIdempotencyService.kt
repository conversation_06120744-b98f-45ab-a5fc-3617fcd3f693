package ro.animaliaprogramari.animalia.application.service

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.RevenueCatWebhookEvent
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.jpa.SpringRevenueCatWebhookEventRepository

/**
 * Service for managing RevenueCat webhook event idempotency
 * Prevents duplicate processing of webhook events
 */
@Service
class RevenueCatWebhookIdempotencyService(
    private val webhookEventRepository: SpringRevenueCatWebhookEventRepository,
) {
    private val logger = LoggerFactory.getLogger(RevenueCatWebhookIdempotencyService::class.java)

    /**
     * Check if a webhook event has already been processed
     * @param eventId The unique event ID from RevenueCat
     * @return true if the event has already been processed, false otherwise
     */
    @Transactional(readOnly = true)
    fun isEventAlreadyProcessed(eventId: String): Boolean {
        val exists = webhookEventRepository.existsByEventId(eventId)
        if (exists) {
            logger.info("RevenueCat webhook event $eventId has already been processed, skipping")
        }
        return exists
    }

    /**
     * Mark a webhook event as processed
     * @param eventId The unique event ID from RevenueCat
     * @return true if the event was successfully marked as processed, false if it was already processed
     */
    @Transactional
    fun markEventAsProcessed(eventId: String): Boolean {
        return try {
            // Check if already exists (double-check in case of race condition)
            if (webhookEventRepository.existsByEventId(eventId)) {
                logger.warn("RevenueCat webhook event $eventId was already processed by another thread")
                return false
            }

            // Create new event record
            val webhookEvent = RevenueCatWebhookEvent.create(eventId)
            webhookEventRepository.save(webhookEvent)
            
            logger.info("Marked RevenueCat webhook event $eventId as processed")
            true
        } catch (e: Exception) {
            logger.error("Failed to mark RevenueCat webhook event $eventId as processed", e)
            false
        }
    }

    /**
     * Process a webhook event with idempotency protection
     * @param eventId The unique event ID from RevenueCat
     * @param processor The function to execute if the event hasn't been processed yet
     * @return true if the event was processed (either now or previously), false if processing failed
     */
    @Transactional
    fun processEventIdempotently(eventId: String, processor: () -> Unit): Boolean {
        return try {
            // Check if already processed
            if (isEventAlreadyProcessed(eventId)) {
                return true // Already processed, consider it successful
            }

            // Process the event
            processor()

            // Mark as processed
            markEventAsProcessed(eventId)
        } catch (e: Exception) {
            logger.error("Failed to process RevenueCat webhook event $eventId", e)
            false
        }
    }
}
