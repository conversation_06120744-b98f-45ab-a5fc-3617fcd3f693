package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.MassSmsTemplate
import ro.animaliaprogramari.animalia.domain.model.SalonId

/**
 * Repository interface for mass SMS templates
 */
interface MassSmsTemplateRepository {
    
    /**
     * Save a mass SMS template
     */
    fun save(template: MassSmsTemplate): MassSmsTemplate
    
    /**
     * Find template by ID
     */
    fun findById(id: String): MassSmsTemplate?
    
    /**
     * Find templates by salon ID including default templates
     */
    fun findBySalonIdOrDefault(salonId: SalonId): List<MassSmsTemplate>
    
    /**
     * Find templates by salon ID only
     */
    fun findBySalonId(salonId: SalonId): List<MassSmsTemplate>
    
    /**
     * Find default templates
     */
    fun findDefaultTemplates(): List<MassSmsTemplate>
    
    /**
     * Find templates by category
     */
    fun findBySalonIdAndCategory(salonId: SalonId, category: String): List<MassSmsTemplate>
    
    /**
     * Find template by name and salon
     */
    fun findBySalonIdAndName(salonId: SalonId, name: String): MassSmsTemplate?
    
    /**
     * Check if template name exists for salon
     */
    fun existsBySalonIdAndName(salonId: SalonId, name: String): Boolean
    
    /**
     * Delete template by ID
     */
    fun deleteById(id: String)
    
    /**
     * Count templates by salon
     */
    fun countBySalonId(salonId: SalonId): Long
    
    /**
     * Get most used templates by salon
     */
    fun findMostUsedBySalonId(salonId: SalonId, limit: Int): List<MassSmsTemplate>
}
