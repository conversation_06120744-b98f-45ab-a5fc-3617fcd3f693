package ro.animaliaprogramari.animalia.application.usecase

import org.springframework.stereotype.Service
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.inbound.ClientManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.ClientRepository
import ro.animaliaprogramari.animalia.application.port.outbound.DomainEventPublisher
import ro.animaliaprogramari.animalia.application.port.outbound.PetRepository
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.event.ClientDeletedEvent
import ro.animaliaprogramari.animalia.domain.event.ClientRegisteredEvent
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

/**
 * Implementation of client management use cases
 */
@Service
class ClientManagementUseCaseImpl(
    private val clientRepository: ClientRepository,
    private val appointmentRepository: AppointmentRepository,
    private val petRepository: PetRepository,
    private val domainEventPublisher: DomainEventPublisher,
) : ClientManagementUseCase {
    override fun registerClient(command: RegisterClientCommand): Client {
        val client =
            Client.create(
                name = command.name,
                phone = command.phone,
                email = command.email,
                address = command.address,
                notes = command.notes,
            )

        val savedClient = clientRepository.save(client)

        // Publish domain event
        val event =
            ClientRegisteredEvent(
                eventId = UUID.randomUUID().toString(),
                occurredAt = LocalDateTime.now(),
                aggregateId = savedClient.id.value,
                clientId = savedClient.id,
                clientName = savedClient.name,
                email = savedClient.email,
                phone = savedClient.phone,
            )

        domainEventPublisher.publish(event)

        return savedClient
    }

    override fun updateClient(command: UpdateClientCommand): Client {
        val existingClient =
            clientRepository.findById(command.clientId)
                ?: throw EntityNotFoundException("Client", command.clientId.value)

        val updatedClient =
            existingClient.update(
                name = command.name,
                phone = command.phone,
                email = command.email,
                address = command.address,
                notes = command.notes,
            )

        return clientRepository.save(updatedClient)
    }

    override fun deactivateClient(command: DeactivateClientCommand): Client {
        val client =
            clientRepository.findById(command.clientId)
                ?: throw EntityNotFoundException("Client", command.clientId.value)

        val deactivatedClient = client.deactivate()
        return clientRepository.save(deactivatedClient)
    }

    override fun activateClient(command: ActivateClientCommand): Client {
        val client =
            clientRepository.findById(command.clientId)
                ?: throw EntityNotFoundException("Client", command.clientId.value)

        val activatedClient = client.activate()
        return clientRepository.save(activatedClient)
    }

    override fun getClientById(query: GetClientByIdQuery): Client? {
        return clientRepository.findById(query.clientId)
    }

    override fun getClientByEmail(query: GetClientByEmailQuery): Client? {
        return clientRepository.findByEmail(query.email)
    }

    override fun searchClients(query: SearchClientsQuery): List<Client> {
        return clientRepository.findAll(
            search = query.searchTerm,
            isActive = query.isActive,
            limit = query.limit,
            offset = query.offset,
        )
    }

    override fun getAllClients(query: GetAllClientsQuery): List<Client> {
        return clientRepository.findAll(
            search = null,
            isActive = query.isActive,
            userId = query.userId,
            limit = query.limit,
            offset = query.offset,
        )
    }

    override fun getClientsByGroomer(query: GetClientsByGroomerQuery): List<Client> {
        return clientRepository.findByGroomer(
            userId = query.userId,
            isActive = query.isActive,
            limit = query.limit,
            offset = query.offset,
        )
    }

    override fun deleteClient(command: DeleteClientCommand): Client {
        // 1. Validate client exists
        val client =
            clientRepository.findById(command.clientId)
                ?: throw EntityNotFoundException("Client", command.clientId.value)

        // 2. Check for active appointments (prevent deletion if client has future appointments)
        val activeAppointments =
            appointmentRepository.findByClientId(command.clientId)
                .filter { appointment ->
                    appointment.status == AppointmentStatus.SCHEDULED &&
                        appointment.appointmentDate >= LocalDate.now()
                }

        if (activeAppointments.isNotEmpty()) {
            throw BusinessRuleViolationException(
                "Cannot delete client with ${activeAppointments.size} active appointment(s). " +
                    "Please cancel or complete all future appointments first.",
            )
        }

        // 3. Soft delete the client (deactivate instead of hard delete)
        val deletedClient = client.deactivate()
        val savedClient = clientRepository.save(deletedClient)

        // 4. Client is automatically removed from salon via FK relationship (salonId set to null in deactivate)

        // 5. Optionally deactivate client's pets (soft delete)
        val clientPets = petRepository.findByClientId(command.clientId, activeOnly = false)
        clientPets.forEach { pet ->
            if (pet.isActive) {
                val deactivatedPet = pet.deactivate()
                petRepository.save(deactivatedPet)
            }
        }

        // 6. Publish domain event
        val event =
            ClientDeletedEvent(
                eventId = UUID.randomUUID().toString(),
                occurredAt = LocalDateTime.now(),
                aggregateId = savedClient.id.value,
                clientId = savedClient.id,
                clientName = savedClient.name,
                salonId = command.salonId,
                deletedBy = command.requestedBy,
            )

        domainEventPublisher.publish(event)

        return savedClient
    }

    override fun registerMultipleClients(command: RegisterMultipleClientsCommand): List<Client> {
        val clients = command.clients.map { clientData ->
            Client.create(
                name = clientData.name,
                phone = clientData.phone,
                email = clientData.email,
                address = clientData.address,
                notes = clientData.notes,
                salonId = command.salonId
            )
        }

        val savedClients = clientRepository.saveAll(clients)

        // Publish domain events for each client
        savedClients.forEach { savedClient ->
            val event = ClientRegisteredEvent(
                eventId = UUID.randomUUID().toString(),
                occurredAt = LocalDateTime.now(),
                aggregateId = savedClient.id.value,
                clientId = savedClient.id,
                clientName = savedClient.name,
                email = savedClient.email,
                phone = savedClient.phone,
            )
            domainEventPublisher.publish(event)
        }

        return savedClients
    }
}
