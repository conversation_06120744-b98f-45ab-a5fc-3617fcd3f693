package ro.animaliaprogramari.animalia.application.port.outbound

import ro.animaliaprogramari.animalia.domain.model.PhoneNumber
import ro.animaliaprogramari.animalia.domain.model.PhoneVerificationCode
import ro.animaliaprogramari.animalia.domain.model.PhoneVerificationCodeId

/**
 * Outbound port for phone verification code persistence
 */
interface PhoneVerificationCodeRepository {
    /**
     * Save a phone verification code
     */
    fun save(verificationCode: PhoneVerificationCode): PhoneVerificationCode

    /**
     * Find a verification code by ID
     */
    fun findById(id: PhoneVerificationCodeId): PhoneVerificationCode?

    /**
     * Find the latest active verification code for a phone number
     */
    fun findLatestByPhoneNumber(phoneNumber: PhoneNumber): PhoneVerificationCode?

    /**
     * Find all active verification codes for a phone number
     */
    fun findActiveByPhoneNumber(phoneNumber: PhoneNumber): List<PhoneVerificationCode>

    /**
     * Invalidate all existing verification codes for a phone number
     */
    fun invalidateAllForPhoneNumber(phoneNumber: PhoneNumber)

    /**
     * Delete expired verification codes (for cleanup)
     */
    fun deleteExpiredCodes()

    /**
     * Check if a phone number exists in the system and requires verification
     */
    fun phoneNumberExistsInSystem(phoneNumber: PhoneNumber): Boolean

    /**
     * Find a valid (not used, not expired) verification code for a phone number
     */
    fun findValidCodeForPhoneNumber(phoneNumber: PhoneNumber): PhoneVerificationCode?

    /**
     * Mark a verification code as used
     */
    fun markAsUsed(verificationCode: PhoneVerificationCode): PhoneVerificationCode
}
