package ro.animaliaprogramari.animalia

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.runApplication
import org.springframework.transaction.annotation.EnableTransactionManagement
import ro.animaliaprogramari.animalia.config.BusinessRulesConfig
import java.time.ZoneId
import java.util.TimeZone
import javax.annotation.PostConstruct

@SpringBootApplication
@EnableConfigurationProperties(BusinessRulesConfig::class)
@EnableTransactionManagement
class AnimaliaProgramariBackendApplication {
    @PostConstruct
    fun init() {
        // Set the default timezone for the entire application to Europe/Bucharest
        val bucharestTimeZone = TimeZone.getTimeZone(ZoneId.of("Europe/Bucharest"))
        TimeZone.setDefault(bucharestTimeZone)

        // Log the timezone setting for verification
        println("=== TIMEZONE CONFIGURATION ===")
        println("Application timezone set to: ${TimeZone.getDefault().id}")
        println("System default zone: ${ZoneId.systemDefault()}")
        println("==============================")
    }
}

fun main(args: Array<String>) {
    runApplication<AnimaliaProgramariBackendApplication>(*args)
}
