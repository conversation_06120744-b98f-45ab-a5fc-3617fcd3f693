package ro.animaliaprogramari.animalia.config

/**
 * Configuration class containing all push notification templates for appointment notifications.
 * All messages are in English and follow a professional, warm, and personalized tone.
 * These templates are used for push notifications sent to staff members.
 */
object PushNotificationTemplates {

    // ========== APPOINTMENT REMINDER NOTIFICATIONS ==========

    // ========== APPOINTMENT STATUS NOTIFICATIONS ==========

    /**
     * Title for new appointment notifications
     */
    const val NEW_APPOINTMENT_TITLE = "New Appointment"

    /**
     * Title for appointment cancellation notifications
     */
    const val APPOINTMENT_CANCELLED_TITLE = "Appointment Cancelled"

    /**
     * Title for appointment rescheduled notifications
     */
    const val APPOINTMENT_RESCHEDULED_TITLE = "Appointment Rescheduled"

    /**
     * Title for appointment deleted notifications
     */
    const val APPOINTMENT_DELETED_TITLE = "Appointment Deleted"

    /**
     * Message for new appointment notification
     * Variables: {clientName}, {petName}, {date}, {time}
     */
    const val NEW_APPOINTMENT_MESSAGE = "New appointment: {clientName} - {petName} on {date} at {time}"

    /**
     * Message for appointment cancellation notification
     * Variables: {clientName}, {petName}, {date}, {time}
     */
    const val APPOINTMENT_CANCELLED_MESSAGE = "Appointment cancelled: {clientName} - {petName} on {date} at {time}"

    /**
     * Message for appointment rescheduled notification
     * Variables: {clientName}, {petName}, {oldDate}, {oldTime}, {newDate}, {newTime}
     */
    const val APPOINTMENT_RESCHEDULED_MESSAGE = "Rescheduled: {clientName} - {petName} from {oldDate} {oldTime} to {newDate} at {newTime}"

    /**
     * Message for appointment deleted notification
     * Variables: {clientName}, {petName}, {date}, {time}
     */
    const val APPOINTMENT_DELETED_MESSAGE = "Appointment deleted: {clientName} - {petName} on {date} at {time}"

    // ========== TEST NOTIFICATIONS ==========

    // ========== DEFAULT VALUES ==========

    /**
     * Default client name when client information is not available
     */
    const val DEFAULT_CLIENT_NAME = "Client"

    /**
     * Default pet name when pet information is not available
     */
    const val DEFAULT_PET_NAME = "Pet"

    /**
     * Default notification title when specific title is not available
     */

    // ========== HELPER METHODS ==========

    /**
     * Build a test notification message with index and timestamp
     */
    fun buildTestNotificationMessage(index: Int, timestamp: String): String {
        return "This is a test notification #{index} generated at {timestamp}"
            .replace("{index}", index.toString())
            .replace("{timestamp}", timestamp)
    }
}
