package com.animalia.repository;

import com.animalia.model.SalonWebPreferences;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for SalonWebPreferences entity
 */
@Repository
public interface SalonWebPreferencesRepository extends JpaRepository<SalonWebPreferences, String> {

    /**
     * Find web preferences by salon ID
     * @param salonId the salon ID
     * @return Optional containing the web preferences if found
     */
    Optional<SalonWebPreferences> findBySalonId(String salonId);

    /**
     * Find active web preferences by salon ID
     * @param salonId the salon ID
     * @return Optional containing the active web preferences if found
     */
    @Query("SELECT swp FROM SalonWebPreferences swp WHERE swp.salonId = :salonId AND swp.isActive = true")
    Optional<SalonWebPreferences> findActiveBySalonId(@Param("salonId") String salonId);

    /**
     * Check if web preferences exist for a salon
     * @param salonId the salon ID
     * @return true if preferences exist, false otherwise
     */
    boolean existsBySalonId(String salonId);

    /**
     * Delete web preferences by salon ID
     * @param salonId the salon ID
     */
    void deleteBySalonId(String salonId);
}
