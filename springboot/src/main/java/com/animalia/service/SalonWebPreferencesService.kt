package com.animalia.service

import org.slf4j.LoggerFactory
import org.springframework.data.rest.webmvc.ResourceNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonWebPreferencesRepository
import ro.animaliaprogramari.animalia.domain.model.*
import java.time.LocalDateTime

/**
 * Service for managing salon website preferences
 * Follows hexagonal architecture pattern with domain models and repository ports
 */
@Service
class SalonWebPreferencesService(
    private val salonWebPreferencesRepository: SalonWebPreferencesRepository,
    private val salonRepository: SalonRepository,
) {
    private val logger = LoggerFactory.getLogger(SalonWebPreferencesService::class.java)

    /**
     * Get web preferences for a salon
     * Creates default preferences if none exist
     */
    @Transactional(readOnly = true)
    fun getWebPreferences(salonId: String): SalonWebPreferences {
        logger.info("Getting web preferences for salon: {}", salonId)

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        return salonWebPreferencesRepository.findBySalonId(salonIdDomain)
            ?: run {
                logger.info("Creating default web preferences for salon: {}", salonId)
                createDefaultPreferences(salonIdDomain)
            }
    }

    /**
     * Save web preferences for a salon
     */
    @Transactional
    fun saveWebPreferences(preferences: SalonWebPreferences): SalonWebPreferences {
        logger.info("Saving web preferences for salon: {}", preferences.salonId.value)

        // Verify salon exists
        if (!salonRepository.existsById(preferences.salonId)) {
            throw ResourceNotFoundException("Salon not found with ID: ${preferences.salonId.value}")
        }

        return salonWebPreferencesRepository.save(preferences)
    }

    /**
     * Update web preferences for a salon
     */
    @Transactional
    fun updateWebPreferences(salonId: String, preferences: SalonWebPreferences): SalonWebPreferences {
        logger.info("Updating web preferences for salon: {}", salonId)

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        // Ensure the salon ID matches
        val updatedPreferences = preferences.copy(
            salonId = salonIdDomain,
            updatedAt = LocalDateTime.now()
        )

        return salonWebPreferencesRepository.save(updatedPreferences)
    }

    /**
     * Delete web preferences for a salon
     */
    @Transactional
    fun deleteWebPreferences(salonId: String) {
        logger.info("Deleting web preferences for salon: {}", salonId)

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        salonWebPreferencesRepository.deleteBySalonId(salonIdDomain)
    }

    /**
     * Check if web preferences exist for a salon
     */
    @Transactional(readOnly = true)
    fun hasWebPreferences(salonId: String): Boolean {
        logger.info("Checking web preferences existence for salon: {}", salonId)

        val salonIdDomain = SalonId.of(salonId)
        return salonWebPreferencesRepository.existsBySalonId(salonIdDomain)
    }

    /**
     * Get active web preferences for a salon
     */
    @Transactional(readOnly = true)
    fun getActiveWebPreferences(salonId: String): SalonWebPreferences? {
        logger.info("Getting active web preferences for salon: {}", salonId)

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        return salonWebPreferencesRepository.findActiveBySalonId(salonIdDomain)
    }

    // Legacy methods for backward compatibility with existing controller

    /**
     * Get web preferences for a salon (legacy method)
     * Returns Optional for backward compatibility
     */
    fun getWebPreferences(salonId: String): java.util.Optional<com.animalia.model.SalonWebPreferences> {
        logger.info("Getting web preferences for salon (legacy): {}", salonId)

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            return java.util.Optional.empty()
        }

        val domainPreferences = salonWebPreferencesRepository.findBySalonId(salonIdDomain)
            ?: return java.util.Optional.empty()

        // Convert domain model to JPA entity for backward compatibility
        val entity = convertDomainToEntity(domainPreferences)
        return java.util.Optional.of(entity)
    }

    /**
     * Save web preferences for a salon (legacy method)
     */
    fun saveWebPreferences(salonId: String, preferences: com.animalia.model.SalonWebPreferences?): com.animalia.model.SalonWebPreferences {
        logger.info("Saving web preferences for salon (legacy): {}", salonId)

        if (preferences == null) {
            throw IllegalArgumentException("Preferences cannot be null")
        }

        val salonIdDomain = SalonId.of(salonId)

        // Verify salon exists
        if (!salonRepository.existsById(salonIdDomain)) {
            throw ResourceNotFoundException("Salon not found with ID: $salonId")
        }

        // Convert JPA entity to domain model
        val domainPreferences = convertEntityToDomain(preferences, salonIdDomain)

        // Save using domain repository
        val savedDomain = salonWebPreferencesRepository.save(domainPreferences)

        // Convert back to entity for return
        return convertDomainToEntity(savedDomain)
    }

    /**
     * Convert domain model to JPA entity for backward compatibility
     */
    private fun convertDomainToEntity(domain: SalonWebPreferences): com.animalia.model.SalonWebPreferences {
        val entity = com.animalia.model.SalonWebPreferences()
        entity.id = domain.id.value
        entity.salonId = domain.salonId.value
        entity.bookingWebsiteUrl = domain.bookingWebsiteUrl
        entity.businessName = domain.businessName
        entity.businessDescription = domain.businessDescription
        entity.businessAddress = domain.businessAddress
        entity.contactPhone = domain.contactPhone
        entity.contactEmail = domain.contactEmail
        entity.facebookLink = domain.facebookLink
        entity.instagramLink = domain.instagramLink
        entity.tiktokLink = domain.tiktokLink
        entity.isActive = domain.isActive
        entity.createdAt = domain.createdAt
        entity.updatedAt = domain.updatedAt

        // Convert enums
        entity.cancellationPolicy = when (domain.cancellationPolicy) {
            CancellationPolicy.HOURS_24 -> com.animalia.model.SalonWebPreferences.CancellationPolicy.HOURS_24
            CancellationPolicy.HOURS_48 -> com.animalia.model.SalonWebPreferences.CancellationPolicy.HOURS_48
            CancellationPolicy.HOURS_72 -> com.animalia.model.SalonWebPreferences.CancellationPolicy.HOURS_72
            CancellationPolicy.NO_CHANGES -> com.animalia.model.SalonWebPreferences.CancellationPolicy.NO_CHANGES
        }

        entity.bookingAcceptance = when (domain.bookingAcceptance) {
            BookingAcceptance.AUTOMATIC -> com.animalia.model.SalonWebPreferences.BookingAcceptance.AUTOMATIC
            BookingAcceptance.MANUAL -> com.animalia.model.SalonWebPreferences.BookingAcceptance.MANUAL
        }

        // Convert collections to JSON
        if (domain.websitePhotos.isNotEmpty()) {
            entity.websitePhotos = com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(domain.websitePhotos)
        }

        if (domain.businessHours.isNotEmpty()) {
            entity.businessHours = com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(domain.businessHours)
        }

        return entity
    }

    /**
     * Convert JPA entity to domain model
     */
    private fun convertEntityToDomain(entity: com.animalia.model.SalonWebPreferences, salonId: SalonId): SalonWebPreferences {
        val objectMapper = com.fasterxml.jackson.databind.ObjectMapper()

        // Parse JSON fields
        val websitePhotos = if (entity.websitePhotos.isNullOrBlank()) {
            emptyList()
        } else {
            try {
                objectMapper.readValue(entity.websitePhotos, List::class.java) as List<String>
            } catch (e: Exception) {
                emptyList()
            }
        }

        val businessHours = if (entity.businessHours.isNullOrBlank()) {
            emptyMap()
        } else {
            try {
                objectMapper.readValue(entity.businessHours, Map::class.java) as Map<String, Map<String, Any>>
            } catch (e: Exception) {
                emptyMap()
            }
        }

        return SalonWebPreferences(
            id = if (entity.id.isNullOrBlank()) SalonWebPreferencesId.generate() else SalonWebPreferencesId.of(entity.id),
            salonId = salonId,
            bookingWebsiteUrl = entity.bookingWebsiteUrl ?: "",
            businessName = entity.businessName ?: "",
            businessDescription = entity.businessDescription ?: "",
            businessAddress = entity.businessAddress ?: "",
            contactPhone = entity.contactPhone ?: "",
            contactEmail = entity.contactEmail ?: "",
            facebookLink = entity.facebookLink ?: "",
            instagramLink = entity.instagramLink ?: "",
            tiktokLink = entity.tiktokLink ?: "",
            websitePhotos = websitePhotos,
            businessHours = businessHours,
            cancellationPolicy = when (entity.cancellationPolicy) {
                com.animalia.model.SalonWebPreferences.CancellationPolicy.HOURS_24 -> CancellationPolicy.HOURS_24
                com.animalia.model.SalonWebPreferences.CancellationPolicy.HOURS_48 -> CancellationPolicy.HOURS_48
                com.animalia.model.SalonWebPreferences.CancellationPolicy.HOURS_72 -> CancellationPolicy.HOURS_72
                com.animalia.model.SalonWebPreferences.CancellationPolicy.NO_CHANGES -> CancellationPolicy.NO_CHANGES
                null -> CancellationPolicy.HOURS_24
            },
            bookingAcceptance = when (entity.bookingAcceptance) {
                com.animalia.model.SalonWebPreferences.BookingAcceptance.AUTOMATIC -> BookingAcceptance.AUTOMATIC
                com.animalia.model.SalonWebPreferences.BookingAcceptance.MANUAL -> BookingAcceptance.MANUAL
                null -> BookingAcceptance.AUTOMATIC
            },
            isActive = entity.isActive ?: true,
            createdAt = entity.createdAt ?: LocalDateTime.now(),
            updatedAt = entity.updatedAt ?: LocalDateTime.now()
        )
    }

    /**
     * Create default web preferences for a salon
     */
    private fun createDefaultPreferences(salonId: SalonId): SalonWebPreferences {
        val defaultPreferences = SalonWebPreferences(
            id = SalonWebPreferencesId.generate(),
            salonId = salonId,
            bookingWebsiteUrl = "",
            businessName = "",
            businessDescription = "",
            businessAddress = "",
            contactPhone = "",
            contactEmail = "",
            facebookLink = "",
            instagramLink = "",
            tiktokLink = "",
            websitePhotos = emptyList(),
            businessHours = emptyMap(),
            cancellationPolicy = CancellationPolicy.HOURS_24,
            bookingAcceptance = BookingAcceptance.AUTOMATIC,
            isActive = true,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        return salonWebPreferencesRepository.save(defaultPreferences)
    }
}import com.animalia.model.SalonWebPreferences
import com.animalia.repository.SalonWebPreferencesRepository
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*
import java.util.Map

/**
 * Service for managing salon website preferences
 */
@Service
@Transactional
class SalonWebPreferencesService {
    @Autowired
    private val repository: SalonWebPreferencesRepository? = null

    @Autowired
    private val objectMapper: ObjectMapper? = null

    /**
     * Get web preferences for a salon
     * @param salonId the salon ID
     * @return Optional containing the web preferences if found
     */
    fun getWebPreferences(salonId: String?): Optional<SalonWebPreferences?>? {
        logger.debug("Getting web preferences for salon: {}", salonId)
        return repository!!.findActiveBySalonId(salonId)
    }

    /**
     * Save or update web preferences for a salon
     * @param salonId the salon ID
     * @param preferences the web preferences to save
     * @return the saved web preferences
     */
    fun saveWebPreferences(salonId: String?, preferences: SalonWebPreferences?): SalonWebPreferences {
        logger.info("Saving web preferences for salon: {}", salonId)

        // Check if preferences already exist
        val existingPreferences = repository!!.findBySalonId(salonId)

        val toSave: SalonWebPreferences
        if (existingPreferences.isPresent()) {
            // Update existing preferences
            toSave = existingPreferences.get()
            updatePreferencesFields(toSave, preferences)
            logger.debug("Updating existing web preferences for salon: {}", salonId)
        } else {
            // Create new preferences
            if (preferences != null) {
                toSave = preferences
                toSave.setId(UUID.randomUUID().toString())
                toSave.setSalonId(salonId)
                toSave.setIsActive(true)
            }
            logger.debug("Creating new web preferences for salon: {}", salonId)
        }

        return repository.save<SalonWebPreferences?>(toSave)
    }

    /**
     * Update existing preferences with new values
     */
    private fun updatePreferencesFields(existing: SalonWebPreferences, updated: SalonWebPreferences?) {
        // Basic Info
        if (updated.getBookingWebsiteUrl() != null) {
            existing.setBookingWebsiteUrl(updated.getBookingWebsiteUrl())
        }
        if (updated.getBusinessName() != null) {
            existing.setBusinessName(updated.getBusinessName())
        }
        if (updated.getBusinessDescription() != null) {
            existing.setBusinessDescription(updated.getBusinessDescription())
        }
        if (updated.getBusinessAddress() != null) {
            existing.setBusinessAddress(updated.getBusinessAddress())
        }
        if (updated.getContactPhone() != null) {
            existing.setContactPhone(updated.getContactPhone())
        }
        if (updated.getContactEmail() != null) {
            existing.setContactEmail(updated.getContactEmail())
        }
        if (updated.getFacebookLink() != null) {
            existing.setFacebookLink(updated.getFacebookLink())
        }
        if (updated.getInstagramLink() != null) {
            existing.setInstagramLink(updated.getInstagramLink())
        }
        if (updated.getTiktokLink() != null) {
            existing.setTiktokLink(updated.getTiktokLink())
        }

        // Website Photos
        if (updated.getWebsitePhotos() != null) {
            existing.setWebsitePhotos(updated.getWebsitePhotos())
        }

        // Business Hours
        if (updated.getBusinessHours() != null) {
            existing.setBusinessHours(updated.getBusinessHours())
        }

        // Extra Info
        if (updated.getCancellationPolicy() != null) {
            existing.setCancellationPolicy(updated.getCancellationPolicy())
        }

        // Booking Acceptance
        if (updated.getBookingAcceptance() != null) {
            existing.setBookingAcceptance(updated.getBookingAcceptance())
        }
    }

    /**
     * Delete web preferences for a salon
     * @param salonId the salon ID
     */
    fun deleteWebPreferences(salonId: String?) {
        logger.info("Deleting web preferences for salon: {}", salonId)
        repository!!.deleteBySalonId(salonId)
    }

    /**
     * Check if web preferences exist for a salon
     * @param salonId the salon ID
     * @return true if preferences exist, false otherwise
     */
    fun hasWebPreferences(salonId: String?): Boolean {
        return repository!!.existsBySalonId(salonId)
    }

    /**
     * Convert photo URLs list to JSON string
     * @param photoUrls list of photo URLs
     * @return JSON string representation
     */
    fun convertPhotosToJson(photoUrls: MutableList<String?>?): String? {
        try {
            return objectMapper!!.writeValueAsString(photoUrls)
        } catch (e: JsonProcessingException) {
            logger.error("Error converting photos to JSON", e)
            return "[]"
        }
    }

    /**
     * Convert business hours map to JSON string
     * @param businessHours map of business hours
     * @return JSON string representation
     */
    fun convertBusinessHoursToJson(businessHours: MutableMap<String?, MutableMap<String?, Any?>?>?): String? {
        try {
            return objectMapper!!.writeValueAsString(businessHours)
        } catch (e: JsonProcessingException) {
            logger.error("Error converting business hours to JSON", e)
            return "{}"
        }
    }

    /**
     * Parse JSON string to photo URLs list
     * @param photosJson JSON string
     * @return list of photo URLs
     */
    fun parsePhotosFromJson(photosJson: String?): MutableList<String?>? {
        try {
            return objectMapper!!.readValue<MutableList<*>?>(photosJson, MutableList::class.java)
        } catch (e: Exception) {
            logger.error("Error parsing photos from JSON", e)
            return mutableListOf<String?>()
        }
    }

    /**
     * Parse JSON string to business hours map
     * @param businessHoursJson JSON string
     * @return map of business hours
     */
    fun parseBusinessHoursFromJson(businessHoursJson: String?): MutableMap<String?, MutableMap<String?, Any?>?>? {
        try {
            return objectMapper!!.readValue<MutableMap<*, *>?>(businessHoursJson, MutableMap::class.java)
        } catch (e: Exception) {
            logger.error("Error parsing business hours from JSON", e)
            return Map.of<String?, MutableMap<String?, Any?>?>()
        }
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(SalonWebPreferencesService::class.java)
    }
}
