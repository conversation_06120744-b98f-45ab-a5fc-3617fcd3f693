package com.animalia.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Entity representing salon website management preferences
 */
@Entity
@Table(name = "salon_web_preferences")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SalonWebPreferences {

    @Id
    @Column(name = "id")
    private String id;

    @Column(name = "salon_id", nullable = false, unique = true)
    @JsonProperty("salon_id")
    private String salonId;

    // Basic Info
    @Column(name = "booking_website_url", length = 500)
    @JsonProperty("booking_website_url")
    private String bookingWebsiteUrl;

    @Column(name = "business_name")
    @JsonProperty("business_name")
    private String businessName;

    @Column(name = "business_description", columnDefinition = "TEXT")
    @JsonProperty("business_description")
    private String businessDescription;

    @Column(name = "business_address", columnDefinition = "TEXT")
    @JsonProperty("business_address")
    private String businessAddress;

    @Column(name = "contact_phone", length = 50)
    @JsonProperty("contact_phone")
    private String contactPhone;

    @Column(name = "contact_email")
    @JsonProperty("contact_email")
    private String contactEmail;

    @Column(name = "facebook_link", length = 500)
    @JsonProperty("facebook_link")
    private String facebookLink;

    @Column(name = "instagram_link", length = 500)
    @JsonProperty("instagram_link")
    private String instagramLink;

    @Column(name = "tiktok_link", length = 500)
    @JsonProperty("tiktok_link")
    private String tiktokLink;

    // Website Photos (stored as JSON)
    @Column(name = "website_photos", columnDefinition = "TEXT")
    @JsonProperty("website_photos")
    private String websitePhotos; // JSON array of photo URLs

    // Business Hours (stored as JSON)
    @Column(name = "business_hours", columnDefinition = "TEXT")
    @JsonProperty("business_hours")
    private String businessHours; // JSON object with daily hours

    // Extra Info
    @Enumerated(EnumType.STRING)
    @Column(name = "cancellation_policy", length = 50)
    @JsonProperty("cancellation_policy")
    private CancellationPolicy cancellationPolicy;

    // Booking Acceptance
    @Enumerated(EnumType.STRING)
    @Column(name = "booking_acceptance", length = 20)
    @JsonProperty("booking_acceptance")
    private BookingAcceptance bookingAcceptance;

    // Metadata
    @Column(name = "is_active", nullable = false)
    @JsonProperty("is_active")
    private Boolean isActive = true;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    @JsonProperty("updated_at")
    private LocalDateTime updatedAt;

    // Enums
    public enum CancellationPolicy {
        HOURS_24,
        HOURS_48,
        HOURS_72,
        NO_CHANGES
    }

    public enum BookingAcceptance {
        AUTOMATIC,
        MANUAL
    }

    // Constructors
    public SalonWebPreferences() {}

    public SalonWebPreferences(String id, String salonId) {
        this.id = id;
        this.salonId = salonId;
        this.isActive = true;
        this.cancellationPolicy = CancellationPolicy.HOURS_24;
        this.bookingAcceptance = BookingAcceptance.AUTOMATIC;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getSalonId() { return salonId; }
    public void setSalonId(String salonId) { this.salonId = salonId; }

    public String getBookingWebsiteUrl() { return bookingWebsiteUrl; }
    public void setBookingWebsiteUrl(String bookingWebsiteUrl) { this.bookingWebsiteUrl = bookingWebsiteUrl; }

    public String getBusinessName() { return businessName; }
    public void setBusinessName(String businessName) { this.businessName = businessName; }

    public String getBusinessDescription() { return businessDescription; }
    public void setBusinessDescription(String businessDescription) { this.businessDescription = businessDescription; }

    public String getBusinessAddress() { return businessAddress; }
    public void setBusinessAddress(String businessAddress) { this.businessAddress = businessAddress; }

    public String getContactPhone() { return contactPhone; }
    public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }

    public String getContactEmail() { return contactEmail; }
    public void setContactEmail(String contactEmail) { this.contactEmail = contactEmail; }

    public String getFacebookLink() { return facebookLink; }
    public void setFacebookLink(String facebookLink) { this.facebookLink = facebookLink; }

    public String getInstagramLink() { return instagramLink; }
    public void setInstagramLink(String instagramLink) { this.instagramLink = instagramLink; }

    public String getTiktokLink() { return tiktokLink; }
    public void setTiktokLink(String tiktokLink) { this.tiktokLink = tiktokLink; }

    public String getWebsitePhotos() { return websitePhotos; }
    public void setWebsitePhotos(String websitePhotos) { this.websitePhotos = websitePhotos; }

    public String getBusinessHours() { return businessHours; }
    public void setBusinessHours(String businessHours) { this.businessHours = businessHours; }

    public CancellationPolicy getCancellationPolicy() { return cancellationPolicy; }
    public void setCancellationPolicy(CancellationPolicy cancellationPolicy) { this.cancellationPolicy = cancellationPolicy; }

    public BookingAcceptance getBookingAcceptance() { return bookingAcceptance; }
    public void setBookingAcceptance(BookingAcceptance bookingAcceptance) { this.bookingAcceptance = bookingAcceptance; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
