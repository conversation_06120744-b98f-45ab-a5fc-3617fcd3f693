# Development profile configuration
# This file contains development-specific overrides

# Server configuration for development
server.port=8081
server.servlet.context-path=/api

# Development database configuration - Local PostgreSQL
spring.datasource.url=${DATABASE_URL:*********************************************}
spring.datasource.username=${DATABASE_USERNAME:animalia_dev_user}
spring.datasource.password=${DATABASE_PASSWORD:dev_password_123}
spring.datasource.driver-class-name=org.postgresql.Driver

# Development connection pool settings (smaller pool for dev)
spring.datasource.hikari.maximum-pool-size=${DB_POOL_SIZE:5}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:1}
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}
spring.datasource.hikari.max-lifetime=${DB_MAX_LIFETIME:1800000}
spring.datasource.hikari.leak-detection-threshold=${DB_LEAK_DETECTION:60000}
spring.datasource.hikari.pool-name=AnimaliaDevPool

# JPA/Hibernate configuration for development
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.hbm2ddl.auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.globally_quoted_identifiers=false
spring.jpa.properties.hibernate.globally_quoted_identifiers_skip_column_definitions=true

# Flyway configuration - ENABLED for migration
spring.flyway.enabled=true
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=1
spring.flyway.locations=classpath:db/migration
spring.flyway.validate-on-migrate=false
spring.flyway.out-of-order=false

# Development logging configuration
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=ERROR
logging.level.org.hibernate.SQL=ERROR
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.ro.animaliaprogramari=DEBUG
logging.level.org.springframework.transaction=DEBUG

# Enable development tools
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# CORS configuration for development (allow all origins)
cors.allowed-origins=http://localhost:3000,http://localhost:8080,http://localhost:4200

# Security configuration for development
jwt.secret=${JWT_SECRET:devSecretKeyForJwtTokenGenerationAndValidationWithExtraLengthToMeetHS512RequirementsInDevelopment}
jwt.expiration=${JWT_EXPIRATION:2592000000}

# Firebase configuration for development
firebase.project-id=${FIREBASE_PROJECT_ID:animalia-de0f1}
firebase.database-url=${FIREBASE_DATABASE_URL:https://animalia-grooming-default-rtdb.europe-west1.firebasedatabase.app}
firebase.storage-bucket=${FIREBASE_STORAGE_BUCKET:animalia-grooming.appspot.com}

# Enable Docker Compose for development
spring.docker.compose.enabled=true
spring.docker.compose.file=docker-compose.yml

# Enable Spring Data REST for development debugging
spring.data.rest.enabled=true

# Actuator endpoints for development monitoring
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.env.show-values=always

# Development-specific optimizations
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Timezone
spring.jpa.properties.hibernate.jdbc.time_zone=Europe/Bucharest

# Development notification settings
notifications.sms.enabled=true
notifications.push.enabled=true
notifications.async.enabled=true
notifications.async.threadPoolSize=2

# Development phone validation
phone.validation.countryCode=+40
phone.validation.minLength=10
phone.validation.maxLength=10



# Development Cloudinary settings (use test credentials)
cloudinary.cloud-name=${CLOUDINARY_CLOUD_NAME:datjgpnel}
cloudinary.api-key=${CLOUDINARY_API_KEY:616156997515572}
cloudinary.api-secret=${CLOUDINARY_API_SECRET:g6BReXQ1_jxqJ-whjWMGN_CATT0}

# Development email settings
spring.mail.host=${MAIL_HOST:localhost}
spring.mail.port=${MAIL_PORT:1025}
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

