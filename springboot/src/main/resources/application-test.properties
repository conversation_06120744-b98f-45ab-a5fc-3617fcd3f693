# Test profile configuration
# This file contains test-specific overrides

# Server configuration for test
server.port=8080
server.servlet.context-path=/api

# Test database configuration - Local PostgreSQL
spring.datasource.url=${DATABASE_URL:**********************************************}
spring.datasource.username=${DATABASE_USERNAME:animalia_test_user}
spring.datasource.password=${DATABASE_PASSWORD:test_password_456}
spring.datasource.driver-class-name=org.postgresql.Driver

# Test connection pool settings (medium pool for test)
spring.datasource.hikari.maximum-pool-size=${DB_POOL_SIZE:8}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:2}
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}
spring.datasource.hikari.max-lifetime=${DB_MAX_LIFETIME:1800000}
spring.datasource.hikari.leak-detection-threshold=${DB_LEAK_DETECTION:60000}
spring.datasource.hikari.pool-name=AnimaliaTestPool

# JPA/Hibernate configuration for test
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.hbm2ddl.auto=create-drop
spring.jpa.properties.hibernate.globally_quoted_identifiers=false
spring.jpa.properties.hibernate.globally_quoted_identifiers_skip_column_definitions=true

# Flyway configuration - DISABLED for test (using Hibernate)
spring.flyway.enabled=false

# Test logging configuration (less verbose than dev)
logging.level.org.springframework.web=INFO
logging.level.org.springframework.security=INFO
logging.level.org.hibernate.SQL=INFO
logging.level.ro.animaliaprogramari=INFO
logging.level.org.springframework.transaction=INFO

# Disable development tools in test
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false

# CORS configuration for test (more restrictive)
cors.allowed-origins=http://localhost:3000,http://localhost:8081,http://localhost:4200,https://test.animalia-app.com

# Security configuration for test
jwt.secret=${JWT_SECRET:testSecretKeyForJwtTokenGenerationAndValidationWithExtraLengthToMeetHS512RequirementsInTestEnvironment}
jwt.expiration=${JWT_EXPIRATION:2592000000}

# Firebase configuration for test (can use same as dev or separate project)
firebase.project-id=${FIREBASE_PROJECT_ID:animalia-de0f1}
firebase.database-url=${FIREBASE_DATABASE_URL:https://animalia-grooming-default-rtdb.europe-west1.firebasedatabase.app}
firebase.storage-bucket=${FIREBASE_STORAGE_BUCKET:animalia-grooming.appspot.com}

# Disable Docker Compose for test (assuming external containers)
spring.docker.compose.enabled=false

# Disable Spring Data REST for test
spring.data.rest.enabled=false

# Actuator endpoints for test monitoring
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized
management.endpoint.env.show-values=never

# Test-specific optimizations
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Timezone
spring.jpa.properties.hibernate.jdbc.time_zone=Europe/Bucharest

# Test notification settings (enable all for testing)
notifications.sms.enabled=true
notifications.push.enabled=true
notifications.async.enabled=true
notifications.async.threadPoolSize=3

# Test phone validation
phone.validation.countryCode=+40
phone.validation.minLength=10
phone.validation.maxLength=10

# Test Cloudinary settings (use test credentials)
cloudinary.cloud-name=${CLOUDINARY_CLOUD_NAME:}
cloudinary.api-key=${CLOUDINARY_API_KEY:}
cloudinary.api-secret=${CLOUDINARY_API_SECRET:}

# Test email settings (can use real SMTP for testing)
spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Test-specific test data settings
test.data.enabled=true
test.data.create-sample-data=true
