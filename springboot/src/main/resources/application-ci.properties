# CI-specific configuration for faster tests
# Inherits from test profile but with additional optimizations

# Database optimizations for CI
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.connection-timeout=10000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=600000

# JPA optimizations for CI
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.properties.hibernate.jdbc.batch_size=50
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
spring.jpa.properties.hibernate.generate_statistics=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Disable Flyway
spring.flyway.enabled=false

# Logging optimizations
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN
logging.level.com.zaxxer.hikari=WARN
logging.level.org.postgresql=WARN
logging.level.org.testcontainers=WARN
logging.level.org.springframework.web=WARN
logging.level.org.springframework.security=WARN

# Disable unnecessary features for CI
spring.jmx.enabled=false
management.endpoints.enabled-by-default=false
management.endpoint.health.enabled=true

# Timezone
spring.jpa.properties.hibernate.jdbc.time_zone=Europe/Bucharest

# Test-specific optimizations
spring.test.database.replace=none
spring.sql.init.mode=never
