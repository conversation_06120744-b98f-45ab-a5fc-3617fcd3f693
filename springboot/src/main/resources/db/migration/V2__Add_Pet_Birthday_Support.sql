-- Add birth_date column to pets table (with defensive check)
DO $$
BEGIN
    -- Check if pets table exists before trying to alter it
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'pets') THEN
        -- Add birth_date column if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'pets' AND column_name = 'birth_date'
        ) THEN
            ALTER TABLE pets ADD COLUMN birth_date DATE;
        END IF;
    ELSE
        -- If pets table doesn't exist, create it first
        CREATE TABLE pets
        (
            id                 VARCHAR(255) NOT NULL,
            client_id          VARCHAR(255) NOT NULL,
            name               VARCHAR(255) NOT NULL,
            breed              VARCHAR(255),
            species            VARCHAR(255),
            size               VARCHAR(255),
            age                INTEGER,
            weight             DECIMAL(5, 2),
            color              VARCHAR(100),
            gender             VARCHAR(20),
            notes              TEXT,
            medical_conditions TEXT,
            is_active          BOOLEAN DEFAULT TRUE,
            created_at         TIMESTAMP WITHOUT TIME ZONE,
            updated_at         TIMESTAMP WITHOUT TIME ZONE,
            photo_url          VARCHAR(500),
            birth_date         DATE,
            CONSTRAINT pk_pets PRIMARY KEY (id)
        );

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_pets_breed_size ON pets (breed, size);
        CREATE INDEX IF NOT EXISTS idx_pets_client_active ON pets (client_id, is_active);
        CREATE INDEX IF NOT EXISTS idx_pets_name_client ON pets (name, client_id);
        CREATE INDEX IF NOT EXISTS idx_pets_species_breed ON pets (species, breed);
    END IF;
END $$;

-- Add birthday_messages column to sms_reminder_settings table
-- ALTER TABLE sms_reminder_settings
--     ADD COLUMN IF NOT EXISTS birthday_messages BOOLEAN NOT NULL DEFAULT FALSE;

DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'sms_reminder_settings'
    ) THEN
        ALTER TABLE sms_reminder_settings
            ADD COLUMN IF NOT EXISTS birthday_messages BOOLEAN NOT NULL DEFAULT FALSE;
    END IF;
END $$;
