-- Fix missing core tables issue
-- This migration ensures essential tables exist before subsequent migrations try to modify them
-- This is a defensive migration to handle cases where V1 didn't complete successfully

-- Ensure salons table exists (referenced by many other tables)
CREATE TABLE IF NOT EXISTS salons
(
    id                     VARCHAR(255) NOT NULL,
    name                   VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    address                TEXT,
    city                   VARCHAR(255),
    phone                  VARCHAR(50),
    email                  VARCHAR(255),
    owner_id               VARCHAR(255) NOT NULL,
    is_active              BOOLEAN DEFAULT TRUE,
    client_ids             TEXT,
    created_at             TIMESTAMP WITHOUT TIME ZONE,
    updated_at             TIMESTAMP WITHOUT TIME ZONE,
    description            VARCHAR(255),
    additional_slots_count INTEGER DEFAULT 0,
    CONSTRAINT pk_salons PRIMARY KEY (id)
);

-- Ensure clients table exists
CREATE TABLE IF NOT EXISTS clients
(
    id         VARCHAR(255) NOT NULL,
    salon_id   VARCHAR(255),
    name       <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    phone      <PERSON><PERSON>HA<PERSON>(50),
    email      VARCHAR(255),
    address    TEXT,
    notes      TEXT,
    is_active  BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_clients PRIMARY KEY (id)
);

-- Ensure pets table exists
CREATE TABLE IF NOT EXISTS pets
(
    id                 VARCHAR(255) NOT NULL,
    client_id          VARCHAR(255) NOT NULL,
    name               VARCHAR(255) NOT NULL,
    breed              VARCHAR(255),
    species            VARCHAR(255),
    size               VARCHAR(255),
    age                INTEGER,
    weight             DECIMAL(5, 2),
    color              VARCHAR(100),
    gender             VARCHAR(20),
    notes              TEXT,
    medical_conditions TEXT,
    is_active          BOOLEAN DEFAULT TRUE,
    created_at         TIMESTAMP WITHOUT TIME ZONE,
    updated_at         TIMESTAMP WITHOUT TIME ZONE,
    photo_url          VARCHAR(500),
    CONSTRAINT pk_pets PRIMARY KEY (id)
);

-- Ensure appointments table exists (referenced by V3)
CREATE TABLE IF NOT EXISTS appointments
(
    id                      VARCHAR(255)                NOT NULL,
    salon_id                VARCHAR(255)                NOT NULL,
    client_id               VARCHAR(255)                NOT NULL,
    pet_id                  VARCHAR(255)                NOT NULL,
    staff_id                VARCHAR(255)                NOT NULL,
    appointment_date        date                        NOT NULL,
    start_time              time WITHOUT TIME ZONE      NOT NULL,
    end_time                time WITHOUT TIME ZONE      NOT NULL,
    status                  VARCHAR(255)                NOT NULL,
    total_price             DECIMAL(10, 2)              NOT NULL,
    total_duration          INTEGER                     NOT NULL,
    notes                   TEXT,
    subscription_id         VARCHAR(255),
    sequence_number         INTEGER,
    is_recurring            BOOLEAN                     NOT NULL,
    completed_at            TIMESTAMP WITHOUT TIME ZONE,
    actual_duration_minutes INTEGER,
    created_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    version                 BIGINT                      NOT NULL,
    CONSTRAINT pk_appointments PRIMARY KEY (id)
);

-- Ensure appointment_subscriptions table exists (referenced by V3)
CREATE TABLE IF NOT EXISTS appointment_subscriptions
(
    id                      VARCHAR(255)                NOT NULL,
    client_id               VARCHAR(255)                NOT NULL,
    salon_id                VARCHAR(255)                NOT NULL,
    original_appointment_id VARCHAR(255)                NOT NULL,
    frequency               VARCHAR(255)                NOT NULL,
    interval_value          INTEGER                     NOT NULL,
    start_date              date                        NOT NULL,
    end_date                date,
    max_occurrences         INTEGER,
    is_active               BOOLEAN                     NOT NULL,
    created_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_appointment_subscriptions PRIMARY KEY (id)
);

-- Ensure sms_reminder_settings table exists (referenced by V2, V5, V6)
CREATE TABLE IF NOT EXISTS sms_reminder_settings
(
    salon_id                  VARCHAR(255)                NOT NULL,
    enabled                   BOOLEAN                     NOT NULL,
    appointment_confirmations BOOLEAN                     NOT NULL,
    completion_messages       BOOLEAN                     NOT NULL,
    follow_up_messages        BOOLEAN                     NOT NULL,
    selected_provider         VARCHAR(255),
    created_at                TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at                TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_sms_reminder_settings PRIMARY KEY (salon_id)
);

-- Create essential indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_salons_owner_id ON salons (owner_id);
CREATE INDEX IF NOT EXISTS idx_salons_active ON salons (is_active);

CREATE INDEX IF NOT EXISTS idx_clients_email_active ON clients (email, is_active);
CREATE INDEX IF NOT EXISTS idx_clients_name ON clients (name);
CREATE INDEX IF NOT EXISTS idx_clients_phone_active ON clients (phone, is_active);
CREATE INDEX IF NOT EXISTS idx_clients_salon_active ON clients (salon_id, is_active);

CREATE INDEX IF NOT EXISTS idx_pets_breed_size ON pets (breed, size);
CREATE INDEX IF NOT EXISTS idx_pets_client_active ON pets (client_id, is_active);
CREATE INDEX IF NOT EXISTS idx_pets_name_client ON pets (name, client_id);
CREATE INDEX IF NOT EXISTS idx_pets_species_breed ON pets (species, breed);

CREATE INDEX IF NOT EXISTS idx_appointments_client_date ON appointments (client_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_pet_date ON appointments (pet_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_salon_date ON appointments (salon_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_status_date ON appointments (status, appointment_date);
