-- Create salon_web_preferences table for website management settings
-- This table stores all website customization preferences for each salon

CREATE TABLE IF NOT EXISTS salon_web_preferences (
    id VARCHAR(255) NOT NULL,
    salon_id VARCHAR(255) NOT NULL,
    
    -- Basic Info
    booking_website_url VARCHAR(500),
    business_name VARCHAR(255),
    business_description TEXT,
    business_address TEXT,
    contact_phone VARCHAR(50),
    contact_email VARCHAR(255),
    facebook_link VARCHAR(500),
    instagram_link VARCHAR(500),
    tiktok_link VARCHAR(500),
    
    -- Website Photos (JSON array of photo URLs)
    website_photos TEXT, -- JSON array: ["url1", "url2", ...]
    
    -- Business Hours (JSON object)
    business_hours TEXT, -- JSON: {"monday": {"open": "09:00", "close": "18:00", "isOpen": true}, ...}
    
    -- Extra Info
    cancellation_policy VARCHAR(50), -- HOURS_24, HOURS_48, HOURS_72, NO_CHANGES
    
    -- Booking Acceptance
    booking_acceptance VARCHAR(20), -- AUTOMATIC, MANUAL
    
    -- Metadata
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT pk_salon_web_preferences PRIMARY KEY (id),
    CONSTRAINT uk_salon_web_preferences_salon_id UNIQUE (salon_id),
    CONSTRAINT fk_salon_web_preferences_salon_id FOREIGN KEY (salon_id) REFERENCES salons(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_salon_web_preferences_salon_id ON salon_web_preferences(salon_id);
CREATE INDEX IF NOT EXISTS idx_salon_web_preferences_active ON salon_web_preferences(is_active);
CREATE INDEX IF NOT EXISTS idx_salon_web_preferences_booking_acceptance ON salon_web_preferences(booking_acceptance);

-- Add comments for documentation
COMMENT ON TABLE salon_web_preferences IS 'Stores website management and customization preferences for each salon';
COMMENT ON COLUMN salon_web_preferences.booking_website_url IS 'Auto-generated booking website URL (e.g., https://animalia-programari.ro/salon_name)';
COMMENT ON COLUMN salon_web_preferences.website_photos IS 'JSON array of photo URLs for website gallery';
COMMENT ON COLUMN salon_web_preferences.business_hours IS 'JSON object containing business hours for each day of the week';
COMMENT ON COLUMN salon_web_preferences.cancellation_policy IS 'Cancellation policy: HOURS_24, HOURS_48, HOURS_72, NO_CHANGES';
COMMENT ON COLUMN salon_web_preferences.booking_acceptance IS 'Booking acceptance mode: AUTOMATIC or MANUAL';

-- End of migration
