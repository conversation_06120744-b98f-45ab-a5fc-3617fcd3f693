-- Add payment model support for recurring appointments

-- Ensure required tables exist before altering them and creating foreign keys
CREATE TABLE IF NOT EXISTS salons
(
    id                     VARCHAR(255) NOT NULL,
    name                   VARCHAR(255) NOT NULL,
    address                TEXT,
    city                   VARCHAR(255),
    phone                  VARCHAR(50),
    email                  VARCHAR(255),
    owner_id               VARCHAR(255) NOT NULL,
    is_active              BOOLEAN DEFAULT TRUE,
    client_ids             TEXT,
    created_at             TIMESTAMP WITHOUT TIME ZONE,
    updated_at             TIMESTAMP WITHOUT TIME ZONE,
    description            VARCHAR(255),
    additional_slots_count INTEGER DEFAULT 0,
    CONSTRAINT pk_salons PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS clients
(
    id         VARCHAR(255) NOT NULL,
    salon_id   VARCHAR(255),
    name       VARCHAR(255) NOT NULL,
    phone      VARCHAR(50),
    email      VARCHAR(255),
    address    TEXT,
    notes      TEXT,
    is_active  BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_clients PRIMARY KEY (id)
);
CREATE TABLE IF NOT EXISTS appointments
(
    id                      VARCHAR(255)                NOT NULL,
    salon_id                VARCHAR(255)                NOT NULL,
    client_id               VARCHAR(255)                NOT NULL,
    pet_id                  VARCHAR(255)                NOT NULL,
    staff_id                VARCHAR(255)                NOT NULL,
    appointment_date        date                        NOT NULL,
    start_time              time WITHOUT TIME ZONE      NOT NULL,
    end_time                time WITHOUT TIME ZONE      NOT NULL,
    status                  VARCHAR(255)                NOT NULL,
    total_price             DECIMAL(10, 2)              NOT NULL,
    total_duration          INTEGER                     NOT NULL,
    notes                   TEXT,
    subscription_id         VARCHAR(255),
    sequence_number         INTEGER,
    is_recurring            BOOLEAN                     NOT NULL,
    completed_at            TIMESTAMP WITHOUT TIME ZONE,
    actual_duration_minutes INTEGER,
    created_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    version                 BIGINT                      NOT NULL,
    CONSTRAINT pk_appointments PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS appointment_subscriptions
(
    id                      VARCHAR(255)                NOT NULL,
    client_id               VARCHAR(255)                NOT NULL,
    salon_id                VARCHAR(255)                NOT NULL,
    original_appointment_id VARCHAR(255)                NOT NULL,
    frequency               VARCHAR(255)                NOT NULL,
    interval_value          INTEGER                     NOT NULL,
    start_date              date                        NOT NULL,
    end_date                date,
    max_occurrences         INTEGER,
    is_active               BOOLEAN                     NOT NULL,
    created_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_appointment_subscriptions PRIMARY KEY (id)
);

-- Add is_prepaid column to appointments table
ALTER TABLE appointments
    ADD COLUMN IF NOT EXISTS is_prepaid BOOLEAN NOT NULL DEFAULT FALSE;

-- Add payment model fields to appointment_subscriptions table
ALTER TABLE appointment_subscriptions
    ADD COLUMN IF NOT EXISTS payment_model VARCHAR(50) NOT NULL DEFAULT 'PER_APPOINTMENT';

ALTER TABLE appointment_subscriptions
    ADD COLUMN IF NOT EXISTS discount_percentage DECIMAL(5,2);

ALTER TABLE appointment_subscriptions
    ADD COLUMN IF NOT EXISTS total_upfront_amount DECIMAL(10,2);

ALTER TABLE appointment_subscriptions
    ADD COLUMN IF NOT EXISTS appointment_price DECIMAL(10,2) NOT NULL DEFAULT 0.00;

-- Create recurring_appointment_payments table for payment tracking
CREATE TABLE IF NOT EXISTS recurring_appointment_payments (
    id VARCHAR(255) PRIMARY KEY,
    subscription_id VARCHAR(255) NOT NULL,
    salon_id VARCHAR(255) NOT NULL,
    client_id VARCHAR(255) NOT NULL,
    payment_model VARCHAR(50) NOT NULL DEFAULT 'PER_APPOINTMENT',
    payment_amount DECIMAL(10,2) NOT NULL,
    original_total_amount DECIMAL(10,2),
    discount_percentage DECIMAL(5,2),
    discount_amount DECIMAL(10,2),
    number_of_appointments INTEGER NOT NULL DEFAULT 1,
    appointment_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    payment_status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    payment_method VARCHAR(50),
    payment_reference VARCHAR(255),
    notes TEXT,
    paid_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_recurring_payments_subscription 
        FOREIGN KEY (subscription_id) REFERENCES appointment_subscriptions(id) ON DELETE CASCADE,
    CONSTRAINT fk_recurring_payments_salon 
        FOREIGN KEY (salon_id) REFERENCES salons(id) ON DELETE CASCADE,
    CONSTRAINT fk_recurring_payments_client 
        FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_recurring_payments_subscription_id 
    ON recurring_appointment_payments(subscription_id);

CREATE INDEX IF NOT EXISTS idx_recurring_payments_salon_id 
    ON recurring_appointment_payments(salon_id);

CREATE INDEX IF NOT EXISTS idx_recurring_payments_client_id 
    ON recurring_appointment_payments(client_id);

CREATE INDEX IF NOT EXISTS idx_recurring_payments_status 
    ON recurring_appointment_payments(payment_status);

CREATE INDEX IF NOT EXISTS idx_recurring_payments_created_at 
    ON recurring_appointment_payments(created_at);

-- Add comments for documentation
COMMENT ON COLUMN appointments.is_prepaid IS 'Indicates if this appointment was paid for upfront as part of a recurring series';

COMMENT ON COLUMN appointment_subscriptions.payment_model IS 'Payment model: PER_APPOINTMENT or UPFRONT_WITH_DISCOUNT';
COMMENT ON COLUMN appointment_subscriptions.discount_percentage IS 'Discount percentage for upfront payments (0-100)';
COMMENT ON COLUMN appointment_subscriptions.total_upfront_amount IS 'Total amount paid upfront after discount';
COMMENT ON COLUMN appointment_subscriptions.appointment_price IS 'Original price per appointment before discount';

COMMENT ON TABLE recurring_appointment_payments IS 'Tracks payments for recurring appointment subscriptions';
COMMENT ON COLUMN recurring_appointment_payments.payment_model IS 'Payment model used: PER_APPOINTMENT or UPFRONT_WITH_DISCOUNT';
COMMENT ON COLUMN recurring_appointment_payments.payment_amount IS 'Actual amount paid (after discount for upfront payments)';
COMMENT ON COLUMN recurring_appointment_payments.original_total_amount IS 'Original total before discount';
COMMENT ON COLUMN recurring_appointment_payments.discount_amount IS 'Amount saved due to discount';
COMMENT ON COLUMN recurring_appointment_payments.payment_status IS 'Payment status: PENDING, COMPLETED, FAILED, REFUNDED';
