-- Add WhatsApp feature flag
-- This allows dynamic control of WhatsApp messaging functionality
-- INSERT INTO feature_flags (flag_name, flag_value, description, created_at, updated_at)
-- VALUES ('whatsapp_enabled', false, 'Enable WhatsApp messaging notifications via <PERSON><PERSON><PERSON>', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
-- ON CONFLICT (flag_name) DO NOTHING;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'feature_flags'
    ) THEN
        CREATE TABLE feature_flags (
            flag_name VARCHAR(255) PRIMARY KEY,
            flag_value BOOLEAN NOT NULL,
            description TEXT,
            created_at TIMESTAMP WITHOUT TIME ZONE,
            updated_at TIMESTAMP WITHOUT TIME ZONE
        );
    END IF;
END $$;

INSERT INTO feature_flags (flag_name, flag_value, description, created_at, updated_at)
VALUES ('whatsapp_enabled', false, 'Enable WhatsApp messaging notifications via Twilio', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (flag_name) DO NOTHING;
