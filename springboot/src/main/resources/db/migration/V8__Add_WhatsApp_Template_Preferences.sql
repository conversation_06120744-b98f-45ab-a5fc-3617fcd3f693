-- Create WhatsApp template preferences table
-- This table stores user preferences for WhatsApp message templates
-- Each user can select their preferred template for each message type

CREATE TABLE IF NOT EXISTS whatsapp_template_preferences (
    id VARCHAR(255) NOT NULL,
    salon_id VARCHAR(255) NOT NULL,
    message_type VARCHAR(50) NOT NULL,
    whatsapp_template_id VARCHAR(255) NOT NULL,
    content_sid VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT pk_whatsapp_template_preferences PRIMARY KEY (id),
    CONSTRAINT uk_user_message_type UNIQUE (salon_id, message_type)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_whatsapp_prefs_salon_id ON whatsapp_template_preferences (salon_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_prefs_message_type ON whatsapp_template_preferences (message_type);

-- Add comments for documentation
COMMENT ON TABLE whatsapp_template_preferences IS 'Stores user preferences for WhatsApp message templates';
COMMENT ON COLUMN whatsapp_template_preferences.id IS 'Unique identifier for the preference record';
COMMENT ON COLUMN whatsapp_template_preferences.salon_id IS 'ID of the user who set this preference';
COMMENT ON COLUMN whatsapp_template_preferences.message_type IS 'Type of message (APPOINTMENT_CONFIRMATION, REMINDER, etc.)';
COMMENT ON COLUMN whatsapp_template_preferences.whatsapp_template_id IS 'ID of the selected WhatsApp template';
COMMENT ON COLUMN whatsapp_template_preferences.content_sid IS 'Twilio ContentSid for the selected template';
COMMENT ON COLUMN whatsapp_template_preferences.created_at IS 'When the preference was first created';
COMMENT ON COLUMN whatsapp_template_preferences.updated_at IS 'When the preference was last updated';
