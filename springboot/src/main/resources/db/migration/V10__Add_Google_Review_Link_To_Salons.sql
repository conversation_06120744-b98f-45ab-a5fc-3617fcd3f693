-- Add google_review_link column to salons table for storing Google review links
-- This is used for follow-up messages to include review links

-- Ensure salons table exists before altering it
CREATE TABLE IF NOT EXISTS salons
(
    id                     VARCHAR(255) NOT NULL,
    name                   VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    address                TEXT,
    city                   VARCHAR(255),
    phone                  VARCHAR(50),
    email                  VARCHAR(255),
    owner_id               VARCHAR(255) NOT NULL,
    is_active              BOOLEAN DEFAULT TRUE,
    client_ids             TEXT,
    created_at             TIMESTAMP WITHOUT TIME ZONE,
    updated_at             TIMESTAMP WITHOUT TIME ZONE,
    description            VARCHAR(255),
    additional_slots_count INTEGER DEFAULT 0,
    CONSTRAINT pk_salons PRIMARY KEY (id)
);

-- Add google_review_link column
ALTER TABLE salons ADD COLUMN IF NOT EXISTS google_review_link TEXT;

