-- Add messaging channel preference to SMS reminder settings
-- Allows users to choose between SMS and WhatsApp for notifications

-- Ensure sms_reminder_settings table exists before altering it
CREATE TABLE IF NOT EXISTS sms_reminder_settings
(
    salon_id                  VARCHAR(255)                NOT NULL,
    enabled                   BOOLEAN                     NOT NULL,
    appointment_confirmations BOOLEAN                     NOT NULL,
    completion_messages       BO<PERSON><PERSON>N                     NOT NULL,
    follow_up_messages        BO<PERSON><PERSON>N                     NOT NULL,
    selected_provider         VARCHAR(255),
    created_at                TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at                TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_sms_reminder_settings PRIMARY KEY (salon_id)
);

-- Add messaging channel column
ALTER TABLE sms_reminder_settings
    ADD COLUMN IF NOT EXISTS messaging_channel VARCHAR(20) NOT NULL DEFAULT 'SMS';

-- Create index for faster lookups by messaging channel
CREATE INDEX IF NOT EXISTS idx_sms_reminder_settings_messaging_channel
    ON sms_reminder_settings(messaging_channel);

COMMENT ON COLUMN sms_reminder_settings.messaging_channel IS 'Preferred messaging channel: SMS or WHATSAPP';

