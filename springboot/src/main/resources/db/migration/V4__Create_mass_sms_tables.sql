-- Create mass_sms_templates table
CREATE TABLE IF NOT EXISTS mass_sms_templates (
    id VARCHAR(36) PRIMARY KEY,
    salon_id VARCHAR(36),
    name VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    usage_count INT NOT NULL DEFAULT 0,
    created_by VARC<PERSON><PERSON>(36) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for mass_sms_templates
CREATE INDEX IF NOT EXISTS idx_mass_sms_templates_salon_id ON mass_sms_templates(salon_id);
CREATE INDEX IF NOT EXISTS idx_mass_sms_templates_category ON mass_sms_templates(category);
CREATE INDEX IF NOT EXISTS idx_mass_sms_templates_is_default ON mass_sms_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_mass_sms_templates_created_at ON mass_sms_templates(created_at);

-- Create mass_sms_campaigns table
CREATE TABLE IF NOT EXISTS mass_sms_campaigns (
    id VARCHAR(36) PRIMARY KEY,
    salon_id VARCHAR(36) NOT NULL,
    message TEXT NOT NULL,
    template_id VARCHAR(36),
    total_recipients INT NOT NULL DEFAULT 0,
    success_count INT NOT NULL DEFAULT 0,
    failure_count INT NOT NULL DEFAULT 0,
    estimated_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    actual_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    scheduled_at TIMESTAMP,
    sent_at TIMESTAMP,
    created_by VARCHAR(36) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_mass_sms_campaigns_template
        FOREIGN KEY (template_id)
        REFERENCES mass_sms_templates(id)
        ON DELETE SET NULL
);

-- Create indexes for mass_sms_campaigns
CREATE INDEX IF NOT EXISTS idx_mass_sms_campaigns_salon_id ON mass_sms_campaigns(salon_id);
CREATE INDEX IF NOT EXISTS idx_mass_sms_campaigns_status ON mass_sms_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_mass_sms_campaigns_created_at ON mass_sms_campaigns(created_at);
CREATE INDEX IF NOT EXISTS idx_mass_sms_campaigns_scheduled_at ON mass_sms_campaigns(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_mass_sms_campaigns_template_id ON mass_sms_campaigns(template_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE OR REPLACE TRIGGER update_mass_sms_templates_updated_at BEFORE UPDATE ON mass_sms_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE TRIGGER update_mass_sms_campaigns_updated_at BEFORE UPDATE ON mass_sms_campaigns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default templates
INSERT INTO mass_sms_templates (id, salon_id, name, content, category, is_default, usage_count, created_by, created_at, updated_at)
VALUES
    (gen_random_uuid()::text, NULL, 'Reminder programare', 'Bună ziua {client_name}! Vă așteptăm mâine la {appointment_time} pentru {pet_name}. Salon {salon_name}', 'reminder', TRUE, 0, 'system', NOW(), NOW()),
    (gen_random_uuid()::text, NULL, 'Promovare sărbători', 'Bună ziua {client_name}! Profită de oferta noastră specială de sărbători! Reducere 20% la toate serviciile pentru {pet_name}. {salon_name}', 'promotion', TRUE, 0, 'system', NOW(), NOW()),
    (gen_random_uuid()::text, NULL, 'Mulțumire după vizită', 'Mulțumim că ați ales {salon_name}! Sperăm că {pet_name} a avut parte de o experiență plăcută. Vă așteptăm din nou!', 'thank_you', TRUE, 0, 'system', NOW(), NOW()),
    (gen_random_uuid()::text, NULL, 'Aniversare client', 'La mulți ani, {client_name}! Cu ocazia zilei dumneavoastră, vă oferim 15% reducere la următoarea vizită pentru {pet_name}. {salon_name}', 'birthday', TRUE, 0, 'system', NOW(), NOW()),
    (gen_random_uuid()::text, NULL, 'Serviciu nou', 'Bună ziua! {salon_name} vă anunță că avem un nou serviciu disponibil pentru {pet_name}. Contactați-ne pentru mai multe detalii!', 'announcement', TRUE, 0, 'system', NOW(), NOW())
ON CONFLICT DO NOTHING;
