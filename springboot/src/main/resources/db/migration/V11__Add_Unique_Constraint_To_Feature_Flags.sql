-- Add unique constraint to feature_flags.flag_name column
-- This is required for ON CONFLICT clauses to work properly

-- First, remove any potential duplicate flag_name entries (if any exist)
-- Keep only the first occurrence of each flag_name
-- DELETE FROM feature_flags
-- WHERE id NOT IN (
--     SELECT MIN(id)
--     FROM feature_flags
--     <PERSON><PERSON><PERSON> BY flag_name
-- );
--
-- -- Add unique constraint to flag_name column
-- ALTER TABLE feature_flags
-- ADD CONSTRAINT uk_feature_flags_flag_name UNIQUE (flag_name);
-- Remove duplicate flag_name entries, keeping only the first occurrence
DELETE FROM feature_flags
WHERE ctid NOT IN (
    SELECT min(ctid)
    FROM feature_flags
    GROUP BY flag_name
);

-- Add unique constraint to flag_name column
ALTER TABLE feature_flags
ADD CONSTRAINT uk_feature_flags_flag_name UNIQUE (flag_name);
