-- Add separate toggles for reschedule and cancellation notifications
-- Previously, all notifications were controlled by the single "appointment_confirmations" toggle

-- Ensure sms_reminder_settings table exists before altering it
CREATE TABLE IF NOT EXISTS sms_reminder_settings
(
    salon_id                  VARCHAR(255)                NOT NULL,
    enabled                   <PERSON><PERSON><PERSON><PERSON><PERSON>                     NOT NULL,
    appointment_confirmations BO<PERSON><PERSON>N                     NOT NULL,
    completion_messages       BO<PERSON><PERSON>N                     NOT NULL,
    follow_up_messages        <PERSON><PERSON><PERSON><PERSON>N                     NOT NULL,
    selected_provider         VARCHAR(255),
    created_at                TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at                TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_sms_reminder_settings PRIMARY KEY (salon_id)
);

-- Add new columns
ALTER TABLE sms_reminder_settings
ADD COLUMN IF NOT EXISTS reschedule_notifications BOOLEAN NOT NULL DEFAULT true;

ALTER TABLE sms_reminder_settings
ADD COLUMN IF NOT EXISTS cancellation_notifications BOOLEAN NOT NULL DEFAULT true;

-- For existing records, set the new columns to match the current appointment_confirmations value
-- This ensures backward compatibility
UPDATE sms_reminder_settings
SET reschedule_notifications = appointment_confirmations,
    cancellation_notifications = appointment_confirmations
WHERE reschedule_notifications IS NULL OR cancellation_notifications IS NULL;

