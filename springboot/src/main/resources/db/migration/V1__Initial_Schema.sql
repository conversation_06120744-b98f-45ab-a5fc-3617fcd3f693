CREATE TABLE IF NOT EXISTS appointment_service_ids
(
    appointment_id VARCHAR(255) NOT NULL,
    service_id     VARCHAR(255)
);

CREATE TABLE IF NOT EXISTS appointment_settings
(
    salon_id                      VARCHAR(255)                NOT NULL,
    auto_finalize_enabled         BOOLEAN                     NOT NULL,
    overdue_notifications_enabled BOOLEAN                     NOT NULL,
    sms_completion_enabled        BOOLEAN                     NOT NULL,
    created_at                    TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at                    TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_appointment_settings PRIMARY KEY (salon_id)
);

CREATE TABLE IF NOT EXISTS appointment_subscriptions
(
    id                      VARCHAR(255)                NOT NULL,
    client_id               VARCHAR(255)                NOT NULL,
    salon_id                VARCHAR(255)                NOT NULL,
    original_appointment_id VARCHAR(255)                NOT NULL,
    staff_id                VARCHAR(255)                NOT NULL,
    service_ids             TEXT                        NOT NULL,
    frequency               INTEGER                     NOT NULL,
    period                  VARCHAR(255)                NOT NULL,
    total_repetitions       INTEGER                     NOT NULL,
    remaining_repetitions   INTEGER                     NOT NULL,
    status                  VARCHAR(255)                NOT NULL,
    start_time              time WITHOUT TIME ZONE      NOT NULL,
    end_time                time WITHOUT TIME ZONE      NOT NULL,
    notes                   TEXT,
    created_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_appointment_subscriptions PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS appointments
(
    id                      VARCHAR(255)                NOT NULL,
    salon_id                VARCHAR(255)                NOT NULL,
    client_id               VARCHAR(255)                NOT NULL,
    pet_id                  VARCHAR(255)                NOT NULL,
    staff_id                VARCHAR(255)                NOT NULL,
    appointment_date        date                        NOT NULL,
    start_time              time WITHOUT TIME ZONE      NOT NULL,
    end_time                time WITHOUT TIME ZONE      NOT NULL,
    status                  VARCHAR(255)                NOT NULL,
    total_price             DECIMAL(10, 2)              NOT NULL,
    total_duration          INTEGER                     NOT NULL,
    notes                   TEXT,
    subscription_id         VARCHAR(255),
    sequence_number         INTEGER,
    is_recurring            BOOLEAN                     NOT NULL,
    completed_at            TIMESTAMP WITHOUT TIME ZONE,
    actual_duration_minutes INTEGER,
    created_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    version                 BIGINT                      NOT NULL,
    CONSTRAINT pk_appointments PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS block_times
(
    id                 VARCHAR(255)                NOT NULL,
    salon_id           VARCHAR(255)                NOT NULL,
    start_time         TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    end_time           TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    reason             VARCHAR(50)                 NOT NULL,
    custom_reason      TEXT,
    staff_ids          TEXT[]                      NOT NULL,
    created_by         VARCHAR(255)                NOT NULL,
    created_at         TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_by         VARCHAR(255),
    updated_at         TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    is_recurring       BOOLEAN                     NOT NULL,
    recurrence_pattern JSONB,
    notes              TEXT,
    status             VARCHAR(20)                 NOT NULL,
    CONSTRAINT pk_block_times PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS clients
(
    id         VARCHAR(255) NOT NULL,
    salon_id   VARCHAR(255),
    name       VARCHAR(255) NOT NULL,
    phone      VARCHAR(50),
    email      VARCHAR(255),
    address    TEXT,
    notes      TEXT,
    is_active  BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_clients PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS custom_closures
(
    id          VARCHAR(255) NOT NULL,
    salon_id    VARCHAR(255) NOT NULL,
    reason      VARCHAR(255) NOT NULL,
    date        date         NOT NULL,
    description TEXT,
    created_at  TIMESTAMP WITHOUT TIME ZONE,
    updated_at  TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_custom_closures PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS fcm_tokens
(
    id          VARCHAR(255)                NOT NULL,
    user_id     VARCHAR(255)                NOT NULL,
    salon_id    VARCHAR(255)                NOT NULL,
    fcm_token   VARCHAR(255)                NOT NULL,
    device_id   VARCHAR(255),
    device_type VARCHAR(255)                NOT NULL,
    is_active   BOOLEAN                     NOT NULL,
    last_used   TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    created_at  TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at  TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_fcm_tokens PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS feature_flags
(
    id          BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    flag_name   VARCHAR(100)                            NOT NULL,
    flag_value  BOOLEAN                                 NOT NULL,
    description TEXT,
    created_at  TIMESTAMP WITHOUT TIME ZONE             NOT NULL,
    updated_at  TIMESTAMP WITHOUT TIME ZONE             NOT NULL,
    CONSTRAINT pk_feature_flags PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS holidays
(
    id             VARCHAR(255) NOT NULL,
    salon_id       VARCHAR(255) NOT NULL,
    name           VARCHAR(255) NOT NULL,
    date           date         NOT NULL,
    is_working_day BOOLEAN,
    type           VARCHAR(255) NOT NULL,
    created_at     TIMESTAMP WITHOUT TIME ZONE,
    updated_at     TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_holidays PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS notification_history
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    salon_id       VARCHAR(255)                            NOT NULL,
    title          VARCHAR(255)                            NOT NULL,
    message        TEXT                                    NOT NULL,
    type           VARCHAR(50)                             NOT NULL,
    read_status    BOOLEAN                                 NOT NULL,
    timestamp      TIMESTAMP WITHOUT TIME ZONE             NOT NULL,
    appointment_id VARCHAR(255),
    client_id      VARCHAR(255),
    metadata       TEXT,
    created_at     TIMESTAMP WITHOUT TIME ZONE             NOT NULL,
    updated_at     TIMESTAMP WITHOUT TIME ZONE             NOT NULL,
    CONSTRAINT pk_notification_history PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS notification_settings
(
    user_id                    VARCHAR(255)                NOT NULL,
    salon_id                   VARCHAR(255)                NOT NULL,
    push_notifications_enabled BOOLEAN                     NOT NULL,
    sound_preference           VARCHAR(255)                NOT NULL,
    vibration_enabled          BOOLEAN                     NOT NULL,
    dnd_enabled                BOOLEAN                     NOT NULL,
    dnd_start_time             time WITHOUT TIME ZONE      NOT NULL,
    dnd_end_time               time WITHOUT TIME ZONE      NOT NULL,
    dnd_allow_critical         BOOLEAN                     NOT NULL,
    new_appointments           BOOLEAN                     NOT NULL,
    appointment_cancellations  BOOLEAN                     NOT NULL,
    appointment_rescheduled    BOOLEAN                     NOT NULL,
    default_priority           VARCHAR(255)                NOT NULL,
    created_at                 TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at                 TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_notification_settings PRIMARY KEY (user_id, salon_id)
);

CREATE TABLE IF NOT EXISTS notifications
(
    id              VARCHAR(255)                NOT NULL,
    type            VARCHAR(255)                NOT NULL,
    recipient_type  VARCHAR(255)                NOT NULL,
    recipient_value VARCHAR(255)                NOT NULL,
    content_type    VARCHAR(255)                NOT NULL,
    title           VARCHAR(255),
    body            TEXT                        NOT NULL,
    data            TEXT,
    status          VARCHAR(255)                NOT NULL,
    appointment_id  VARCHAR(255),
    salon_id        VARCHAR(255)                NOT NULL,
    sent_at         TIMESTAMP WITHOUT TIME ZONE,
    delivered_at    TIMESTAMP WITHOUT TIME ZONE,
    failure_reason  VARCHAR(255),
    retry_count     INTEGER                     NOT NULL,
    max_retries     INTEGER                     NOT NULL,
    is_read         BOOLEAN                     NOT NULL,
    created_at      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_notifications PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS pets
(
    id                 VARCHAR(255) NOT NULL,
    client_id          VARCHAR(255) NOT NULL,
    name               VARCHAR(255) NOT NULL,
    breed              VARCHAR(255),
    species            VARCHAR(255),
    size               VARCHAR(255),
    age                INTEGER,
    weight             DECIMAL(5, 2),
    color              VARCHAR(100),
    gender             VARCHAR(20),
    notes              TEXT,
    medical_conditions TEXT,
    is_active          BOOLEAN DEFAULT TRUE,
    created_at         TIMESTAMP WITHOUT TIME ZONE,
    updated_at         TIMESTAMP WITHOUT TIME ZONE,
    photo_url          VARCHAR(500),
    CONSTRAINT pk_pets PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS phone_verification_codes
(
    id           VARCHAR(255)                NOT NULL,
    phone_number VARCHAR(20)                 NOT NULL,
    code         VARCHAR(6)                  NOT NULL,
    expires_at   TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    is_used      BOOLEAN                     NOT NULL,
    used_at      TIMESTAMP WITHOUT TIME ZONE,
    attempts     INTEGER                     NOT NULL,
    max_attempts INTEGER                     NOT NULL,
    created_at   TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at   TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_phone_verification_codes PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS salon_invitations
(
    id                              VARCHAR(255)                NOT NULL,
    salon_id                        VARCHAR(255)                NOT NULL,
    inviter_user_id                 VARCHAR(255)                NOT NULL,
    invited_user_phone              VARCHAR(50)                 NOT NULL,
    proposed_role                   VARCHAR(255)                NOT NULL,
    proposed_permissions            TEXT,
    proposed_nickname               VARCHAR(100),
    proposed_client_data_permission VARCHAR(255)                NOT NULL,
    status                          VARCHAR(255)                NOT NULL,
    message                         TEXT,
    invited_at                      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    responded_at                    TIMESTAMP WITHOUT TIME ZONE,
    expires_at                      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    resend_count                    INTEGER                     NOT NULL,
    last_resend_at                  TIMESTAMP WITHOUT TIME ZONE,
    cancelled_at                    TIMESTAMP WITHOUT TIME ZONE,
    cancelled_by                    VARCHAR(255),
    created_at                      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at                      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_salon_invitations PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS salon_settings
(
    id            UUID         NOT NULL,
    setting_key   VARCHAR(255) NOT NULL,
    setting_value TEXT         NOT NULL,
    description   TEXT,
    updated_at    TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_salon_settings PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS salon_subscriptions
(
    id                         VARCHAR(255)                NOT NULL,
    user_id                    VARCHAR(255)                NOT NULL,
    salon_id                   VARCHAR(255)                NOT NULL,
    tier                       VARCHAR(255)                NOT NULL,
    status                     VARCHAR(255)                NOT NULL,
    start_date                 TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    end_date                   TIMESTAMP WITHOUT TIME ZONE,
    trial_end_date             TIMESTAMP WITHOUT TIME ZONE,
    is_trial_active            BOOLEAN                     NOT NULL,
    revenue_cat_customer_id    VARCHAR(255),
    revenue_cat_entitlement_id VARCHAR(255),
    created_at                 TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at                 TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_salon_subscriptions PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS salon_working_hours_settings
(
    salon_id   VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_salon_working_hours_settings PRIMARY KEY (salon_id)
);

CREATE TABLE IF NOT EXISTS salons
(
    id                     VARCHAR(255) NOT NULL,
    name                   VARCHAR(255) NOT NULL,
    address                TEXT,
    city                   VARCHAR(255),
    phone                  VARCHAR(50),
    email                  VARCHAR(255),
    owner_id               VARCHAR(255) NOT NULL,
    is_active              BOOLEAN DEFAULT TRUE,
    client_ids             TEXT,
    created_at             TIMESTAMP WITHOUT TIME ZONE,
    updated_at             TIMESTAMP WITHOUT TIME ZONE,
    description            VARCHAR(255),
    additional_slots_count INTEGER DEFAULT 0,
    CONSTRAINT pk_salons PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS service_requirements
(
    service_id  VARCHAR(255) NOT NULL,
    requirement VARCHAR(255)
);

CREATE TABLE IF NOT EXISTS services
(
    id              VARCHAR(255)                NOT NULL,
    salon_id        VARCHAR(255)                NOT NULL,
    name            VARCHAR(255)                NOT NULL,
    description     TEXT,
    price           DECIMAL(10, 2)              NOT NULL,
    duration        INTEGER                     NOT NULL,
    category        VARCHAR(255)                NOT NULL,
    display_order   INTEGER                     NOT NULL,
    is_active       BOOLEAN                     NOT NULL,
    size_prices     TEXT,
    size_durations  TEXT,
    min_price       DECIMAL(10, 2),
    max_price       DECIMAL(10, 2),
    size_min_prices TEXT,
    size_max_prices TEXT,
    created_at      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_services PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS sms_logs
(
    id               VARCHAR(255)                NOT NULL,
    salon_id         VARCHAR(255)                NOT NULL,
    appointment_id   VARCHAR(255),
    client_id        VARCHAR(255),
    phone_number     VARCHAR(255)                NOT NULL,
    message_content  TEXT                        NOT NULL,
    message_type     VARCHAR(255)                NOT NULL,
    client_name      VARCHAR(255),
    pet_name         VARCHAR(255),
    appointment_date date,
    appointment_time time WITHOUT TIME ZONE,
    status           VARCHAR(255)                NOT NULL,
    sent_at          TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    created_at       TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_sms_logs PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS sms_quota
(
    id           VARCHAR(255)                NOT NULL,
    salon_id     VARCHAR(255)                NOT NULL,
    total_quota  INTEGER                     NOT NULL,
    used_quota   INTEGER                     NOT NULL,
    quota_period VARCHAR(255)                NOT NULL,
    created_at   TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at   TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_sms_quota PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS sms_rate_limits
(
    id                      VARCHAR(255)                NOT NULL,
    phone_number            VARCHAR(20)                 NOT NULL,
    sent_count              INTEGER                     NOT NULL,
    window_start            TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    window_duration_minutes INTEGER                     NOT NULL,
    max_sms_per_window      INTEGER                     NOT NULL,
    last_sent_at            TIMESTAMP WITHOUT TIME ZONE,
    created_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at              TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_sms_rate_limits PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS sms_reminder_settings
(
    salon_id                  VARCHAR(255)                NOT NULL,
    enabled                   BOOLEAN                     NOT NULL,
    appointment_confirmations BOOLEAN                     NOT NULL,
    completion_messages       BOOLEAN                     NOT NULL,
    follow_up_messages        BOOLEAN                     NOT NULL,
    selected_provider         VARCHAR(255),
    created_at                TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at                TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_sms_reminder_settings PRIMARY KEY (salon_id)
);

CREATE TABLE IF NOT EXISTS sms_reminder_timings
(
    id           VARCHAR(255)                NOT NULL,
    salon_id     VARCHAR(255)                NOT NULL,
    hours_before INTEGER                     NOT NULL,
    is_enabled   BOOLEAN                     NOT NULL,
    created_at   TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at   TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_sms_reminder_timings PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS sms_templates
(
    id               VARCHAR(255)                NOT NULL,
    salon_id         VARCHAR(255)                NOT NULL,
    template_type    VARCHAR(255)                NOT NULL,
    template_content TEXT                        NOT NULL,
    is_active        BOOLEAN                     NOT NULL,
    created_at       TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at       TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_sms_templates PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS staff
(
    id            VARCHAR(255)                NOT NULL,
    user_id       VARCHAR(255)                NOT NULL,
    nickname      VARCHAR(255),
    salon_id      VARCHAR(255)                NOT NULL,
    role          VARCHAR(255)                NOT NULL,
    working_hours TEXT,
    permissions   TEXT,
    hire_date     date                        NOT NULL,
    is_active     BOOLEAN DEFAULT TRUE        NOT NULL,
    created_at    TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at    TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_staff PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS staff_specializations
(
    staff_id       VARCHAR(255) NOT NULL,
    specialization VARCHAR(255)
);

CREATE TABLE IF NOT EXISTS staff_working_hours
(
    id                    VARCHAR(255) NOT NULL,
    staff_id              VARCHAR(255) NOT NULL,
    salon_id              VARCHAR(255) NOT NULL,
    weekly_schedule       TEXT,
    holidays              TEXT,
    custom_closures       TEXT,
    inherit_from_business BOOLEAN,
    created_at            TIMESTAMP WITHOUT TIME ZONE,
    updated_at            TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_staff_working_hours PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS subscription_metadata
(
    subscription_id VARCHAR(255) NOT NULL,
    metadata_value  VARCHAR(255),
    metadata_key    VARCHAR(255) NOT NULL,
    CONSTRAINT pk_subscription_metadata PRIMARY KEY (subscription_id, metadata_key)
);

CREATE TABLE IF NOT EXISTS users
(
    id               VARCHAR(255) NOT NULL,
    firebase_uid     VARCHAR(255) NOT NULL,
    email            VARCHAR(255),
    phone_number     VARCHAR(20),
    name             VARCHAR(255) NOT NULL,
    role             VARCHAR(50),
    current_salon_id VARCHAR(255),
    is_active        BOOLEAN DEFAULT TRUE,
    created_at       TIMESTAMP WITHOUT TIME ZONE,
    updated_at       TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_users PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS weekly_schedule
(
    id                     VARCHAR(255)           NOT NULL,
    salon_id               VARCHAR(255)           NOT NULL,
    day_of_week            VARCHAR(255)           NOT NULL,
    start_time             time WITHOUT TIME ZONE NOT NULL,
    end_time               time WITHOUT TIME ZONE NOT NULL,
    is_day_off             BOOLEAN,
    lunch_break_enabled    BOOLEAN,
    lunch_break_start_time time WITHOUT TIME ZONE,
    lunch_break_end_time   time WITHOUT TIME ZONE,
    created_at             TIMESTAMP WITHOUT TIME ZONE,
    updated_at             TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_weekly_schedule PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS breeds
(
    id         VARCHAR(255)                NOT NULL,
    name       VARCHAR(255)                NOT NULL,
    species    VARCHAR(50)                 NOT NULL, -- 'dog', 'cat', 'other'
    size       VARCHAR(10)                 NOT NULL, -- 'S', 'M', 'L'
    is_active  BOOLEAN                     NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    CONSTRAINT pk_breeds PRIMARY KEY (id),
    CONSTRAINT uk_breeds_name_species UNIQUE (name, species)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_breeds_species ON breeds (species);
CREATE INDEX IF NOT EXISTS idx_breeds_size ON breeds (size);
CREATE INDEX IF NOT EXISTS idx_breeds_active ON breeds (is_active);

CREATE INDEX IF NOT EXISTS idx_appointments_client_date ON appointments (client_id, appointment_date);

CREATE INDEX IF NOT EXISTS idx_appointments_date_range ON appointments (appointment_date, start_time, end_time);

CREATE INDEX IF NOT EXISTS idx_appointments_pet_date ON appointments (pet_id, appointment_date);

CREATE INDEX IF NOT EXISTS idx_appointments_recurring ON appointments (is_recurring, subscription_id);

CREATE INDEX IF NOT EXISTS idx_appointments_salon_date ON appointments (salon_id, appointment_date);

CREATE INDEX IF NOT EXISTS idx_appointments_salon_staff_date ON appointments (salon_id, staff_id, appointment_date);

CREATE INDEX IF NOT EXISTS idx_appointments_sequence ON appointments (subscription_id, sequence_number);

CREATE INDEX IF NOT EXISTS idx_appointments_staff_date_time ON appointments (staff_id, appointment_date, start_time);

CREATE INDEX IF NOT EXISTS idx_appointments_status_date ON appointments (status, appointment_date);

CREATE INDEX IF NOT EXISTS idx_appointments_subscription ON appointments (subscription_id);

CREATE INDEX IF NOT EXISTS idx_clients_email_active ON clients (email, is_active);

CREATE INDEX IF NOT EXISTS idx_clients_name ON clients (name);

CREATE INDEX IF NOT EXISTS idx_clients_phone_active ON clients (phone, is_active);

CREATE INDEX IF NOT EXISTS idx_clients_salon_active ON clients (salon_id, is_active);

CREATE INDEX IF NOT EXISTS idx_feature_flags_name ON feature_flags (flag_name);

CREATE INDEX IF NOT EXISTS idx_notification_appointment ON notification_history (appointment_id);

CREATE INDEX IF NOT EXISTS idx_notification_client ON notification_history (client_id);

CREATE INDEX IF NOT EXISTS idx_notification_salon_timestamp ON notification_history (salon_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_notification_salon_type ON notification_history (salon_id, type);

CREATE INDEX IF NOT EXISTS idx_notification_timestamp ON notification_history (timestamp);

CREATE INDEX IF NOT EXISTS idx_notification_type_read ON notification_history (type, read_status);

CREATE INDEX IF NOT EXISTS idx_pets_breed_size ON pets (breed, size);

CREATE INDEX IF NOT EXISTS idx_pets_client_active ON pets (client_id, is_active);

CREATE INDEX IF NOT EXISTS idx_pets_name_client ON pets (name, client_id);

CREATE INDEX IF NOT EXISTS idx_pets_species_breed ON pets (species, breed);

CREATE INDEX IF NOT EXISTS idx_phone_verification_expires ON phone_verification_codes (expires_at);

CREATE INDEX IF NOT EXISTS idx_phone_verification_phone ON phone_verification_codes (phone_number);

CREATE INDEX IF NOT EXISTS idx_phone_verification_phone_active ON phone_verification_codes (phone_number, is_used, expires_at);

CREATE INDEX IF NOT EXISTS idx_salon_invitations_expires_at ON salon_invitations (expires_at);

CREATE INDEX IF NOT EXISTS idx_salon_invitations_invited_user_phone ON salon_invitations (invited_user_phone);

CREATE INDEX IF NOT EXISTS idx_salon_invitations_inviter_user_id ON salon_invitations (inviter_user_id);

CREATE INDEX IF NOT EXISTS idx_salon_invitations_salon_id ON salon_invitations (salon_id);

CREATE INDEX IF NOT EXISTS idx_salon_invitations_status ON salon_invitations (status);


CREATE INDEX IF NOT EXISTS idx_sms_quota_salon_id ON sms_quota (salon_id);

CREATE INDEX IF NOT EXISTS idx_sms_rate_limit_phone ON sms_rate_limits (phone_number);

CREATE INDEX IF NOT EXISTS idx_sms_rate_limit_phone_window ON sms_rate_limits (phone_number, window_start);

CREATE INDEX IF NOT EXISTS idx_sms_rate_limit_window ON sms_rate_limits (window_start);

CREATE INDEX IF NOT EXISTS idx_sms_reminder_timings_enabled ON sms_reminder_timings (is_enabled);

CREATE INDEX IF NOT EXISTS idx_sms_reminder_timings_salon_id ON sms_reminder_timings (salon_id);

CREATE INDEX IF NOT EXISTS idx_sms_templates_salon_id ON sms_templates (salon_id);

CREATE INDEX IF NOT EXISTS idx_sms_templates_type ON sms_templates (template_type);

CREATE INDEX IF NOT EXISTS idx_staff_active_hire_date ON staff (is_active, hire_date);

CREATE INDEX IF NOT EXISTS idx_staff_role_salon ON staff (role, salon_id);

CREATE INDEX IF NOT EXISTS idx_staff_salon_active ON staff (salon_id, is_active);

CREATE INDEX IF NOT EXISTS idx_staff_user_salon ON staff (user_id, salon_id);
