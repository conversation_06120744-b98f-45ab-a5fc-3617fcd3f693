package ro.animaliaprogramari.animalia.adapter.inbound.rest

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.UserManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.validation.ValidationService

@DisplayName("Simple Controller Tests")
class SimpleControllerTest {

    private lateinit var mockMvc: MockMvc
    private lateinit var objectMapper: ObjectMapper
    private lateinit var authenticationUseCase: AuthenticationUseCase
    private lateinit var userManagementUseCase: UserManagementUseCase
    private lateinit var validationService: ValidationService
    private lateinit var userRepository: UserRepository
    private lateinit var authenticationController: AuthenticationController

    @BeforeEach
    fun setUp() {
        authenticationUseCase = mockk()
        userManagementUseCase = mockk()
        validationService = mockk()
        userRepository = mockk()
        authenticationController = AuthenticationController(
            authenticationUseCase, userManagementUseCase, validationService, userRepository
        )
        objectMapper = ObjectMapper()
        mockMvc = MockMvcBuilders.standaloneSetup(authenticationController).build()
        
        mockkObject(SecurityUtils)
    }

    @Test
    @DisplayName("should return user profile when authenticated")
    fun shouldReturnUserProfile() {
        // Given
        val mockUser = AuthenticatedUser(
            userId = UserId.generate(),
            firebaseUid = "test-firebase-uid",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40731446896",
            name = "Test User",
            role = UserRole.STAFF,
            currentSalonId = null,
            isActive = true,
            staffAssociations = emptyList()
        )
        every { SecurityUtils.getCurrentUser() } returns mockUser

        // When & Then
        mockMvc.perform(get("/auth/profile"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data.id").value(mockUser.userId.value))
            .andExpect(jsonPath("$.data.name").value(mockUser.name))
    }

    @Test
    @DisplayName("should return 401 when not authenticated")
    fun shouldReturn401WhenNotAuthenticated() {
        // Given
        every { SecurityUtils.getCurrentUser() } returns null

        // When & Then
        mockMvc.perform(get("/auth/profile"))
            .andExpect(status().isUnauthorized)
            .andExpect(jsonPath("$.success").value(false))
    }

    @Test
    @DisplayName("should logout successfully")
    fun shouldLogoutSuccessfully() {
        // When & Then
        mockMvc.perform(post("/auth/logout"))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data.message").value("Logout successful"))
    }
}