package ro.animaliaprogramari.animalia.adapter.outbound.persistence.mapper

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.CustomServiceData
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.Appointment as AppointmentEntity
import ro.animaliaprogramari.animalia.domain.model.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

@DisplayName("AppointmentEntityMapper Custom Services")
class AppointmentEntityMapperCustomServicesTest {

    private lateinit var mapper: AppointmentEntityMapper

    companion object {
        // Test constants to avoid magic values
        private const val ORIGINAL_SERVICE_NAME = "Basic Grooming"
        private const val CUSTOM_SERVICE_NAME = "Premium Grooming"
        private const val CUSTOM_SERVICE_PRICE = 75.0
        private const val CUSTOM_SERVICE_DESCRIPTION = "Premium grooming with special care"

        private val TEST_APPOINTMENT_ID = AppointmentId.of("test-appointment-1")
        private val TEST_SALON_ID = SalonId.of("test-salon-1")
        private val TEST_CLIENT_ID = ClientId.of("test-client-1")
        private val TEST_PET_ID = PetId.of("test-pet-1")
        private val TEST_STAFF_ID = StaffId.of("test-staff-1")
        private val TEST_SERVICE_ID_1 = ServiceId.of("service-1")
        private val TEST_SERVICE_ID_2 = ServiceId.of("service-2")

        private val TEST_DATE = LocalDate.now().plusDays(1)
        private val TEST_START_TIME = LocalTime.of(10, 0)
        private val TEST_END_TIME = LocalTime.of(11, 0)
        private val TEST_CREATED_AT = LocalDateTime.now()
        private val TEST_UPDATED_AT = LocalDateTime.now()
    }

    @BeforeEach
    fun setUp() {
        mapper = AppointmentEntityMapper()
    }

    private fun createTestAppointment(
        customServices: Map<ServiceId, CustomServiceData>? = null
    ): Appointment {
        return Appointment(
            id = TEST_APPOINTMENT_ID,
            salonId = TEST_SALON_ID,
            clientId = TEST_CLIENT_ID,
            petId = TEST_PET_ID,
            staffId = TEST_STAFF_ID,
            appointmentDate = TEST_DATE,
            startTime = TEST_START_TIME,
            endTime = TEST_END_TIME,
            status = AppointmentStatus.SCHEDULED,
            serviceIds = listOf(TEST_SERVICE_ID_1, TEST_SERVICE_ID_2),
            customServices = customServices,
            totalPrice = Money.of(100.0),
            totalMyDuration = MyDuration.ofMinutes(90),
            notes = "Test appointment notes",
            subscriptionId = null,
            sequenceNumber = null,
            isRecurring = false,
            completedAt = null,
            actualDurationMinutes = null,
            createdAt = TEST_CREATED_AT,
            updatedAt = TEST_UPDATED_AT,
            version = 1
        )
    }

    private fun createCustomServiceData(
        originalName: String = ORIGINAL_SERVICE_NAME,
        customName: String = CUSTOM_SERVICE_NAME,
        customPrice: Double = CUSTOM_SERVICE_PRICE,
        customDescription: String? = CUSTOM_SERVICE_DESCRIPTION
    ): CustomServiceData {
        return CustomServiceData(
            originalServiceName = originalName,
            customName = customName,
            customPrice = customPrice,
            customDescription = customDescription
        )
    }

    @Nested
    @DisplayName("Domain to Entity Mapping")
    inner class DomainToEntityMappingTests {

        @Test
        @DisplayName("Should map appointment without custom services to entity")
        fun shouldMapAppointmentWithoutCustomServicesToEntity() {
            // Arrange
            val appointment = createTestAppointment(customServices = null)

            // Act
            val entity = mapper.toEntity(appointment)

            // Assert
            assertNotNull(entity)
            assertEquals(TEST_APPOINTMENT_ID.value, entity.id)
            assertEquals(TEST_SALON_ID.value, entity.salonId)
            assertEquals(TEST_CLIENT_ID.value, entity.clientId)
            assertEquals(TEST_PET_ID.value, entity.petId)
            assertEquals(TEST_STAFF_ID.value, entity.staffId)
            assertEquals(TEST_DATE, entity.appointmentDate)
            assertEquals(TEST_START_TIME, entity.startTime)
            assertEquals(TEST_END_TIME, entity.endTime)
            assertEquals("scheduled", entity.status)
            assertEquals(listOf("service-1", "service-2"), entity.serviceIds)
            assertNull(entity.customServices)
            assertThat(entity.totalPrice).isEqualByComparingTo(BigDecimal.valueOf(100.00))
            assertEquals(90, entity.totalDurationMinutes)
            assertEquals("Test appointment notes", entity.notes)
        }

        @Test
        @DisplayName("Should map appointment with single custom service to entity")
        fun shouldMapAppointmentWithSingleCustomServiceToEntity() {
            // Arrange
            val customServiceData = createCustomServiceData()
            val customServices = mapOf(TEST_SERVICE_ID_1 to customServiceData)
            val appointment = createTestAppointment(customServices = customServices)

            // Act
            val entity = mapper.toEntity(appointment)

            // Assert
            assertNotNull(entity)
            assertNotNull(entity.customServices)

            // Verify JSON serialization contains expected data
            val customServicesJson = entity.customServices!!
            assertEquals(true, customServicesJson.contains("service-1"))
            assertEquals(true, customServicesJson.contains(ORIGINAL_SERVICE_NAME))
            assertEquals(true, customServicesJson.contains(CUSTOM_SERVICE_NAME))
            assertEquals(true, customServicesJson.contains(CUSTOM_SERVICE_PRICE.toString()))
            assertEquals(true, customServicesJson.contains(CUSTOM_SERVICE_DESCRIPTION))
        }

        @Test
        @DisplayName("Should map appointment with multiple custom services to entity")
        fun shouldMapAppointmentWithMultipleCustomServicesToEntity() {
            // Arrange
            val customService1 = createCustomServiceData(
                originalName = "Service 1",
                customName = "Custom Service 1",
                customPrice = 50.0
            )
            val customService2 = createCustomServiceData(
                originalName = "Service 2",
                customName = "Custom Service 2",
                customPrice = 75.0
            )
            val customServices = mapOf(
                TEST_SERVICE_ID_1 to customService1,
                TEST_SERVICE_ID_2 to customService2
            )
            val appointment = createTestAppointment(customServices = customServices)

            // Act
            val entity = mapper.toEntity(appointment)

            // Assert
            assertNotNull(entity)
            assertNotNull(entity.customServices)

            val customServicesJson = entity.customServices!!
            assertEquals(true, customServicesJson.contains("service-1"))
            assertEquals(true, customServicesJson.contains("service-2"))
            assertEquals(true, customServicesJson.contains("Custom Service 1"))
            assertEquals(true, customServicesJson.contains("Custom Service 2"))
            assertEquals(true, customServicesJson.contains("50.0"))
            assertEquals(true, customServicesJson.contains("75.0"))
        }

        @Test
        @DisplayName("Should map appointment with empty custom services map to entity")
        fun shouldMapAppointmentWithEmptyCustomServicesMapToEntity() {
            // Arrange
            val customServices = emptyMap<ServiceId, CustomServiceData>()
            val appointment = createTestAppointment(customServices = customServices)

            // Act
            val entity = mapper.toEntity(appointment)

            // Assert
            assertNotNull(entity)
            assertNull(entity.customServices)
        }
    }

    @Nested
    @DisplayName("Entity to Domain Mapping")
    inner class EntityToDomainMappingTests {

        @Test
        @DisplayName("Should map entity without custom services to domain")
        fun shouldMapEntityWithoutCustomServicesToDomain() {
            // Arrange
            val entity = AppointmentEntity().apply {
                id = TEST_APPOINTMENT_ID.value
                salonId = TEST_SALON_ID.value
                clientId = TEST_CLIENT_ID.value
                petId = TEST_PET_ID.value
                staffId = TEST_STAFF_ID.value
                appointmentDate = TEST_DATE
                startTime = TEST_START_TIME
                endTime = TEST_END_TIME
                status = "scheduled"
                serviceIds = listOf("service-1", "service-2")
                customServices = null
                totalPrice = BigDecimal.valueOf(100.0)
                totalDurationMinutes = 90
                notes = "Test appointment notes"
                createdAt = TEST_CREATED_AT
                updatedAt = TEST_UPDATED_AT
                version = 1
            }

            // Act
            val appointment = mapper.toDomain(entity)

            // Assert
            assertNotNull(appointment)
            assertEquals(TEST_APPOINTMENT_ID, appointment.id)
            assertEquals(TEST_SALON_ID, appointment.salonId)
            assertEquals(TEST_CLIENT_ID, appointment.clientId)
            assertEquals(TEST_PET_ID, appointment.petId)
            assertEquals(TEST_STAFF_ID, appointment.staffId)
            assertEquals(TEST_DATE, appointment.appointmentDate)
            assertEquals(TEST_START_TIME, appointment.startTime)
            assertEquals(TEST_END_TIME, appointment.endTime)
            assertEquals(AppointmentStatus.SCHEDULED, appointment.status)
            assertEquals(listOf(TEST_SERVICE_ID_1, TEST_SERVICE_ID_2), appointment.serviceIds)
            assertNull(appointment.customServices)
//            assertThat(appointment.totalPrice).isEqualTo(Money.of(100.0)) is fine
//            assertEquals(Money.of(100.0), appointment.totalPrice)
            assertEquals(MyDuration.ofMinutes(90), appointment.totalMyDuration)
            assertEquals("Test appointment notes", appointment.notes)
        }

        @Test
        @DisplayName("Should map entity with valid custom services JSON to domain")
        fun shouldMapEntityWithValidCustomServicesJsonToDomain() {
            // Arrange
            val customServicesJson = """
                {
                    "service-1": {
                        "originalServiceName": "$ORIGINAL_SERVICE_NAME",
                        "customName": "$CUSTOM_SERVICE_NAME",
                        "customPrice": $CUSTOM_SERVICE_PRICE,
                        "customDescription": "$CUSTOM_SERVICE_DESCRIPTION"
                    }
                }
            """.trimIndent()

            val entity = AppointmentEntity().apply {
                id = TEST_APPOINTMENT_ID.value
                salonId = TEST_SALON_ID.value
                clientId = TEST_CLIENT_ID.value
                petId = TEST_PET_ID.value
                staffId = TEST_STAFF_ID.value
                appointmentDate = TEST_DATE
                startTime = TEST_START_TIME
                endTime = TEST_END_TIME
                status = "scheduled"
                serviceIds = listOf("service-1")
                customServices = customServicesJson
                totalPrice = BigDecimal.valueOf(75.0)
                totalDurationMinutes = 60
                notes = "Test appointment notes"
                createdAt = TEST_CREATED_AT
                updatedAt = TEST_UPDATED_AT
                version = 1
            }

            // Act
            val appointment = mapper.toDomain(entity)

            // Assert
            assertNotNull(appointment)
            assertNotNull(appointment.customServices)
            assertEquals(1, appointment.customServices!!.size)

            val customService = appointment.customServices!![TEST_SERVICE_ID_1]
            assertNotNull(customService)
            assertEquals(ORIGINAL_SERVICE_NAME, customService.originalServiceName)
            assertEquals(CUSTOM_SERVICE_NAME, customService.customName)
            assertEquals(CUSTOM_SERVICE_PRICE, customService.customPrice)
            assertEquals(CUSTOM_SERVICE_DESCRIPTION, customService.customDescription)
        }

        @Test
        @DisplayName("Should handle invalid custom services JSON gracefully")
        fun shouldHandleInvalidCustomServicesJsonGracefully() {
            // Arrange
            val invalidJson = "{ invalid json }"
            val entity = AppointmentEntity().apply {
                id = TEST_APPOINTMENT_ID.value
                salonId = TEST_SALON_ID.value
                clientId = TEST_CLIENT_ID.value
                petId = TEST_PET_ID.value
                staffId = TEST_STAFF_ID.value
                appointmentDate = TEST_DATE
                startTime = TEST_START_TIME
                endTime = TEST_END_TIME
                status = "scheduled"
                serviceIds = listOf("service-1")
                customServices = invalidJson
                totalPrice = BigDecimal.valueOf(50.0)
                totalDurationMinutes = 60
                notes = "Test appointment notes"
                createdAt = TEST_CREATED_AT
                updatedAt = TEST_UPDATED_AT
                version = 1
            }

            // Act
            val appointment = mapper.toDomain(entity)

            // Assert
            assertNotNull(appointment)
            assertNull(appointment.customServices)
        }

        @Test
        @DisplayName("Should handle empty custom services JSON")
        fun shouldHandleEmptyCustomServicesJson() {
            // Arrange
            val entity = AppointmentEntity().apply {
                id = TEST_APPOINTMENT_ID.value
                salonId = TEST_SALON_ID.value
                clientId = TEST_CLIENT_ID.value
                petId = TEST_PET_ID.value
                staffId = TEST_STAFF_ID.value
                appointmentDate = TEST_DATE
                startTime = TEST_START_TIME
                endTime = TEST_END_TIME
                status = "scheduled"
                serviceIds = listOf("service-1")
                customServices = ""
                totalPrice = BigDecimal.valueOf(50.0)
                totalDurationMinutes = 60
                notes = "Test appointment notes"
                createdAt = TEST_CREATED_AT
                updatedAt = TEST_UPDATED_AT
                version = 1
            }

            // Act
            val appointment = mapper.toDomain(entity)

            // Assert
            assertNotNull(appointment)
            assertNull(appointment.customServices)
        }
    }

    @Nested
    @DisplayName("JSON Serialization and Deserialization")
    inner class JsonSerializationTests {

        @Test
        @DisplayName("Should handle custom service with null description")
        fun shouldHandleCustomServiceWithNullDescription() {
            // Arrange
            val customServiceData = createCustomServiceData(customDescription = null)
            val customServices = mapOf(TEST_SERVICE_ID_1 to customServiceData)
            val appointment = createTestAppointment(customServices = customServices)

            // Act
            val entity = mapper.toEntity(appointment)
            val mappedBackAppointment = mapper.toDomain(entity)

            // Assert
            assertNotNull(mappedBackAppointment.customServices)
            val customService = mappedBackAppointment.customServices!![TEST_SERVICE_ID_1]
            assertNotNull(customService)
            assertEquals(ORIGINAL_SERVICE_NAME, customService.originalServiceName)
            assertEquals(CUSTOM_SERVICE_NAME, customService.customName)
            assertEquals(CUSTOM_SERVICE_PRICE, customService.customPrice)
            assertNull(customService.customDescription)
        }

        @Test
        @DisplayName("Should handle custom service with empty description")
        fun shouldHandleCustomServiceWithEmptyDescription() {
            // Arrange
            val customServiceData = createCustomServiceData(customDescription = "")
            val customServices = mapOf(TEST_SERVICE_ID_1 to customServiceData)
            val appointment = createTestAppointment(customServices = customServices)

            // Act
            val entity = mapper.toEntity(appointment)
            val mappedBackAppointment = mapper.toDomain(entity)

            // Assert
            assertNotNull(mappedBackAppointment.customServices)
            val customService = mappedBackAppointment.customServices!![TEST_SERVICE_ID_1]
            assertNotNull(customService)
            assertEquals("", customService.customDescription)
        }

        @Test
        @DisplayName("Should handle special characters in custom service data")
        fun shouldHandleSpecialCharactersInCustomServiceData() {
            // Arrange
            val specialOriginalName = "Grooming & Spa Service (Premium)"
            val specialCustomName = "Custom Grooming & Spa Service (VIP) - 50% Off!"
            val specialDescription = "Special service with quotes \"premium\" and symbols: @#$%^&*()"

            val customServiceData = createCustomServiceData(
                originalName = specialOriginalName,
                customName = specialCustomName,
                customDescription = specialDescription
            )
            val customServices = mapOf(TEST_SERVICE_ID_1 to customServiceData)
            val appointment = createTestAppointment(customServices = customServices)

            // Act
            val entity = mapper.toEntity(appointment)
            val mappedBackAppointment = mapper.toDomain(entity)

            // Assert
            assertNotNull(mappedBackAppointment.customServices)
            val customService = mappedBackAppointment.customServices!![TEST_SERVICE_ID_1]
            assertNotNull(customService)
            assertEquals(specialOriginalName, customService.originalServiceName)
            assertEquals(specialCustomName, customService.customName)
            assertEquals(specialDescription, customService.customDescription)
        }

        @Test
        @DisplayName("Should handle Unicode characters in custom service data")
        fun shouldHandleUnicodeCharactersInCustomServiceData() {
            // Arrange
            val unicodeOriginalName = "Îngrijire Câini 🐕"
            val unicodeCustomName = "Îngrijire Personalizată Câini 🐕✨"
            val unicodeDescription = "Serviciu premium pentru câini cu îngrijire specială 🌟"

            val customServiceData = createCustomServiceData(
                originalName = unicodeOriginalName,
                customName = unicodeCustomName,
                customDescription = unicodeDescription
            )
            val customServices = mapOf(TEST_SERVICE_ID_1 to customServiceData)
            val appointment = createTestAppointment(customServices = customServices)

            // Act
            val entity = mapper.toEntity(appointment)
            val mappedBackAppointment = mapper.toDomain(entity)

            // Assert
            assertNotNull(mappedBackAppointment.customServices)
            val customService = mappedBackAppointment.customServices!![TEST_SERVICE_ID_1]
            assertNotNull(customService)
            assertEquals(unicodeOriginalName, customService.originalServiceName)
            assertEquals(unicodeCustomName, customService.customName)
            assertEquals(unicodeDescription, customService.customDescription)
        }

        @Test
        @DisplayName("Should preserve decimal precision in custom price")
        fun shouldPreserveDecimalPrecisionInCustomPrice() {
            // Arrange
            val precisePrice = 123.456789
            val customServiceData = createCustomServiceData(customPrice = precisePrice)
            val customServices = mapOf(TEST_SERVICE_ID_1 to customServiceData)
            val appointment = createTestAppointment(customServices = customServices)

            // Act
            val entity = mapper.toEntity(appointment)
            val mappedBackAppointment = mapper.toDomain(entity)

            // Assert
            assertNotNull(mappedBackAppointment.customServices)
            val customService = mappedBackAppointment.customServices!![TEST_SERVICE_ID_1]
            assertNotNull(customService)
            assertEquals(precisePrice, customService.customPrice)
        }
    }

    @Nested
    @DisplayName("Round-trip Mapping")
    inner class RoundTripMappingTests {

        @Test
        @DisplayName("Should preserve all data in round-trip mapping without custom services")
        fun shouldPreserveAllDataInRoundTripMappingWithoutCustomServices() {
            // Arrange
            val originalAppointment = createTestAppointment(customServices = null)

            // Act
            val entity = mapper.toEntity(originalAppointment)
            val mappedBackAppointment = mapper.toDomain(entity)

            // Assert
            assertEquals(originalAppointment.id, mappedBackAppointment.id)
            assertEquals(originalAppointment.salonId, mappedBackAppointment.salonId)
            assertEquals(originalAppointment.clientId, mappedBackAppointment.clientId)
            assertEquals(originalAppointment.petId, mappedBackAppointment.petId)
            assertEquals(originalAppointment.staffId, mappedBackAppointment.staffId)
            assertEquals(originalAppointment.appointmentDate, mappedBackAppointment.appointmentDate)
            assertEquals(originalAppointment.startTime, mappedBackAppointment.startTime)
            assertEquals(originalAppointment.endTime, mappedBackAppointment.endTime)
            assertEquals(originalAppointment.status, mappedBackAppointment.status)
            assertEquals(originalAppointment.serviceIds, mappedBackAppointment.serviceIds)
            assertEquals(originalAppointment.customServices, mappedBackAppointment.customServices)
            assertEquals(originalAppointment.totalPrice, mappedBackAppointment.totalPrice)
            assertEquals(originalAppointment.totalMyDuration, mappedBackAppointment.totalMyDuration)
            assertEquals(originalAppointment.notes, mappedBackAppointment.notes)
        }

        @Test
        @DisplayName("Should preserve all data in round-trip mapping with custom services")
        fun shouldPreserveAllDataInRoundTripMappingWithCustomServices() {
            // Arrange
            val customService1 = createCustomServiceData(
                originalName = "Service 1",
                customName = "Custom Service 1",
                customPrice = 50.0,
                customDescription = "Description 1"
            )
            val customService2 = createCustomServiceData(
                originalName = "Service 2",
                customName = "Custom Service 2",
                customPrice = 75.0,
                customDescription = null
            )
            val customServices = mapOf(
                TEST_SERVICE_ID_1 to customService1,
                TEST_SERVICE_ID_2 to customService2
            )
            val originalAppointment = createTestAppointment(customServices = customServices)

            // Act
            val entity = mapper.toEntity(originalAppointment)
            val mappedBackAppointment = mapper.toDomain(entity)

            // Assert
            assertEquals(originalAppointment.id, mappedBackAppointment.id)
            assertEquals(originalAppointment.customServices?.size, mappedBackAppointment.customServices?.size)

            // Verify first custom service
            val mappedCustomService1 = mappedBackAppointment.customServices!![TEST_SERVICE_ID_1]
            assertNotNull(mappedCustomService1)
            assertEquals(customService1.originalServiceName, mappedCustomService1.originalServiceName)
            assertEquals(customService1.customName, mappedCustomService1.customName)
            assertEquals(customService1.customPrice, mappedCustomService1.customPrice)
            assertEquals(customService1.customDescription, mappedCustomService1.customDescription)

            // Verify second custom service
            val mappedCustomService2 = mappedBackAppointment.customServices!![TEST_SERVICE_ID_2]
            assertNotNull(mappedCustomService2)
            assertEquals(customService2.originalServiceName, mappedCustomService2.originalServiceName)
            assertEquals(customService2.customName, mappedCustomService2.customName)
            assertEquals(customService2.customPrice, mappedCustomService2.customPrice)
            assertEquals(customService2.customDescription, mappedCustomService2.customDescription)
        }
    }
}
