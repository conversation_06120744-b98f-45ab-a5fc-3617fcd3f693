package ro.animaliaprogramari.animalia.adapter.inbound.rest

import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.*
import ro.animaliaprogramari.animalia.adapter.inbound.security.SecurityUtils
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationResult
import ro.animaliaprogramari.animalia.application.port.inbound.AuthenticationUseCase
import ro.animaliaprogramari.animalia.application.port.inbound.UserManagementUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.UserRepository
import ro.animaliaprogramari.animalia.domain.exception.BusinessRuleViolationException
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.validation.ValidationResult
import ro.animaliaprogramari.animalia.domain.validation.ValidationService
import java.time.LocalDateTime
import kotlin.test.fail

@DisplayName("AuthenticationController")
class AuthenticationControllerTest {

    private lateinit var mockMvc: MockMvc
    private lateinit var objectMapper: ObjectMapper
    private lateinit var authenticationUseCase: AuthenticationUseCase
    private lateinit var userManagementUseCase: UserManagementUseCase
    private lateinit var validationService: ValidationService
    private lateinit var userRepository: UserRepository
    private lateinit var authenticationController: AuthenticationController

    companion object {
        private const val VALID_FIREBASE_TOKEN = "valid-firebase-token"
        private const val INVALID_FIREBASE_TOKEN = "invalid-firebase-token"
        private const val VALID_JWT_TOKEN = "valid-jwt-token"
        private const val VALID_REFRESH_TOKEN = "valid-refresh-token"
        private const val TEST_USER_ID = "test-user-123"
        private const val TEST_FIREBASE_UID = "test-firebase-uid"
        private const val TEST_EMAIL = "<EMAIL>"
        private const val TEST_PHONE = "+40731446896"
        private const val TEST_USER_NAME = "Test User"
        private const val PLATFORM_ANDROID = "android"
        private const val APP_VERSION = "1.0.0"
        private const val CONFIRMATION_TEXT = "confirm"
    }

    @BeforeEach
    fun setUp() {
        authenticationUseCase = mockk()
        userManagementUseCase = mockk()
        validationService = mockk()
        userRepository = mockk()
        authenticationController = AuthenticationController(
            authenticationUseCase,
            userManagementUseCase,
            validationService,
            userRepository
        )
        objectMapper = ObjectMapper()
        mockMvc = MockMvcBuilders.standaloneSetup(authenticationController).build()

        // Mock SecurityUtils
        mockkObject(SecurityUtils)
    }

    @Nested
    @DisplayName("POST /auth/firebase-login")
    inner class FirebaseLogin {

        @Test
        @DisplayName("should authenticate successfully with valid Firebase token")
        fun shouldAuthenticateSuccessfully() {
            // Given
            val request = FirebaseAuthRequest(
                firebaseToken = VALID_FIREBASE_TOKEN,
                platform = PLATFORM_ANDROID,
                appVersion = APP_VERSION
            )
            val mockUser = createMockAuthenticatedUser()
            val mockJwtToken = JwtToken(VALID_JWT_TOKEN, LocalDateTime.now().plusHours(1))
            val authResult = AuthenticationResult.success(mockUser, mockJwtToken)

            every { authenticationUseCase.authenticateWithFirebase(any()) } returns authResult

            // When & Then
            mockMvc.perform(
                post("/auth/firebase-login")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.accessToken").value(VALID_JWT_TOKEN))
                .andExpect(jsonPath("$.data.userId").value(mockUser.userId.value))
                .andExpect(jsonPath("$.data.userName").value(TEST_USER_NAME))
                .andExpect(jsonPath("$.data.userRole").value("STAFF"))
                .andExpect(jsonPath("$.data.isActive").value(true))

            verify { authenticationUseCase.authenticateWithFirebase(any()) }
        }

        @Test
        @DisplayName("should return 401 for invalid Firebase token")
        fun shouldReturn401ForInvalidToken() {
            // Given
            val request = FirebaseAuthRequest(
                firebaseToken = INVALID_FIREBASE_TOKEN,
                platform = PLATFORM_ANDROID,
                appVersion = APP_VERSION
            )

            every { authenticationUseCase.authenticateWithFirebase(any()) } returns AuthenticationResult.failure("Invalid Firebase token")

            // When & Then
            mockMvc.perform(
                post("/auth/firebase-login")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Invalid Firebase token"))

            verify { authenticationUseCase.authenticateWithFirebase(any()) }
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val request = FirebaseAuthRequest(
                firebaseToken = VALID_FIREBASE_TOKEN,
                platform = PLATFORM_ANDROID,
                appVersion = APP_VERSION
            )

            every { authenticationUseCase.authenticateWithFirebase(any()) } throws RuntimeException("Service error")

            // When & Then
            mockMvc.perform(
                post("/auth/firebase-login")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Invalid request: Service error"))

            verify { authenticationUseCase.authenticateWithFirebase(any()) }
        }
    }

    @Nested
    @DisplayName("POST /auth/refresh")
    inner class RefreshToken {

        @Test
        @DisplayName("should refresh token successfully")
        fun shouldRefreshTokenSuccessfully() {
            // Given
            val request = RefreshTokenRequest(refreshToken = VALID_REFRESH_TOKEN)
            val mockUser = createMockAuthenticatedUser()
            val newJwtToken = JwtToken("new-access-token", LocalDateTime.now().plusHours(1))
            val authResult = AuthenticationResult.success(mockUser, newJwtToken)

            every { authenticationUseCase.refreshToken(any()) } returns authResult

            // When & Then
            mockMvc.perform(
                post("/auth/refresh")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.accessToken").value("new-access-token"))
                .andExpect(jsonPath("$.data.userId").value(mockUser.userId.value))

            verify { authenticationUseCase.refreshToken(any()) }
        }

        @Test
        @DisplayName("should return 401 for invalid refresh token")
        fun shouldReturn401ForInvalidRefreshToken() {
            // Given
            val request = RefreshTokenRequest(refreshToken = "invalid-token")

            every { authenticationUseCase.refreshToken(any()) } returns AuthenticationResult.failure("Invalid refresh token")

            // When & Then
            mockMvc.perform(
                post("/auth/refresh")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Invalid refresh token"))

            verify { authenticationUseCase.refreshToken(any()) }
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val request = RefreshTokenRequest(refreshToken = VALID_REFRESH_TOKEN)

            every { authenticationUseCase.refreshToken(any()) } throws RuntimeException("Service error")

            // When & Then
            mockMvc.perform(
                post("/auth/refresh")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Invalid request: Service error"))
        }
    }

    @Nested
    @DisplayName("GET /auth/profile")
    inner class GetProfile {

        @Test
        @DisplayName("should return user profile when authenticated")
        fun shouldReturnUserProfile() {
            // Given
            val mockUser = createMockAuthenticatedUser()
            every { SecurityUtils.getCurrentUser() } returns mockUser

            // When & Then
            mockMvc.perform(get("/auth/profile"))
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(mockUser.userId.value))
                .andExpect(jsonPath("$.data.name").value(mockUser.name))
                .andExpect(jsonPath("$.data.email").value(TEST_EMAIL))
                .andExpect(jsonPath("$.data.role").value("STAFF"))
                .andExpect(jsonPath("$.data.isActive").value(true))
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(get("/auth/profile"))
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            every { SecurityUtils.getCurrentUser() } throws RuntimeException("Service error")

            // When & Then
            mockMvc.perform(get("/auth/profile"))
                .andExpect(status().isInternalServerError)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").exists())
        }
    }

    @Nested
    @DisplayName("PATCH /auth/profile/name")
    inner class UpdateUserName {

        @Test
        @DisplayName("should update user name successfully")
        fun shouldUpdateUserNameSuccessfully() {
            // Given
            val request = UpdateUserNameRequest(name = "Updated Name")
            val mockUser = createMockAuthenticatedUser()
            val updatedUser = User(
                id = mockUser.userId,
                firebaseUid = mockUser.firebaseUid,
                name = "Updated Name",
                email = mockUser.email,
                phoneNumber = mockUser.phoneNumber,
                role = mockUser.role,
                isActive = mockUser.isActive,
                createdAt = java.time.LocalDateTime.now(),
                updatedAt = java.time.LocalDateTime.now()
            )

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { validationService.validateUpdateUserName(any()) } returns ValidationResult.success()
            every { userManagementUseCase.updateUser(any()) } returns updatedUser

            // When & Then
            mockMvc.perform(
                patch("/auth/profile/name")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.name").value("Updated Name"))

            verify { userManagementUseCase.updateUser(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            val request = UpdateUserNameRequest(name = "Updated Name")
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(
                patch("/auth/profile/name")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))
        }

        @Test
        @DisplayName("should handle validation errors")
        fun shouldHandleValidationErrors() {
            // Given
            val request = UpdateUserNameRequest(name = "")
            val mockUser = createMockAuthenticatedUser()

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { validationService.validateUpdateUserName(any()) } returns ValidationResult.errors(listOf("Name is required"))

            // When & Then - validation throws BusinessRuleViolationException in standalone setup
            try {
                mockMvc.perform(
                    patch("/auth/profile/name")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                )
                fail("Expected BusinessRuleViolationException to be thrown")
            } catch (e: Exception) {
                // Expect the validation exception to be thrown
                assert(e.cause is BusinessRuleViolationException)
            }
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val request = UpdateUserNameRequest(name = "Updated Name")
            val mockUser = createMockAuthenticatedUser()

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { validationService.validateUpdateUserName(any()) } returns ValidationResult.success()
            every { userManagementUseCase.updateUser(any()) } throws RuntimeException("Service error")

            // When & Then - exception is thrown directly in standalone setup
            try {
                mockMvc.perform(
                    patch("/auth/profile/name")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request))
                )
                fail("Expected RuntimeException to be thrown")
            } catch (e: Exception) {
                // Expect the service exception to be thrown
                assert(e.cause is RuntimeException)
            }
        }
    }

    @Nested
    @DisplayName("POST /auth/logout")
    inner class Logout {

        @Test
        @DisplayName("should logout successfully")
        fun shouldLogoutSuccessfully() {
            // When & Then
            mockMvc.perform(post("/auth/logout"))
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.message").value("Logout successful"))
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // This test is not applicable since logout doesn't call any external services
            // The logout endpoint just returns a success message
            mockMvc.perform(post("/auth/logout"))
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
        }
    }

    @Nested
    @DisplayName("DELETE /auth/delete-account")
    inner class DeleteAccount {

        @Test
        @DisplayName("should delete account successfully with valid confirmation")
        fun shouldDeleteAccountSuccessfully() {
            // Given
            val request = DeleteAccountRequest(confirmationText = CONFIRMATION_TEXT)
            val mockUser = createMockAuthenticatedUser()

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { userManagementUseCase.deleteUserAccount(any()) } returns true

            // When & Then
            mockMvc.perform(
                delete("/auth/delete-account")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.message").exists())

            verify { userManagementUseCase.deleteUserAccount(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            val request = DeleteAccountRequest(confirmationText = CONFIRMATION_TEXT)
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(
                delete("/auth/delete-account")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))
        }

        @Test
        @DisplayName("should return 400 for invalid confirmation text")
        fun shouldReturn400ForInvalidConfirmation() {
            // Given
            val request = DeleteAccountRequest(confirmationText = "invalid")
            val mockUser = createMockAuthenticatedUser()

            every { SecurityUtils.getCurrentUser() } returns mockUser

            // When & Then
            mockMvc.perform(
                delete("/auth/delete-account")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Confirmation text must be exactly 'confirm' to proceed with account deletion"))
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val request = DeleteAccountRequest(confirmationText = CONFIRMATION_TEXT)
            val mockUser = createMockAuthenticatedUser()

            every { SecurityUtils.getCurrentUser() } returns mockUser
            every { userManagementUseCase.deleteUserAccount(any()) } throws RuntimeException("Service error")

            // When & Then
            mockMvc.perform(
                delete("/auth/delete-account")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isInternalServerError)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").exists())
        }
    }

    @Nested
    @DisplayName("GET /auth/users")
    inner class GetUsersForImpersonation {

        @Test
        @DisplayName("should return users list for admin")
        fun shouldReturnUsersListForAdmin() {
            // Given
            val adminUser = createMockAdminUser()
            val mockUsers = listOf(
                User(
                    id = UserId(TEST_USER_ID),
                    firebaseUid = "firebase-123",
                    name = TEST_USER_NAME,
                    email = Email.of("<EMAIL>"),
                    phoneNumber = "+***********",
                    role = UserRole.USER,
                    isActive = true,
                    createdAt = java.time.LocalDateTime.now(),
                    updatedAt = java.time.LocalDateTime.now()
                )
            )

            every { SecurityUtils.getCurrentUser() } returns adminUser
            every { userRepository.findAll(isActive = true) } returns mockUsers

            // When & Then
            mockMvc.perform(get("/auth/users"))
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray)
                .andExpect(jsonPath("$.data[0].id").value(TEST_USER_ID))
                .andExpect(jsonPath("$.data[0].name").value(TEST_USER_NAME))

            verify { userRepository.findAll(isActive = true) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(get("/auth/users"))
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))
        }

        @Test
        @DisplayName("should return 403 for non-admin user")
        fun shouldReturn403ForNonAdmin() {
            // Given
            val staffUser = createMockAuthenticatedUser()
            every { SecurityUtils.getCurrentUser() } returns staffUser

            // When & Then
            mockMvc.perform(get("/auth/users"))
                .andExpect(status().isForbidden)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Access denied - admin role required"))
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val adminUser = createMockAdminUser()
            every { SecurityUtils.getCurrentUser() } returns adminUser
            every { userRepository.findAll(isActive = true) } throws RuntimeException("Service error")

            // When & Then
            mockMvc.perform(get("/auth/users"))
                .andExpect(status().isInternalServerError)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").exists())
        }
    }

    // Helper methods
    private fun createMockAuthenticatedUser(): AuthenticatedUser {
        return AuthenticatedUser(
            userId = UserId(TEST_USER_ID),
            firebaseUid = TEST_FIREBASE_UID,
            name = TEST_USER_NAME,
            email = Email.of(TEST_EMAIL),
            phoneNumber = TEST_PHONE,
            role = UserRole.STAFF,
            currentSalonId = null,
            isActive = true,
            staffAssociations = emptyList()
        )
    }

    private fun createMockAdminUser(): AuthenticatedUser {
        return AuthenticatedUser(
            userId = UserId("admin-user-123"),
            firebaseUid = "admin-firebase-uid",
            name = "Admin User",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40731446897",
            role = UserRole.ADMIN,
            currentSalonId = null,
            isActive = true,
            staffAssociations = emptyList()
        )
    }

    @Nested
    @DisplayName("POST /auth/impersonate")
    inner class ImpersonateUser {

        @Test
        @DisplayName("should impersonate user successfully for admin")
        fun shouldImpersonateUserSuccessfully() {
            // Given
            val request = ImpersonateUserRequest(targetUserId = TEST_USER_ID)
            val adminUser = createMockAdminUser()
            val targetUser = createMockAuthenticatedUser()
            val impersonationToken = JwtToken("impersonation-token", LocalDateTime.now().plusHours(1))
            val authResult = AuthenticationResult.success(targetUser, impersonationToken)

            every { SecurityUtils.getCurrentUser() } returns adminUser
            every { authenticationUseCase.impersonateUser(any()) } returns authResult

            // When & Then
            mockMvc.perform(
                post("/auth/impersonate")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.accessToken").value("impersonation-token"))
                .andExpect(jsonPath("$.data.userId").value(TEST_USER_ID))
                .andExpect(jsonPath("$.data.userName").value(TEST_USER_NAME))

            verify { authenticationUseCase.impersonateUser(any()) }
        }

        @Test
        @DisplayName("should return 401 when not authenticated")
        fun shouldReturn401WhenNotAuthenticated() {
            // Given
            val request = ImpersonateUserRequest(targetUserId = TEST_USER_ID)
            every { SecurityUtils.getCurrentUser() } returns null

            // When & Then
            mockMvc.perform(
                post("/auth/impersonate")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isUnauthorized)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("User not authenticated"))
        }

        @Test
        @DisplayName("should return 403 for non-admin user")
        fun shouldReturn403ForNonAdmin() {
            // Given
            val request = ImpersonateUserRequest(targetUserId = TEST_USER_ID)
            val staffUser = createMockAuthenticatedUser()
            every { SecurityUtils.getCurrentUser() } returns staffUser

            // When & Then
            mockMvc.perform(
                post("/auth/impersonate")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isForbidden)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Access denied - admin role required"))
        }

        @Test
        @DisplayName("should handle invalid user ID")
        fun shouldHandleInvalidUserId() {
            // Given
            val request = ImpersonateUserRequest(targetUserId = "")
            val adminUser = createMockAdminUser()

            every { SecurityUtils.getCurrentUser() } returns adminUser
            // Note: UserId.of("") throws IllegalArgumentException before reaching the use case

            // When & Then
            mockMvc.perform(
                post("/auth/impersonate")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Impersonation failed: User ID cannot be blank"))
        }

        @Test
        @DisplayName("should handle user not found")
        fun shouldHandleUserNotFound() {
            // Given
            val request = ImpersonateUserRequest(targetUserId = "non-existent-user")
            val adminUser = createMockAdminUser()

            every { SecurityUtils.getCurrentUser() } returns adminUser
            every { authenticationUseCase.impersonateUser(any()) } throws EntityNotFoundException("User not found")

            // When & Then
            mockMvc.perform(
                post("/auth/impersonate")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Impersonation failed: User not found"))
        }

        @Test
        @DisplayName("should handle service exception")
        fun shouldHandleServiceException() {
            // Given
            val request = ImpersonateUserRequest(targetUserId = TEST_USER_ID)
            val adminUser = createMockAdminUser()

            every { SecurityUtils.getCurrentUser() } returns adminUser
            every { authenticationUseCase.impersonateUser(any()) } throws RuntimeException("Service error")

            // When & Then
            mockMvc.perform(
                post("/auth/impersonate")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request))
            )
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error").value("Impersonation failed: Service error"))
        }
    }
}
