package ro.animaliaprogramari.animalia.infrastructure.scheduler

import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.context.ApplicationEventPublisher
import ro.animaliaprogramari.animalia.application.port.outbound.AppointmentRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.domain.event.appointment.AppointmentReminderEvent
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.port.outbound.SmsReminderTimingRepository
import ro.animaliaprogramari.animalia.testutil.TestObjectMother
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.assertEquals

@DisplayName("CustomReminderScheduler")
class CustomReminderSchedulerTest {

    private val appointmentRepository = mockk<AppointmentRepository>()
    private val smsReminderTimingRepository = mockk<SmsReminderTimingRepository>()
    private val eventPublisher = mockk<ApplicationEventPublisher>(relaxed = true)
    private val staffRepository = mockk<StaffRepository>()

    private lateinit var scheduler: CustomReminderScheduler

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        scheduler = CustomReminderScheduler(
            appointmentRepository = appointmentRepository,
            smsReminderTimingRepository = smsReminderTimingRepository,
            eventPublisher = eventPublisher,
            staffRepository = staffRepository
        )
    }

    @Nested
    @DisplayName("processCustomReminders")
    inner class ProcessCustomRemindersTests {

        @Test
        fun `should process all enabled timings`() {
            // Given
            val timing1 = createTestTiming(hoursBefore = 2)
            val timing2 = createTestTiming(hoursBefore = 24)
            val enabledTimings = listOf(timing1, timing2)

            every { smsReminderTimingRepository.findAllEnabled() } returns enabledTimings
            every { appointmentRepository.findByDateAndStatus(any(), any()) } returns emptyList()

            // When
            scheduler.processCustomReminders()

            // Then
            verify(exactly = 1) { smsReminderTimingRepository.findAllEnabled() }
            verify(exactly = 2) { appointmentRepository.findByDateAndStatus(any(), any()) }
        }

        @Test
        fun `should handle empty timings list`() {
            // Given
            every { smsReminderTimingRepository.findAllEnabled() } returns emptyList()

            // When
            scheduler.processCustomReminders()

            // Then
            verify(exactly = 1) { smsReminderTimingRepository.findAllEnabled() }
            verify(exactly = 0) { appointmentRepository.findByDateAndStatus(any(), any()) }
            verify(exactly = 0) { eventPublisher.publishEvent(any()) }
        }
    }

    @Nested
    @DisplayName("processTimingReminders")
    inner class ProcessTimingRemindersTests {

        @Test
        fun `should send reminder for appointment exactly at target time`() {
            // Given - Appointment at 14:00, timing 2 hours before, current time 12:00
            val now = LocalDateTime.of(2024, 1, 15, 12, 0)
            val appointmentTime = LocalTime.of(14, 0)
            val appointmentDate = now.toLocalDate()

            val timing = createTestTiming(hoursBefore = 2)
            val appointment = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime,
                salonId = timing.salonId
            )
            val staff = createTestStaff(id = appointment.staffId)

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf(appointment)
            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf()
            every { staffRepository.findById(appointment.staffId) } returns staff

            // When
            scheduler.processTimingReminders(timing, now)

            // Then
            verify(exactly = 1) { eventPublisher.publishEvent(any<AppointmentReminderEvent>()) }
        }

        @Test
        fun `should send reminder within 15-minute window`() {
            // Given - Appointment at 14:00, timing 2 hours before, current time 12:05 (115 minutes before)
            val now = LocalDateTime.of(2024, 1, 15, 12, 5)
            val appointmentTime = LocalTime.of(14, 0)
            val appointmentDate = now.toLocalDate()

            val timing = createTestTiming(hoursBefore = 2)
            val appointment = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime,
                salonId = timing.salonId
            )
            val staff = createTestStaff(id = appointment.staffId)

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf(appointment)
            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf()
            every { staffRepository.findById(appointment.staffId) } returns staff

            // When
            scheduler.processTimingReminders(timing, now)

            // Then
            verify(exactly = 1) { eventPublisher.publishEvent(any<AppointmentReminderEvent>()) }
        }

        @Test
        fun `should not send reminder outside 15-minute window`() {
            // Given - Appointment at 14:00, timing 2 hours before, current time 11:40 (140 minutes before)
            val now = LocalDateTime.of(2024, 1, 15, 11, 40)
            val appointmentTime = LocalTime.of(14, 0)
            val appointmentDate = now.toLocalDate()

            val timing = createTestTiming(hoursBefore = 2)
            val appointment = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime,
                salonId = timing.salonId
            )

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf(appointment)
            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf()

            // When
            scheduler.processTimingReminders(timing, now)

            // Then
            verify(exactly = 0) { eventPublisher.publishEvent(any<AppointmentReminderEvent>()) }
        }

        @Test
        fun `should not send reminder too late`() {
            // Given - Appointment at 14:00, timing 2 hours before, current time 12:20 (100 minutes before)
            val now = LocalDateTime.of(2024, 1, 15, 12, 20)
            val appointmentTime = LocalTime.of(14, 0)
            val appointmentDate = now.toLocalDate()

            val timing = createTestTiming(hoursBefore = 2)
            val appointment = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime,
                salonId = timing.salonId
            )

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf(appointment)
            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf()

            // When
            scheduler.processTimingReminders(timing, now)

            // Then
            verify(exactly = 0) { eventPublisher.publishEvent(any<AppointmentReminderEvent>()) }
        }

        @Test
        fun `should filter appointments by salon`() {
            // Given
            val now = LocalDateTime.of(2024, 1, 15, 12, 0)
            val appointmentTime = LocalTime.of(14, 0)
            val appointmentDate = now.toLocalDate()

            val timing = createTestTiming(hoursBefore = 2)
            val correctSalonAppointment = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime,
                salonId = timing.salonId
            )
            val wrongSalonAppointment = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime,
                salonId = SalonId.of("different-salon")
            )

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns
                listOf(correctSalonAppointment, wrongSalonAppointment)

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) }
                listOf(correctSalonAppointment, wrongSalonAppointment)
            every { staffRepository.findById(correctSalonAppointment.staffId) } returns createTestStaff(id = correctSalonAppointment.staffId)

            // When
            scheduler.processTimingReminders(timing, now)

            // Then
            verify(exactly = 1) { eventPublisher.publishEvent(any<AppointmentReminderEvent>()) }
        }

        @Test
        fun `should handle day-before reminders correctly`() {
            // Given - Appointment tomorrow at 14:00, timing 24 hours before, current time today 14:00
            val now = LocalDateTime.of(2024, 1, 15, 14, 0)
            val appointmentDate = now.toLocalDate().plusDays(1)
            val appointmentTime = LocalTime.of(14, 0)

            val timing = createTestTiming(hoursBefore = 24)
            val appointment = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime,
                salonId = timing.salonId
            )
            val staff = createTestStaff(id = appointment.staffId)

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf(appointment)
            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf()
            every { staffRepository.findById(appointment.staffId) } returns staff

            // When
            scheduler.processTimingReminders(timing, now)

            // Then
            verify(exactly = 1) { eventPublisher.publishEvent(any<AppointmentReminderEvent>()) }
        }

        @Test
        fun `should handle multiple appointments needing reminders`() {
            // Given
            val now = LocalDateTime.of(2024, 1, 15, 12, 0)
            val appointmentTime0 = LocalTime.of(13, 46) // this will do
            val appointmentTime1 = LocalTime.of(14, 0) // this will do
            val appointmentTime4 = LocalTime.of(14, 14) // this wont do
            val appointmentTime3 = LocalTime.of(14, 15) // this wont do
            val appointmentTime2 = LocalTime.of(14, 30) // this wont do
            val appointmentDate = now.toLocalDate()

            val timing = createTestTiming(hoursBefore = 2)
            val appointment0 = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime0,
                salonId = timing.salonId
            )
            val appointment1 = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime1,
                salonId = timing.salonId
            )
            val appointment2 = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime2,
                salonId = timing.salonId
            )
            val appointment3 = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime3,
                salonId = timing.salonId
            )
            val appointment4 = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime4,
                salonId = timing.salonId
            )

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns
                listOf(appointment1, appointment2, appointment3, appointment4, appointment0)

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) }
                listOf(appointment1, appointment2, appointment3, appointment4, appointment0)
            every { staffRepository.findById(appointment0.staffId) } returns createTestStaff(id = appointment0.staffId)
            every { staffRepository.findById(appointment1.staffId) } returns createTestStaff(id = appointment1.staffId)
            every { staffRepository.findById(appointment2.staffId) } returns createTestStaff(id = appointment2.staffId)
            every { staffRepository.findById(appointment3.staffId) } returns createTestStaff(id = appointment3.staffId)
            every { staffRepository.findById(appointment4.staffId) } returns createTestStaff(id = appointment4.staffId)

            // When
            scheduler.processTimingReminders(timing, now)

            // Then
            verify(exactly = 2) { eventPublisher.publishEvent(any<AppointmentReminderEvent>()) }
        }

        @Test
        fun `should handle missing staff gracefully`() {
            // Given
            val now = LocalDateTime.of(2024, 1, 15, 12, 0)
            val appointmentTime = LocalTime.of(14, 0)
            val appointmentDate = now.toLocalDate()

            val timing = createTestTiming(hoursBefore = 2)
            val appointment = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime,
                salonId = timing.salonId
            )

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf(appointment)
            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf()
            every { staffRepository.findById(appointment.staffId) } returns null

            // When
            scheduler.processTimingReminders(timing, now)

            // Then
            verify(exactly = 1) { eventPublisher.publishEvent(any<AppointmentReminderEvent>()) }

            // Verify the event uses staffId as fallback userId
            val eventSlot = slot<AppointmentReminderEvent>()
            verify { eventPublisher.publishEvent(capture(eventSlot)) }
            assertEquals(UserId.of(appointment.staffId.value), eventSlot.captured.userId)
        }

        @Test
        fun `should create correct reminder event`() {
            // Given
            val now = LocalDateTime.of(2024, 1, 15, 12, 0)
            val appointmentTime = LocalTime.of(14, 0)
            val appointmentDate = now.toLocalDate()

            val timing = createTestTiming(hoursBefore = 2)
            val appointment = createTestAppointment(
                date = appointmentDate,
                startTime = appointmentTime,
                salonId = timing.salonId
            )
            val staff = createTestStaff(id = appointment.staffId)

            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf(appointment)
            every { appointmentRepository.findByDateAndStatus(appointmentDate, AppointmentStatus.SCHEDULED) } returns listOf()
            every { staffRepository.findById(appointment.staffId) } returns staff

            // When
            scheduler.processTimingReminders(timing, now)

            // Then
            val eventSlot = slot<AppointmentReminderEvent>()
            verify { eventPublisher.publishEvent(capture(eventSlot)) }

            val event = eventSlot.captured
            assertEquals(appointment.id, event.appointmentId)
            assertEquals(appointment.clientId, event.clientId)
            assertEquals(appointment.appointmentDate, event.appointmentDate)
            assertEquals(appointment.startTime, event.startTime)
            assertEquals(appointment.salonId, event.salonId)
            assertEquals("reminder-${timing.hoursBefore}h-${appointment.id.value}", event.eventId)
            assertEquals(appointment.id.value, event.aggregateId)
            assertEquals(staff.userId, event.userId)
        }
    }

    // Helper methods - simplified using Object Mother
    private fun createTestTiming(
        hoursBefore: Int = 2,
        salonId: SalonId = SalonId.of("test-salon")
    ): SmsReminderTiming {
        return TestObjectMother.SmsReminderTimings.custom(
            hoursBefore = hoursBefore,
            salonId = salonId
        )
    }

    private fun createTestAppointment(
        date: LocalDate,
        startTime: LocalTime,
        salonId: SalonId
    ): Appointment {
        return TestObjectMother.Appointments.scheduled(
            appointmentDate = date,
            startTime = startTime,
            endTime = startTime.plusHours(1),
            salonId = salonId
        )
    }

    private fun createTestStaff(id: StaffId): Staff {
        return TestObjectMother.Staff.groomer(id = id)
    }
}
