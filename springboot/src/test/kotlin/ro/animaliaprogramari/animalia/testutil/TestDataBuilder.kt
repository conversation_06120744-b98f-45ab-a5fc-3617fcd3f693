package ro.animaliaprogramari.animalia.testutil

import ro.animaliaprogramari.animalia.domain.model.*
import java.time.DayOfWeek
import java.time.LocalDateTime
import java.time.ZonedDateTime

/**
 * Test data builder for creating test objects with sensible defaults
 * Follows the Test Data Builder pattern for maintainable and readable tests
 */
object TestDataBuilder {
    /**
     * Creates a valid BlockTime for testing
     */
    fun aBlockTime(
        id: BlockTimeId = BlockTimeId.generate(),
        salonId: SalonId = SalonId.of("test-salon-1"),
        startTime: ZonedDateTime = ZonedDateTime.now().plusHours(1),
        endTime: ZonedDateTime = ZonedDateTime.now().plusHours(3),
        reason: BlockReason = BlockReason.PAUZA,
        customReason: String? = null,
        staffIds: Set<StaffId> = setOf(StaffId.of("staff-1")),
        createdBy: UserId = UserId.of("user-1"),
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedBy: UserId? = null,
        updatedAt: LocalDateTime = LocalDateTime.now(),
        isRecurring: Boolean = false,
        recurrencePattern: RecurrencePattern? = null,
        notes: String? = null,
        status: BlockTimeStatus = BlockTimeStatus.ACTIVE,
    ): BlockTime {
        return BlockTime(
            id = id,
            salonId = salonId,
            startTime = startTime,
            endTime = endTime,
            reason = reason,
            customReason = customReason,
            staffIds = staffIds,
            createdBy = createdBy,
            createdAt = createdAt,
            updatedBy = updatedBy,
            updatedAt = updatedAt,
            isRecurring = isRecurring,
            recurrencePattern = recurrencePattern,
            notes = notes,
            status = status,
        )
    }

    /**
     * Creates a valid RecurrencePattern for testing
     */
    fun aRecurrencePattern(
        type: RecurrenceType = RecurrenceType.WEEKLY,
        interval: Int = 1,
        daysOfWeek: Set<DayOfWeek>? = setOf(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY),
        dayOfMonth: Int? = null,
        endDate: ZonedDateTime? = null,
        occurrences: Int? = null,
    ): RecurrencePattern {
        return RecurrencePattern(
            type = type,
            interval = interval,
            daysOfWeek = daysOfWeek,
            dayOfMonth = dayOfMonth,
            endDate = endDate,
            occurrences = occurrences,
        )
    }

    /**
     * Creates a valid Staff for testing
     */
    fun aStaff(
        id: StaffId = StaffId.of("staff-1"),
        userId: UserId = UserId.of("user-1"),
        salonId: SalonId = SalonId.of("salon-1"),
        role: StaffRole = StaffRole.GROOMER,
        nickname: String? = "TestGroomer",
        isActive: Boolean = true,
        permissions: StaffPermissions = aStaffPermissions(),
    ): Staff {
        return Staff(
            id = id,
            userId = userId,
            salonId = salonId,
            role = role,
            nickname = nickname,
            isActive = isActive,
            permissions = permissions,
        )
    }

    /**
     * Creates valid StaffPermissions for testing
     */
    fun aStaffPermissions(
        clientDataAccess: ClientDataAccess = ClientDataAccess.NONE,
        canManageAppointments: Boolean = true,
        canManageServices: Boolean = false,
        canViewReports: Boolean = false,
        canManageSchedule: Boolean = true,
    ): StaffPermissions {
        return StaffPermissions(
            clientDataAccess = clientDataAccess,
            canManageAppointments = canManageAppointments,
            canManageServices = canManageServices,
            canViewReports = canViewReports,
            canManageSchedule = canManageSchedule,
        )
    }

    /**
     * Creates a valid User for testing
     */
    fun aUser(
        id: UserId = UserId.of("user-1"),
        firebaseUid: String = "firebase-uid-1",
        name: String = "Test User",
        email: Email? = Email.of("<EMAIL>"),
        phoneNumber: String? = "+40123456789",
        role: UserRole = UserRole.USER,
    ): User {
        return User(
            id = id,
            firebaseUid = firebaseUid,
            name = name,
            email = email,
            phoneNumber = phoneNumber,
            role = role,
        )
    }

    /**
     * Creates a valid Salon for testing
     */
    fun aSalon(
        id: SalonId = SalonId.of("salon-1"),
        name: String = "Test Salon",
        address: String? = "Test Address",
        city: String? = "Test City",
        phone: PhoneNumber? = PhoneNumber.of("+40123456789"),
        email: Email? = Email.of("<EMAIL>"),
        ownerId: UserId = UserId.of("owner-1"),
        description: String? = "Test salon description",
    ): Salon {
        return Salon(
            id = id,
            name = name,
            address = address,
            city = city,
            phone = phone,
            email = email,
            ownerId = ownerId,
            description = description,
        )
    }

    /**
     * Creates a valid Appointment for testing
     */
    fun anAppointment(
        id: AppointmentId = AppointmentId.of("appointment-1"),
        salonId: SalonId = SalonId.of("salon-1"),
        staffId: StaffId = StaffId.of("staff-1"),
        clientId: ClientId = ClientId.of("client-1"),
        petId: PetId = PetId.of("pet-1"),
        appointmentDate: java.time.LocalDate = java.time.LocalDate.now().plusDays(1),
        startTime: java.time.LocalTime = java.time.LocalTime.of(10, 0),
        endTime: java.time.LocalTime = java.time.LocalTime.of(11, 0),
        status: AppointmentStatus = AppointmentStatus.CONFIRMED,
        serviceIds: List<ServiceId> = listOf(ServiceId.of("service-1")),
        totalPrice: Money = Money.of(100.0),
        totalMyDuration: MyDuration = MyDuration.ofHours(1),
        notes: String? = null,
    ): Appointment {
        return Appointment(
            id = id,
            salonId = salonId,
            clientId = clientId,
            petId = petId,
            staffId = staffId,
            appointmentDate = appointmentDate,
            startTime = startTime,
            endTime = endTime,
            status = status,
            serviceIds = serviceIds,
            totalPrice = totalPrice,
            totalMyDuration = totalMyDuration,
            notes = notes,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            version = 0,
        )
    }

    /**
     * Builder class for more complex BlockTime scenarios
     */
    class BlockTimeBuilder {
        private var blockTime = aBlockTime()

        fun withId(id: BlockTimeId) = apply { blockTime = blockTime.copy(id = id) }

        fun withTimeRange(
            start: ZonedDateTime,
            end: ZonedDateTime,
        ) = apply {
            blockTime = blockTime.copy(startTime = start, endTime = end)
        }

        fun withReason(
            reason: BlockReason,
            customReason: String? = null,
        ) = apply {
            blockTime = blockTime.copy(reason = reason, customReason = customReason)
        }

        fun withStaff(vararg staffIds: String) =
            apply {
                blockTime = blockTime.copy(staffIds = staffIds.map { StaffId.of(it) }.toSet())
            }

        fun withRecurrence(pattern: RecurrencePattern) =
            apply {
                blockTime = blockTime.copy(isRecurring = true, recurrencePattern = pattern)
            }

        fun withNotes(notes: String) = apply { blockTime = blockTime.copy(notes = notes) }

        fun cancelled() = apply { blockTime = blockTime.copy(status = BlockTimeStatus.CANCELLED) }

        fun inPast() =
            apply {
                val pastStart = ZonedDateTime.now().minusHours(3)
                val pastEnd = ZonedDateTime.now().minusHours(1)
                blockTime = blockTime.copy(startTime = pastStart, endTime = pastEnd)
            }

        fun inFuture() =
            apply {
                val futureStart = ZonedDateTime.now().plusHours(1)
                val futureEnd = ZonedDateTime.now().plusHours(3)
                blockTime = blockTime.copy(startTime = futureStart, endTime = futureEnd)
            }

        fun build(): BlockTime = blockTime
    }

    /**
     * Creates a BlockTimeBuilder for fluent test data creation
     */
    fun blockTime(): BlockTimeBuilder = BlockTimeBuilder()

}
