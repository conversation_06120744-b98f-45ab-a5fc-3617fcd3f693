package ro.animaliaprogramari.animalia.integration

import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import org.springframework.web.context.WebApplicationContext
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import org.springframework.transaction.annotation.Transactional
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import ro.animaliaprogramari.animalia.application.command.VerifyPurchaseCommand
import ro.animaliaprogramari.animalia.application.port.inbound.SubscriptionUseCase
import ro.animaliaprogramari.animalia.application.port.outbound.SalonRepository
import ro.animaliaprogramari.animalia.application.port.outbound.SalonSubscriptionRepository
import ro.animaliaprogramari.animalia.application.port.outbound.StaffRepository
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.SmsQuotaService
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@Testcontainers
@ActiveProfiles("integration")
@TestPropertySource(locations = ["classpath:application-integration.properties"])
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("Subscription Webhook Integration Tests")
class SubscriptionWebhookIntegrationTest {

    companion object {
        // RevenueCat test constants based on official documentation
        private const val REVENUE_CAT_CUSTOMER_ID = "6955768f-d885-4177-bc59-597d2d27bc0a"
        private const val APP_USER_ID = "test-user-123"
        private const val ENTERPRISE_PRODUCT_ID = "animalia_enterprise_monthly"
        private const val FREELANCER_PRODUCT_ID = "animalia_freelancer_monthly"
        private const val TEAM_PRODUCT_ID = "animalia_team_monthly"
        private const val WEBHOOK_ENDPOINT = "/subscriptions/webhook"
        private const val WEBHOOK_SECRET = "test-webhook-secret"
        private const val WEBHOOK_PROCESSING_DELAY_MS = 200L

        // RevenueCat timestamp constants (milliseconds since epoch)
        private const val PURCHASE_TIME_MS = 1640995200000L // 2022-01-01T00:00:00Z
        private const val EXPIRATION_TIME_MS = 1643673600000L // 2022-02-01T00:00:00Z
        private const val TRIAL_EXPIRATION_MS = 1641600000000L // 2022-01-08T00:00:00Z (7 days trial)

        @Container
        @JvmStatic
        val postgres: PostgreSQLContainer<*> = PostgreSQLContainer("postgres:15")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test")
            .withReuse(true)
    }

    @Autowired
    private lateinit var smsQuotaService: SmsQuotaService

    @Autowired
    private lateinit var webApplicationContext: WebApplicationContext

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var subscriptionUseCase: SubscriptionUseCase

    @Autowired
    private lateinit var subscriptionRepository: SalonSubscriptionRepository

    @Autowired
    private lateinit var salonRepository: SalonRepository

    @Autowired
    private lateinit var staffRepository: StaffRepository

    private val testUserId = UserId.of("test-user-123")
    private val testSalonId = SalonId.of("test-salon-456")

    @BeforeEach
    @Transactional
    fun setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build()
        cleanupTestData()
        createTestSalon()
        createTestStaff()
        createInitialFreeSubscription()
    }

    @AfterEach
    @Transactional
    fun tearDown() {
        cleanupTestData()
    }

    private fun cleanupTestData() {
        // Clean up subscriptions
        subscriptionRepository.findByUserId(testUserId).forEach { subscription ->
            try {
                // Note: In a real scenario, you might want to use a proper delete method
                // For now, we'll rely on the test transaction rollback
            } catch (e: Exception) {
                // Ignore cleanup errors in tests
            }
        }
    }

    private fun createTestSalon() {
        val salon = Salon.create(
            name = "Test Salon Integration",
            address = "123 Test Street",
            city = "Test City",
            phone = PhoneNumber.of("+40123456789"),
            email = Email.of("<EMAIL>"),
            ownerId = testUserId,
            description = "Test salon for integration tests"
        ).copy(id = testSalonId)
        salonRepository.save(salon)
    }

    private fun createTestStaff() {
        val staff = Staff.createChiefGroomer(
            userId = testUserId,
            salonId = testSalonId
        )
        staffRepository.save(staff)
    }

    private fun createInitialFreeSubscription() {
        val freeSubscription = SalonSubscription.createFree(
            userId = testUserId,
            salonId = testSalonId
        )
        subscriptionRepository.save(freeSubscription)
    }

    @Nested
    @DisplayName("RevenueCat Webhook Event Processing")
    open inner class RevenueCatWebhookEventTests {

        @Test
        @DisplayName("Should test webhook endpoint is accessible")
        @Transactional
        open fun `should test webhook endpoint is accessible`() {
            // Test the GET endpoint first to ensure the controller is working
            val result = mockMvc.perform(
                get("$WEBHOOK_ENDPOINT/test")
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andDo { println("GET Response status: ${it.response.status}") }
            .andDo { println("GET Response body: ${it.response.contentAsString}") }
            .andDo { println("GET Response error: ${it.response.errorMessage}") }

            // Print the result for debugging
            println("Test endpoint result: $result")
        }

        @Test
        @DisplayName("Should process INITIAL_PURCHASE webhook event successfully")
        @Transactional
        open fun `should process INITIAL_PURCHASE webhook event successfully`() {
            // Given - Initial state verification
            val initialSubscription = subscriptionRepository.findByUserId(testUserId)
                .filter { it.status == SubscriptionStatus.ACTIVE }
                .maxByOrNull { it.tier.ordinal }
            assertNotNull(initialSubscription, "Initial FREE subscription should exist")
            assertEquals(SubscriptionTier.FREE, initialSubscription.tier, "Initial subscription should be FREE")

            // When - Send INITIAL_PURCHASE webhook with realistic RevenueCat payload
            val webhookPayload = createInitialPurchaseWebhookPayload(FREELANCER_PRODUCT_ID)

            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(webhookPayload))
            )
            .andDo { println("INITIAL_PURCHASE Response status: ${it.response.status}") }
            .andDo { println("INITIAL_PURCHASE Response body: ${it.response.contentAsString}") }
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.status").value("success"))

            // Then - Check subscription was created by webhook
            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val createdSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(createdSubscription, "Subscription should be created by INITIAL_PURCHASE webhook")
            assertEquals(SubscriptionTier.FREELANCER, createdSubscription.tier, "Subscription should be FREELANCER tier")
            assertEquals(APP_USER_ID, createdSubscription.revenueCatCustomerId, "RevenueCat customer ID should match")
            assertTrue(createdSubscription.status == SubscriptionStatus.ACTIVE, "Subscription should be active")

            // Assert SMS credits: FREELANCER = 100 SMS
            assertEquals(100, createdSubscription.tier.smsQuota, "FREELANCER tier should have 100 SMS credits")
        }

        @Test
        @DisplayName("Should process INITIAL_PURCHASE with trial conversion successfully")
        @Transactional
        open fun `should process INITIAL_PURCHASE with trial successfully`() {
            // Given - Initial state
            val initialSubscription = subscriptionRepository.findByUserId(testUserId)
                .filter { it.status == SubscriptionStatus.ACTIVE }
                .maxByOrNull { it.tier.ordinal }
            assertNotNull(initialSubscription)

            // When - Send INITIAL_PURCHASE webhook with trial conversion
            val webhookPayload = createInitialPurchaseWebhookPayload(TEAM_PRODUCT_ID, isTrialConversion = false, isTrial = true)

            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(webhookPayload))
            )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.status").value("success"))

            // Then - Verify trial subscription was created
            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val trialSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(trialSubscription, "Trial subscription should be created")
            assertEquals(SubscriptionTier.TEAM, trialSubscription.tier, "Should be TEAM tier")
            assertTrue(trialSubscription.isTrialActive, "Should be marked as trial active")

            // Assert SMS credits: TEAM = 200 SMS
            assertEquals(200, trialSubscription.tier.smsQuota, "TEAM tier should have 200 SMS credits")
        }

        @Test
        @DisplayName("Should process RENEWAL webhook event successfully")
        @Transactional
        open fun `should process RENEWAL webhook event successfully`() {
            // Given - Create initial subscription first
            val initialPurchasePayload = createInitialPurchaseWebhookPayload(FREELANCER_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(initialPurchasePayload))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            // When - Send RENEWAL webhook with realistic RevenueCat payload
            val renewalPayload = createRenewalWebhookPayload(FREELANCER_PRODUCT_ID)

            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(renewalPayload))
            )
            .andDo { println("RENEWAL Response status: ${it.response.status}") }
            .andDo { println("RENEWAL Response body: ${it.response.contentAsString}") }
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.status").value("success"))

            // Then - Verify subscription was renewed
            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val renewedSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(renewedSubscription, "Subscription should be found after renewal")
            assertEquals(SubscriptionTier.FREELANCER, renewedSubscription.tier, "Subscription tier should remain FREELANCER")
            assertTrue(renewedSubscription.status == SubscriptionStatus.ACTIVE, "Subscription should still be active")

            // Assert SMS credits: FREELANCER = 100 SMS
            assertEquals(100, renewedSubscription.tier.smsQuota, "FREELANCER tier should have 100 SMS credits")
        }

        @Test
        @DisplayName("Should process CANCELLATION webhook event successfully")
        @Transactional
        open fun `should process CANCELLATION webhook event successfully`() {
            // Given - Create initial subscription first
            val initialPurchasePayload = createInitialPurchaseWebhookPayload(TEAM_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(initialPurchasePayload))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            // When - Send CANCELLATION webhook
            val cancellationPayload = createCancellationWebhookPayload(TEAM_PRODUCT_ID)

            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(cancellationPayload))
            )
            .andDo { println("CANCELLATION Response status: ${it.response.status}") }
            .andDo { println("CANCELLATION Response body: ${it.response.contentAsString}") }
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.status").value("success"))

            // Then - Verify subscription was processed (implementation may vary)
            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val subscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(subscription, "Subscription should still exist after cancellation")
            // Note: Cancellation handling may vary - subscription might remain active until expiration

            // Assert SMS credits: TEAM = 200 SMS
            assertEquals(200, subscription.tier.smsQuota, "TEAM tier should have 200 SMS credits")
        }

        @Test
        @DisplayName("Should process EXPIRATION webhook event successfully")
        @Transactional
        open fun `should process EXPIRATION webhook event successfully`() {
            // Given - Create initial subscription first
            val initialPurchasePayload = createInitialPurchaseWebhookPayload(ENTERPRISE_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(initialPurchasePayload))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            // When - Send EXPIRATION webhook
            val expirationPayload = createExpirationWebhookPayload(ENTERPRISE_PRODUCT_ID)

            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(expirationPayload))
            )
            .andDo { println("EXPIRATION Response status: ${it.response.status}") }
            .andDo { println("EXPIRATION Response body: ${it.response.contentAsString}") }
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.status").value("success"))

            // Then - Verify subscription expiration was processed
            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val subscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(subscription, "Subscription should exist after expiration processing")
            assertEquals(SubscriptionStatus.EXPIRED, subscription.status, "Subscription should be marked as EXPIRED")

            // Assert SMS credits: ENTERPRISE = 500 SMS
            assertEquals(500, subscription.tier.smsQuota, "ENTERPRISE tier should have 500 SMS credits")
        }

        @Test
        @DisplayName("Should process PRODUCT_CHANGE webhook event successfully")
        @Transactional
        open fun `should process PRODUCT_CHANGE webhook event successfully`() {
            // Given - Create initial subscription first
            val initialPurchasePayload = createInitialPurchaseWebhookPayload(FREELANCER_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(initialPurchasePayload))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            // When - Send PRODUCT_CHANGE webhook (upgrade from FREELANCER to TEAM)
            val productChangePayload = createProductChangeWebhookPayload(FREELANCER_PRODUCT_ID, TEAM_PRODUCT_ID)

            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(productChangePayload))
            )
            .andDo { println("PRODUCT_CHANGE Response status: ${it.response.status}") }
            .andDo { println("PRODUCT_CHANGE Response body: ${it.response.contentAsString}") }
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.status").value("success"))

            // Then - Verify subscription tier was upgraded
            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val upgradedSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(upgradedSubscription, "Subscription should exist after product change")
            assertEquals(SubscriptionTier.TEAM, upgradedSubscription.tier, "Subscription should be upgraded to TEAM tier")
            assertTrue(upgradedSubscription.status == SubscriptionStatus.ACTIVE, "Subscription should remain active")

            // Assert SMS credits: TEAM = 200 SMS (upgraded from FREELANCER 100)
            assertEquals(200, upgradedSubscription.tier.smsQuota, "TEAM tier should have 200 SMS credits")
        }

        @Test
        @DisplayName("Should process webhook and then verify purchase successfully (end-to-end)")
        @Transactional
        open fun `should process webhook and then verify purchase successfully (end-to-end)`() {
            // Given - Initial state verification
            val initialSubscription = subscriptionRepository.findByUserId(testUserId)
                .filter { it.status == SubscriptionStatus.ACTIVE }
                .maxByOrNull { it.tier.ordinal }
            assertNotNull(initialSubscription, "Initial FREE subscription should exist")
            assertEquals(SubscriptionTier.FREE, initialSubscription.tier, "Initial subscription should be FREE")

            // When - Step 1: Send INITIAL_PURCHASE webhook (simulating RevenueCat webhook)
            val webhookPayload = createInitialPurchaseWebhookPayload(ENTERPRISE_PRODUCT_ID)

            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(webhookPayload))
            )
            .andDo { println("Webhook Response status: ${it.response.status}") }
            .andDo { println("Webhook Response body: ${it.response.contentAsString}") }
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.status").value("success"))

            // Then - Step 2: Verify that subscription was created by webhook
            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val subscriptionAfterWebhook = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(subscriptionAfterWebhook, "Subscription should be created by webhook")
            assertEquals(SubscriptionTier.ENTERPRISE, subscriptionAfterWebhook.tier, "Subscription should be ENTERPRISE")
            assertEquals(APP_USER_ID, subscriptionAfterWebhook.revenueCatCustomerId, "RevenueCat customer ID should match")
            assertTrue(subscriptionAfterWebhook.status == SubscriptionStatus.ACTIVE, "Subscription should be active")

            // When - Step 3: Call verify purchase (simulating mobile app call after webhook)
            val verifyPurchaseCommand = createVerifyPurchaseCommand(ENTERPRISE_PRODUCT_ID)
            val verifiedSubscription = subscriptionUseCase.verifyPurchase(verifyPurchaseCommand)

            // Then - Step 4: Verify that verifyPurchase found the subscription created by webhook
            assertNotNull(verifiedSubscription, "Verify purchase should return the subscription")
            assertEquals(SubscriptionTier.ENTERPRISE, verifiedSubscription.tier, "Verified subscription should be ENTERPRISE tier")
            assertEquals(APP_USER_ID, verifiedSubscription.revenueCatCustomerId, "RevenueCat customer ID should match")
            assertEquals(testUserId, verifiedSubscription.userId, "User ID should match")
            assertEquals(SubscriptionStatus.ACTIVE, verifiedSubscription.status, "Subscription should be active")

            // Then - Step 5: Verify database consistency and no duplicates
            val finalSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(finalSubscription, "Final subscription should exist in database")
            assertEquals(verifiedSubscription.id, finalSubscription.id, "Should be the same subscription instance")

            // Verify no duplicate subscriptions were created
            val allUserSubscriptions = subscriptionRepository.findByUserId(testUserId)
            val activeSubscriptions = allUserSubscriptions.filter { it.status == SubscriptionStatus.ACTIVE }
            assertEquals(1, activeSubscriptions.size, "Should have exactly one active subscription")

            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(500, remainingQuota, "ENTERPRISE tier should have 500 SMS credits")
            }
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    open inner class EdgeCaseTests {

        @Test
        @DisplayName("Should handle multiple tier upgrades correctly with realistic payloads")
        @Transactional
        open fun `should handle multiple tier upgrades correctly with realistic payloads`() {
            // Given - Start with FREE subscription
            val initialSubscription = subscriptionRepository.findByUserId(testUserId)
                .filter { it.status == SubscriptionStatus.ACTIVE }
                .maxByOrNull { it.tier.ordinal }
            assertNotNull(initialSubscription)
            assertEquals(SubscriptionTier.FREE, initialSubscription.tier)

            // When - First purchase FREELANCER
            val freelancerWebhook = createInitialPurchaseWebhookPayload(FREELANCER_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(freelancerWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            // Then - Verify FREELANCER subscription created
            val freelancerSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(freelancerSubscription)
            assertEquals(SubscriptionTier.FREELANCER, freelancerSubscription.tier)

            // When - Upgrade to ENTERPRISE via PRODUCT_CHANGE
            val upgradeWebhook = createProductChangeWebhookPayload(FREELANCER_PRODUCT_ID, ENTERPRISE_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(upgradeWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            // Then - Verify ENTERPRISE upgrade
            val enterpriseSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(enterpriseSubscription)
            assertEquals(SubscriptionTier.ENTERPRISE, enterpriseSubscription.tier)
            // Note: Depending on implementation, this might be the same instance or a new one

            // Assert SMS credits: ENTERPRISE = 500 SMS (upgraded from FREELANCER 100)
            assertEquals(500, enterpriseSubscription.tier.smsQuota, "ENTERPRISE tier should have 500 SMS credits")
        }

        @Test
        @DisplayName("Should handle webhook authentication correctly")
        @Transactional
        open fun `should handle webhook authentication correctly`() {
            // Given - Valid webhook payload
            val webhookPayload = createInitialPurchaseWebhookPayload(FREELANCER_PRODUCT_ID)

            // When - Send webhook without proper authorization
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", "invalid-secret")
                    .content(objectMapper.writeValueAsString(webhookPayload))
            )
            .andDo { println("Invalid auth Response status: ${it.response.status}") }
            .andExpect(status().isUnauthorized)

            // When - Send webhook with correct authorization
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(webhookPayload))
            )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.status").value("success"))
        }

        @Test
        @DisplayName("Should handle malformed webhook payloads gracefully")
        @Transactional
        @Disabled
        open fun `should handle malformed webhook payloads gracefully`() {
            // Given - Malformed webhook payload (missing required fields)
            val malformedPayload = mapOf(
                "api_version" to "1.0",
                "event" to mapOf(
                    "type" to "INITIAL_PURCHASE"
                    // Missing required fields like app_user_id, product_id, etc.
                )
            )

            // When - Send malformed webhook
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(malformedPayload))
            )
            .andDo { println("Malformed payload Response status: ${it.response.status}") }
            .andDo { println("Malformed payload Response body: ${it.response.contentAsString}") }
            // Should handle gracefully without crashing
            .andExpect(status().isOk) // Or appropriate error status depending on implementation
        }

        @Test
        @DisplayName("Should verify purchase works correctly after webhook processing")
        @Transactional
        open fun `should verify purchase works correctly after webhook processing`() {
            // Given - Send webhook first to create subscription
            val webhookPayload = createInitialPurchaseWebhookPayload(ENTERPRISE_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(webhookPayload))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            // When - Call verify purchase (simulating mobile app verification)
            val verifyPurchaseCommand = createVerifyPurchaseCommand(ENTERPRISE_PRODUCT_ID)
            val verifiedSubscription = subscriptionUseCase.verifyPurchase(verifyPurchaseCommand)

            // Then - Should find the subscription created by webhook
            assertNotNull(verifiedSubscription, "Should find subscription created by webhook")
            assertEquals(SubscriptionTier.ENTERPRISE, verifiedSubscription.tier, "Should be ENTERPRISE tier")
            assertEquals(APP_USER_ID, verifiedSubscription.userId.value, "RevenueCat customer ID should match")
            assertTrue(verifiedSubscription.status == SubscriptionStatus.ACTIVE, "Subscription should be active")

            // Assert SMS credits: ENTERPRISE = 500 SMS
            assertEquals(500, verifiedSubscription.tier.smsQuota, "ENTERPRISE tier should have 500 SMS credits")
        }
    }

    @Nested
    @DisplayName("SMS Credits Security Tests")
    open inner class SmsCreditsSecurityTests {

        @Test
        @DisplayName("Should handle upgrade path without SMS credit accumulation")
        @Transactional
        @Disabled
        open fun `should handle upgrade path without SMS credit accumulation`() {
            // Given - Start with initial FREE subscription
            val initialSubscription = subscriptionRepository.findByUserId(testUserId)
                .filter { it.status == SubscriptionStatus.ACTIVE }
                .maxByOrNull { it.tier.ordinal }
            assertNotNull(initialSubscription, "Initial FREE subscription should exist")
            assertEquals(SubscriptionTier.FREE, initialSubscription.tier, "Initial subscription should be FREE")

            // Step 1: Upgrade to FREELANCER
            val freelancerWebhook = createInitialPurchaseWebhookPayload(FREELANCER_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(freelancerWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val freelancerSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(freelancerSubscription, "FREELANCER subscription should be created")
            assertEquals(SubscriptionTier.FREELANCER, freelancerSubscription.tier, "Should be FREELANCER tier")

            // Verify actual SMS credits allocated via service
            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(100, remainingQuota, "FREELANCER tier should have exactly 100 SMS credits")
            }

            // Step 2: Upgrade to TEAM via PRODUCT_CHANGE
            val teamUpgradeWebhook = createProductChangeWebhookPayload(FREELANCER_PRODUCT_ID, TEAM_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(teamUpgradeWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val teamSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(teamSubscription, "TEAM subscription should exist after upgrade")
            assertEquals(SubscriptionTier.TEAM, teamSubscription.tier, "Should be upgraded to TEAM tier")

            // Verify actual SMS credits allocated via service (no accumulation)
            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(200, remainingQuota, "TEAM should have exactly 200 SMS credits (not 300 from accumulation)")
            }

            // Step 3: Upgrade to ENTERPRISE via PRODUCT_CHANGE
            val enterpriseUpgradeWebhook = createProductChangeWebhookPayload(TEAM_PRODUCT_ID, ENTERPRISE_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(enterpriseUpgradeWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val enterpriseSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(enterpriseSubscription, "ENTERPRISE subscription should exist after upgrade")
            assertEquals(SubscriptionTier.ENTERPRISE, enterpriseSubscription.tier, "Should be upgraded to ENTERPRISE tier")

            // Security Assertion: Verify no SMS credit accumulation via service
            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(500, remainingQuota, "ENTERPRISE should have exactly 500 SMS credits (not 800 from accumulation)")
            }

            // Verify only one active subscription exists (no duplicates)
            val allActiveSubscriptions = subscriptionRepository.findByUserId(testUserId)
                .filter { it.status == SubscriptionStatus.ACTIVE }
            assertEquals(1, allActiveSubscriptions.size, "Should have exactly one active subscription")
        }

        @Test
        @DisplayName("Should handle downgrade path with proper SMS credit reduction")
        @Transactional
        @Disabled
        open fun `should handle downgrade path with proper SMS credit reduction`() {
            // Given - Start with ENTERPRISE subscription
            val enterpriseWebhook = createInitialPurchaseWebhookPayload(ENTERPRISE_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(enterpriseWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val enterpriseSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(enterpriseSubscription, "ENTERPRISE subscription should be created")
            assertEquals(SubscriptionTier.ENTERPRISE, enterpriseSubscription.tier, "Should be ENTERPRISE tier")

            // Verify initial SMS credits via service
            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(500, remainingQuota, "ENTERPRISE should have 500 SMS credits initially")
            }

            // Step 1: Downgrade to TEAM via PRODUCT_CHANGE
            val teamDowngradeWebhook = createProductChangeWebhookPayload(ENTERPRISE_PRODUCT_ID, TEAM_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(teamDowngradeWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val teamSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(teamSubscription, "TEAM subscription should exist after downgrade")
            assertEquals(SubscriptionTier.TEAM, teamSubscription.tier, "Should be downgraded to TEAM tier")

            // Verify SMS credits reduced via service (not retained from higher tier)
            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(200, remainingQuota, "TEAM should have exactly 200 SMS credits (not retain 500 from ENTERPRISE)")
            }

            // Step 2: Downgrade to FREELANCER via PRODUCT_CHANGE
            val freelancerDowngradeWebhook = createProductChangeWebhookPayload(TEAM_PRODUCT_ID, FREELANCER_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(freelancerDowngradeWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val freelancerSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(freelancerSubscription, "FREELANCER subscription should exist after downgrade")
            assertEquals(SubscriptionTier.FREELANCER, freelancerSubscription.tier, "Should be downgraded to FREELANCER tier")

            // Security Assertion: Verify SMS credits are properly reduced via service
            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(100, remainingQuota, "FREELANCER should have exactly 100 SMS credits (not retain higher tier credits)")
            }

            // Verify subscription history integrity
            val allUserSubscriptions = subscriptionRepository.findByUserId(testUserId)
            val activeSubscriptions = allUserSubscriptions.filter { it.status == SubscriptionStatus.ACTIVE }
            assertEquals(1, activeSubscriptions.size, "Should have exactly one active subscription after downgrades")
        }

        @Test
        @DisplayName("Should prevent SMS credit multiplication through renewal exploits")
        @Transactional
        open fun `should prevent SMS credit multiplication through renewal exploits`() {
            // Given - Create initial TEAM subscription
            val initialTeamWebhook = createInitialPurchaseWebhookPayload(TEAM_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(initialTeamWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val initialSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(initialSubscription, "Initial TEAM subscription should be created")
            assertEquals(SubscriptionTier.TEAM, initialSubscription.tier, "Should be TEAM tier")

            // Verify initial SMS credits via service
            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(200, remainingQuota, "TEAM should have 200 SMS credits initially")
            }

            // Attempt 1: Send first RENEWAL webhook
            val firstRenewalWebhook = createRenewalWebhookPayload(TEAM_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(firstRenewalWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val afterFirstRenewal = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(afterFirstRenewal, "Subscription should exist after first renewal")
            assertEquals(SubscriptionTier.TEAM, afterFirstRenewal.tier, "Should remain TEAM tier")

            // Verify SMS credits not multiplied after first renewal
            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(200, remainingQuota, "TEAM should still have exactly 200 SMS credits (not 400 from renewal)")
            }

            // Attempt 2: Send second RENEWAL webhook (potential exploit attempt)
            val secondRenewalWebhook = createRenewalWebhookPayload(TEAM_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(secondRenewalWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val afterSecondRenewal = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(afterSecondRenewal, "Subscription should exist after second renewal")
            assertEquals(SubscriptionTier.TEAM, afterSecondRenewal.tier, "Should remain TEAM tier")

            // Verify SMS credits not multiplied after second renewal
            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(200, remainingQuota, "TEAM should still have exactly 200 SMS credits (not 600 from multiple renewals)")
            }

            // Attempt 3: Send third RENEWAL webhook (aggressive exploit attempt)
            val thirdRenewalWebhook = createRenewalWebhookPayload(TEAM_PRODUCT_ID)
            mockMvc.perform(
                post(WEBHOOK_ENDPOINT)
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", WEBHOOK_SECRET)
                    .content(objectMapper.writeValueAsString(thirdRenewalWebhook))
            ).andExpect(status().isOk)

            Thread.sleep(WEBHOOK_PROCESSING_DELAY_MS)

            val finalSubscription = subscriptionRepository.findByRevenueCatCustomerId(APP_USER_ID)
            assertNotNull(finalSubscription, "Subscription should exist after third renewal")
            assertEquals(SubscriptionTier.TEAM, finalSubscription.tier, "Should remain TEAM tier")

            // Security Assertion: Verify no SMS credit multiplication via service
            smsQuotaService.getRemainingQuota(salonId = testSalonId).let { remainingQuota ->
                assertEquals(200, remainingQuota, "TEAM should have exactly 200 SMS credits (not 800 from multiple renewals)")
            }

            // Verify subscription integrity
            val allUserSubscriptions = subscriptionRepository.findByUserId(testUserId)
            val activeSubscriptions = allUserSubscriptions.filter { it.status == SubscriptionStatus.ACTIVE }
            assertEquals(1, activeSubscriptions.size, "Should have exactly one active subscription")
            assertEquals(finalSubscription.id, activeSubscriptions[0].id, "Should be the same subscription instance")
        }
    }

    // Test Data Builders and Helper Methods
    private fun createVerifyPurchaseCommand(productId: String): VerifyPurchaseCommand {
        return VerifyPurchaseCommand(
            userId = testUserId,
            salonId = testSalonId,
            packageId = productId,
            revenueCatCustomerId = REVENUE_CAT_CUSTOMER_ID,
            revenueCatEntitlementId = "premium_access",
            customerInfo = mapOf("originalAppUserId" to APP_USER_ID)
        )
    }

    /**
     * Creates realistic RevenueCat INITIAL_PURCHASE webhook payload
     * Based on official RevenueCat documentation sample events
     */
    private fun createInitialPurchaseWebhookPayload(
        productId: String,
        isTrialConversion: Boolean = false,
        isTrial: Boolean = false
    ): Map<String, Any> {
        return mapOf(
            "api_version" to "1.0",
            "event" to mapOf(
                "aliases" to listOf(REVENUE_CAT_CUSTOMER_ID),
                "app_id" to "1234567890",
                "app_user_id" to APP_USER_ID,
                "commission_percentage" to 0.3,
                "country_code" to "RO",
                "currency" to "RON",
                "entitlement_id" to "premium_access",
                "entitlement_ids" to listOf("premium_access"),
                "environment" to "SANDBOX",
                "event_timestamp_ms" to PURCHASE_TIME_MS,
                "expiration_at_ms" to EXPIRATION_TIME_MS,
                "id" to "12345678-1234-1234-1234-123456789012",
                "is_family_share" to false,
                "is_trial_conversion" to isTrialConversion,
                "offer_code" to null,
                "original_app_user_id" to APP_USER_ID,
                "original_transaction_id" to "1000000123456789",
                "period_type" to if (isTrial) "TRIAL" else "NORMAL",
                "presented_offering_id" to "default",
                "price" to 99.0,
                "price_in_purchased_currency" to 99.0,
                "product_id" to productId,
                "purchased_at_ms" to PURCHASE_TIME_MS,
                "store" to "APP_STORE",
                "subscriber_attributes" to mapOf(
                    "\$email" to mapOf(
                        "updated_at_ms" to PURCHASE_TIME_MS,
                        "value" to "<EMAIL>"
                    )
                ),
                "takehome_percentage" to 0.7,
                "tax_percentage" to 0.0,
                "transaction_id" to "1000000123456789",
                "type" to "INITIAL_PURCHASE"
            )
        )
    }

    /**
     * Creates realistic RevenueCat RENEWAL webhook payload
     * Based on official RevenueCat documentation sample events
     */
    private fun createRenewalWebhookPayload(productId: String): Map<String, Any> {
        return mapOf(
            "api_version" to "1.0",
            "event" to mapOf(
                "aliases" to listOf(REVENUE_CAT_CUSTOMER_ID),
                "app_id" to "1234567890",
                "app_user_id" to APP_USER_ID,
                "commission_percentage" to 0.3,
                "country_code" to "RO",
                "currency" to "RON",
                "entitlement_id" to "premium_access",
                "entitlement_ids" to listOf("premium_access"),
                "environment" to "SANDBOX",
                "event_timestamp_ms" to EXPIRATION_TIME_MS,
                "expiration_at_ms" to EXPIRATION_TIME_MS + 2592000000L, // +30 days
                "id" to "12345678-1234-1234-1234-123456789013",
                "is_family_share" to false,
                "is_trial_conversion" to false,
                "offer_code" to null,
                "original_app_user_id" to APP_USER_ID,
                "original_transaction_id" to "1000000123456789",
                "period_type" to "NORMAL",
                "presented_offering_id" to "default",
                "price" to 99.0,
                "price_in_purchased_currency" to 99.0,
                "product_id" to productId,
                "purchased_at_ms" to EXPIRATION_TIME_MS,
                "store" to "APP_STORE",
                "subscriber_attributes" to mapOf(
                    "\$email" to mapOf(
                        "updated_at_ms" to EXPIRATION_TIME_MS,
                        "value" to "<EMAIL>"
                    )
                ),
                "takehome_percentage" to 0.7,
                "tax_percentage" to 0.0,
                "transaction_id" to "1000000123456790",
                "type" to "RENEWAL"
            )
        )
    }

    /**
     * Creates realistic RevenueCat CANCELLATION webhook payload
     * Based on official RevenueCat documentation sample events
     */
    private fun createCancellationWebhookPayload(productId: String): Map<String, Any> {
        return mapOf(
            "api_version" to "1.0",
            "event" to mapOf(
                "aliases" to listOf(REVENUE_CAT_CUSTOMER_ID),
                "app_id" to "1234567890",
                "app_user_id" to APP_USER_ID,
                "cancel_reason" to "UNSUBSCRIBE",
                "commission_percentage" to 0.3,
                "country_code" to "RO",
                "currency" to "RON",
                "entitlement_id" to "premium_access",
                "entitlement_ids" to listOf("premium_access"),
                "environment" to "SANDBOX",
                "event_timestamp_ms" to EXPIRATION_TIME_MS + 86400000L, // +1 day
                "expiration_at_ms" to EXPIRATION_TIME_MS,
                "id" to "12345678-1234-1234-1234-123456789014",
                "is_family_share" to false,
                "is_trial_conversion" to false,
                "offer_code" to null,
                "original_app_user_id" to APP_USER_ID,
                "original_transaction_id" to "1000000123456789",
                "period_type" to "NORMAL",
                "presented_offering_id" to "default",
                "price" to 99.0,
                "price_in_purchased_currency" to 99.0,
                "product_id" to productId,
                "purchased_at_ms" to PURCHASE_TIME_MS,
                "store" to "APP_STORE",
                "subscriber_attributes" to mapOf(
                    "\$email" to mapOf(
                        "updated_at_ms" to EXPIRATION_TIME_MS + 86400000L,
                        "value" to "<EMAIL>"
                    )
                ),
                "takehome_percentage" to 0.7,
                "tax_percentage" to 0.0,
                "transaction_id" to "1000000123456789",
                "type" to "CANCELLATION"
            )
        )
    }

    /**
     * Creates realistic RevenueCat EXPIRATION webhook payload
     * Based on official RevenueCat documentation sample events
     */
    private fun createExpirationWebhookPayload(productId: String): Map<String, Any> {
        return mapOf(
            "api_version" to "1.0",
            "event" to mapOf(
                "aliases" to listOf(REVENUE_CAT_CUSTOMER_ID),
                "app_id" to "1234567890",
                "app_user_id" to APP_USER_ID,
                "commission_percentage" to 0.3,
                "country_code" to "RO",
                "currency" to "RON",
                "entitlement_id" to null,
                "entitlement_ids" to listOf("premium_access"),
                "environment" to "SANDBOX",
                "event_timestamp_ms" to EXPIRATION_TIME_MS,
                "expiration_at_ms" to EXPIRATION_TIME_MS,
                "id" to "12345678-1234-1234-1234-123456789015",
                "is_family_share" to false,
                "is_trial_conversion" to false,
                "offer_code" to null,
                "original_app_user_id" to APP_USER_ID,
                "original_transaction_id" to "1000000123456789",
                "period_type" to "NORMAL",
                "presented_offering_id" to "default",
                "price" to 99.0,
                "price_in_purchased_currency" to 99.0,
                "product_id" to productId,
                "purchased_at_ms" to PURCHASE_TIME_MS,
                "store" to "APP_STORE",
                "subscriber_attributes" to mapOf(
                    "\$email" to mapOf(
                        "updated_at_ms" to EXPIRATION_TIME_MS,
                        "value" to "<EMAIL>"
                    )
                ),
                "takehome_percentage" to 0.7,
                "tax_percentage" to 0.0,
                "transaction_id" to "1000000123456789",
                "type" to "EXPIRATION"
            )
        )
    }

    /**
     * Creates realistic RevenueCat PRODUCT_CHANGE webhook payload
     * Based on official RevenueCat documentation sample events
     */
    private fun createProductChangeWebhookPayload(oldProductId: String, newProductId: String): Map<String, Any> {
        return mapOf(
            "api_version" to "1.0",
            "event" to mapOf(
                "aliases" to listOf(REVENUE_CAT_CUSTOMER_ID),
                "app_id" to "1234567890",
                "app_user_id" to APP_USER_ID,
                "commission_percentage" to 0.3,
                "country_code" to "RO",
                "currency" to "RON",
                "entitlement_id" to "premium_access",
                "entitlement_ids" to listOf("premium_access"),
                "environment" to "SANDBOX",
                "event_timestamp_ms" to EXPIRATION_TIME_MS + 3600000L, // +1 hour
                "expiration_at_ms" to EXPIRATION_TIME_MS + 2592000000L, // +30 days
                "id" to "12345678-1234-1234-1234-123456789016",
                "is_family_share" to false,
                "is_trial_conversion" to false,
                "new_product_id" to newProductId,
                "offer_code" to null,
                "original_app_user_id" to APP_USER_ID,
                "original_transaction_id" to "1000000123456789",
                "period_type" to "NORMAL",
                "presented_offering_id" to "default",
                "price" to 199.0, // New tier price
                "price_in_purchased_currency" to 199.0,
                "product_id" to oldProductId,
                "purchased_at_ms" to EXPIRATION_TIME_MS + 3600000L,
                "store" to "APP_STORE",
                "subscriber_attributes" to mapOf(
                    "\$email" to mapOf(
                        "updated_at_ms" to EXPIRATION_TIME_MS + 3600000L,
                        "value" to "<EMAIL>"
                    )
                ),
                "takehome_percentage" to 0.7,
                "tax_percentage" to 0.0,
                "transaction_id" to "1000000123456791",
                "type" to "PRODUCT_CHANGE"
            )
        )
    }
}
