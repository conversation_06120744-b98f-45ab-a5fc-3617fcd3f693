package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.assertDoesNotThrow
import kotlin.test.*
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.*
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.AppointmentSchedulingService
import ro.animaliaprogramari.animalia.domain.service.OptimizedSchedulingConflictService
import ro.animaliaprogramari.animalia.domain.service.DetailedScheduleConflict
import ro.animaliaprogramari.animalia.domain.service.ScheduleConflictType
import ro.animaliaprogramari.animalia.application.monitoring.WorkflowMetrics
import ro.animaliaprogramari.animalia.adapter.outbound.sms.SmsoSmsSender
import ro.animaliaprogramari.animalia.domain.validation.ValidationService
import ro.animaliaprogramari.animalia.domain.repository.AppointmentSubscriptionRepository
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

@DisplayName("AppointmentManagementUseCaseImpl Tests")
class AppointmentManagementUseCaseImplTest {
    private lateinit var useCase: AppointmentManagementUseCaseImpl

    private lateinit var appointmentRepository: AppointmentRepository
    private lateinit var clientRepository: ClientRepository
    private lateinit var petRepository: PetRepository
    private lateinit var userRepository: UserRepository
    private lateinit var salonRepository: SalonRepository
    private lateinit var salonServiceRepository: SalonServiceRepository
    private lateinit var staffRepository: StaffRepository
    private lateinit var blockTimeRepository: BlockTimeRepository
    private lateinit var workingHoursRepository: WorkingHoursRepository
    private lateinit var staffWorkingHoursRepository: StaffWorkingHoursRepository
    private lateinit var domainEventPublisher: DomainEventPublisher
    private lateinit var appointmentSchedulingService: AppointmentSchedulingService
    private lateinit var optimizedConflictService: OptimizedSchedulingConflictService
    private lateinit var validationService: ValidationService
    private lateinit var appointmentAlternativeSuggestionService: AppointmentAlternativeSuggestionService
    private lateinit var smsSender: SmsoSmsSender
    private lateinit var workflowMetrics: WorkflowMetrics
    private lateinit var appointmentSubscriptionUseCase: AppointmentSubscriptionUseCaseImpl
    private lateinit var appointmentSubscriptionRepository: AppointmentSubscriptionRepository

    // Test data
    private val salonId = SalonId.of("salon-1")
    private val staffId = StaffId.of("staff-1")
    private val clientId = ClientId.of("client-1")
    private val petId = PetId.of("pet-1")
    private val userId = UserId.of("user-1")
    private val serviceId = ServiceId.of("service-1")
    private val appointmentId = AppointmentId.of("appointment-1")
    private lateinit var salon: Salon
    private lateinit var staff: Staff
    private lateinit var client: Client
    private lateinit var pet: Pet
    private lateinit var salonService: SalonService
    private lateinit var workingHours: WorkingHoursSettings
    private lateinit var staffWorkingHours: StaffWorkingHoursSettings

    @BeforeEach
    fun setUp() {
        appointmentRepository = mockk()
        clientRepository = mockk()
        petRepository = mockk()
        userRepository = mockk()
        salonRepository = mockk()
        salonServiceRepository = mockk()
        staffRepository = mockk()
        blockTimeRepository = mockk()
        workingHoursRepository = mockk()
        staffWorkingHoursRepository = mockk()
        domainEventPublisher = mockk(relaxed = true)
        appointmentSchedulingService = mockk()
        optimizedConflictService = mockk()
        validationService = mockk(relaxed = true)
        appointmentAlternativeSuggestionService = mockk()
        smsSender = mockk(relaxed = true)
        workflowMetrics = mockk(relaxed = true)
        appointmentSubscriptionUseCase = mockk(relaxed = true)
        appointmentSubscriptionRepository = mockk(relaxed = true)

        useCase = AppointmentManagementUseCaseImpl(
            appointmentRepository,
            clientRepository,
            petRepository,
            salonRepository,
            salonServiceRepository,
            staffRepository,
            blockTimeRepository,
            workingHoursRepository,
            staffWorkingHoursRepository,
            domainEventPublisher,
            optimizedConflictService,
            validationService,
            appointmentAlternativeSuggestionService,
            workflowMetrics,
            appointmentSubscriptionUseCase,
            appointmentSubscriptionRepository
        )

        // Initialize test data
        salon = createTestSalon()
        staff = createTestStaff()
        client = createTestClient()
        pet = createTestPet()
        salonService = createTestSalonService()
        workingHours = createTestWorkingHours()
        staffWorkingHours = createTestStaffWorkingHours()

        // Setup workflow metrics to execute blocks directly
        every { workflowMetrics.executeWorkflow<Any>(any(), any(), any()) } answers {
            val block = lastArg<() -> Any>()
            block()
        }
        every { workflowMetrics.executeAppointmentStep<Any>(any(), any(), any(), any(), any()) } answers {
            val block = lastArg<() -> Any>()
            block()
        }
    }

    @Nested
    @DisplayName("scheduleAppointment() Tests")
    inner class ScheduleAppointmentTests {

    }

    @Nested
    @DisplayName("getAppointmentById() Tests")
    inner class GetAppointmentByIdTests {

        @Test
        @DisplayName("Should return appointment when found")
        fun shouldReturnAppointmentWhenFound() {
            // Given
            val query = GetAppointmentByIdQuery(appointmentId = appointmentId, salonId = salonId)
            val appointment = createTestAppointment()

            every { appointmentRepository.findById(appointmentId) } returns appointment

            // When
            val result = useCase.getAppointmentById(query)

            // Then
            assertNotNull(result)
            assertEquals(appointmentId, result.id)
            assertEquals(salonId, result.salonId)
            verify { appointmentRepository.findById(appointmentId) }
        }

        @Test
        @DisplayName("Should return null when appointment not found")
        fun shouldReturnNullWhenAppointmentNotFound() {
            // Given
            val query = GetAppointmentByIdQuery(appointmentId = appointmentId, salonId = salonId)
            every { appointmentRepository.findById(appointmentId) } returns null

            // When
            val result = useCase.getAppointmentById(query)

            // Then
            assertNull(result)
            verify { appointmentRepository.findById(appointmentId) }
        }

        @Test
        @DisplayName("Should throw BusinessRuleViolationException when appointment belongs to different salon")
        fun shouldThrowBusinessRuleViolationExceptionWhenAppointmentBelongsToDifferentSalon() {
            // Given
            val differentSalonId = SalonId.of("different-salon")
            val query = GetAppointmentByIdQuery(
                appointmentId = appointmentId,
                salonId = differentSalonId,
                requesterId = userId // Add requesterId to trigger salon validation
            )
            val appointment = createTestAppointment().copy(salonId = salonId)

            every { appointmentRepository.findById(appointmentId) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, differentSalonId) } returns staff

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.getAppointmentById(query)
            }
            assertTrue(exception.message!!.contains("does not belong to the specified salon"))
        }
    }

    @Nested
    @DisplayName("cancelAppointment() Tests")
    inner class CancelAppointmentTests {

        @Test
        @DisplayName("Should successfully cancel appointment when valid")
        fun shouldSuccessfullyCancelAppointmentWhenValid() {
            // Given
            val appointment = createTestAppointment()
            val command = CancelAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                reason = "Client requested cancellation",
                cancellerUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { appointmentRepository.save(any()) } returns appointment.copy(status = AppointmentStatus.CANCELLED)

            // When
            val result = useCase.cancelAppointment(command)

            // Then
            assertNotNull(result)
            assertEquals(AppointmentStatus.CANCELLED, result.status)
            verify { appointmentRepository.save(any()) }
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when appointment not found")
        fun shouldThrowEntityNotFoundExceptionWhenAppointmentNotFound() {
            // Given
            val command = CancelAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                reason = "Client requested cancellation",
                cancellerUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns null
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.cancelAppointment(command)
            }
            assertTrue(exception.message!!.contains("Appointment not found"))
        }

        @Test
        @DisplayName("Should throw BusinessRuleViolationException when appointment belongs to different salon")
        fun shouldThrowBusinessRuleViolationExceptionWhenAppointmentBelongsToDifferentSalon() {
            // Given
            val differentSalonId = SalonId.of("different-salon")
            val appointment = createTestAppointment().copy(salonId = differentSalonId)
            val command = CancelAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                reason = "Client requested cancellation",
                cancellerUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.cancelAppointment(command)
            }
            assertTrue(exception.message!!.contains("does not belong to the specified salon"))
        }

        @Test
        @DisplayName("Should throw BusinessRuleViolationException when appointment is already cancelled")
        fun shouldThrowBusinessRuleViolationExceptionWhenAppointmentAlreadyCancelled() {
            // Given
            val cancelledAppointment = createTestAppointment().copy(status = AppointmentStatus.CANCELLED)
            val command = CancelAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                reason = "Client requested cancellation",
                cancellerUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns cancelledAppointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                useCase.cancelAppointment(command)
            }
            assertTrue(exception.message!!.contains("cannot be cancelled"))
        }
    }

    @Nested
    @DisplayName("completeAppointment() Tests")
    inner class CompleteAppointmentTests {

        @Test
        @DisplayName("Should successfully complete appointment when valid")
        fun shouldSuccessfullyCompleteAppointmentWhenValid() {
            // Given
            val appointment = createTestAppointment()
            val command = CompleteAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                notes = "Service completed successfully",
                completerUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { appointmentRepository.save(any()) } returns appointment.copy(status = AppointmentStatus.COMPLETED)

            // When
            val result = useCase.completeAppointment(command)

            // Then
            assertNotNull(result)
            assertEquals(AppointmentStatus.COMPLETED, result.status)
            verify { appointmentRepository.save(any()) }
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when appointment not found")
        fun shouldThrowEntityNotFoundExceptionWhenAppointmentNotFound() {
            // Given
            val command = CompleteAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                notes = "Service completed successfully",
                completerUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns null
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.completeAppointment(command)
            }
            assertTrue(exception.message!!.contains("Appointment not found"))
        }

        @Test
        @DisplayName("Should throw BusinessRuleViolationException when appointment belongs to different salon")
        fun shouldThrowBusinessRuleViolationExceptionWhenAppointmentBelongsToDifferentSalon() {
            // Given
            val differentSalonId = SalonId.of("different-salon")
            val appointment = createTestAppointment().copy(salonId = differentSalonId)
            val command = CompleteAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                notes = "Service completed successfully",
                completerUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.completeAppointment(command)
            }
            assertTrue(exception.message!!.contains("does not belong to the specified salon"))
        }
    }

    @Nested
    @DisplayName("getSalonAppointments() Tests")
    inner class GetSalonAppointmentsTests {

        @Test
        @DisplayName("Should return salon appointments when user has access")
        fun shouldReturnSalonAppointmentsWhenUserHasAccess() {
            // Given
            val query = GetSalonAppointmentsQuery(
                salonId = salonId,
                requesterId = userId,
                date = LocalDate.now()
            )
            val appointments = listOf(createTestAppointment())

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { appointmentRepository.findBySalonIdWithFilters(salonId, query.date, null, null, null, null, null) } returns appointments

            // When
            val result = useCase.getSalonAppointments(query)

            // Then
            assertEquals(1, result.size)
            assertEquals(appointments, result)
            verify { appointmentRepository.findBySalonIdWithFilters(any(), any(), any(), any(), any(), any(), any()) }
        }

        @Test
        @DisplayName("Should throw UnauthorizedException when user has no access")
        fun shouldThrowUnauthorizedExceptionWhenUserHasNoAccess() {
            // Given
            val query = GetSalonAppointmentsQuery(
                salonId = salonId,
                requesterId = userId,
                date = LocalDate.now()
            )

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns null

            // When & Then
            val exception = assertThrows<UnauthorizedException> {
                useCase.getSalonAppointments(query)
            }
            assertTrue(exception.message!!.contains("does not have access to this salon"))
        }

        @Test
        @DisplayName("Should throw UnauthorizedException when staff is not active")
        fun shouldThrowUnauthorizedExceptionWhenStaffNotActive() {
            // Given
            val query = GetSalonAppointmentsQuery(
                salonId = salonId,
                requesterId = userId,
                date = LocalDate.now()
            )
            val inactiveStaff = staff.copy(isActive = false)

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns inactiveStaff

            // When & Then
            val exception = assertThrows<UnauthorizedException> {
                useCase.getSalonAppointments(query)
            }
            assertTrue(exception.message!!.contains("does not have access to this salon"))
        }
    }

    @Nested
    @DisplayName("getStaffAppointments() Tests")
    inner class GetStaffAppointmentsTests {

        @Test
        @DisplayName("Should return staff appointments when user has access")
        fun shouldReturnStaffAppointmentsWhenUserHasAccess() {
            // Given
            val query = GetStaffAppointmentsQuery(
                salonId = salonId,
                staffId = staffId,
                requesterId = userId,
                date = LocalDate.now()
            )
            val appointments = listOf(createTestAppointment())

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { appointmentRepository.findBySalonIdAndStaffIdWithFilters(salonId, staffId, query.date, null, null, null) } returns appointments

            // When
            val result = useCase.getStaffAppointments(query)

            // Then
            assertEquals(1, result.size)
            assertEquals(appointments, result)
            verify { appointmentRepository.findBySalonIdAndStaffIdWithFilters(any(), any(), any(), any(), any(), any()) }
        }

        @Test
        @DisplayName("Should throw UnauthorizedException when user has no access")
        fun shouldThrowUnauthorizedExceptionWhenUserHasNoAccess() {
            // Given
            val query = GetStaffAppointmentsQuery(
                salonId = salonId,
                staffId = staffId,
                requesterId = userId,
                date = LocalDate.now()
            )

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns null

            // When & Then
            val exception = assertThrows<UnauthorizedException> {
                useCase.getStaffAppointments(query)
            }
            assertTrue(exception.message!!.contains("does not have access to this salon"))
        }
    }

    @Nested
    @DisplayName("getClientAppointments() Tests")
    inner class GetClientAppointmentsTests {

        @Test
        @DisplayName("Should return client appointments when valid")
        fun shouldReturnClientAppointmentsWhenValid() {
            // Given
            val query = GetClientAppointmentsQuery(
                salonId = salonId,
                clientId = clientId,
                requesterId = userId
            )
            val appointments = listOf(createTestAppointment())

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { appointmentRepository.findBySalonIdWithFilters(salonId, query.date, query.startDate, query.endDate, query.status, clientId, null) } returns appointments

            // When
            val result = useCase.getClientAppointments(query)

            // Then
            assertEquals(1, result.size)
            assertEquals(appointments, result)
        }

        @Test
        @DisplayName("Should throw UnauthorizedException when user has no access")
        fun shouldThrowUnauthorizedExceptionWhenUserHasNoAccess() {
            // Given
            val query = GetClientAppointmentsQuery(
                salonId = salonId,
                clientId = clientId,
                requesterId = userId
            )

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns null

            // When & Then
            val exception = assertThrows<UnauthorizedException> {
                useCase.getClientAppointments(query)
            }
            assertTrue(exception.message!!.contains("does not have access to this salon"))
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when client not found")
        fun shouldThrowEntityNotFoundExceptionWhenClientNotFound() {
            // Given
            val query = GetClientAppointmentsQuery(
                salonId = salonId,
                clientId = clientId,
                requesterId = userId
            )

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { clientRepository.findById(clientId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.getClientAppointments(query)
            }
            assertTrue(exception.message!!.contains("Client"))
        }
    }

    @Nested
    @DisplayName("getAppointmentsByDateRange() Tests")
    inner class GetAppointmentsByDateRangeTests {

        @Test
        @DisplayName("Should return appointments by date range")
        fun shouldReturnAppointmentsByDateRange() {
            // Given
            val dateRange = DateRange(LocalDate.now(), LocalDate.now().plusDays(7))
            val query = GetAppointmentsByDateRangeQuery(
                dateRange = dateRange,
                salonId = salonId,
                staffId = staffId,
                status = AppointmentStatus.SCHEDULED
            )
            val appointments = listOf(createTestAppointment())

            every { appointmentRepository.findByDateRange(any(), any(), any(), any()) } returns appointments

            // When
            val result = useCase.getAppointmentsByDateRange(query)

            // Then
            assertEquals(1, result.size)
            assertEquals(appointments, result)
            verify { appointmentRepository.findByDateRange(dateRange.startDate, dateRange.endDate, staffId, AppointmentStatus.SCHEDULED) }
        }
    }

    @Nested
    @DisplayName("getAppointmentsByGroomer() Tests")
    inner class GetAppointmentsByGroomerTests {

        @Test
        @DisplayName("Should return appointments by groomer")
        fun shouldReturnAppointmentsByGroomer() {
            // Given
            val dateRange = DateRange(LocalDate.now(), LocalDate.now().plusDays(7))
            val query = GetAppointmentsByGroomerQuery(
                staffId = staffId,
                salonId = salonId,
                dateRange = dateRange,
                status = AppointmentStatus.SCHEDULED
            )
            val appointments = listOf(createTestAppointment())

            every { appointmentRepository.findByStaffId(any(), any(), any(), any()) } returns appointments

            // When
            val result = useCase.getAppointmentsByGroomer(query)

            // Then
            assertEquals(1, result.size)
            assertEquals(appointments, result)
            verify { appointmentRepository.findByStaffId(staffId, dateRange.startDate, dateRange.endDate, AppointmentStatus.SCHEDULED) }
        }
    }

    @Nested
    @DisplayName("updateAppointment() Tests")
    inner class UpdateAppointmentTests {

        @Test
        @DisplayName("Should throw NotImplementedError as functionality is not yet implemented")
        fun shouldThrowNotImplementedErrorAsFunctionalityIsNotYetImplemented() {
            // Given
            val appointment = createTestAppointment()
            val command = UpdateAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                notes = "Updated notes",
                updaterUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff

            // When & Then
            val exception = assertThrows<NotImplementedError> {
                useCase.updateAppointment(command)
            }
            assertTrue(exception.message!!.contains("Update appointment functionality not yet implemented"))
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when appointment not found")
        fun shouldThrowEntityNotFoundExceptionWhenAppointmentNotFound() {
            // Given
            val command = UpdateAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                notes = "Updated notes",
                updaterUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns null
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.updateAppointment(command)
            }
            assertTrue(exception.message!!.contains("Appointment not found"))
        }
    }

    @Nested
    @DisplayName("rescheduleAppointment() Tests")
    inner class RescheduleAppointmentTests {

        @Test
        @DisplayName("Should successfully reschedule appointment when valid")
        fun shouldSuccessfullyRescheduleAppointmentWhenValid() {
            // Given
            val appointment = createTestAppointment()
            val newDate = LocalDate.now().plusDays(2)
            val newStartTime = LocalTime.of(14, 0)
            val newEndTime = LocalTime.of(15, 0)
            val command = RescheduleAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                newDate = newDate,
                newStartTime = newStartTime,
                newEndTime = newEndTime,
                reschedulerUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { appointmentRepository.save(any()) } returns appointment.copy(
                appointmentDate = newDate,
                startTime = newStartTime,
                endTime = newEndTime
            )

            // When
            val result = useCase.rescheduleAppointment(command)

            // Then
            assertNotNull(result)
            assertEquals(newDate, result.appointmentDate)
            assertEquals(newStartTime, result.startTime)
            assertEquals(newEndTime, result.endTime)
            verify { appointmentRepository.save(any()) }
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when appointment not found")
        fun shouldThrowEntityNotFoundExceptionWhenAppointmentNotFound() {
            // Given
            val command = RescheduleAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                newDate = LocalDate.now().plusDays(2),
                newStartTime = LocalTime.of(14, 0),
                newEndTime = LocalTime.of(15, 0),
                reschedulerUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns null
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.rescheduleAppointment(command)
            }
            assertTrue(exception.message!!.contains("Appointment not found"))
        }
    }

    @Nested
    @DisplayName("deleteAppointment() Tests")
    inner class DeleteAppointmentTests {

        @Test
        @DisplayName("Should successfully delete appointment when valid")
        fun shouldSuccessfullyDeleteAppointmentWhenValid() {
            // Given
            val appointment = createTestAppointment()
            val command = DeleteAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                deleterUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns appointment
            every { appointmentRepository.deleteById(appointmentId) } returns true

            // When
            val result = useCase.deleteAppointment(command)

            // Then
            assertTrue(result)
            verify { appointmentRepository.deleteById(appointmentId) }
        }

        @Test
        @DisplayName("Should return false when appointment not found")
        fun shouldReturnFalseWhenAppointmentNotFound() {
            // Given
            val command = DeleteAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                deleterUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns null

            // When
            val result = useCase.deleteAppointment(command)

            // Then
            assertFalse(result)
            verify { appointmentRepository.findById(appointmentId) }
            verify(exactly = 0) { appointmentRepository.deleteById(any()) }
        }

        @Test
        @DisplayName("Should throw BusinessRuleViolationException when appointment belongs to different salon")
        fun shouldThrowBusinessRuleViolationExceptionWhenAppointmentBelongsToDifferentSalon() {
            // Given
            val differentSalonId = SalonId.of("different-salon")
            val appointment = createTestAppointment().copy(salonId = differentSalonId)
            val command = DeleteAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                deleterUserId = userId
            )

            every { appointmentRepository.findById(appointmentId) } returns appointment

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.deleteAppointment(command)
            }
            assertTrue(exception.message!!.contains("does not belong to the specified salon"))
            verify { appointmentRepository.findById(appointmentId) }
            verify(exactly = 0) { appointmentRepository.deleteById(any()) }
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling Tests")
    inner class EdgeCasesAndErrorHandlingTests {

        @Test
        @DisplayName("Should handle concurrent appointment scheduling gracefully")
        fun shouldHandleConcurrentAppointmentSchedulingGracefully() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findByIds(listOf(serviceId)) } returns listOf(salonService)
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()

            // Simulate conflict detection finding a conflict (race condition)
            val conflicts = listOf(
                DetailedScheduleConflict(
                    type = ScheduleConflictType.APPOINTMENT,
                    message = "Overlaps with existing appointment",
                    timeSlot = ro.animaliaprogramari.animalia.domain.service.TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0)),
                    conflictingAppointmentId = AppointmentId.generate()
                )
            )
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns conflicts
            every { optimizedConflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any()) } returns false
            every { staffRepository.findAvailableStaff(any(), any(), any(), any()) } returns emptyList()

            // When & Then
            val exception = assertThrows<AppointmentSchedulingConflictException> {
                useCase.scheduleAppointment(command)
            }
            assertTrue(exception.message!!.contains("Conflicts detected") || exception.cause is TimeSlotUnavailableException)
        }

        @Test
        @DisplayName("Should handle missing working hours gracefully")
        fun shouldHandleMissingWorkingHoursGracefully() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findByIds(listOf(serviceId)) } returns listOf(salonService)
            every { salonServiceRepository.findById(serviceId) } returns salonService
            // Return default working hours instead of null to test the actual business logic
            every { workingHoursRepository.findBySalonId(salonId) } returns WorkingHoursSettings.createDefault(salonId)
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { appointmentRepository.save(any()) } returns createTestAppointment()

            // When & Then - This should succeed with default working hours
            assertDoesNotThrow {
                val result = useCase.scheduleAppointment(command)
                assertNotNull(result)
            }
        }

        @Test
        @DisplayName("Should handle invalid service IDs gracefully")
        fun shouldHandleInvalidServiceIdsGracefully() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findByIds(listOf(serviceId)) } returns emptyList() // No services found
            every { salonServiceRepository.findById(serviceId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.scheduleAppointment(command)
            }
            assertTrue(exception.message!!.contains("Service"))
        }
    }



    @Nested
    @DisplayName("NotImplementedMethodsTests")
    inner class NotImplementedMethodsTests {

        @Test
        @DisplayName("Should throw NotImplementedError for markNoShow")
        fun shouldThrowNotImplementedErrorForMarkNoShow() {
            // Given
            val command = MarkNoShowCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                markerUserId = userId,
                noShowNotes = "Client did not show up"
            )

            // When & Then
            val exception = assertThrows<NotImplementedError> {
                useCase.markNoShow(command)
            }
            assertTrue(exception.message!!.contains("Mark no-show functionality not yet implemented"))
        }

        @Test
        @DisplayName("Should throw NotImplementedError for markInProgress")
        fun shouldThrowNotImplementedErrorForMarkInProgress() {
            // Given
            val command = MarkInProgressCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                markerUserId = userId
            )

            // When & Then
            val exception = assertThrows<NotImplementedError> {
                useCase.markInProgress(command)
            }
            assertTrue(exception.message!!.contains("Mark in-progress functionality not yet implemented"))
        }

        @Test
        @DisplayName("Should throw NotImplementedError for checkAvailability")
        fun shouldThrowNotImplementedErrorForCheckAvailability() {
            // Given
            val query = CheckAvailabilityQuery(
                salonId = salonId,
                staffId = staffId,
                date = LocalDate.now().plusDays(1),
                startTime = LocalTime.of(10, 0),
                endTime = LocalTime.of(11, 0)
            )

            // When & Then
            val exception = assertThrows<NotImplementedError> {
                useCase.checkAvailability(query)
            }
            assertTrue(exception.message!!.contains("Check availability functionality not yet implemented"))
        }

        @Test
        @DisplayName("Should throw NotImplementedError for getConflictingAppointments")
        fun shouldThrowNotImplementedErrorForGetConflictingAppointments() {
            // Given
            val query = GetConflictingAppointmentsQuery(
                salonId = salonId,
                staffId = staffId,
                date = LocalDate.now().plusDays(1),
                startTime = LocalTime.of(10, 0),
                endTime = LocalTime.of(11, 0)
            )

            // When & Then
            val exception = assertThrows<NotImplementedError> {
                useCase.getConflictingAppointments(query)
            }
            assertTrue(exception.message!!.contains("Get conflicting appointments functionality not yet implemented"))
        }

        @Test
        @DisplayName("Should throw NotImplementedError for getAvailableSlots")
        fun shouldThrowNotImplementedErrorForGetAvailableSlots() {
            // Given
            val query = GetAvailableSlotsQuery(
                salonId = salonId,
                staffId = staffId,
                date = LocalDate.now().plusDays(1),
                serviceDuration = MyDuration.ofMinutes(60)
            )

            // When & Then
            val exception = assertThrows<NotImplementedError> {
                useCase.getAvailableSlots(query)
            }
            assertTrue(exception.message!!.contains("Get available slots functionality not yet implemented"))
        }

        @Test
        @DisplayName("Should throw NotImplementedError for getAppointmentSummary")
        fun shouldThrowNotImplementedErrorForGetAppointmentSummary() {
            // Given
            val query = GetAppointmentSummaryQuery(
                salonId = salonId,
                dateRange = DateRange(LocalDate.now(), LocalDate.now().plusDays(7))
            )

            // When & Then
            val exception = assertThrows<NotImplementedError> {
                useCase.getAppointmentSummary(query)
            }
            assertTrue(exception.message!!.contains("Get appointment summary functionality not yet implemented"))
        }

        @Test
        @DisplayName("Should throw NotImplementedError for createBulkAppointments")
        fun shouldThrowNotImplementedErrorForCreateBulkAppointments() {
            // Given
            val command = CreateBulkAppointmentsCommand(
                salonId = salonId,
                appointments = listOf(createTestScheduleCommand()),
                creatorUserId = userId
            )

            // When & Then
            val exception = assertThrows<NotImplementedError> {
                useCase.createBulkAppointments(command)
            }
            assertTrue(exception.message!!.contains("Create bulk appointments functionality not yet implemented"))
        }
    }

    @Nested
    @DisplayName("ConflictDetectionAndAlternativesTests")
    inner class ConflictDetectionAndAlternativesTests {

        @Test
        @DisplayName("Should throw AppointmentSchedulingConflictException when TimeSlotUnavailableException occurs")
        fun shouldThrowAppointmentSchedulingConflictExceptionWhenTimeSlotUnavailable() {
            // Given
            val command = createTestScheduleCommand()
            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findByIds(listOf(serviceId)) } returns listOf(salonService)
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null

            // Mock the conflict detection to return conflicts (which will trigger TimeSlotUnavailableException)
            val conflicts = listOf(
                DetailedScheduleConflict(
                    type = ScheduleConflictType.APPOINTMENT,
                    message = "Existing appointment conflict",
                    timeSlot = ro.animaliaprogramari.animalia.domain.service.TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0)),
                    conflictingAppointmentId = AppointmentId.of("conflict-1")
                )
            )
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns conflicts

            // Mock alternative suggestion service
            every { appointmentAlternativeSuggestionService.suggestAlternatives(command, any()) } returns emptyList()

            // Mock the findConflicts method that's called in the catch block
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(salonId, staffId, command.appointmentDate) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(salonId, any(), any(), setOf(staffId)) } returns emptyList()

            // When & Then
            val exception = assertThrows<AppointmentSchedulingConflictException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Intervalul solicitat nu este disponibil", exception.message)
            assertNotNull(exception.alternatives)
        }

        @Test
        @DisplayName("Should handle appointment persistence failure gracefully")
        fun shouldHandleAppointmentPersistenceFailureGracefully() {
            // Given
            val command = createTestScheduleCommand()
            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findByIds(listOf(serviceId)) } returns listOf(salonService)
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null

            every { appointmentRepository.save(any()) } throws RuntimeException("Database connection failed")

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.scheduleAppointment(command)
            }

            assertTrue(exception.message!!.contains("Failed to save appointment"))
        }
    }

    @Nested
    @DisplayName("AdditionalValidationTests")
    inner class AdditionalValidationTests {

        @Test
        @DisplayName("Should handle multiple services in appointment")
        fun shouldHandleMultipleServicesInAppointment() {
            // Given
            val secondServiceId = ServiceId.of("service-2")
            val secondService = salonService.copy(id = secondServiceId, name = "Second Service")
            val command = createTestScheduleCommand().copy(
                serviceIds = listOf(serviceId, secondServiceId)
            )

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { salonServiceRepository.findById(secondServiceId) } returns secondService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment()

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            verify { salonServiceRepository.findById(serviceId) }
            verify { salonServiceRepository.findById(secondServiceId) }
        }

        @Test
        @DisplayName("Should handle appointment with notes")
        fun shouldHandleAppointmentWithNotes() {
            // Given
            val command = createTestScheduleCommand().copy(
                notes = "Special handling required for this pet"
            )

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findByIds(listOf(serviceId)) } returns listOf(salonService)
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment().copy(notes = "Special handling required for this pet")

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            assertEquals("Special handling required for this pet", result.notes)
        }
    }

    @Nested
    @DisplayName("EdgeCaseValidationTests")
    inner class EdgeCaseValidationTests {

        @Test
        @DisplayName("Should handle appointment with future date")
        fun shouldHandleAppointmentWithFutureDate() {
            // Given
            val futureDate = LocalDate.now().plusDays(30)
            val command = createTestScheduleCommand().copy(
                appointmentDate = futureDate
            )

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findByIds(listOf(serviceId)) } returns listOf(salonService)
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment().copy(appointmentDate = futureDate)

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            assertEquals(futureDate, result.appointmentDate)
        }

        @Test
        @DisplayName("Should handle appointment with early morning time")
        fun shouldHandleAppointmentWithEarlyMorningTime() {
            // Given
            val earlyTime = LocalTime.of(8, 0)
            val command = createTestScheduleCommand().copy(
                startTime = earlyTime,
                endTime = earlyTime.plusHours(1)
            )

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findByIds(listOf(serviceId)) } returns listOf(salonService)
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment().copy(startTime = earlyTime, endTime = earlyTime.plusHours(1))

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            assertEquals(earlyTime, result.startTime)
        }
    }

    @Nested
    @DisplayName("RepositoryInteractionTests")
    inner class RepositoryInteractionTests {

        @Test
        @DisplayName("Should verify all repository calls during successful appointment scheduling")
        fun shouldVerifyAllRepositoryCallsDuringSuccessfulAppointmentScheduling() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet

            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment()

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            verify { salonRepository.findById(salonId) }
            verify { staffRepository.findById(staffId) }
            verify { clientRepository.findById(clientId) }
            verify { petRepository.findById(petId) }
            verify { salonServiceRepository.findById(serviceId) }
            verify { workingHoursRepository.findBySalonId(salonId) }
            verify { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) }
            verify { appointmentRepository.save(any()) }
        }

        @Test
        @DisplayName("Should handle empty block time list")
        fun shouldHandleEmptyBlockTimeList() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet

            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment()

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            verify { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) }
        }
    }

    @Nested
    @DisplayName("ServiceInteractionTests")
    inner class ServiceInteractionTests {

        @Test
        @DisplayName("Should verify conflict detection service is called")
        fun shouldVerifyConflictDetectionServiceIsCalled() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findByIds(listOf(serviceId)) } returns listOf(salonService)
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment()

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            verify { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) }
        }

        @Test
        @DisplayName("Should handle workflow metrics recording")
        fun shouldHandleWorkflowMetricsRecording() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findByIds(listOf(serviceId)) } returns listOf(salonService)
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment()

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            // Verify that the appointment was successfully created (metrics would be recorded internally)
            assertEquals(AppointmentStatus.SCHEDULED, result.status)
        }
    }

    @Nested
    @DisplayName("WorkflowMetricsAndErrorHandlingTests")
    inner class WorkflowMetricsAndErrorHandlingTests {

        @Test
        @DisplayName("Should record workflow metrics for successful appointment scheduling")
        fun shouldRecordWorkflowMetricsForSuccessfulAppointmentScheduling() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment()
            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                block.invoke()
            }

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            verify { workflowMetrics.executeWorkflow(
                workflowName = "schedule_appointment",
                tags = mapOf(
                    "salon_id" to command.salonId.value,
                    "staff_id" to command.staffId.value,
                    "is_new_client" to command.isNewClient.toString(),
                    "is_new_pet" to command.isNewPet.toString(),
                    "has_repetition" to command.hasRecurrence().toString()
                ),
                operation = any()
            ) }
        }

        @Test
        @DisplayName("Should record detailed exception metrics when TimeSlotUnavailableException occurs")
        @Disabled
        fun shouldRecordDetailedExceptionMetricsWhenTimeSlotUnavailableExceptionOccurs() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null

            // Mock conflict detection to return conflicts
            val conflicts = listOf(
                DetailedScheduleConflict(
                    type = ScheduleConflictType.APPOINTMENT,
                    message = "Existing appointment conflict",
                    timeSlot = ro.animaliaprogramari.animalia.domain.service.TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0)),
                    conflictingAppointmentId = AppointmentId.of("conflict-1")
                )
            )
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns conflicts
            every { appointmentAlternativeSuggestionService.suggestAlternatives(command, any()) } returns emptyList()
            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                try {
                    block.invoke()
                } catch (e: TimeSlotUnavailableException) {
                    throw AppointmentSchedulingConflictException("Intervalul solicitat nu este disponibil", emptyList(), emptyList(), e)
                }
            }
            every { workflowMetrics.recordDetailedException(any(), any(), any(), any()) } just Runs
            every { workflowMetrics.recordAppointmentOutcome(any(), any(), any(), any(), any()) } just Runs

            // When & Then
            val exception = assertThrows<AppointmentSchedulingConflictException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Intervalul solicitat nu este disponibil", exception.message)
            verify { workflowMetrics.recordDetailedException(
                exception = any<TimeSlotUnavailableException>(),
                context = "appointment_scheduling_conflict",
                salonId = command.salonId.value,
                additionalTags = mapOf(
                    "staff_id" to command.staffId.value,
                    "appointment_date" to command.appointmentDate.toString(),
                    "time_slot" to "${command.startTime}-${command.endTime}"
                )
            ) }
            verify { workflowMetrics.recordAppointmentOutcome(
                outcome = "scheduling_conflict",
                salonId = command.salonId.value,
                staffId = command.staffId.value,
                appointmentDate = command.appointmentDate,
                additionalTags = mapOf(
                    "conflict_type" to "time_slot_unavailable",
                    "conflicts_detected" to "1",
                    "alternatives_generated" to "0"
                )
            ) }
        }

        @Test
        @DisplayName("Should record metrics when alternative generation service fails and falls back to legacy")
        @Disabled
        fun shouldRecordMetricsWhenAlternativeGenerationServiceFailsAndFallsBackToLegacy() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null

            // Mock conflict detection to return conflicts
            val conflicts = listOf(
                DetailedScheduleConflict(
                    type = ScheduleConflictType.APPOINTMENT,
                    message = "Existing appointment conflict",
                    timeSlot = ro.animaliaprogramari.animalia.domain.service.TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0)),
                    conflictingAppointmentId = AppointmentId.of("conflict-1")
                )
            )
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns conflicts

            // Mock alternative suggestion service to fail
            every { appointmentAlternativeSuggestionService.suggestAlternatives(command, any()) } throws RuntimeException("Service unavailable")

            // Mock legacy alternative suggestion dependencies
            every { staffRepository.findAvailableStaff(any(), any(), any(), any()) } returns emptyList()

            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                block.invoke()
            }
            every { workflowMetrics.recordDetailedException(any(), any(), any(), any()) } just Runs
            every { workflowMetrics.recordAppointmentOutcome(any(), any(), any(), any(), any()) } just Runs

            // When & Then
            val exception = assertThrows<AppointmentSchedulingConflictException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Intervalul solicitat nu este disponibil", exception.message)
            verify { workflowMetrics.recordDetailedException(
                exception = any<RuntimeException>(),
                context = "alternative_generation_fallback",
                salonId = command.salonId.value,
                additionalTags = mapOf("fallback_reason" to "new_service_failed")
            ) }
        }
    }

    @Nested
    @DisplayName("AlternativeSuggestionLogicTests")
    inner class AlternativeSuggestionLogicTests {

        @Test
        @DisplayName("Should generate time-based alternatives with different hour offsets")
        fun shouldGenerateTimeBasedAlternativesWithDifferentHourOffsets() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null

            // Mock conflict detection to return conflicts
            val conflicts = listOf(
                DetailedScheduleConflict(
                    type = ScheduleConflictType.APPOINTMENT,
                    message = "Existing appointment conflict",
                    timeSlot = ro.animaliaprogramari.animalia.domain.service.TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0)),
                    conflictingAppointmentId = AppointmentId.of("conflict-1")
                )
            )
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns conflicts

            // Mock isSlotAvailable for alternative suggestion logic
            every { optimizedConflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any()) } returns false

            // Mock alternative suggestion service to fail to trigger legacy path
            every { appointmentAlternativeSuggestionService.suggestAlternatives(command, any()) } throws RuntimeException("Service unavailable")

            // Mock legacy alternative suggestion dependencies - return available staff for staff alternatives
            val alternativeStaff = staff.copy(id = StaffId.of("alt-staff-1"), nickname = "Alternative Staff")
            every { staffRepository.findAvailableStaff(any(), any(), any(), any()) } returns listOf(alternativeStaff)

            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                block.invoke()
            }
            every { workflowMetrics.recordDetailedException(any(), any(), any(), any()) } just Runs
            every { workflowMetrics.recordAppointmentOutcome(any(), any(), any(), any(), any()) } just Runs

            // When & Then
            val exception = assertThrows<AppointmentSchedulingConflictException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Intervalul solicitat nu este disponibil", exception.message)
            assertNotNull(exception.alternatives)
            // Verify that legacy alternative generation was triggered
            verify { staffRepository.findAvailableStaff(any(), any(), any(), any()) }
        }

        @Test
        @DisplayName("Should handle date-based alternatives for next available days")
        fun shouldHandleDateBasedAlternativesForNextAvailableDays() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null

            // Mock conflict detection to return conflicts
            val conflicts = listOf(
                DetailedScheduleConflict(
                    type = ScheduleConflictType.BLOCK_TIME,
                    message = "Outside working hours",
                    timeSlot = ro.animaliaprogramari.animalia.domain.service.TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0))
                )
            )
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns conflicts

            // Mock isSlotAvailable for alternative suggestion logic
            every { optimizedConflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any()) } returns false

            // Mock alternative suggestion service to fail to trigger legacy path
            every { appointmentAlternativeSuggestionService.suggestAlternatives(command, any()) } throws RuntimeException("Service unavailable")

            // Mock legacy alternative suggestion dependencies
            every { staffRepository.findAvailableStaff(any(), any(), any(), any()) } returns emptyList()

            every { workflowMetrics.executeWorkflow<Appointment>(any(), any(), any()) } answers {
                val block = arg<() -> Appointment>(2)
                block.invoke()
            }
            every { workflowMetrics.recordDetailedException(any(), any(), any(), any()) } just Runs
            every { workflowMetrics.recordAppointmentOutcome(any(), any(), any(), any(), any()) } just Runs

            // When & Then
            val exception = assertThrows<AppointmentSchedulingConflictException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Intervalul solicitat nu este disponibil", exception.message)
            assertNotNull(exception.alternatives)
            // Verify that the exception was properly thrown (conflicts may be empty in this test scenario)
            assertNotNull(exception.conflicts)
        }

        @Test
        @DisplayName("Should handle staff alternative suggestions when original staff is unavailable")
        fun shouldHandleStaffAlternativeSuggestionsWhenOriginalStaffIsUnavailable() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null

            // Mock conflict detection to return staff-related conflicts
            val conflicts = listOf(
                DetailedScheduleConflict(
                    type = ScheduleConflictType.APPOINTMENT,
                    message = "Staff has another appointment",
                    timeSlot = ro.animaliaprogramari.animalia.domain.service.TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0)),
                    conflictingAppointmentId = AppointmentId.of("conflict-1")
                )
            )
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns conflicts

            // Mock isSlotAvailable for alternative suggestion logic
            every { optimizedConflictService.isSlotAvailable(any(), any(), any(), any(), any(), any(), any(), any()) } returns false

            // Mock alternative suggestion service to fail to trigger legacy path
            every { appointmentAlternativeSuggestionService.suggestAlternatives(command, any()) } throws RuntimeException("Service unavailable")

            // Mock multiple available staff for staff alternatives
            val alternativeStaff1 = staff.copy(id = StaffId.of("alt-staff-1"), nickname = "Alternative Staff 1")
            val alternativeStaff2 = staff.copy(id = StaffId.of("alt-staff-2"), nickname = "Alternative Staff 2")
            every { staffRepository.findAvailableStaff(any(), any(), any(), any()) } returns listOf(alternativeStaff1, alternativeStaff2)

            every { workflowMetrics.executeWorkflow<Appointment>(any(), any(), any()) } answers {
                val block = arg<() -> Appointment>(2)
                block.invoke()
            }
            every { workflowMetrics.recordDetailedException(any(), any(), any(), any()) } just Runs
            every { workflowMetrics.recordAppointmentOutcome(any(), any(), any(), any(), any()) } just Runs

            // When & Then
            val exception = assertThrows<AppointmentSchedulingConflictException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Intervalul solicitat nu este disponibil", exception.message)
            assertNotNull(exception.alternatives)
            // Verify that staff alternatives were considered
            verify { staffRepository.findAvailableStaff(
                command.salonId,
                command.appointmentDate,
                any(),
                listOf(command.staffId)
            ) }
        }
    }

    @Nested
    @DisplayName("PermissionValidationAndAuthorizationTests")
    inner class PermissionValidationAndAuthorizationTests {

        @Test
        @DisplayName("Should handle getSalonAppointments with valid staff access")
        fun shouldHandleGetSalonAppointmentsWithValidStaffAccess() {
            // Given
            val query = GetSalonAppointmentsQuery(
                salonId = salonId,
                requesterId = userId,
                date = LocalDate.now(),
                startDate = null,
                endDate = null,
                status = null,
                clientId = null,
                staffId = null
            )

            val appointments = listOf(createTestAppointment())

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { appointmentRepository.findBySalonIdWithFilters(
                salonId = salonId,
                date = query.date,
                startDate = query.startDate,
                endDate = query.endDate,
                status = query.status,
                clientId = query.clientId,
                staffId = query.staffId
            ) } returns appointments

            // When
            val result = useCase.getSalonAppointments(query)

            // Then
            assertNotNull(result)
            assertEquals(1, result.size)
            assertEquals(appointments[0].id, result[0].id)
            verify { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify { appointmentRepository.findBySalonIdWithFilters(any(), any(), any(), any(), any(), any(), any()) }
        }

        @Test
        @DisplayName("Should throw UnauthorizedException when staff not found for getSalonAppointments")
        fun shouldThrowUnauthorizedExceptionWhenStaffNotFoundForGetSalonAppointments() {
            // Given
            val query = GetSalonAppointmentsQuery(
                salonId = salonId,
                requesterId = userId,
                date = LocalDate.now(),
                startDate = null,
                endDate = null,
                status = null,
                clientId = null,
                staffId = null
            )

            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns null

            // When & Then
            val exception = assertThrows<UnauthorizedException> {
                useCase.getSalonAppointments(query)
            }

            assertEquals("User does not have access to this salon", exception.message)
            verify { staffRepository.findByUserIdAndSalonId(userId, salonId) }
        }

        @Test
        @DisplayName("Should throw UnauthorizedException when staff is inactive for getSalonAppointments")
        fun shouldThrowUnauthorizedExceptionWhenStaffIsInactiveForGetSalonAppointments() {
            // Given
            val query = GetSalonAppointmentsQuery(
                salonId = salonId,
                requesterId = userId,
                date = LocalDate.now(),
                startDate = null,
                endDate = null,
                status = null,
                clientId = null,
                staffId = null
            )

            val inactiveStaff = staff.copy(isActive = false)
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns inactiveStaff

            // When & Then
            val exception = assertThrows<UnauthorizedException> {
                useCase.getSalonAppointments(query)
            }

            assertEquals("User does not have access to this salon", exception.message)
            verify { staffRepository.findByUserIdAndSalonId(userId, salonId) }
        }

        @Test
        @DisplayName("Should handle updateAppointment permission validation for chief groomer")
        fun shouldHandleUpdateAppointmentPermissionValidationForChiefGroomer() {
            // Given
            val appointment = createTestAppointment()
            val command = UpdateAppointmentCommand(
                appointmentId = appointment.id,
                salonId = salonId,
                updaterUserId = userId,
                notes = "Updated notes"
            )

            val chiefGroomerStaff = staff.copy(role = StaffRole.CHIEF_GROOMER)

            every { appointmentRepository.findById(appointment.id) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns chiefGroomerStaff

            // When & Then
            val exception = assertThrows<NotImplementedError> {
                useCase.updateAppointment(command)
            }

            assertEquals("Update appointment functionality not yet implemented", exception.message)
            verify { appointmentRepository.findById(appointment.id) }
            verify { staffRepository.findByUserIdAndSalonId(userId, salonId) }
        }

        @Test
        @DisplayName("Should throw UnauthorizedException when staff cannot modify appointment")
        fun shouldThrowUnauthorizedExceptionWhenStaffCannotModifyAppointment() {
            // Given
            val appointment = createTestAppointment()
            val command = UpdateAppointmentCommand(
                appointmentId = appointment.id,
                salonId = salonId,
                updaterUserId = userId,
                notes = "Updated notes"
            )

            val unauthorizedStaff = staff.copy(
                id = StaffId.of("different-staff"),
                role = StaffRole.GROOMER,
                permissions = StaffPermissions(
                    clientDataAccess = ClientDataAccess.NONE,
                    canManageAppointments = false,
                    canManageServices = false,
                    canViewReports = false,
                    canManageSchedule = false
                )
            )

            every { appointmentRepository.findById(appointment.id) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns unauthorizedStaff

            // When & Then
            val exception = assertThrows<UnauthorizedException> {
                useCase.updateAppointment(command)
            }

            assertEquals("User does not have permission to modify this appointment", exception.message)
            verify { appointmentRepository.findById(appointment.id) }
            verify { staffRepository.findByUserIdAndSalonId(userId, salonId) }
        }

        @Test
        @DisplayName("Should allow staff with canManageAppointments permission to modify appointment")
        fun shouldAllowStaffWithCanManageAppointmentsPermissionToModifyAppointment() {
            // Given
            val appointment = createTestAppointment()
            val command = UpdateAppointmentCommand(
                appointmentId = appointment.id,
                salonId = salonId,
                updaterUserId = userId,
                notes = "Updated notes"
            )

            val authorizedStaff = staff.copy(
                id = StaffId.of("different-staff"),
                role = StaffRole.GROOMER,
                permissions = StaffPermissions(
                    clientDataAccess = ClientDataAccess.NONE,
                    canManageAppointments = true,
                    canManageServices = false,
                    canViewReports = false,
                    canManageSchedule = true
                )
            )

            every { appointmentRepository.findById(appointment.id) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns authorizedStaff

            // When & Then
            val exception = assertThrows<NotImplementedError> {
                useCase.updateAppointment(command)
            }

            assertEquals("Update appointment functionality not yet implemented", exception.message)
            verify { appointmentRepository.findById(appointment.id) }
            verify { staffRepository.findByUserIdAndSalonId(userId, salonId) }
        }
    }

    @Nested
    @DisplayName("AppointmentLifecycleManagementTests")
    inner class AppointmentLifecycleManagementTests {

        @Test
        @DisplayName("Should handle cancelAppointment with valid permissions")
        fun shouldHandleCancelAppointmentWithValidPermissions() {
            // Given
            val appointment = createTestAppointment()
            val command = CancelAppointmentCommand(
                appointmentId = appointment.id,
                salonId = salonId,
                cancellerUserId = userId,
                reason = "Client requested cancellation"
            )

            val cancelledAppointment = appointment.copy(
                status = AppointmentStatus.CANCELLED,
                notes = "Client requested cancellation"
            )

            every { appointmentRepository.findById(appointment.id) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { appointmentRepository.save(any()) } returns cancelledAppointment
            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                block.invoke()
            }

            // When
            val result = useCase.cancelAppointment(command)

            // Then
            assertNotNull(result)
            assertEquals(AppointmentStatus.CANCELLED, result.status)
            verify { appointmentRepository.findById(appointment.id) }
            verify { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify { appointmentRepository.save(any()) }
            verify { workflowMetrics.executeWorkflow(
                workflowName = "cancel_appointment",
                tags = mapOf(
                    "salon_id" to command.salonId.value,
                    "appointment_id" to command.appointmentId.value
                ),
                operation = any()
            ) }
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when appointment not found for cancellation")
        fun shouldThrowEntityNotFoundExceptionWhenAppointmentNotFoundForCancellation() {
            // Given
            val appointmentId = AppointmentId.of("non-existent")
            val command = CancelAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                cancellerUserId = userId,
                reason = "Client requested cancellation"
            )

            every { appointmentRepository.findById(appointmentId) } returns null
            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                block.invoke()
            }

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.cancelAppointment(command)
            }

            assertEquals("Appointment not found: non-existent", exception.message)
            verify { appointmentRepository.findById(appointmentId) }
        }

        @Test
        @DisplayName("Should throw BusinessRuleViolationException when appointment belongs to different salon for cancellation")
        fun shouldThrowBusinessRuleViolationExceptionWhenAppointmentBelongsToDifferentSalonForCancellation() {
            // Given
            val differentSalonId = SalonId.of("different-salon")
            val appointment = createTestAppointment().copy(salonId = differentSalonId)
            val command = CancelAppointmentCommand(
                appointmentId = appointment.id,
                salonId = salonId,
                cancellerUserId = userId,
                reason = "Client requested cancellation"
            )

            every { appointmentRepository.findById(appointment.id) } returns appointment
            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                block.invoke()
            }

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.cancelAppointment(command)
            }

            assertEquals("Appointment does not belong to the specified salon", exception.message)
            verify { appointmentRepository.findById(appointment.id) }
        }

        @Test
        @DisplayName("Should handle completeAppointment with valid permissions")
        fun shouldHandleCompleteAppointmentWithValidPermissions() {
            // Given
            val appointment = createTestAppointment()
            val command = CompleteAppointmentCommand(
                appointmentId = appointment.id,
                salonId = salonId,
                completerUserId = userId,
                notes = "Service completed successfully"
            )

            val completedAppointment = appointment.copy(
                status = AppointmentStatus.COMPLETED,
                notes = "Service completed successfully"
            )

            every { appointmentRepository.findById(appointment.id) } returns appointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { appointmentRepository.save(any()) } returns completedAppointment
            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                block.invoke()
            }

            // When
            val result = useCase.completeAppointment(command)

            // Then
            assertNotNull(result)
            assertEquals(AppointmentStatus.COMPLETED, result.status)
            verify { appointmentRepository.findById(appointment.id) }
            verify { staffRepository.findByUserIdAndSalonId(userId, salonId) }
            verify { appointmentRepository.save(any()) }
            verify { workflowMetrics.executeWorkflow(
                workflowName = "complete_appointment",
                tags = mapOf(
                    "salon_id" to command.salonId.value,
                    "appointment_id" to command.appointmentId.value
                ),
                operation = any()
            ) }
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when appointment not found for completion")
        fun shouldThrowEntityNotFoundExceptionWhenAppointmentNotFoundForCompletion() {
            // Given
            val appointmentId = AppointmentId.of("non-existent")
            val command = CompleteAppointmentCommand(
                appointmentId = appointmentId,
                salonId = salonId,
                completerUserId = userId,
                notes = "Service completed successfully"
            )

            every { appointmentRepository.findById(appointmentId) } returns null
            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                block.invoke()
            }

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.completeAppointment(command)
            }

            assertEquals("Appointment not found: non-existent", exception.message)
            verify { appointmentRepository.findById(appointmentId) }
        }

        @Test
        @DisplayName("Should throw BusinessRuleViolationException when appointment belongs to different salon for completion")
        fun shouldThrowBusinessRuleViolationExceptionWhenAppointmentBelongsToDifferentSalonForCompletion() {
            // Given
            val differentSalonId = SalonId.of("different-salon")
            val appointment = createTestAppointment().copy(salonId = differentSalonId)
            val command = CompleteAppointmentCommand(
                appointmentId = appointment.id,
                salonId = salonId,
                completerUserId = userId,
                notes = "Service completed successfully"
            )

            every { appointmentRepository.findById(appointment.id) } returns appointment
            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                block.invoke()
            }

            // When & Then
            val exception = assertThrows<BusinessRuleViolationException> {
                useCase.completeAppointment(command)
            }

            assertEquals("Appointment does not belong to the specified salon", exception.message)
            verify { appointmentRepository.findById(appointment.id) }
        }
    }

    @Nested
    @DisplayName("ComplexValidationAndEntityLoadingTests")
    inner class ComplexValidationAndEntityLoadingTests {

        @Test
        @DisplayName("Should throw EntityNotFoundException when salon not found")
        fun shouldThrowEntityNotFoundExceptionWhenSalonNotFound() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Salon not found: ${salonId.value}", exception.message)
            verify { salonRepository.findById(salonId) }
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when staff not found")
        @Disabled
        fun shouldThrowEntityNotFoundExceptionWhenStaffNotFound() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Staff not found: ${staffId.value}", exception.message)
            verify { salonRepository.findById(salonId) }
            verify { staffRepository.findById(staffId) }
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when client not found")
        fun shouldThrowEntityNotFoundExceptionWhenClientNotFound() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { clientRepository.findById(clientId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Client not found: ${clientId.value}", exception.message)
            verify { salonRepository.findById(salonId) }
            verify { staffRepository.findById(staffId) }
            verify { clientRepository.findById(clientId) }
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when pet not found")
        fun shouldThrowEntityNotFoundExceptionWhenPetNotFound() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Pet not found: ${petId.value}", exception.message)
            verify { salonRepository.findById(salonId) }
            verify { staffRepository.findById(staffId) }
            verify { clientRepository.findById(clientId) }
            verify { petRepository.findById(petId) }
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException when service not found")
        fun shouldThrowEntityNotFoundExceptionWhenServiceNotFound() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns null

            // When & Then
            val exception = assertThrows<EntityNotFoundException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Service not found: ${serviceId.value}", exception.message)
            verify { salonRepository.findById(salonId) }
            verify { staffRepository.findById(staffId) }
            verify { clientRepository.findById(clientId) }
            verify { petRepository.findById(petId) }
            verify { salonServiceRepository.findById(serviceId) }
        }

        @Test
        @DisplayName("Should handle new client creation when isNewClient is true")
        @Disabled
        fun shouldHandleNewClientCreationWhenIsNewClientIsTrue() {
            // Given
            val command = createTestScheduleCommand().copy(
                isNewClient = true,
                clientName = "New Client Name",
                clientPhone = PhoneNumber.of("+***********")
            )

            val newClient = client.copy(
                id = ClientId.of("new-client-id"),
                name = "New Client Name",
                phone = PhoneNumber.of("+***********")
            )

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(any()) } returns null
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { clientRepository.save(any()) } returns newClient
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment()

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            verify { clientRepository.save(any()) }
            verify { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) }
        }

        @Test
        @DisplayName("Should handle new pet creation when isNewPet is true")
        fun shouldHandleNewPetCreationWhenIsNewPetIsTrue() {
            // Given
            val command = createTestScheduleCommand().copy(
                isNewPet = true,
                petName = "New Pet Name",
                petSpecies = "dog",
                petBreed = "Golden Retriever",
                petSize = "large"
            )

            val newPet = pet.copy(
                id = PetId.of("new-pet-id"),
                name = "New Pet Name",
                species = "dog",
                breed = "Golden Retriever",
                size = "large"
            )

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(any()) } returns null
            every { petRepository.save(any()) } returns newPet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment()

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            verify { petRepository.save(any()) }
        }
    }

    @Nested
    @DisplayName("AsyncOperationsAndComplexBusinessLogicTests")
    inner class AsyncOperationsAndComplexBusinessLogicTests {

        @Test
        @DisplayName("Should handle async validation operations with multiple concurrent calls")
        fun shouldHandleAsyncValidationOperationsWithMultipleConcurrentCalls() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService

            // Mock async operations that are called in validateAppointmentBusinessRules
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()

            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null
            every { appointmentRepository.save(any()) } returns createTestAppointment()

            // When
            val result = useCase.scheduleAppointment(command)

            // Then
            assertNotNull(result)
            // Verify that all async operations were called
            verify { workingHoursRepository.findBySalonId(salonId) }
            verify { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) }
            verify { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) }
            verify { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) }
        }

        @Test
        @DisplayName("Should handle complex conflict detection with multiple conflict types")
        fun shouldHandleComplexConflictDetectionWithMultipleConflictTypes() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null

            // Mock multiple types of conflicts
            val conflicts = listOf(
                DetailedScheduleConflict(
                    type = ScheduleConflictType.APPOINTMENT,
                    message = "Existing appointment conflict",
                    timeSlot = ro.animaliaprogramari.animalia.domain.service.TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0)),
                    conflictingAppointmentId = AppointmentId.of("conflict-1")
                ),
                DetailedScheduleConflict(
                    type = ScheduleConflictType.STAFF_UNAVAILABLE,
                    message = "Outside working hours",
                    timeSlot = ro.animaliaprogramari.animalia.domain.service.TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0))
                ),
                DetailedScheduleConflict(
                    type = ScheduleConflictType.BLOCK_TIME,
                    message = "Block time conflict",
                    timeSlot = ro.animaliaprogramari.animalia.domain.service.TimeSlot.of(LocalTime.of(10, 0), LocalTime.of(11, 0))
                )
            )
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns conflicts
            every { appointmentAlternativeSuggestionService.suggestAlternatives(command, any()) } returns emptyList()
            every { workflowMetrics.executeWorkflow<Appointment>(any(), any(), any()) } answers {
                val block = arg<() -> Appointment>(2)
                block.invoke()
            }
            every { workflowMetrics.recordDetailedException(any(), any(), any(), any()) } just Runs
            every { workflowMetrics.recordAppointmentOutcome(any(), any(), any(), any(), any()) } just Runs

            // When & Then
            val exception = assertThrows<AppointmentSchedulingConflictException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Intervalul solicitat nu este disponibil", exception.message)
            assertTrue(exception.conflicts.isNotEmpty())
            // The implementation may consolidate conflicts, so we just verify that conflicts are detected
            assertNotNull(exception.conflicts)
        }

        @Test
        @DisplayName("Should handle appointment rescheduling validation with complex conflict detection")
        fun shouldHandleAppointmentReschedulingValidationWithComplexConflictDetection() {
            // Given
            val originalAppointment = createTestAppointment()
            val command = RescheduleAppointmentCommand(
                appointmentId = originalAppointment.id,
                salonId = salonId,
                newDate = LocalDate.now().plusDays(1),
                newStartTime = LocalTime.of(14, 0),
                newEndTime = LocalTime.of(15, 0),
                newStaffId = staffId,
                reschedulerUserId = userId,
                reason = "Client requested reschedule"
            )

            every { appointmentRepository.findById(originalAppointment.id) } returns originalAppointment
            every { staffRepository.findByUserIdAndSalonId(userId, salonId) } returns staff
            every { staffRepository.findById(staffId) } returns staff
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { appointmentRepository.save(any()) } returns originalAppointment.copy(
                appointmentDate = command.newDate ?: LocalDate.now().plusDays(1),
                startTime = command.newStartTime,
                endTime = command.newEndTime
            )

            // When
            val result = useCase.rescheduleAppointment(command)

            // Then
            assertNotNull(result)
            verify { appointmentRepository.findById(originalAppointment.id) }
            verify { staffRepository.findByUserIdAndSalonId(userId, salonId) }
        }

        @Disabled
        @Test
        @DisplayName("Should handle general exception with detailed metrics recording")
        fun shouldHandleGeneralExceptionWithDetailedMetricsRecording() {
            // Given
            val command = createTestScheduleCommand()

            every { salonRepository.findById(salonId) } returns salon
            every { staffRepository.findById(staffId) } returns staff
            every { staffRepository.findByUserIdAndSalonId(any(), any()) } returns staff
            every { clientRepository.findById(clientId) } returns client
            every { petRepository.findById(petId) } returns pet
            every { salonServiceRepository.findById(serviceId) } returns salonService
            every { workingHoursRepository.findBySalonId(salonId) } returns workingHours
            every { staffWorkingHoursRepository.findByStaffIdAndSalonId(staffId, salonId) } returns staffWorkingHours
            every { appointmentRepository.findBySalonIdAndStaffIdAndDate(any(), any(), any()) } returns emptyList()
            every { blockTimeRepository.findOverlappingBlocks(any(), any(), any(), any()) } returns emptyList()
            every { optimizedConflictService.detectConflicts(any(), any(), any(), any(), any(), any(), any(), any()) } returns emptyList()
            every { clientRepository.findByPhoneAndSalonId(PhoneNumber.of("+***********"), salonId) } returns null
            every { userRepository.findById(any()) } returns null

            // Mock appointment save to throw a general exception
            every { appointmentRepository.save(any()) } throws RuntimeException("Database connection failed")

            every { workflowMetrics.executeWorkflow(any(), any(), any<() -> Appointment>()) } answers {
                val block = thirdArg<() -> Appointment>()
                block.invoke()
            }
            every { workflowMetrics.recordDetailedException(any(), any(), any(), any()) } just Runs
            every { workflowMetrics.recordAppointmentWorkflowMetrics(any(), any(), any(), any(), any(), any()) } just Runs
            every { workflowMetrics.recordAppointmentOutcome(any(), any(), any(), any(), any()) } just Runs

            // When & Then
            val exception = assertThrows<RuntimeException> {
                useCase.scheduleAppointment(command)
            }

            assertEquals("Failed to save appointment: Database connection failed", exception.message)
            verify { workflowMetrics.recordDetailedException(
                exception = any<RuntimeException>(),
                context = "appointment_scheduling_unexpected",
                salonId = command.salonId.value,
                additionalTags = mapOf(
                    "staff_id" to command.staffId.value,
                    "appointment_date" to command.appointmentDate.toString(),
                    "time_slot" to "${command.startTime}-${command.endTime}",
                    "is_new_client" to command.isNewClient.toString(),
                    "is_new_pet" to command.isNewPet.toString()
                )
            ) }
            verify { workflowMetrics.recordAppointmentWorkflowMetrics(
                operation = "schedule",
                salonId = command.salonId.value,
                staffId = command.staffId.value,
                success = false,
                durationMs = any(),
                conflictsDetected = any(),
                alternativesGenerated = any(),
                errorType = "BusinessRuleViolationException"
            ) }
            verify { workflowMetrics.recordAppointmentOutcome(
                outcome = "scheduling_internal_error",
                salonId = command.salonId.value,
                staffId = command.staffId.value,
                appointmentDate = command.appointmentDate,
                additionalTags = mapOf(
                    "error_type" to "RuntimeException",
                    "error_message" to "Database connection failed"
                )
            ) }
        }
    }

    // Helper methods for creating test data
    private fun createTestSalon(): Salon {
        return Salon(
            id = salonId,
            name = "Test Salon",
            address = "Test Address",
            city = "Test City",
            phone = PhoneNumber.of("+***********"),
            email = Email.of("<EMAIL>"),
            ownerId = userId,
            description = "Test salon description",
            isActive = true,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }

    private fun createTestStaff(): Staff {
        return Staff.createChiefGroomer(
            userId = userId,
            salonId = salonId
        ).copy(
            id = staffId,
            nickname = "Test Staff",
            isActive = true,
            permissions = StaffPermissions(
                canManageAppointments = true,
                canManageServices = true,
                canViewReports = true,
                clientDataAccess = ClientDataAccess.FULL,
                canManageSchedule = true
            )
        )
    }

    private fun createTestClient(): Client {
        return Client(
            id = clientId,
            salonId = salonId,
            name = "Test Client",
            phone = PhoneNumber.of("+***********"),
            email = Email.of("<EMAIL>"),
            address = "Test Address",
            notes = null,
            isActive = true,
            userIds = emptySet(),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }

    private fun createTestPet(): Pet {
        return Pet(
            id = petId,
            clientId = clientId,
            name = "Test Pet",
            breed = "Labrador",
            birthDate = LocalDate.now().minusYears(3),
            age = 3,
            weight = null,
            color = null,
            gender = null,
            notes = null,
            medicalConditions = null,
            isActive = true,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now(),
            size = "Large",
            species = "Dog",
            photoUrl = null
        )
    }

    private fun createTestSalonService(): SalonService {
        return SalonService(
            id = serviceId,
            salonId = salonId,
            name = "Test Service",
            description = "Test service description",
            myDuration = MyDuration.ofMinutes(60),
            basePrice = Money.of(100.0),
            category = ServiceCategory.GROOMING,
            isActive = true,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }

    private fun createTestWorkingHours(): WorkingHoursSettings {
        return WorkingHoursSettings(
            salonId = salonId,
            weeklySchedule = mapOf(
                DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.SATURDAY to DaySchedule.workingDay(LocalTime.of(10, 0), LocalTime.of(15, 0)),
                DayOfWeek.SUNDAY to DaySchedule.dayOff()
            ),
            holidays = emptyList(),
            customClosures = emptyList()
        )
    }

    private fun createTestStaffWorkingHours(): StaffWorkingHoursSettings {
        return StaffWorkingHoursSettings(
            staffId = staffId,
            salonId = salonId,
            weeklySchedule = mapOf(
                DayOfWeek.MONDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.TUESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.WEDNESDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.THURSDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.FRIDAY to DaySchedule.workingDay(LocalTime.of(9, 0), LocalTime.of(17, 0)),
                DayOfWeek.SATURDAY to DaySchedule.workingDay(LocalTime.of(10, 0), LocalTime.of(15, 0)),
                DayOfWeek.SUNDAY to DaySchedule.dayOff()
            ),
            holidays = emptyList(),
            customClosures = emptyList(),
            inheritFromBusiness = false
        )
    }

    private fun createTestScheduleCommand(): ScheduleAppointmentCommand {
        return ScheduleAppointmentCommand(
            salonId = salonId,
            staffId = staffId,
            clientId = clientId,
            petId = petId,
            appointmentDate = LocalDate.now().plusDays(1),
            startTime = LocalTime.of(10, 0),
            endTime = LocalTime.of(11, 0),
            serviceIds = listOf(serviceId),
            notes = "Test appointment",
            clientName = "Test Client",
            clientPhone = PhoneNumber.of("+***********"),
            isNewClient = false,
            petName = "Test Pet",
            petSpecies = "Dog",
            isNewPet = false,
            petBreed = "Labrador",
            petSize = "Large"
        )
    }

    private fun createTestAppointment(): Appointment {
        return Appointment(
            id = appointmentId,
            salonId = salonId,
            clientId = clientId,
            petId = petId,
            staffId = staffId,
            serviceIds = listOf(serviceId),
            appointmentDate = LocalDate.now().plusDays(1),
            startTime = LocalTime.of(10, 0),
            endTime = LocalTime.of(11, 0),
            totalMyDuration = MyDuration.ofMinutes(60),
            status = AppointmentStatus.SCHEDULED,
            notes = "Test appointment",
            totalPrice = Money.of(100.0),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }
}
