//package ro.animaliaprogramari.animalia.application.service
//
//import io.mockk.every
//import io.mockk.mockk
//import org.junit.jupiter.api.Assertions.*
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.Test
//import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsLogEntity
//import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsStatus
//import ro.animaliaprogramari.animalia.adapter.outbound.persistence.repository.SmsLogRepository
//import ro.animaliaprogramari.animalia.application.port.outbound.SalonSubscriptionRepository
//import ro.animaliaprogramari.animalia.domain.model.*
//import ro.animaliaprogramari.animalia.domain.service.SmsLengthCalculator
//import java.time.LocalDateTime
//
//class SmsUsageStatisticsServiceTest {
//
//    private val smsLogRepository = mockk<SmsLogRepository>()
//    private val smsLengthCalculator = mockk<SmsLengthCalculator>()
//    private val salonSubscriptionRepository = mockk<SalonSubscriptionRepository>()
//
//    private lateinit var service: SmsUsageStatisticsService
//    private val testSalonId = SalonId.of("test-salon-id")
//
//    @BeforeEach
//    fun setUp() {
//        service = SmsUsageStatisticsService(
//            smsLogRepository,
//            smsLengthCalculator,
//            salonSubscriptionRepository
//        )
//    }
//
//    @Test
//    fun `should calculate statistics with no SMS over quota for FREE tier`() {
//        // Given
//        val subscription = createSubscription(SubscriptionTier.FREE) // 0 SMS quota
//        every { salonSubscriptionRepository.findBySalonId(testSalonId) } returns subscription
//        every { smsLogRepository.findBySalonIdAndStatusAndSentAtBetween(any(), any(), any(), any()) } returns emptyList()
//        every { smsLogRepository.findBySalonIdAndStatus(any(), any()) } returns emptyList()
//
//        // When
//        val result = service.getUsageStatistics(testSalonId)
//
//        // Then
//        assertEquals("test-salon-id", result.salonId)
//        assertEquals(0L, result.currentMonthSms)
//        assertEquals(0L, result.currentMonthBillableSms)
//        assertEquals(0, result.smsQuota)
//        assertEquals("0.00", result.currentMonthCost)
//    }
//
//    @Test
//    fun `should calculate statistics with SMS under quota for FREELANCER tier`() {
//        // Given
//        val subscription = createSubscription(SubscriptionTier.FREELANCER) // 100 SMS quota
//        val smsLogs = listOf(createSmsLog("Short message")) // 1 SMS unit
//
//        every { salonSubscriptionRepository.findBySalonId(testSalonId) } returns subscription
//        every { smsLogRepository.findBySalonIdAndStatusAndSentAtBetween(any(), any(), any(), any()) } returns smsLogs
//        every { smsLogRepository.findBySalonIdAndStatus(any(), any()) } returns smsLogs
//        every { smsLengthCalculator.calculateSmsUnits("Short message") } returns 1
//
//        // When
//        val result = service.getUsageStatistics(testSalonId)
//
//        // Then
//        assertEquals(1L, result.currentMonthSms)
//        assertEquals(0L, result.currentMonthBillableSms) // Under quota, so 0 billable
//        assertEquals(100, result.smsQuota)
//        assertEquals("0.00", result.currentMonthCost) // No cost for SMS under quota
//    }
//
//    @Test
//    fun `should calculate statistics with SMS over quota for FREELANCER tier`() {
//        // Given
//        val subscription = createSubscription(SubscriptionTier.FREELANCER) // 100 SMS quota
//        val smsLogs = (1..150).map { createSmsLog("Message $it") } // 150 SMS units
//
//        every { salonSubscriptionRepository.findBySalonId(testSalonId) } returns subscription
//        every { smsLogRepository.findBySalonIdAndStatusAndSentAtBetween(any(), any(), any(), any()) } returns smsLogs
//        every { smsLogRepository.findBySalonIdAndStatus(any(), any()) } returns smsLogs
//        every { smsLengthCalculator.calculateSmsUnits(any()) } returns 1
//
//        // When
//        val result = service.getUsageStatistics(testSalonId)
//
//        // Then
//        assertEquals(150L, result.currentMonthSms)
//        assertEquals(50L, result.currentMonthBillableSms) // 150 - 100 quota = 50 billable
//        assertEquals(100, result.smsQuota)
//        assertEquals("2.50", result.currentMonthCost) // 50 * 0.05 = 2.50 EUR
//    }
//
//    @Test
//    fun `should calculate statistics with long SMS messages`() {
//        // Given
//        val subscription = createSubscription(SubscriptionTier.FREELANCER) // 100 SMS quota
//        val longMessage = "A".repeat(300) // Long message = 2 SMS units
//        val smsLogs = (1..60).map { createSmsLog(longMessage) } // 60 messages * 2 units = 120 SMS units
//
//        every { salonSubscriptionRepository.findBySalonId(testSalonId) } returns subscription
//        every { smsLogRepository.findBySalonIdAndStatusAndSentAtBetween(any(), any(), any(), any()) } returns smsLogs
//        every { smsLogRepository.findBySalonIdAndStatus(any(), any()) } returns smsLogs
//        every { smsLengthCalculator.calculateSmsUnits(longMessage) } returns 2
//
//        // When
//        val result = service.getUsageStatistics(testSalonId)
//
//        // Then
//        assertEquals(120L, result.currentMonthSms) // 60 messages * 2 units
//        assertEquals(20L, result.currentMonthBillableSms) // 120 - 100 quota = 20 billable
//        assertEquals("1.00", result.currentMonthCost) // 20 * 0.05 = 1.00 EUR
//    }
//
//    @Test
//    fun `should handle salon without subscription`() {
//        // Given
//        val smsLogs = listOf(createSmsLog("Test message"))
//
//        every { salonSubscriptionRepository.findBySalonId(testSalonId) } returns null
//        every { smsLogRepository.findBySalonIdAndStatusAndSentAtBetween(any(), any(), any(), any()) } returns smsLogs
//        every { smsLogRepository.findBySalonIdAndStatus(any(), any()) } returns smsLogs
//        every { smsLengthCalculator.calculateSmsUnits("Test message") } returns 1
//
//        // When
//        val result = service.getUsageStatistics(testSalonId)
//
//        // Then
//        assertEquals(1L, result.currentMonthSms)
//        assertEquals(1L, result.currentMonthBillableSms) // No quota, so all SMS are billable
//        assertEquals(0, result.smsQuota) // Default quota is 0
//        assertEquals("0.05", result.currentMonthCost) // 1 * 0.05 = 0.05 EUR
//    }
//
//    @Test
//    fun `should calculate monthly breakdown with quota consideration`() {
//        // Given
//        val subscription = createSubscription(SubscriptionTier.TEAM) // 200 SMS quota
//        val smsLogs = (1..250).map { createSmsLog("Message $it") } // 250 SMS units
//
//        every { salonSubscriptionRepository.findBySalonId(testSalonId) } returns subscription
//        every { smsLogRepository.findBySalonIdAndStatusAndSentAtBetween(any(), any(), any(), any()) } returns smsLogs
//        every { smsLengthCalculator.calculateSmsUnits(any()) } returns 1
//
//        // When
//        val result = service.getMonthlyBreakdown(testSalonId, 1)
//
//        // Then
//        assertEquals(1, result.size)
//        val monthlyUsage = result[0]
//        assertEquals(250L, monthlyUsage.smsCount)
//        assertEquals(50L, monthlyUsage.billableSmsCount) // 250 - 200 quota = 50 billable
//        assertEquals("2.50", monthlyUsage.cost) // 50 * 0.05 = 2.50 EUR
//    }
//
//    @Test
//    fun `should calculate different costs for different subscription tiers`() {
//        // Test data: 150 SMS units for each tier
//        val smsLogs = (1..150).map { createSmsLog("Message $it") }
//        every { smsLogRepository.findBySalonIdAndStatusAndSentAtBetween(any(), any(), any(), any()) } returns smsLogs
//        every { smsLogRepository.findBySalonIdAndStatus(any(), any()) } returns smsLogs
//        every { smsLengthCalculator.calculateSmsUnits(any()) } returns 1
//
//        // Test FREE tier (0 quota)
//        every { salonSubscriptionRepository.findBySalonId(testSalonId) } returns createSubscription(SubscriptionTier.FREE)
//        val freeResult = service.getUsageStatistics(testSalonId)
//        assertEquals(150L, freeResult.currentMonthBillableSms)
//        assertEquals("7.50", freeResult.currentMonthCost) // 150 * 0.05
//
//        // Test FREELANCER tier (100 quota)
//        every { salonSubscriptionRepository.findBySalonId(testSalonId) } returns createSubscription(SubscriptionTier.FREELANCER)
//        val freelancerResult = service.getUsageStatistics(testSalonId)
//        assertEquals(50L, freelancerResult.currentMonthBillableSms) // 150 - 100
//        assertEquals("2.50", freelancerResult.currentMonthCost) // 50 * 0.05
//
//        // Test TEAM tier (200 quota)
//        every { salonSubscriptionRepository.findBySalonId(testSalonId) } returns createSubscription(SubscriptionTier.TEAM)
//        val teamResult = service.getUsageStatistics(testSalonId)
//        assertEquals(0L, teamResult.currentMonthBillableSms) // 150 - 200 = 0 (max with 0)
//        assertEquals("0.00", teamResult.currentMonthCost) // 0 * 0.05
//    }
//
//    private fun createSubscription(tier: SubscriptionTier): SalonSubscription {
//        return SalonSubscription(
//            id = SubscriptionId.generate(),
//            userId = UserId.of("test-user"),
//            salonId = testSalonId,
//            tier = tier,
//            status = SubscriptionStatus.ACTIVE,
//            startDate = LocalDateTime.now().minusDays(30),
//            endDate = LocalDateTime.now().plusDays(30),
//            trialEndDate = null,
//            revenueCatCustomerId = null,
//            revenueCatEntitlementId = null
//        )
//    }
//
//    private fun createSmsLog(message: String): SmsLogEntity {
//        return SmsLogEntity(
//            id = "test-id-${System.nanoTime()}",
//            salonId = testSalonId.value,
//            recipientPhoneNumber = "+40123456789",
//            messageContent = message,
//            status = SmsStatus.SENT,
//            sentAt = LocalDateTime.now(),
//            createdAt = LocalDateTime.now(),
//            updatedAt = LocalDateTime.now()
//        )
//    }
//}
