package ro.animaliaprogramari.animalia.application.service

import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Nested
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import ro.animaliaprogramari.animalia.adapter.inbound.rest.dto.sms.*
import ro.animaliaprogramari.animalia.adapter.outbound.persistence.entity.SmsStatus
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.MessagingStrategyService
import ro.animaliaprogramari.animalia.domain.service.SmsQuotaService
import ro.animaliaprogramari.animalia.domain.service.SmsLengthCalculator
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class MassSmsServiceTest {

    // Mocked dependencies
    private val clientRepository = mockk<ClientRepository>()
    private val petRepository = mockk<PetRepository>()
    private val massSmsRepository = mockk<MassSmsRepository>()
    private val massSmsTemplateRepository = mockk<MassSmsTemplateRepository>()
    private val smsSender = mockk<MessagingStrategyService>()
    private val smsQuotaService = mockk<SmsQuotaService>()
    private val smsLengthCalculator = mockk<SmsLengthCalculator>()
    private val smsLogService = mockk<SmsLogService>()
    private val stripeSmsBillingService = mockk<StripeSmsBillingService>()

    // Service under test
    private lateinit var massSmsService: MassSmsService

    // Test data
    private val salonId = SalonId.of("test-salon-123")
    private val userId = UserId.of("test-user-456")
    private val testMessage = "Test SMS message"
    private val clientIds = listOf("client-1", "client-2", "client-3")

    private val testClient1 = Client(
        id = ClientId.of("client-1"),
        salonId = salonId,
        name = "John Doe",
        phone = PhoneNumber.of("+40712345678"),
        email = null,
        address = null,
        notes = null,
        isActive = true,
        userIds = emptySet(),
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    private val testClient2 = Client(
        id = ClientId.of("client-2"),
        salonId = salonId,
        name = "Jane Smith",
        phone = PhoneNumber.of("+40712345679"),
        email = null,
        address = null,
        notes = null,
        isActive = true,
        userIds = emptySet(),
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    private val testClient3 = Client(
        id = ClientId.of("client-3"),
        salonId = salonId,
        name = "Bob Johnson",
        phone = null, // No phone number
        email = null,
        address = null,
        notes = null,
        isActive = true,
        userIds = emptySet(),
        createdAt = LocalDateTime.now(),
        updatedAt = LocalDateTime.now()
    )

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        massSmsService = MassSmsService(
            clientRepository = clientRepository,
            petRepository = petRepository,
            massSmsRepository = massSmsRepository,
            massSmsTemplateRepository = massSmsTemplateRepository,
            messageService = smsSender,
            smsQuotaService = smsQuotaService,
            smsLengthCalculator = smsLengthCalculator,
            smsLogService = smsLogService,
            stripeSmsBillingService = stripeSmsBillingService
        )
    }

    @Nested
    inner class SendMassSmsTests {

        @Test
        fun `should send mass SMS successfully when all conditions are met`() {
            // Given
            val clients = listOf(testClient1, testClient2)
            every { clientRepository.findBySalonId(salonId, isActive = true) } returns clients
            every { smsLengthCalculator.calculateSmsUnits(testMessage) } returns 1
            every { smsQuotaService.canSendSms(salonId, 2) } returns true
            every { massSmsRepository.save(any()) } returnsArgument 0
            every { smsSender.sendMessage(any(), any(), any()) } returns return
            every { smsLogService.logSms(any(), any(), any(), any(), any(), any(), any(), any()) } returns mockk()
            every { smsQuotaService.recordMultipleSmsUnits(salonId, 2) } returns mockk()
            every { stripeSmsBillingService.reportSmsUsage(salonId, 2) } returns Unit

            // When
            val result = massSmsService.sendMassSms(
                salonId = salonId,
                clientIds = listOf("client-1", "client-2"),
                message = testMessage,
                userId = userId
            )

            // Then
            assertEquals("COMPLETED", result.status)
            assertEquals(2, result.totalRecipients)
            assertEquals(2, result.successCount)
            assertEquals(0, result.failureCount)
            assertEquals(0.10, result.actualCost) // 2 SMS * 0.05 cost per SMS
            assertTrue(result.failures.isEmpty())

            verify { smsSender.sendMessage("+40712345678", testMessage, any())}
            verify { smsSender.sendMessage("+40712345679", testMessage, any())}
            verify { smsQuotaService.recordMultipleSmsUnits(salonId, 2) }
            verify { stripeSmsBillingService.reportSmsUsage(salonId, 2) }
        }

        @Test
        fun `should handle clients without phone numbers`() {
            // Given
            val clients = listOf(testClient1, testClient3) // client3 has no phone
            every { clientRepository.findBySalonId(salonId, isActive = true) } returns clients
            every { smsLengthCalculator.calculateSmsUnits(testMessage) } returns 1
            every { smsQuotaService.canSendSms(salonId, 1) } returns true
            every { massSmsRepository.save(any()) } returnsArgument 0
            every { smsSender.sendMessage(any(), any(), any()) } returns return
            every { smsLogService.logSms(any(), any(), any(), any(), any(), any(), any(), any()) } returns mockk()
            every { smsQuotaService.recordMultipleSmsUnits(salonId, 1) } returns mockk()
            every { stripeSmsBillingService.reportSmsUsage(salonId, 1) } returns Unit

            // When
            val result = massSmsService.sendMassSms(
                salonId = salonId,
                clientIds = listOf("client-1", "client-3"),
                message = testMessage,
                userId = userId
            )

            // Then
            assertEquals("COMPLETED", result.status)
            assertEquals(1, result.totalRecipients) // Only client1 has phone
            assertEquals(1, result.successCount)
            assertEquals(0, result.failureCount)
            assertEquals(0.05, result.actualCost)

        }

        @Test
        fun `should return failure when no valid clients found`() {
            // Given
            every { clientRepository.findBySalonId(salonId, isActive = true) } returns listOf(testClient3) // No phone

            // When
            val result = massSmsService.sendMassSms(
                salonId = salonId,
                clientIds = listOf("client-3"),
                message = testMessage,
                userId = userId
            )

            // Then
            assertEquals("FAILED", result.status)
            assertEquals(0, result.totalRecipients)
            assertEquals(0, result.successCount)
            assertEquals(0, result.failureCount)
            assertEquals(0.0, result.actualCost)
            assertEquals(1, result.failures.size)
            assertEquals("NO_VALID_CLIENTS", result.failures.first().errorCode)

        }

        @Test
        fun `should return failure when SMS quota is insufficient`() {
            // Given
            val clients = listOf(testClient1, testClient2)
            every { clientRepository.findBySalonId(salonId, isActive = true) } returns clients
            every { smsLengthCalculator.calculateSmsUnits(testMessage) } returns 1
            every { smsQuotaService.canSendSms(salonId, 2) } returns false

            // When
            val result = massSmsService.sendMassSms(
                salonId = salonId,
                clientIds = listOf("client-1", "client-2"),
                message = testMessage,
                userId = userId
            )

            // Then
            assertEquals("FAILED", result.status)
            assertEquals(2, result.totalRecipients)
            assertEquals(0, result.successCount)
            assertEquals(2, result.failureCount)
            assertEquals(0.0, result.actualCost)
            assertEquals(1, result.failures.size)
            assertEquals("INSUFFICIENT_QUOTA", result.failures.first().errorCode)

        }

        @Test
        fun `should schedule SMS for future delivery`() {
            // Given
            val clients = listOf(testClient1)
            val scheduledAt = LocalDateTime.now().plusHours(2)
            every { clientRepository.findBySalonId(salonId, isActive = true) } returns clients
            every { smsLengthCalculator.calculateSmsUnits(testMessage) } returns 1
            every { smsQuotaService.canSendSms(salonId, 1) } returns true
            every { massSmsRepository.save(any()) } returnsArgument 0

            // When
            val result = massSmsService.sendMassSms(
                salonId = salonId,
                clientIds = listOf("client-1"),
                message = testMessage,
                scheduledAt = scheduledAt,
                userId = userId
            )

            // Then
            assertEquals("SCHEDULED", result.status)
            assertEquals(1, result.totalRecipients)
            assertEquals(0, result.successCount)
            assertEquals(0, result.failureCount)
            assertEquals(scheduledAt, result.scheduledAt)

        }
    }

    @Nested
    inner class PreviewMassSmsTests {

        @Test
        fun `should generate preview with cost breakdown`() {
            // Given
            val clients = listOf(testClient1, testClient2)
            val pets = listOf(
                Pet.create(
                    clientId = ClientId.of("client-1"),
                    name = "Buddy",
                    breed = "Golden Retriever",
                    species = "Dog"
                )
            )

            every { clientRepository.findBySalonId(salonId, isActive = true) } returns clients
            every { petRepository.findByClientIds(any()) } returns pets
            every { smsLengthCalculator.calculateSmsUnits(testMessage) } returns 1

            // When
            val result = massSmsService.previewMassSms(
                salonId = salonId,
                clientIds = listOf("client-1", "client-2"),
                message = testMessage
            )

            // Then
            assertEquals(2, result.totalRecipients)
            assertEquals(0.10, result.estimatedCost) // 2 SMS * 0.05
            assertEquals(1, result.smsSegments)
            assertEquals(testMessage.length, result.characterCount)
            assertEquals(2, result.recipients.size)

            val recipient1 = result.recipients.find { it.clientId == "client-1" }!!
            assertEquals("John Doe", recipient1.clientName)
            assertEquals("+40712345678", recipient1.phoneNumber)
            assertEquals(listOf("Buddy"), recipient1.petNames)
            assertTrue(recipient1.canReceiveSms)
        }

        @Test
        fun `should handle clients without phone numbers in preview`() {
            // Given
            val clients = listOf(testClient1, testClient3) // client3 has no phone
            every { clientRepository.findBySalonId(salonId, isActive = true) } returns clients
            every { petRepository.findByClientIds(any()) } returns emptyList()
            every { smsLengthCalculator.calculateSmsUnits(testMessage) } returns 1

            // When
            val result = massSmsService.previewMassSms(
                salonId = salonId,
                clientIds = listOf("client-1", "client-3"),
                message = testMessage
            )

            // Then
            assertEquals(1, result.totalRecipients) // Only client1 has phone
            assertEquals(0.05, result.estimatedCost)

            val validRecipient = result.recipients.find { it.clientId == "client-1" }!!
            assertTrue(validRecipient.canReceiveSms)
        }

        @Test
        fun `should calculate multi-segment SMS cost correctly`() {
            // Given
            val longMessage = "A".repeat(200) // Long message requiring multiple segments
            val clients = listOf(testClient1)
            every { clientRepository.findBySalonId(salonId, isActive = true) } returns clients
            every { petRepository.findByClientIds(any()) } returns emptyList()
            every { smsLengthCalculator.calculateSmsUnits(longMessage) } returns 2

            // When
            val result = massSmsService.previewMassSms(
                salonId = salonId,
                clientIds = listOf("client-1"),
                message = longMessage
            )

            // Then
            assertEquals(1, result.totalRecipients)
            assertEquals(0.10, result.estimatedCost) // 1 client * 2 segments * 0.05
            assertEquals(2, result.smsSegments)
            assertEquals(longMessage.length, result.characterCount)
        }
    }

    @Nested
    inner class TemplateManagementTests {

        @Test
        fun `should get mass SMS templates for salon`() {
            // Given
            val templates = listOf(
                MassSmsTemplate.create(
                    salonId = salonId,
                    name = "Promotion Template",
                    content = "Special offer at {SALON_NAME}!",
                    category = "promotion",
                    createdBy = userId
                ),
                MassSmsTemplate.createDefault(
                    name = "Default Template",
                    content = "Default message",
                    category = "announcement"
                )
            )
            every { massSmsTemplateRepository.findBySalonIdOrDefault(salonId) } returns templates

            // When
            val result = massSmsService.getMassSmsTemplates(salonId)

            // Then
            assertEquals(2, result.size)
            assertEquals("Promotion Template", result[0].name)
            assertEquals("promotion", result[0].category)
            assertFalse(result[0].isDefault)
            assertTrue(result[1].isDefault)
        }

        @Test
        fun `should save new mass SMS template`() {
            // Given
            val request = SaveMassSmsTemplateRequest(
                name = "New Template",
                content = "New template content",
                category = "custom"
            )
            every { massSmsTemplateRepository.save(any()) } returnsArgument 0

            // When
            val result = massSmsService.saveMassSmsTemplate(salonId, request, userId)

            // Then
            assertEquals("New Template", result.name)
            assertEquals("New template content", result.content)
            assertEquals("custom", result.category)
            assertFalse(result.isDefault)

            verify { massSmsTemplateRepository.save(any()) }
        }
    }

    @Nested
    inner class HistoryTests {

        @Test
        fun `should get mass SMS history with pagination`() {
            // Given
            val campaigns = listOf(
                MassSmsCampaign.create(
                    id = "campaign-1",
                    salonId = salonId,
                    message = "Test message 1",
                    createdBy = userId
                ).complete(successCount = 5, failureCount = 0, actualCost = 0.25),
                MassSmsCampaign.create(
                    id = "campaign-2",
                    salonId = salonId,
                    message = "Test message 2",
                    createdBy = userId
                ).complete(successCount = 3, failureCount = 2, actualCost = 0.15)
            )
            val page = PageImpl(campaigns, PageRequest.of(0, 10), 2)
            every { massSmsRepository.findBySalonIdOrderByCreatedAtDesc(salonId, any()) } returns page

            // When
            val result = massSmsService.getMassSmsHistory(salonId, 0, 10)

            // Then
            assertEquals(2, result.campaigns.size)
            assertEquals("campaign-1", result.campaigns[0].campaignId)
            assertEquals("Test message 1", result.campaigns[0].message)
            assertEquals(5, result.campaigns[0].successCount)
            assertEquals(0, result.campaigns[0].failureCount)
            assertEquals("COMPLETED", result.campaigns[0].status)
        }
    }

    @Nested
    inner class DataValidationTests {

        @Test
        fun `should handle empty client list`() {
            // Given
            every { clientRepository.findBySalonId(salonId, isActive = true) } returns emptyList()
            every { massSmsRepository.save(any()) } returnsArgument 0

            // When
            val result = massSmsService.sendMassSms(
                salonId = salonId,
                clientIds = emptyList(),
                message = testMessage,
                userId = userId
            )

            // Then
            assertEquals("FAILED", result.status)
            assertEquals(0, result.totalRecipients)
            assertEquals("NO_VALID_CLIENTS", result.failures.first().errorCode)
        }

        @Test
        fun `should handle empty message`() {
            // Given
            val clients = listOf(testClient1)
            every { clientRepository.findBySalonId(salonId, isActive = true) } returns clients
            every { petRepository.findByClientIds(listOf(ClientId.of("client-1"))) } returns emptyList()
            every { smsLengthCalculator.calculateSmsUnits("") } returns 0

            // When
            val result = massSmsService.previewMassSms(
                salonId = salonId,
                clientIds = listOf("client-1"),
                message = ""
            )

            // Then
            assertEquals(0, result.smsSegments)
            assertEquals(0.0, result.estimatedCost)
        }

        @Test
        fun `should handle invalid salon ID`() {
            // Given
            val invalidSalonId = SalonId.of("invalid-salon")
            every { clientRepository.findBySalonId(invalidSalonId, isActive = true) } returns emptyList()

            // When
            val result = massSmsService.sendMassSms(
                salonId = invalidSalonId,
                clientIds = listOf("client-1"),
                message = testMessage,
                userId = userId
            )

            // Then
            assertEquals("FAILED", result.status)
            assertEquals(0, result.totalRecipients)
        }
    }
}
