package ro.animaliaprogramari.animalia.application.usecase

import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import ro.animaliaprogramari.animalia.adapter.inbound.event.FirebasePushService
import ro.animaliaprogramari.animalia.application.command.*
import ro.animaliaprogramari.animalia.application.port.outbound.*
import ro.animaliaprogramari.animalia.application.query.*
import ro.animaliaprogramari.animalia.domain.exception.EntityNotFoundException
import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.notification.*

class NotificationManagementUseCaseImplTest {

    private val notificationRepository = mockk<NotificationRepository>(relaxed = true)
    private val fcmTokenRepository = mockk<FcmTokenRepository>(relaxed = true)
    private val salonRepository = mockk<SalonRepository>()
    private val userRepository = mockk<UserRepository>()
    private val firebasePushService = mockk<FirebasePushService>(relaxed = true)

    private lateinit var useCase: NotificationManagementUseCaseImpl

    private val userId = UserId.of("user-1")
    private val salonId = SalonId.of("salon-1")
    private val notificationId = NotificationId.of("notification-1")

    @BeforeEach
    fun setup() {
        clearAllMocks()
        useCase = NotificationManagementUseCaseImpl(
            notificationRepository = notificationRepository,
            fcmTokenRepository = fcmTokenRepository,
            salonRepository = salonRepository,
            userRepository = userRepository,
            firebasePushService = firebasePushService,
        )
    }

    @Nested
    inner class GenerateTestData {
        /**
         * Scenario: Generate test data for a salon
         * Given: Salon exists and no FCM tokens exist
         * When: Test data is generated
         * Then: Correct number of notifications and FCM tokens are created
         */

        @Test
        fun `should generate test notifications and FCM tokens when salon exists`() {
            // Given
            every { salonRepository.findById(salonId) } returns mockk(relaxed = true)
            every { fcmTokenRepository.findActiveBySalonId(salonId) } returns emptyList()
            every { fcmTokenRepository.save(any()) } answers { firstArg() }
            every { notificationRepository.save(any()) } answers { firstArg() }

            val command = GenerateTestNotificationDataCommand(
                userId = userId,
                salonId = salonId,
                count = 5
            )

            // When
            val result = useCase.generateTestData(command)

            // Then
            assertEquals(5, result.notificationsCreated)
            assertEquals(3, result.fcmTokensCreated) // 3 test tokens created when none exist
            assertEquals(5, result.notificationIds.size)
            verify(exactly = 3) { fcmTokenRepository.save(any()) }
            verify(exactly = 5) { notificationRepository.save(any()) }
        }

        /**
         * Scenario: Generate test data for a salon when FCM tokens already exist
         * Given: Salon exists and FCM tokens exist
         * When: Test data is generated
         * Then: Correct number of notifications are created, no FCM tokens are created
         */
        @Test
        fun `should generate only notifications when FCM tokens already exist`() {
            // Given
            every { salonRepository.findById(salonId) } returns mockk(relaxed = true)
            every { fcmTokenRepository.findActiveBySalonId(salonId) } returns listOf(mockk(relaxed = true))
            every { notificationRepository.save(any()) } answers { firstArg() }

            val command = GenerateTestNotificationDataCommand(
                userId = userId,
                salonId = salonId,
                count = 3
            )

            // When
            val result = useCase.generateTestData(command)

            // Then
            assertEquals(3, result.notificationsCreated)
            assertEquals(0, result.fcmTokensCreated) // No tokens created when they exist
            verify(exactly = 0) { fcmTokenRepository.save(any()) }
            verify(exactly = 3) { notificationRepository.save(any()) }
        }

        /**
         * Scenario: Generate test data for a non-existent salon
         * Given: Salon does not exist
         * When: Test data is generated
         * Then: EntityNotFoundException is thrown
         */
        @Test
        fun `should throw when salon does not exist`() {
            // Given
            every { salonRepository.findById(salonId) } returns null
            val command = GenerateTestNotificationDataCommand(userId, salonId, 5)

            // When & Then
            assertThrows(EntityNotFoundException::class.java) {
                useCase.generateTestData(command)
            }
            verify(exactly = 0) { notificationRepository.save(any()) }
        }
    }

    @Nested
    inner class GetNotificationStats {
        /**
         * Scenario: Get notification stats for a salon
         * Given: Salon exists
         * When: Stats are requested
         * Then: Correct stats are returned
         */
        @Test
        fun `should return notification stats when salon exists`() {
            // Given
            every { salonRepository.findById(salonId) } returns mockk(relaxed = true)
            every { notificationRepository.countByStatusAndSalonId(NotificationStatus.SENT, salonId) } returns 10L
            every { notificationRepository.countByStatusAndSalonId(NotificationStatus.DELIVERED, salonId) } returns 8L
            every { notificationRepository.countByStatusAndSalonId(NotificationStatus.FAILED, salonId) } returns 2L
            every { notificationRepository.countByStatusAndSalonId(NotificationStatus.PENDING, salonId) } returns 3L
            every { notificationRepository.countByTypeAndSalonId(SmsNotificationType.PUSH, salonId) } returns 15L
            every { notificationRepository.countTodayBySalonId(salonId) } returns 5L
            every { notificationRepository.countThisWeekBySalonId(salonId) } returns 20L
            every { notificationRepository.countThisMonthBySalonId(salonId) } returns 50L
            every { fcmTokenRepository.countActiveBySalonId(salonId) } returns 4L
            every { notificationRepository.getAverageDeliveryTime(salonId) } returns 2.5

            val query = GetNotificationStatsQuery(userId, salonId)

            // When
            val result = useCase.getNotificationStats(query)

            // Then
            assertEquals(23L, result.totalNotifications) // 10+8+2+3
            assertEquals(18L, result.readCount) // sent + delivered
            assertEquals(5L, result.unreadCount) // pending + failed
            assertEquals(5L, result.todayCount)
            assertEquals(20L, result.weekCount)
            assertEquals(50L, result.monthCount)
            assertEquals(15L, result.byType["PUSH"])
            assertTrue(result.additionalStats?.containsKey("successRate") == true)
        }
        /**
         * Scenario: Get notification stats for a non-existent salon
         * Given: Salon does not exist
         * When: Stats are requested
         * Then: EntityNotFoundException is thrown
         */
        @Test
        fun `should throw when salon does not exist`() {
            // Given
            every { salonRepository.findById(salonId) } returns null
            val query = GetNotificationStatsQuery(userId, salonId)

            // When & Then
            assertThrows(EntityNotFoundException::class.java) {
                useCase.getNotificationStats(query)
            }
        }
    }

    @Nested
    inner class MarkAllAsRead {
        /**
         * Scenario: Mark all notifications as read for a user
         * Given: User has notifications
         * When: Mark all as read is called
         * Then: All notifications are marked as read
         */
        @Test
        fun `should mark all notifications as read for user`() {
            // Given
            every { notificationRepository.markAsReadByUserId(userId, salonId) } returns 5
            val command = BulkMarkNotificationsAsReadCommand(userId, salonId)

            // When
            val result = useCase.markAllAsRead(command)

            // Then
            assertEquals(5, result.notificationsMarked)
            assertTrue(result.message.contains("5"))
            verify(exactly = 1) { notificationRepository.markAsReadByUserId(userId, salonId) }
        }
    }

    @Nested
    inner class MarkNotificationAsRead {
        /**
         * Scenario: Mark a specific notification as read
         * Given: Notification exists and belongs to user
         * When: Mark as read is called
         * Then: Notification is marked as read
         */

        @Test
        fun `should mark notification as read when user owns it`() {
            // Given
            val notification = Notification.createPush(
                recipient = userId,
                title = "Test",
                body = "Test body",
                data = emptyMap(),
                appointmentId = null,
                salonId = salonId
            ).copy(id = notificationId) // Set the expected ID
            every { notificationRepository.findById(notificationId) } returns notification
            every { notificationRepository.save(any()) } answers { firstArg() }

            val command = MarkNotificationAsReadCommand(notificationId, userId, salonId)

            // When
            val result = useCase.markNotificationAsRead(command)

            // Then
            assertEquals(notificationId.value, result.id)
            verify(exactly = 1) { notificationRepository.save(any()) }
        }

        @Test
        fun `should throw when notification does not exist`() {
            // Given
            every { notificationRepository.findById(notificationId) } returns null
            val command = MarkNotificationAsReadCommand(notificationId, userId, salonId)

            // When & Then
            assertThrows(EntityNotFoundException::class.java) {
                useCase.markNotificationAsRead(command)
            }
        }

        @Test
        fun `should throw when user does not own notification`() {
            // Given
            val otherUserId = UserId.of("other-user")
            val notification = Notification.createPush(
                recipient = otherUserId,
                title = "Test",
                body = "Test body",
                data = emptyMap(),
                appointmentId = null,
                salonId = salonId
            )
            every { notificationRepository.findById(notificationId) } returns notification

            val command = MarkNotificationAsReadCommand(notificationId, userId, salonId)

            // When & Then
            assertThrows(EntityNotFoundException::class.java) {
                useCase.markNotificationAsRead(command)
            }
        }
    }

    @Nested
    inner class SendTestNotification {
        /**
         * Scenario: Send test notification
         * Given: User and salon exist
         * When: Test notification is sent
         * Then: Notification is created and sent via Firebase
         */

        @Test
        fun `should send test notification successfully`() {
            // Given
            every { notificationRepository.save(any()) } answers { firstArg() }
            every { firebasePushService.sendToStaff(any(), any(), any()) } returns Unit
            every { fcmTokenRepository.countActiveBySalonId(salonId) } returns 3L

            val command = SendTestNotificationCommand(
                userId = userId,
                salonId = salonId,
                title = "Test Title",
                message = "Test Message"
            )

            // When
            val result = useCase.sendTestNotification(command)

            // Then
            assertTrue(result.success)
            assertNotNull(result.notificationId)
            assertEquals(3, result.devicesNotified)
            verify(exactly = 2) { notificationRepository.save(any()) } // save + mark as sent
            verify(exactly = 1) { firebasePushService.sendToStaff(salonId.value, "Test Title", "Test Message") }
        }
        /**
         * Scenario: Handle Firebase service failure gracefully
         * Given: Firebase service throws an exception
         * When: Test notification is sent
         * Then: Notification is created but not sent, appropriate error message is returned
         */

        @Test
        fun `should handle firebase service failure gracefully`() {
            // Given
            every { notificationRepository.save(any()) } answers { firstArg() }
            every { firebasePushService.sendToStaff(any(), any(), any()) } throws RuntimeException("Firebase error")

            val command = SendTestNotificationCommand(
                userId = userId,
                salonId = salonId,
                title = "Test Title",
                message = "Test Message"
            )

            // When
            val result = useCase.sendTestNotification(command)

            // Then
            assertFalse(result.success)
            assertNull(result.notificationId)
            assertEquals(0, result.devicesNotified)
            assertTrue(result.message.contains("Failed"))
        }
    }

    @Nested
    inner class RegisterFcmToken {
        /**
         * Scenario: Register new FCM token
         * Given: User and salon exist
         * When: New FCM token is registered
         * Then: Token is created and saved
         */

        @Test
        fun `should register new FCM token when user and salon exist`() {
            // Given
            every { userRepository.findById(userId) } returns mockk(relaxed = true)
            every { salonRepository.findById(salonId) } returns mockk(relaxed = true)
            every { fcmTokenRepository.findActiveByUserIdAndSalonId(userId, salonId) } returns emptyList()
            every { fcmTokenRepository.deactivateOtherTokens(userId, salonId, any()) } returns 1
            every { fcmTokenRepository.save(any()) } answers { firstArg() }

            val command = RegisterFcmTokenCommand(
                userId = userId,
                salonId = salonId,
                token = "new-token",
                deviceId = "device-1",
                deviceType = "mobile"
            )

            // When
            val result = useCase.registerFcmToken(command)

            // Then
            assertEquals("new-token", result.token)
            assertEquals("device-1", result.deviceId)
            verify(exactly = 1) { fcmTokenRepository.save(any()) }
            verify(exactly = 1) { fcmTokenRepository.deactivateOtherTokens(userId, salonId, "new-token") }
        }
        /**
         * Scenario: Reactivate existing FCM token
         * Given: User and salon exist, token already exists
         * When: Existing FCM token is registered
         * Then: Token is reactivated
         */

        @Test
        fun `should reactivate existing token when already registered`() {
            // Given
            val existingToken = FcmToken.create(userId, salonId, "existing-token", "device-1", DeviceType.MOBILE)
            every { userRepository.findById(userId) } returns mockk(relaxed = true)
            every { salonRepository.findById(salonId) } returns mockk(relaxed = true)
            every { fcmTokenRepository.findActiveByUserIdAndSalonId(userId, salonId) } returns listOf(existingToken)
            every { fcmTokenRepository.save(any()) } answers { firstArg() }

            val command = RegisterFcmTokenCommand(
                userId = userId,
                salonId = salonId,
                token = "existing-token",
                deviceId = "device-1",
                deviceType = "mobile"
            )

            // When
            val result = useCase.registerFcmToken(command)

            // Then
            assertEquals("existing-token", result.token)
            verify(exactly = 1) { fcmTokenRepository.save(any()) }
            verify(exactly = 0) { fcmTokenRepository.deactivateOtherTokens(any(), any(), any()) }
        }
        /**
         * Scenario: Handle invalid device type gracefully
         * Given: Invalid device type is provided
         * When: FCM token is registered
         * Then: Token is created with default device type
         */

        @Test
        fun `should throw when user does not exist`() {
            // Given
            every { userRepository.findById(userId) } returns null
            val command = RegisterFcmTokenCommand(userId, salonId, "token", "device", "mobile")

            // When & Then
            assertThrows(EntityNotFoundException::class.java) {
                useCase.registerFcmToken(command)
            }
        }
        /**
         * Scenario: Handle salon not found gracefully
         * Given: Salon does not exist
         * When: FCM token is registered
         * Then: EntityNotFoundException is thrown
         */

        @Test
        fun `should throw when salon does not exist`() {
            // Given
            every { userRepository.findById(userId) } returns mockk(relaxed = true)
            every { salonRepository.findById(salonId) } returns null
            val command = RegisterFcmTokenCommand(userId, salonId, "token", "device", "mobile")

            // When & Then
            assertThrows(EntityNotFoundException::class.java) {
                useCase.registerFcmToken(command)
            }
        }
    }

    @Nested
    inner class UpdateFcmToken {
        /**
         * Scenario: Update existing FCM token
         * Given: Existing token exists
         * When: Token is updated
         * Then: Token is updated and saved
         */

        @Test
        fun `should update FCM token when existing token found`() {
            // Given
            val existingToken = FcmToken.create(userId, salonId, "old-token", "device-1", DeviceType.MOBILE)
            every { fcmTokenRepository.findByToken("old-token") } returns existingToken
            every { fcmTokenRepository.save(any()) } answers { firstArg() }

            val command = UpdateFcmTokenCommand(userId, salonId, "old-token", "new-token")

            // When
            val result = useCase.updateFcmToken(command)

            // Then
            assertEquals("new-token", result.token)
            verify(exactly = 1) { fcmTokenRepository.save(any()) }
        }
        /**
         * Scenario: Handle non-existent token gracefully
         * Given: Token does not exist
         * When: Token is updated
         * Then: EntityNotFoundException is thrown
         */

        @Test
        fun `should throw when token does not exist`() {
            // Given
            every { fcmTokenRepository.findByToken("non-existent-token") } returns null
            val command = UpdateFcmTokenCommand(userId, salonId, "non-existent-token", "new-token")

            // When & Then
            assertThrows(EntityNotFoundException::class.java) {
                useCase.updateFcmToken(command)
            }
        }
    }

    @Nested
    inner class DeactivateFcmToken {
        /**
         * Scenario: Deactivate existing FCM token
         * Given: Existing token exists
         * When: Token is deactivated
         * Then: Token is deactivated and saved
         */

        @Test
        fun `should deactivate FCM token when token exists`() {
            // Given
            val existingToken = FcmToken.create(userId, salonId, "token-to-deactivate", "device-1", DeviceType.MOBILE)
            every { fcmTokenRepository.findByToken("token-to-deactivate") } returns existingToken
            every { fcmTokenRepository.save(any()) } answers { firstArg() }

            val command = DeactivateFcmTokenCommand(userId, salonId, "token-to-deactivate")

            // When
            val result = useCase.deactivateFcmToken(command)

            // Then
            assertTrue(result)
            verify(exactly = 1) { fcmTokenRepository.save(any()) }
        }
        /**
         * Scenario: Handle non-existent token gracefully
         * Given: Token does not exist
         * When: Token is deactivated
         * Then: False is returned
         */

        @Test
        fun `should return false when token does not exist`() {
            // Given
            every { fcmTokenRepository.findByToken("non-existent-token") } returns null
            val command = DeactivateFcmTokenCommand(userId, salonId, "non-existent-token")

            // When
            val result = useCase.deactivateFcmToken(command)

            // Then
            assertFalse(result)
            verify(exactly = 0) { fcmTokenRepository.save(any()) }
        }
    }

    @Nested
    inner class GetUserNotifications {
        /**
         * Scenario: Get user notifications with pagination
         * Given: User has notifications
         * When: Notifications are requested
         * Then: Correct notifications are returned
         */

        @Test
        fun `should return paginated notifications for user`() {
            // Given
            val notifications = listOf(
                Notification.createPush(userId, "Title 1", "Body 1", emptyMap(), null, salonId),
                Notification.createPush(userId, "Title 2", "Body 2", emptyMap(), null, salonId)
            )
            every { notificationRepository.findByUserIdAndType(userId, salonId, null, 10, 10) } returns notifications
            every { notificationRepository.countByUserId(userId, salonId) } returns 15L
            every { notificationRepository.countUnreadByUserId(userId, salonId) } returns 5L

            val query = GetUserNotificationsQuery(userId, salonId, 1, 10, false, null)

            // When
            val result = useCase.getUserNotifications(query)

            // Then
            assertEquals(2, result.notifications.size)
            assertEquals(15L, result.totalCount)
            assertEquals(5L, result.unreadCount)
            assertEquals(1, result.page)
            assertEquals(10, result.pageSize)
            assertFalse(result.hasMore) // (10 + 10) < 15 = false
        }

        /**
         * Scenario: Filter notifications by type
         */

        @Test
        fun `should filter by notification type when provided`() {
            // Given
            every { notificationRepository.findByUserIdAndType(userId, salonId, SmsNotificationType.PUSH, 10, 10) } returns emptyList()
            every { notificationRepository.countByUserId(userId, salonId) } returns 0L
            every { notificationRepository.countUnreadByUserId(userId, salonId) } returns 0L

            val query = GetUserNotificationsQuery(userId, salonId, 1, 10, false, "PUSH")

            // When
            val result = useCase.getUserNotifications(query)

            // Then
            assertEquals(0, result.notifications.size)
            verify(exactly = 1) { notificationRepository.findByUserIdAndType(userId, salonId, SmsNotificationType.PUSH, 10, 10) }
        }
    }

    @Nested
    inner class GetUserFcmTokens {
        /**
         * Scenario: Get active FCM tokens for user and salon
         * Given: User and salon exist
         * When: FCM tokens are requested
         * Then: Correct tokens are returned
         */

        @Test
        fun `should return active tokens for user and salon when activeOnly is true`() {
            // Given
            val tokens = listOf(
                FcmToken.create(userId, salonId, "token-1", "device-1", DeviceType.MOBILE),
                FcmToken.create(userId, salonId, "token-2", "device-2", DeviceType.WEB)
            )
            every { fcmTokenRepository.findActiveByUserIdAndSalonId(userId, salonId) } returns tokens

            val query = GetUserFcmTokensQuery(userId, salonId, activeOnly = true)

            // When
            val result = useCase.getUserFcmTokens(query)

            // Then
            assertEquals(2, result.size)
            assertEquals("token-1", result[0].token)
            assertEquals("token-2", result[1].token)
            verify(exactly = 1) { fcmTokenRepository.findActiveByUserIdAndSalonId(userId, salonId) }
        }
        /**
         * Scenario: Get all FCM tokens for user
         * Given: User and salon exist
         * When: FCM tokens are requested with activeOnly = false
         * Then: All tokens are returned
         */

        @Test
        fun `should return all tokens for user when activeOnly is false`() {
            // Given
            val tokens = listOf(FcmToken.create(userId, salonId, "token-1", "device-1", DeviceType.MOBILE))
            every { fcmTokenRepository.findActiveByUserId(userId) } returns tokens

            val query = GetUserFcmTokensQuery(userId, salonId, activeOnly = false)

            // When
            val result = useCase.getUserFcmTokens(query)

            // Then
            assertEquals(1, result.size)
            verify(exactly = 1) { fcmTokenRepository.findActiveByUserId(userId) }
        }
    }
}
