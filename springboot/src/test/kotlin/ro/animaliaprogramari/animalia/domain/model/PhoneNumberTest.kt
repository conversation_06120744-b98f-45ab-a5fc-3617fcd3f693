package ro.animaliaprogramari.animalia.domain.model

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlin.test.assertFalse

/**
 * Unit tests for PhoneNumber domain model
 * Tests basic Romanian and international phone number functionality
 */
@DisplayName("PhoneNumber Domain Model")
class PhoneNumberTest {

    @Test
    @DisplayName("Should create phone number with valid Romanian international format")
    fun shouldCreatePhoneNumberWithValidInternationalFormat() {
        // When
        val phoneNumber = PhoneNumber.of("+40712345678")

        // Then
        assertEquals("+40712345678", phoneNumber.value)
    }

    @Test
    @DisplayName("Should create phone number with valid Romanian local format")
    fun shouldCreatePhoneNumberWithValidLocalFormat() {
        // When
        val phoneNumber = PhoneNumber.of("0712345678")

        // Then
        assertEquals("0712345678", phoneNumber.value)
    }

    @Test
    @DisplayName("Should create phone number with valid international formats from various countries")
    fun shouldCreatePhoneNumberWithValidInternationalFormats() {
        // Moldova
        val moldovaPhone = PhoneNumber.of("+37379999999")
        assertEquals("+37379999999", moldovaPhone.value)

        // USA
        val usaPhone = PhoneNumber.of("+12125551234")
        assertEquals("+12125551234", usaPhone.value)

        // UK
        val ukPhone = PhoneNumber.of("+442071234567")
        assertEquals("+442071234567", ukPhone.value)

        // Germany
        val germanyPhone = PhoneNumber.of("+4930123456")
        assertEquals("+4930123456", germanyPhone.value)

        // France
        val francePhone = PhoneNumber.of("+33123456789")
        assertEquals("+33123456789", francePhone.value)
    }

    @Test
    @DisplayName("Should convert local format to international format")
    fun shouldConvertLocalFormatToInternationalFormat() {
        // Given
        val phoneNumber = PhoneNumber.of("0712345678")

        // When
        val internationalFormat = phoneNumber.toInternationalFormat()

        // Then
        assertEquals("+40712345678", internationalFormat)
    }

    @Test
    @DisplayName("Should detect Romanian numbers")
    fun shouldDetectRomanianNumbers() {
        // Romanian numbers
        assertTrue(PhoneNumber.of("+40712345678").isRomanianNumber())
        assertTrue(PhoneNumber.of("0712345678").isRomanianNumber())

        // Non-Romanian numbers
        assertFalse(PhoneNumber.of("+37379999999").isRomanianNumber())
        assertFalse(PhoneNumber.of("+12125551234").isRomanianNumber())
    }

    @Test
    @DisplayName("Should return null for null input using ofNullable")
    fun shouldReturnNullForNullInputUsingOfNullable() {
        // When
        val phoneNumber = PhoneNumber.ofNullable(null)

        // Then
        assertNull(phoneNumber)
    }

    @Test
    @DisplayName("Should return null for blank input using ofNullable")
    fun shouldReturnNullForBlankInputUsingOfNullable() {
        // When
        val phoneNumber = PhoneNumber.ofNullable("")

        // Then
        assertNull(phoneNumber)
    }

    @Test
    @DisplayName("Should return phone number for valid input using ofNullable")
    fun shouldReturnPhoneNumberForValidInputUsingOfNullable() {
        // When
        val phoneNumber = PhoneNumber.ofNullable("0712345678")

        // Then
        assertNotNull(phoneNumber)
        assertEquals("0712345678", phoneNumber.value)
    }

    @Test
    @DisplayName("Should return international phone number for valid input using ofNullable")
    fun shouldReturnInternationalPhoneNumberForValidInputUsingOfNullable() {
        // When
        val phoneNumber = PhoneNumber.ofNullable("+37379999999")

        // Then
        assertNotNull(phoneNumber)
        assertEquals("+37379999999", phoneNumber.value)
    }

    @Test
    @DisplayName("Should validate Romanian format correctly")
    fun shouldValidateRomanianFormatCorrectly() {
        // When & Then
        assertTrue(PhoneNumber.isValidRomanianFormat("+40712345678"))
        assertTrue(PhoneNumber.isValidRomanianFormat("0712345678"))
    }

    @Test
    @DisplayName("Should normalize phone numbers correctly")
    fun shouldNormalizePhoneNumbersCorrectly() {
        // Romanian numbers
        assertEquals("+40712345678", PhoneNumber.normalize("+40712345678"))
        assertEquals("+40712345678", PhoneNumber.normalize("0712345678"))

        // International numbers (already normalized)
        assertEquals("+37379999999", PhoneNumber.normalize("+37379999999"))
        assertEquals("+12125551234", PhoneNumber.normalize("+12125551234"))
    }

    @Test
    @DisplayName("Should validate and normalize phone numbers")
    fun shouldValidateAndNormalizePhoneNumbers() {
        // Romanian numbers
        assertEquals("+40712345678", PhoneNumber.validateAndNormalize("0712345678"))
        assertEquals("+40712345678", PhoneNumber.validateAndNormalize("+40712345678"))

        // International numbers
        assertEquals("+37379999999", PhoneNumber.validateAndNormalize("+37379999999"))
        assertEquals("+12125551234", PhoneNumber.validateAndNormalize("+12125551234"))

        // Invalid inputs
        assertNull(PhoneNumber.validateAndNormalize(null))
        assertNull(PhoneNumber.validateAndNormalize(""))
        assertNull(PhoneNumber.validateAndNormalize("invalid"))
    }

    @Test
    @DisplayName("Should return original value as string")
    fun shouldReturnOriginalValueAsString() {
        // Given
        val phoneNumber = PhoneNumber.of("0712345678")

        // When
        val stringRepresentation = phoneNumber.toString()

        // Then
        assertEquals("0712345678", stringRepresentation)
    }
}
