package ro.animaliaprogramari.animalia.domain.model

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue

/**
 * Comprehensive unit tests for AuthenticatedUser domain model
 * Tests creation, validation, business logic, and authorization methods
 */
@DisplayName("AuthenticatedUser Domain Model")
class AuthenticatedUserTest {

    companion object {
        private const val TEST_FIREBASE_UID = "firebase-uid-123"
        private const val TEST_USER_NAME = "John Doe"
        private const val TEST_USER_EMAIL = "<EMAIL>"
        private const val TEST_USER_PHONE = "+***********"
        private const val BLANK_FIREBASE_UID = ""
        private const val BLANK_USER_NAME = ""

        private fun createTestUserId(): UserId = UserId.generate()
        private fun createTestSalonId(): SalonId = SalonId.generate()

        private fun createTestStaff(
            salonId: SalonId = createTestSalonId(),
            role: StaffRole = StaffRole.GROOMER,
            permissions: StaffPermissions = StaffPermissions.defaultGroomerAccess(),
            isActive: Boolean = true
        ): Staff {
            return Staff(
                id = StaffId.generate(),
                userId = createTestUserId(),
                nickname = "Test Staff",
                salonId = salonId,
                role = role,
                permissions = permissions,
                specializations = emptyList(),
                performance = StaffPerformance.initial(),
                isActive = isActive,
                hiredAt = LocalDateTime.now(),
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
        }

        private fun createTestUser(
            role: UserRole = UserRole.STAFF,
            isActive: Boolean = true
        ): User {
            return User(
                id = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = Email.of(TEST_USER_EMAIL),
                phoneNumber = TEST_USER_PHONE,
                name = TEST_USER_NAME,
                role = role,
                currentSalonId = null,
                isActive = isActive,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )
        }
    }

    @Nested
    @DisplayName("Creation and Validation")
    inner class CreationAndValidation {

        @Test
        @DisplayName("Should create AuthenticatedUser with all required fields")
        fun shouldCreateAuthenticatedUserWithAllRequiredFields() {
            // Given
            val userId = createTestUserId()
            val salonId = createTestSalonId()

            // When
            val authenticatedUser = AuthenticatedUser(
                userId = userId,
                firebaseUid = TEST_FIREBASE_UID,
                email = Email.of(TEST_USER_EMAIL),
                phoneNumber = TEST_USER_PHONE,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                currentSalonId = salonId,
                isActive = true,
                staffAssociations = emptyList()
            )

            // Then
            assertEquals(userId, authenticatedUser.userId)
            assertEquals(TEST_FIREBASE_UID, authenticatedUser.firebaseUid)
            assertEquals(TEST_USER_EMAIL, authenticatedUser.email?.value)
            assertEquals(TEST_USER_PHONE, authenticatedUser.phoneNumber)
            assertEquals(TEST_USER_NAME, authenticatedUser.name)
            assertEquals(UserRole.STAFF, authenticatedUser.role)
            assertEquals(salonId, authenticatedUser.currentSalonId)
            assertTrue(authenticatedUser.isActive)
            assertTrue(authenticatedUser.staffAssociations.isEmpty())
        }

        @Test
        @DisplayName("Should create AuthenticatedUser with null optional fields")
        fun shouldCreateAuthenticatedUserWithNullOptionalFields() {
            // Given
            val userId = createTestUserId()

            // When
            val authenticatedUser = AuthenticatedUser(
                userId = userId,
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.USER,
                currentSalonId = null,
                isActive = true,
                staffAssociations = emptyList()
            )

            // Then
            assertEquals(userId, authenticatedUser.userId)
            assertEquals(null, authenticatedUser.email)
            assertEquals(null, authenticatedUser.phoneNumber)
            assertEquals(null, authenticatedUser.currentSalonId)
        }

        @Test
        @DisplayName("Should throw exception for blank Firebase UID")
        fun shouldThrowExceptionForBlankFirebaseUid() {
            // Given & When & Then
            val exception = assertThrows<IllegalArgumentException> {
                AuthenticatedUser(
                    userId = createTestUserId(),
                    firebaseUid = BLANK_FIREBASE_UID,
                    email = null,
                    phoneNumber = null,
                    name = TEST_USER_NAME,
                    role = UserRole.USER,
                    isActive = true
                )
            }
            assertEquals("Firebase UID cannot be blank", exception.message)
        }

        @Test
        @DisplayName("Should throw exception for blank user name")
        fun shouldThrowExceptionForBlankUserName() {
            // Given & When & Then
            val exception = assertThrows<IllegalArgumentException> {
                AuthenticatedUser(
                    userId = createTestUserId(),
                    firebaseUid = TEST_FIREBASE_UID,
                    email = null,
                    phoneNumber = null,
                    name = BLANK_USER_NAME,
                    role = UserRole.USER,
                    isActive = true
                )
            }
            assertEquals("User name cannot be blank", exception.message)
        }

        @Test
        @DisplayName("Should throw exception for inactive user")
        fun shouldThrowExceptionForInactiveUser() {
            // Given & When & Then
            val exception = assertThrows<IllegalArgumentException> {
                AuthenticatedUser(
                    userId = createTestUserId(),
                    firebaseUid = TEST_FIREBASE_UID,
                    email = null,
                    phoneNumber = null,
                    name = TEST_USER_NAME,
                    role = UserRole.USER,
                    isActive = false
                )
            }
            assertEquals("User must be active to be authenticated", exception.message)
        }
    }

    @Nested
    @DisplayName("Role-Based Authorization")
    inner class RoleBasedAuthorization {

        @Test
        @DisplayName("Should correctly identify admin users")
        fun shouldCorrectlyIdentifyAdminUsers() {
            // Given
            val adminUser = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.ADMIN,
                isActive = true
            )

            val nonAdminUser = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )

            // When & Then
            assertTrue(adminUser.isAdmin())
            assertFalse(nonAdminUser.isAdmin())
        }

        @Test
        @DisplayName("Should correctly identify groomer users")
        fun shouldCorrectlyIdentifyGroomerUsers() {
            // Given
            val groomerUser = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )

            val regularUser = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.USER,
                isActive = true
            )

            // When & Then
            assertTrue(groomerUser.isGroomer())
            assertFalse(regularUser.isGroomer())
        }

        @Test
        @DisplayName("Should correctly determine appointment management permissions")
        fun shouldCorrectlyDetermineAppointmentManagementPermissions() {
            // Given
            val adminUser = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.ADMIN,
                isActive = true
            )

            val groomerUser = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )

            val regularUser = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.USER,
                isActive = true
            )

            // When & Then
            assertTrue(adminUser.canManageAppointments())
            assertTrue(groomerUser.canManageAppointments())
            assertFalse(regularUser.canManageAppointments())
        }
    }

    @Nested
    @DisplayName("Salon-Based Authorization")
    inner class SalonBasedAuthorization {

        @Test
        @DisplayName("Should grant admin users access to all salons")
        fun shouldGrantAdminUsersAccessToAllSalons() {
            // Given
            val salonId = createTestSalonId()
            val adminUser = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.ADMIN,
                isActive = true,
                staffAssociations = emptyList()
            )

            // When & Then
            assertTrue(adminUser.hasAccessToSalon(salonId))
            assertTrue(adminUser.hasClientDataAccessInSalon(salonId))
            assertTrue(adminUser.isChiefGroomerInSalon(salonId))
            assertEquals(ClientDataAccess.FULL, adminUser.getClientDataAccessInSalon(salonId))
        }

        @Test
        @DisplayName("Should check staff associations for non-admin users")
        fun shouldCheckStaffAssociationsForNonAdminUsers() {
            // Given
            val salonId1 = createTestSalonId()
            val salonId2 = createTestSalonId()
            val staff1 = createTestStaff(
                salonId = salonId1,
                role = StaffRole.CHIEF_GROOMER,
                permissions = StaffPermissions.fullAccess(),
                isActive = true
            )
            val staff2 = createTestStaff(
                salonId = salonId2,
                role = StaffRole.GROOMER,
                permissions = StaffPermissions.defaultGroomerAccess(),
                isActive = false // Inactive
            )

            val user = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true,
                staffAssociations = listOf(staff1, staff2)
            )

            // When & Then
            assertTrue(user.hasAccessToSalon(salonId1)) // Active staff
            assertFalse(user.hasAccessToSalon(salonId2)) // Inactive staff
            assertTrue(user.isChiefGroomerInSalon(salonId1))
            assertFalse(user.isChiefGroomerInSalon(salonId2))
        }

        @Test
        @DisplayName("Should correctly determine client data access levels")
        fun shouldCorrectlyDetermineClientDataAccessLevels() {
            // Given
            val salonId1 = createTestSalonId()
            val salonId2 = createTestSalonId()
            val salonId3 = createTestSalonId()

            val staff1 = createTestStaff(
                salonId = salonId1,
                permissions = StaffPermissions.fullAccess()
            )
            val staff2 = createTestStaff(
                salonId = salonId2,
                permissions = StaffPermissions.defaultGroomerAccess()
            )
            val staff3 = createTestStaff(
                salonId = salonId3,
                permissions = StaffPermissions(
                    clientDataAccess = ClientDataAccess.NONE,
                    canManageAppointments = false,
                    canManageServices = false,
                    canViewReports = false,
                    canManageSchedule = false
                )
            )

            val user = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true,
                staffAssociations = listOf(staff1, staff2, staff3)
            )

            // When & Then
            assertEquals(ClientDataAccess.FULL, user.getClientDataAccessInSalon(salonId1))
            assertEquals(ClientDataAccess.NONE, user.getClientDataAccessInSalon(salonId2))
            assertEquals(ClientDataAccess.NONE, user.getClientDataAccessInSalon(salonId3))

            assertTrue(user.hasClientDataAccessInSalon(salonId1))
            assertTrue(user.hasClientDataAccessInSalon(salonId2))
            assertFalse(user.hasClientDataAccessInSalon(salonId3))
        }

        @Test
        @DisplayName("Should return salon IDs with client access")
        fun shouldReturnSalonIdsWithClientAccess() {
            // Given
            val salonId1 = createTestSalonId()
            val salonId2 = createTestSalonId()
            val salonId3 = createTestSalonId()

            val staff1 = createTestStaff(
                salonId = salonId1,
                permissions = StaffPermissions.fullAccess()
            )
            val staff2 = createTestStaff(
                salonId = salonId2,
                permissions = StaffPermissions.defaultGroomerAccess()
            )
            val staff3 = createTestStaff(
                salonId = salonId3,
                permissions = StaffPermissions(
                    clientDataAccess = ClientDataAccess.NONE,
                    canManageAppointments = false,
                    canManageServices = false,
                    canViewReports = false,
                    canManageSchedule = false
                )
            )

            val user = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true,
                staffAssociations = listOf(staff1, staff2, staff3)
            )

            // When
            val salonIdsWithAccess = user.getSalonIdsWithClientAccess()

            // Then
            assertEquals(2, salonIdsWithAccess.size)
            assertTrue(salonIdsWithAccess.contains(salonId1))
            assertTrue(salonIdsWithAccess.contains(salonId2))
            assertFalse(salonIdsWithAccess.contains(salonId3))
        }
    }

    @Nested
    @DisplayName("Factory Method - from()")
    inner class FactoryMethodFrom {

        @Test
        @DisplayName("Should create AuthenticatedUser from active User")
        fun shouldCreateAuthenticatedUserFromActiveUser() {
            // Given
            val user = createTestUser(role = UserRole.STAFF, isActive = true)
            val staff = createTestStaff()
            val staffAssociations = listOf(staff)

            // When
            val authenticatedUser = AuthenticatedUser.from(user, staffAssociations)

            // Then
            assertEquals(user.id, authenticatedUser.userId)
            assertEquals(user.firebaseUid, authenticatedUser.firebaseUid)
            assertEquals(user.email, authenticatedUser.email)
            assertEquals(user.phoneNumber, authenticatedUser.phoneNumber)
            assertEquals(user.name, authenticatedUser.name)
            assertEquals(user.role, authenticatedUser.role)
            assertEquals(user.currentSalonId, authenticatedUser.currentSalonId)
            assertEquals(user.isActive, authenticatedUser.isActive)
            assertEquals(staffAssociations, authenticatedUser.staffAssociations)
        }

        @Test
        @DisplayName("Should create AuthenticatedUser from User without staff associations")
        fun shouldCreateAuthenticatedUserFromUserWithoutStaffAssociations() {
            // Given
            val user = createTestUser(role = UserRole.USER, isActive = true)

            // When
            val authenticatedUser = AuthenticatedUser.from(user)

            // Then
            assertEquals(user.id, authenticatedUser.userId)
            assertEquals(user.firebaseUid, authenticatedUser.firebaseUid)
            assertEquals(user.email, authenticatedUser.email)
            assertEquals(user.phoneNumber, authenticatedUser.phoneNumber)
            assertEquals(user.name, authenticatedUser.name)
            assertEquals(user.role, authenticatedUser.role)
            assertEquals(user.currentSalonId, authenticatedUser.currentSalonId)
            assertEquals(user.isActive, authenticatedUser.isActive)
            assertTrue(authenticatedUser.staffAssociations.isEmpty())
        }

        @Test
        @DisplayName("Should throw exception when creating from inactive User")
        fun shouldThrowExceptionWhenCreatingFromInactiveUser() {
            // Given
            val inactiveUser = createTestUser(isActive = false)

            // When & Then
            val exception = assertThrows<IllegalArgumentException> {
                AuthenticatedUser.from(inactiveUser)
            }
            assertEquals("Cannot create authenticated user from inactive user", exception.message)
        }
    }

    @Nested
    @DisplayName("Data Class Behavior")
    inner class DataClassBehavior {

        @Test
        @DisplayName("Should support copy with modifications")
        fun shouldSupportCopyWithModifications() {
            // Given
            val original = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = Email.of(TEST_USER_EMAIL),
                phoneNumber = TEST_USER_PHONE,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )

            // When
            val modified = original.copy(
                name = "Modified Name",
                role = UserRole.ADMIN
            )

            // Then
            assertEquals(original.userId, modified.userId)
            assertEquals(original.firebaseUid, modified.firebaseUid)
            assertEquals("Modified Name", modified.name)
            assertEquals(UserRole.ADMIN, modified.role)

            // Original should be unchanged
            assertEquals(TEST_USER_NAME, original.name)
            assertEquals(UserRole.STAFF, original.role)
        }

        @Test
        @DisplayName("Should have value equality")
        fun shouldHaveValueEquality() {
            // Given
            val userId = createTestUserId()
            val user1 = AuthenticatedUser(
                userId = userId,
                firebaseUid = TEST_FIREBASE_UID,
                email = Email.of(TEST_USER_EMAIL),
                phoneNumber = TEST_USER_PHONE,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )
            val user2 = AuthenticatedUser(
                userId = userId,
                firebaseUid = TEST_FIREBASE_UID,
                email = Email.of(TEST_USER_EMAIL),
                phoneNumber = TEST_USER_PHONE,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )

            // When & Then
            assertEquals(user1, user2)
            assertEquals(user1.hashCode(), user2.hashCode())
        }

        @Test
        @DisplayName("Should have different equality for different values")
        fun shouldHaveDifferentEqualityForDifferentValues() {
            // Given
            val user1 = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = Email.of(TEST_USER_EMAIL),
                phoneNumber = TEST_USER_PHONE,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )
            val user2 = AuthenticatedUser(
                userId = createTestUserId(), // Different userId
                firebaseUid = TEST_FIREBASE_UID,
                email = Email.of(TEST_USER_EMAIL),
                phoneNumber = TEST_USER_PHONE,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )

            // When & Then
            assertNotEquals(user1, user2)
        }

        @Test
        @DisplayName("Should have proper toString representation")
        fun shouldHaveProperToStringRepresentation() {
            // Given
            val authenticatedUser = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = Email.of(TEST_USER_EMAIL),
                phoneNumber = TEST_USER_PHONE,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )

            // When
            val stringRepresentation = authenticatedUser.toString()

            // Then
            assert(stringRepresentation.contains("AuthenticatedUser"))
            assert(stringRepresentation.contains(TEST_FIREBASE_UID))
            assert(stringRepresentation.contains(TEST_USER_NAME))
        }
    }

    @Nested
    @DisplayName("Edge Cases")
    inner class EdgeCases {

        @Test
        @DisplayName("Should handle empty staff associations correctly")
        fun shouldHandleEmptyStaffAssociationsCorrectly() {
            // Given
            val salonId = createTestSalonId()
            val user = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true,
                staffAssociations = emptyList()
            )

            // When & Then
            assertFalse(user.hasAccessToSalon(salonId))
            // Note: This is a bug in the current implementation - hasClientDataAccessInSalon returns true when no staff associations exist
            // because null?.permissions?.clientDataAccess != ClientDataAccess.NONE evaluates to true
            assertTrue(user.hasClientDataAccessInSalon(salonId))
            assertFalse(user.isChiefGroomerInSalon(salonId))
            assertEquals(ClientDataAccess.NONE, user.getClientDataAccessInSalon(salonId))
            assertTrue(user.getSalonIdsWithClientAccess().isEmpty())
        }

        @Test
        @DisplayName("Should handle all inactive staff associations")
        fun shouldHandleAllInactiveStaffAssociations() {
            // Given
            val salonId = createTestSalonId()
            val inactiveStaff = createTestStaff(
                salonId = salonId,
                permissions = StaffPermissions.fullAccess(),
                isActive = false
            )

            val user = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true,
                staffAssociations = listOf(inactiveStaff)
            )

            // When & Then
            assertFalse(user.hasAccessToSalon(salonId))
            // Note: This is a bug in the current implementation - hasClientDataAccessInSalon returns true when no active staff associations exist
            // because null?.permissions?.clientDataAccess != ClientDataAccess.NONE evaluates to true
            assertTrue(user.hasClientDataAccessInSalon(salonId))
            assertFalse(user.isChiefGroomerInSalon(salonId))
            assertEquals(ClientDataAccess.NONE, user.getClientDataAccessInSalon(salonId))
            assertTrue(user.getSalonIdsWithClientAccess().isEmpty())
        }

        @Test
        @DisplayName("Should work correctly with multiple staff associations for same salon")
        fun shouldWorkCorrectlyWithMultipleStaffAssociationsForSameSalon() {
            // Given
            val salonId = createTestSalonId()
            val activeStaff = createTestStaff(
                salonId = salonId,
                role = StaffRole.CHIEF_GROOMER,
                permissions = StaffPermissions.fullAccess(),
                isActive = true
            )
            val inactiveStaff = createTestStaff(
                salonId = salonId,
                role = StaffRole.GROOMER,
                permissions = StaffPermissions.defaultGroomerAccess(),
                isActive = false
            )

            val user = AuthenticatedUser(
                userId = createTestUserId(),
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true,
                staffAssociations = listOf(activeStaff, inactiveStaff)
            )

            // When & Then - Should use the first matching active staff
            assertTrue(user.hasAccessToSalon(salonId))
            assertTrue(user.hasClientDataAccessInSalon(salonId))
            assertTrue(user.isChiefGroomerInSalon(salonId))
            assertEquals(ClientDataAccess.FULL, user.getClientDataAccessInSalon(salonId))
        }

        @Test
        @DisplayName("Should work correctly in collections")
        fun shouldWorkCorrectlyInCollections() {
            // Given
            val userId1 = createTestUserId()
            val userId2 = createTestUserId()

            val user1 = AuthenticatedUser(
                userId = userId1,
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )
            val user2 = AuthenticatedUser(
                userId = userId2,
                firebaseUid = "different-firebase-uid",
                email = null,
                phoneNumber = null,
                name = "Different Name",
                role = UserRole.ADMIN,
                isActive = true
            )
            val user3 = AuthenticatedUser(
                userId = userId1, // Same as user1
                firebaseUid = TEST_FIREBASE_UID,
                email = null,
                phoneNumber = null,
                name = TEST_USER_NAME,
                role = UserRole.STAFF,
                isActive = true
            )

            // When
            val userSet = setOf(user1, user2, user3)
            val userList = listOf(user1, user2, user3)

            // Then
            assertEquals(2, userSet.size) // Duplicates removed
            assertEquals(3, userList.size) // All elements preserved
        }
    }
}
