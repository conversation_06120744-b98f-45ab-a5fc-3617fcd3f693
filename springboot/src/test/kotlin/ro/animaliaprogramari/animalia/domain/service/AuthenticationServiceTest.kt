package ro.animaliaprogramari.animalia.domain.service

import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.model.Email
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class AuthenticationServiceTest {
    private val service = AuthenticationService()

    @Test
    fun `determineUserRole should return ADMIN for admin email`() {
        val role = service.determineUserRole(Email.of("<EMAIL>"), null, null)
        assertEquals(UserRole.ADMIN, role)
    }

    @Test
    fun `validateUserForAuthentication should detect inactive user`() {
        val user =
            User(
                id = UserId.generate(),
                firebaseUid = "firebase",
                email = Email.of("<EMAIL>"),
                phoneNumber = null,
                name = "Name",
                role = UserRole.USER,
                currentSalonId = null,
                isActive = false,
            )
        val result = service.validateUserForAuthentication(user)
        assertTrue(result is AuthenticationValidationResult.UserInactive)
    }

    @Test
    fun `createOrUpdateUserFromFirebase should preserve custom user names`() {
        // Given: existing user with custom name
        val existingUser = User(
            id = UserId.generate(),
            firebaseUid = "firebase123",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40123456789",
            name = "Custom User Name", // Custom name, not email
            role = UserRole.USER,
        )

        // When: Firebase login with different display name
        val result = service.createOrUpdateUserFromFirebase(
            firebaseUid = "firebase123",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40123456789",
            name = "Firebase Display Name",
            existingUser = existingUser
        )

        // Then: custom name should be preserved
        assertEquals("Custom User Name", result.name)
        assertEquals(Email.of("<EMAIL>"), result.email)
        assertEquals("+40123456789", result.phoneNumber)
    }

    @Test
    fun `createOrUpdateUserFromFirebase should update default email-based names`() {
        // Given: existing user with email as name (default behavior)
        val existingUser = User(
            id = UserId.generate(),
            firebaseUid = "firebase123",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40123456789",
            name = "<EMAIL>", // Name is same as email (default)
            role = UserRole.USER,
        )

        // When: Firebase login with display name
        val result = service.createOrUpdateUserFromFirebase(
            firebaseUid = "firebase123",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40123456789",
            name = "John Doe",
            existingUser = existingUser
        )

        // Then: name should be updated to Firebase display name
        assertEquals("John Doe", result.name)
        assertEquals(Email.of("<EMAIL>"), result.email)
        assertEquals("+40123456789", result.phoneNumber)
    }

    @Test
    fun `createOrUpdateUserFromFirebase should update names that look like emails`() {
        // Given: existing user with email-like name
        val existingUser = User(
            id = UserId.generate(),
            firebaseUid = "firebase123",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40123456789",
            name = "<EMAIL>", // Name looks like email
            role = UserRole.USER,
        )

        // When: Firebase login with display name
        val result = service.createOrUpdateUserFromFirebase(
            firebaseUid = "firebase123",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40123456789",
            name = "Jane Smith",
            existingUser = existingUser
        )

        // Then: name should be updated to Firebase display name
        assertEquals("Jane Smith", result.name)
    }

    @Test
    fun `createOrUpdateUserFromFirebase should not update when Firebase name matches current name`() {
        // Given: existing user with name matching Firebase
        val existingUser = User(
            id = UserId.generate(),
            firebaseUid = "firebase123",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40123456789",
            name = "John Doe",
            role = UserRole.USER,
        )

        // When: Firebase login with same display name
        val result = service.createOrUpdateUserFromFirebase(
            firebaseUid = "firebase123",
            email = Email.of("<EMAIL>"),
            phoneNumber = "+40123456789",
            name = "John Doe", // Same name
            existingUser = existingUser
        )

        // Then: name should remain unchanged
        assertEquals("John Doe", result.name)
    }
}
