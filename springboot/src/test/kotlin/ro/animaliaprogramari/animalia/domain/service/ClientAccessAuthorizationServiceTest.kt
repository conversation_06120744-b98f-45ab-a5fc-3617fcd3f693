import ro.animaliaprogramari.animalia.domain.model.*
import ro.animaliaprogramari.animalia.domain.service.ClientAccessAuthorizationService
import kotlin.test.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class ClientAccessAuthorizationServiceTest {
    private val service = ClientAccessAuthorizationService()

    private fun staff(
        role: StaffRole,
        clientAccess: ClientDataAccess,
    ) = Staff(
        StaffId.of("s"),
        UserId.of("u"),
        "nick",
        SalonId.of("salon"),
        role,
        StaffPermissions(
            clientAccess,
            canManageAppointments = true,
            canManageServices = false,
            canViewReports = false,
            canManageSchedule = true,
        ),
    )

    @Test
    fun `chief groomer full access`() {
        val s = staff(StaffRole.CHIEF_GROOMER, ClientDataAccess.FULL)
        assertTrue(service.canModifyClientData(s, ClientId.of("c")))
        assertTrue(service.canManageClientAppointments(s, ClientId.of("c")))
    }

    @Test
    fun `limited groomer cannot modify`() {
        val s = staff(StaffRole.GROOMER, ClientDataAccess.NONE)
        assertFalse(service.canModifyClientData(s, ClientId.of("c")))
        assertFalse(service.canViewClientAppointments(s, ClientId.of("c")))
    }

    @Test
    fun `no access`() {
        val s = staff(StaffRole.ASSISTANT, ClientDataAccess.NONE)
        assertFalse(service.canAccessClientData(s))
    }
}
