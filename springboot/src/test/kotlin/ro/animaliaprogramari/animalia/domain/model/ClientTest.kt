package ro.animaliaprogramari.animalia.domain.model

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

@DisplayName("Client Domain Model Tests")
class ClientTest {

    companion object {
        private const val CLIENT_NAME = "John Doe"
        private const val CLIENT_ADDRESS = "123 Main Street, Bucharest"
        private const val CLIENT_NOTES = "Prefers morning appointments"
        private val TEST_PHONE = PhoneNumber.of("+40123456789")
        private val TEST_EMAIL = Email.of("<EMAIL>")
        private val TEST_CLIENT_ID = ClientId.generate()
        private val TEST_SALON_ID = SalonId.generate()
        private val TEST_USER_ID = UserId.generate()
        private val TEST_USER_IDS = setOf(TEST_USER_ID)
    }

    private fun createTestClient(
        id: ClientId = TEST_CLIENT_ID,
        salonId: SalonId? = TEST_SALON_ID,
        name: String = CLIENT_NAME,
        phone: PhoneNumber? = TEST_PHONE,
        email: Email? = TEST_EMAIL,
        address: String? = CLIENT_ADDRESS,
        notes: String? = CLIENT_NOTES,
        isActive: Boolean = true,
        userIds: Set<UserId> = TEST_USER_IDS,
        createdAt: LocalDateTime = LocalDateTime.now(),
        updatedAt: LocalDateTime = LocalDateTime.now(),
    ): Client {
        return Client(
            id = id,
            salonId = salonId,
            name = name,
            phone = phone,
            email = email,
            address = address,
            notes = notes,
            isActive = isActive,
            userIds = userIds,
            createdAt = createdAt,
            updatedAt = updatedAt,
        )
    }

    @Nested
    @DisplayName("Client Creation and Validation")
    inner class ClientCreationAndValidation {

        @Test
        @DisplayName("Should create client with all fields")
        fun shouldCreateClientWithAllFields() {
            // Given & When
            val client = createTestClient()

            // Then
            assertEquals(TEST_CLIENT_ID, client.id)
            assertEquals(TEST_SALON_ID, client.salonId)
            assertEquals(CLIENT_NAME, client.name)
            assertEquals(TEST_PHONE, client.phone)
            assertEquals(TEST_EMAIL, client.email)
            assertEquals(CLIENT_ADDRESS, client.address)
            assertEquals(CLIENT_NOTES, client.notes)
            assertTrue(client.isActive)
            assertEquals(TEST_USER_IDS, client.userIds)
            assertNotNull(client.createdAt)
            assertNotNull(client.updatedAt)
        }

        @Test
        @DisplayName("Should create client with minimal required fields")
        fun shouldCreateClientWithMinimalRequiredFields() {
            // Given & When
            val client = createTestClient(
                salonId = null,
                phone = null,
                email = null,
                address = null,
                notes = null,
                userIds = emptySet()
            )

            // Then
            assertEquals(TEST_CLIENT_ID, client.id)
            assertNull(client.salonId)
            assertEquals(CLIENT_NAME, client.name)
            assertNull(client.phone)
            assertNull(client.email)
            assertNull(client.address)
            assertNull(client.notes)
            assertTrue(client.isActive)
            assertTrue(client.userIds.isEmpty())
        }

        @Test
        @DisplayName("Should create client with multiple user associations")
        fun shouldCreateClientWithMultipleUserAssociations() {
            // Given
            val userId1 = UserId.generate()
            val userId2 = UserId.generate()
            val userId3 = UserId.generate()
            val multipleUserIds = setOf(userId1, userId2, userId3)

            // When
            val client = createTestClient(userIds = multipleUserIds)

            // Then
            assertEquals(3, client.userIds.size)
            assertTrue(client.userIds.contains(userId1))
            assertTrue(client.userIds.contains(userId2))
            assertTrue(client.userIds.contains(userId3))
        }

        @Test
        @DisplayName("Should create inactive client")
        fun shouldCreateInactiveClient() {
            // Given & When
            val client = createTestClient(isActive = false)

            // Then
            assertFalse(client.isActive)
        }
    }

    @Nested
    @DisplayName("Constructor Overloads")
    inner class ConstructorOverloads {

        @Test
        @DisplayName("Should create client using secondary constructor with name and phone")
        fun shouldCreateClientUsingSecondaryConstructorWithNameAndPhone() {
            // Given
            val name = "Jane Smith"
            val phone = PhoneNumber.of("+40987654321")

            // When
            val client = Client(name, phone)

            // Then
            assertNotNull(client.id)
            assertNull(client.salonId)
            assertEquals(name, client.name)
            assertEquals(phone, client.phone)
            assertNull(client.email)
            assertNull(client.address)
            assertNull(client.notes)
            assertTrue(client.isActive)
            assertTrue(client.userIds.isEmpty())
            assertNotNull(client.createdAt)
            assertNotNull(client.updatedAt)
        }

        @Test
        @DisplayName("Should create client using secondary constructor with salon")
        fun shouldCreateClientUsingSecondaryConstructorWithSalon() {
            // Given
            val name = "Bob Johnson"
            val phone = PhoneNumber.of("+40555666777")
            val salonId = SalonId.generate()

            // When
            val client = Client(name, phone, salonId)

            // Then
            assertNotNull(client.id)
            assertEquals(salonId, client.salonId)
            assertEquals(name, client.name)
            assertEquals(phone, client.phone)
            assertTrue(client.isActive)
        }

        @Test
        @DisplayName("Should generate unique IDs for different clients using secondary constructor")
        fun shouldGenerateUniqueIdsForDifferentClientsUsingSecondaryConstructor() {
            // Given & When
            val client1 = Client("Client 1", PhoneNumber.of("+40111111111"))
            val client2 = Client("Client 2", PhoneNumber.of("+40222222222"))

            // Then
            assertNotEquals(client1.id, client2.id)
        }
    }

    @Nested
    @DisplayName("Client Information Updates")
    inner class ClientInformationUpdates {

        @Test
        @DisplayName("Should update client name only")
        fun shouldUpdateClientNameOnly() {
            // Given
            val originalClient = createTestClient()
            val newName = "Jane Smith"

            // When
            val updatedClient = originalClient.update(name = newName)

            // Then
            assertEquals(newName, updatedClient.name)
            assertEquals(originalClient.phone, updatedClient.phone)
            assertEquals(originalClient.email, updatedClient.email)
            assertEquals(originalClient.address, updatedClient.address)
            assertEquals(originalClient.notes, updatedClient.notes)
            assertEquals(originalClient.isActive, updatedClient.isActive)
            assertEquals(originalClient.userIds, updatedClient.userIds)
            assertTrue(updatedClient.updatedAt.isAfter(originalClient.updatedAt))
        }

        @Test
        @DisplayName("Should update client phone only")
        fun shouldUpdateClientPhoneOnly() {
            // Given
            val originalClient = createTestClient()
            val newPhone = PhoneNumber.of("+40987654321")

            // When
            val updatedClient = originalClient.update(phone = newPhone)

            // Then
            assertEquals(originalClient.name, updatedClient.name)
            assertEquals(newPhone, updatedClient.phone)
            assertEquals(originalClient.email, updatedClient.email)
            assertEquals(originalClient.address, updatedClient.address)
            assertEquals(originalClient.notes, updatedClient.notes)
            assertTrue(updatedClient.updatedAt.isAfter(originalClient.updatedAt))
        }

        @Test
        @DisplayName("Should update client email only")
        fun shouldUpdateClientEmailOnly() {
            // Given
            val originalClient = createTestClient()
            val newEmail = Email.of("<EMAIL>")

            // When
            val updatedClient = originalClient.update(email = newEmail)

            // Then
            assertEquals(originalClient.name, updatedClient.name)
            assertEquals(originalClient.phone, updatedClient.phone)
            assertEquals(newEmail, updatedClient.email)
            assertEquals(originalClient.address, updatedClient.address)
            assertEquals(originalClient.notes, updatedClient.notes)
            assertTrue(updatedClient.updatedAt.isAfter(originalClient.updatedAt))
        }

        @Test
        @DisplayName("Should update client address only")
        fun shouldUpdateClientAddressOnly() {
            // Given
            val originalClient = createTestClient()
            val newAddress = "456 Oak Avenue, Cluj-Napoca"

            // When
            val updatedClient = originalClient.update(address = newAddress)

            // Then
            assertEquals(originalClient.name, updatedClient.name)
            assertEquals(originalClient.phone, updatedClient.phone)
            assertEquals(originalClient.email, updatedClient.email)
            assertEquals(newAddress, updatedClient.address)
            assertEquals(originalClient.notes, updatedClient.notes)
            assertTrue(updatedClient.updatedAt.isAfter(originalClient.updatedAt))
        }

        @Test
        @DisplayName("Should update client notes only")
        fun shouldUpdateClientNotesOnly() {
            // Given
            val originalClient = createTestClient()
            val newNotes = "Prefers afternoon appointments"

            // When
            val updatedClient = originalClient.update(notes = newNotes)

            // Then
            assertEquals(originalClient.name, updatedClient.name)
            assertEquals(originalClient.phone, updatedClient.phone)
            assertEquals(originalClient.email, updatedClient.email)
            assertEquals(originalClient.address, updatedClient.address)
            assertEquals(newNotes, updatedClient.notes)
            assertTrue(updatedClient.updatedAt.isAfter(originalClient.updatedAt))
        }

        @Test
        @DisplayName("Should update all client information fields")
        fun shouldUpdateAllClientInformationFields() {
            // Given
            val originalClient = createTestClient()
            val newName = "Updated Name"
            val newPhone = PhoneNumber.of("+40999888777")
            val newEmail = Email.of("<EMAIL>")
            val newAddress = "789 Pine Street, Timisoara"
            val newNotes = "Updated notes"
            val newUserIds = setOf(UserId.generate(), UserId.generate())

            // When
            val updatedClient = originalClient.update(
                name = newName,
                phone = newPhone,
                email = newEmail,
                address = newAddress,
                notes = newNotes,
                userIds = newUserIds
            )

            // Then
            assertEquals(newName, updatedClient.name)
            assertEquals(newPhone, updatedClient.phone)
            assertEquals(newEmail, updatedClient.email)
            assertEquals(newAddress, updatedClient.address)
            assertEquals(newNotes, updatedClient.notes)
            assertEquals(newUserIds, updatedClient.userIds)
            assertTrue(updatedClient.updatedAt.isAfter(originalClient.updatedAt))
        }

        @Test
        @DisplayName("Should update user associations")
        fun shouldUpdateUserAssociations() {
            // Given
            val originalClient = createTestClient()
            val newUserId1 = UserId.generate()
            val newUserId2 = UserId.generate()
            val newUserIds = setOf(newUserId1, newUserId2)

            // When
            val updatedClient = originalClient.update(userIds = newUserIds)

            // Then
            assertEquals(originalClient.name, updatedClient.name)
            assertEquals(originalClient.phone, updatedClient.phone)
            assertEquals(newUserIds, updatedClient.userIds)
            assertEquals(2, updatedClient.userIds.size)
            assertTrue(updatedClient.userIds.contains(newUserId1))
            assertTrue(updatedClient.userIds.contains(newUserId2))
            assertTrue(updatedClient.updatedAt.isAfter(originalClient.updatedAt))
        }

        @Test
        @DisplayName("Should clear optional fields when updating with null values")
        fun shouldClearOptionalFieldsWhenUpdatingWithNullValues() {
            // Given
            val originalClient = createTestClient()

            // When
            val updatedClient = originalClient.update(
                phone = null,
                email = null,
                address = null,
                notes = null,
                userIds = emptySet()
            )

            // Then
            assertEquals(originalClient.name, updatedClient.name)
            assertNull(updatedClient.phone)
            assertNull(updatedClient.email)
            assertNull(updatedClient.address)
            assertNull(updatedClient.notes)
            assertTrue(updatedClient.userIds.isEmpty())
            assertTrue(updatedClient.updatedAt.isAfter(originalClient.updatedAt))
        }
    }

    @Nested
    @DisplayName("Client Activation and Deactivation")
    inner class ClientActivationAndDeactivation {

        @Test
        @DisplayName("Should activate inactive client")
        fun shouldActivateInactiveClient() {
            // Given
            val inactiveClient = createTestClient(isActive = false)

            // When
            val activatedClient = inactiveClient.activate()

            // Then
            assertTrue(activatedClient.isActive)
            assertTrue(activatedClient.updatedAt.isAfter(inactiveClient.updatedAt))
        }

        @Test
        @DisplayName("Should activate already active client")
        fun shouldActivateAlreadyActiveClient() {
            // Given
            val activeClient = createTestClient(isActive = true)

            // When
            val activatedClient = activeClient.activate()

            // Then
            assertTrue(activatedClient.isActive)
            assertTrue(activatedClient.updatedAt.isAfter(activeClient.updatedAt))
        }

        @Test
        @DisplayName("Should deactivate active client")
        fun shouldDeactivateActiveClient() {
            // Given
            val activeClient = createTestClient(isActive = true)

            // When
            val deactivatedClient = activeClient.deactivate()

            // Then
            assertFalse(deactivatedClient.isActive)
//            assertTrue(deactivatedClient.updatedAt.isAfter(activeClient.updatedAt))
        }

        @Test
        @DisplayName("Should deactivate already inactive client")
        fun shouldDeactivateAlreadyInactiveClient() {
            // Given
            val inactiveClient = createTestClient(isActive = false)

            // When
            val deactivatedClient = inactiveClient.deactivate()

            // Then
            assertFalse(deactivatedClient.isActive)
            assertTrue(deactivatedClient.updatedAt.isAfter(inactiveClient.updatedAt))
        }
    }

    @Nested
    @DisplayName("Factory Methods")
    inner class FactoryMethods {

        @Test
        @DisplayName("Should create client using companion object create method with all parameters")
        fun shouldCreateClientUsingCompanionObjectCreateMethodWithAllParameters() {
            // Given
            val name = "Factory Client"
            val phone = PhoneNumber.of("+40123123123")
            val email = Email.of("<EMAIL>")
            val address = "Factory Address"
            val notes = "Factory notes"
            val salonId = SalonId.generate()
            val userId1 = UserId.generate()
            val userId2 = UserId.generate()
            val userIds = setOf(userId1, userId2)

            // When
            val client = Client.create(
                name = name,
                phone = phone,
                email = email,
                address = address,
                notes = notes,
                salonId = salonId,
                userIds = userIds
            )

            // Then
            assertNotNull(client.id)
            assertEquals(salonId, client.salonId)
            assertEquals(name, client.name)
            assertEquals(phone, client.phone)
            assertEquals(email, client.email)
            assertEquals(address, client.address)
            assertEquals(notes, client.notes)
            assertTrue(client.isActive)
            assertEquals(userIds, client.userIds)
            assertNotNull(client.createdAt)
            assertNotNull(client.updatedAt)
        }

        @Test
        @DisplayName("Should create client using companion object create method with minimal parameters")
        fun shouldCreateClientUsingCompanionObjectCreateMethodWithMinimalParameters() {
            // Given
            val name = "Minimal Client"

            // When
            val client = Client.create(name = name)

            // Then
            assertNotNull(client.id)
            assertNull(client.salonId)
            assertEquals(name, client.name)
            assertNull(client.phone)
            assertNull(client.email)
            assertNull(client.address)
            assertNull(client.notes)
            assertTrue(client.isActive)
            assertTrue(client.userIds.isEmpty())
            assertNotNull(client.createdAt)
            assertNotNull(client.updatedAt)
        }

        @Test
        @DisplayName("Should create client using companion object create method with default values")
        fun shouldCreateClientUsingCompanionObjectCreateMethodWithDefaultValues() {
            // Given
            val name = "Default Client"
            val phone = PhoneNumber.of("+40555555555")

            // When
            val client = Client.create(name = name, phone = phone)

            // Then
            assertNotNull(client.id)
            assertNull(client.salonId)
            assertEquals(name, client.name)
            assertEquals(phone, client.phone)
            assertNull(client.email)
            assertNull(client.address)
            assertNull(client.notes)
            assertTrue(client.isActive)
            assertTrue(client.userIds.isEmpty())
        }

        @Test
        @DisplayName("Should generate unique IDs for different clients using create method")
        fun shouldGenerateUniqueIdsForDifferentClientsUsingCreateMethod() {
            // Given & When
            val client1 = Client.create("Client 1")
            val client2 = Client.create("Client 2")

            // Then
            assertNotEquals(client1.id, client2.id)
        }
    }

    @Nested
    @DisplayName("Data Class Behavior")
    inner class DataClassBehavior {

        @Test
        @DisplayName("Should support copy with modifications")
        fun shouldSupportCopyWithModifications() {
            // Given
            val originalClient = createTestClient()
            val newName = "Modified Name"
            val newPhone = PhoneNumber.of("+40777888999")

            // When
            val copiedClient = originalClient.copy(
                name = newName,
                phone = newPhone
            )

            // Then
            assertEquals(originalClient.id, copiedClient.id)
            assertEquals(originalClient.salonId, copiedClient.salonId)
            assertEquals(newName, copiedClient.name)
            assertEquals(newPhone, copiedClient.phone)
            assertEquals(originalClient.email, copiedClient.email)
            assertEquals(originalClient.address, copiedClient.address)
            assertEquals(originalClient.notes, copiedClient.notes)
            assertEquals(originalClient.isActive, copiedClient.isActive)
            assertEquals(originalClient.userIds, copiedClient.userIds)
            assertEquals(originalClient.createdAt, copiedClient.createdAt)
            assertEquals(originalClient.updatedAt, copiedClient.updatedAt)
        }

        @Test
        @DisplayName("Should support equality comparison")
        fun shouldSupportEqualityComparison() {
            // Given
            val client1 = createTestClient()
            val client2 = createTestClient()
            val client3 = client1.copy()

            // When & Then
            assertEquals(client1, client3) // Same data
            assertNotEquals(client1, client2) // Different IDs
            assertEquals(client1.hashCode(), client3.hashCode())
        }

        @Test
        @DisplayName("Should have meaningful toString representation")
        fun shouldHaveMeaningfulToStringRepresentation() {
            // Given
            val client = createTestClient()

            // When
            val stringRepresentation = client.toString()

            // Then
            assertTrue(stringRepresentation.contains("Client"))
            assertTrue(stringRepresentation.contains(client.id.value))
            assertTrue(stringRepresentation.contains(client.name))
            assertTrue(stringRepresentation.contains(client.isActive.toString()))
        }

        @Test
        @DisplayName("Should work correctly in collections")
        fun shouldWorkCorrectlyInCollections() {
            // Given
            val client1 = createTestClient()
            val client2 = createTestClient()
            val client3 = client1.copy() // Same as client1

            // When
            val clientSet = setOf(client1, client2, client3)
            val clientList = listOf(client1, client2, client3)

            // Then
            assertEquals(2, clientSet.size) // client1 and client3 are equal, so only 2 unique
            assertEquals(3, clientList.size) // All elements preserved in list
            assertTrue(clientSet.contains(client1))
            assertTrue(clientSet.contains(client2))
        }
    }

    @Nested
    @DisplayName("Edge Cases")
    inner class EdgeCases {

        @Test
        @DisplayName("Should handle empty string values appropriately")
        fun shouldHandleEmptyStringValuesAppropriately() {
            // Given
            val emptyName = ""
            val emptyAddress = ""
            val emptyNotes = ""

            // When
            val client = createTestClient(
                name = emptyName,
                address = emptyAddress,
                notes = emptyNotes
            )

            // Then
            assertEquals(emptyName, client.name)
            assertEquals(emptyAddress, client.address)
            assertEquals(emptyNotes, client.notes)
        }

        @Test
        @DisplayName("Should handle null optional fields")
        fun shouldHandleNullOptionalFields() {
            // Given & When
            val client = createTestClient(
                salonId = null,
                phone = null,
                email = null,
                address = null,
                notes = null,
                userIds = emptySet()
            )

            // Then
            assertNull(client.salonId)
            assertNull(client.phone)
            assertNull(client.email)
            assertNull(client.address)
            assertNull(client.notes)
            assertTrue(client.userIds.isEmpty())
        }

        @Test
        @DisplayName("Should preserve immutability of original client during updates")
        fun shouldPreserveImmutabilityOfOriginalClientDuringUpdates() {
            // Given
            val originalClient = createTestClient()
            val originalName = originalClient.name
            val originalPhone = originalClient.phone
            val originalIsActive = originalClient.isActive

            // When
            val updatedClient = originalClient
                .update(name = "New Name", phone = PhoneNumber.of("+40999999999"))
                .deactivate()

            // Then
            // Original client should remain unchanged
            assertEquals(originalName, originalClient.name)
            assertEquals(originalPhone, originalClient.phone)
            assertEquals(originalIsActive, originalClient.isActive)

            // Updated client should have new values
            assertEquals("New Name", updatedClient.name)
            assertEquals(PhoneNumber.of("+40999999999"), updatedClient.phone)
            assertFalse(updatedClient.isActive)
        }

        @Test
        @DisplayName("Should handle method chaining correctly")
        fun shouldHandleMethodChainingCorrectly() {
            // Given
            val originalClient = createTestClient(isActive = false)

            // When
            val chainedClient = originalClient
                .activate()
                .update(
                    name = "Chained Client",
                    email = Email.of("<EMAIL>"),
                    notes = "Updated via chaining"
                )

            // Then
            assertTrue(chainedClient.isActive)
            assertEquals("Chained Client", chainedClient.name)
            assertEquals(Email.of("<EMAIL>"), chainedClient.email)
            assertEquals("Updated via chaining", chainedClient.notes)
            assertTrue(chainedClient.updatedAt.isAfter(originalClient.updatedAt))
        }

        @Test
        @DisplayName("Should handle extreme timestamp scenarios")
        fun shouldHandleExtremeTimestampScenarios() {
            // Given
            val pastTime = LocalDateTime.of(2020, 1, 1, 0, 0)
            val futureTime = LocalDateTime.of(2030, 12, 31, 23, 59)

            // When
            val clientWithPastTime = createTestClient(
                createdAt = pastTime,
                updatedAt = pastTime
            )
            val clientWithFutureTime = createTestClient(
                createdAt = futureTime,
                updatedAt = futureTime
            )

            // Then
            assertEquals(pastTime, clientWithPastTime.createdAt)
            assertEquals(pastTime, clientWithPastTime.updatedAt)
            assertEquals(futureTime, clientWithFutureTime.createdAt)
            assertEquals(futureTime, clientWithFutureTime.updatedAt)
        }

        @Test
        @DisplayName("Should handle special characters in string fields")
        fun shouldHandleSpecialCharactersInStringFields() {
            // Given
            val specialName = "José María Ñoño-Pérez O'Connor"
            val specialAddress = "Str. Ștefan cel Mare nr. 123, ap. 4B, București"
            val specialNotes = "Client prefers: morning appointments & gentle handling (very important!)"

            // When
            val client = createTestClient(
                name = specialName,
                address = specialAddress,
                notes = specialNotes
            )

            // Then
            assertEquals(specialName, client.name)
            assertEquals(specialAddress, client.address)
            assertEquals(specialNotes, client.notes)
        }

        @Test
        @DisplayName("Should maintain referential integrity during updates")
        fun shouldMaintainReferentialIntegrityDuringUpdates() {
            // Given
            val originalClient = createTestClient()
            val originalId = originalClient.id
            val originalSalonId = originalClient.salonId
            val originalCreatedAt = originalClient.createdAt

            // When
            val updatedClient = originalClient
                .update(name = "Updated Name", notes = "Updated notes")
                .activate()

            // Then
            // These fields should never change during updates
            assertEquals(originalId, updatedClient.id)
            assertEquals(originalSalonId, updatedClient.salonId)
            assertEquals(originalCreatedAt, updatedClient.createdAt)
        }

        @Test
        @DisplayName("Should handle large user ID collections")
        fun shouldHandleLargeUserIdCollections() {
            // Given
            val largeUserIdSet = (1..100).map { UserId.generate() }.toSet()

            // When
            val client = createTestClient(userIds = largeUserIdSet)

            // Then
            assertEquals(100, client.userIds.size)
            assertEquals(largeUserIdSet, client.userIds)
        }

        @Test
        @DisplayName("Should handle duplicate user IDs in set")
        fun shouldHandleDuplicateUserIdsInSet() {
            // Given
            val userId1 = UserId.generate()
            val userId2 = UserId.generate()
            val userIdList = listOf(userId1, userId2, userId1) // Duplicate userId1

            // When
            val client = createTestClient(userIds = userIdList.toSet())

            // Then
            assertEquals(2, client.userIds.size) // Duplicates removed by Set
            assertTrue(client.userIds.contains(userId1))
            assertTrue(client.userIds.contains(userId2))
        }
    }
}
